{"cells": [{"cell_type": "code", "execution_count": 1, "id": "19bf0c78-f3de-4e2b-b1cd-36bfa609171e", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/.local/lib/python3.10/site-packages/matplotlib/projections/__init__.py:63: UserWarning: Unable to import Axes3D. This may be due to multiple versions of Matplotlib being installed (e.g. as a system package and as a pip package). As a result, the 3D projection is not available.\n", "  warnings.warn(\"Unable to import Axes3D. This may be due to multiple versions of \"\n"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n"]}, {"cell_type": "code", "execution_count": 2, "id": "dc34371f-71f5-4f45-80a7-13dff27218b7", "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'warnings' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[2], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m \u001b[43mwarnings\u001b[49m\u001b[38;5;241m.\u001b[39mfilterwarnings(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mignore\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m      3\u001b[0m plt\u001b[38;5;241m.\u001b[39mstyle\u001b[38;5;241m.\u001b[39muse(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mfivethir<PERSON>eight\u001b[39m\u001b[38;5;124m'\u001b[39m)\n\u001b[1;32m      4\u001b[0m sns\u001b[38;5;241m.\u001b[39mset(style\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mwhitegrid\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n", "\u001b[0;31mNameError\u001b[0m: name 'warnings' is not defined"]}], "source": ["warnings.filterwarnings(\"ignore\")\n", "\n", "plt.style.use('fivethirtyeight')\n", "sns.set(style=\"whitegrid\")"]}, {"cell_type": "code", "execution_count": null, "id": "6220bf7c-1a27-4376-8ac2-1782c4c1326c", "metadata": {}, "outputs": [], "source": ["data = pd.read_csv('Brain Dead CompScholar Dataset.csv')\n", "data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "2e58ec7e-16bc-4971-8e77-4faf34053ced", "metadata": {}, "outputs": [], "source": ["column_names = data.columns\n", "print(column_names)"]}, {"cell_type": "code", "execution_count": null, "id": "e9b09059-892c-47e8-890f-cddf5d189c97", "metadata": {}, "outputs": [], "source": ["data.info()"]}, {"cell_type": "code", "execution_count": null, "id": "369b7ad1-20f4-4b8b-a57c-cbab4d5c900f", "metadata": {}, "outputs": [], "source": ["paper_type_counts = data['Paper Type'].value_counts()\n", "print(\"\\nFrequency of each Paper Type:\")\n", "paper_type_counts"]}, {"cell_type": "code", "execution_count": null, "id": "7f06b87b-d5f6-43a9-b653-30e20a50d8b7", "metadata": {}, "outputs": [], "source": ["plt.figure(figsize=(10, 6))\n", "paper_type_counts.plot(kind='bar', color='skyblue')\n", "plt.title('Distribution of Paper Types')\n", "plt.xlabel('Paper Type')\n", "plt.ylabel('Count')\n", "plt.xticks(rotation=45)\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "ed97f5e1-f338-45d9-8514-0ce7d0df9016", "metadata": {}, "outputs": [], "source": ["topic_counts = data['Topic'].value_counts()\n", "print(\"\\nFrequency of each Topic:\")\n", "topic_counts"]}, {"cell_type": "code", "execution_count": null, "id": "eb922a03-9d5a-4e16-aede-3d4d990c88b8", "metadata": {}, "outputs": [], "source": ["plt.figure(figsize=(10, 6))\n", "topic_counts.plot(kind='bar', color='salmon')\n", "plt.title('Distribution of Topics')\n", "plt.xlabel('Topic')\n", "plt.ylabel('Count')\n", "plt.xticks(rotation=45)\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "2b723674-4e96-4e2a-924c-bf81cc6ba748", "metadata": {}, "outputs": [], "source": ["data['abstract_length'] = data['Abstract'].apply(lambda x: len(str(x).split()))\n", "print(\"\\nStatistics of Abstract Lengths (in words):\")\n", "data['abstract_length'].describe()"]}, {"cell_type": "code", "execution_count": null, "id": "68f10917-1a7a-4f24-9afb-eec43d205875", "metadata": {}, "outputs": [], "source": ["plt.figure(figsize=(10, 6))\n", "sns.histplot(data['abstract_length'], bins=20, kde=True, color='green')\n", "plt.title('Distribution of Abstract Lengths (Word Count)')\n", "plt.xlabel('Word Count')\n", "plt.ylabel('Frequency')\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "1692910c-fa57-4198-a492-d094115aa0b1", "metadata": {}, "outputs": [], "source": ["from unsloth import FastLanguageModel, get_chat_template\n", "from datasets import load_dataset\n", "from trl import SFTTrainer\n", "from transformers import TrainingArguments, DataCollatorForSeq2Seq\n", "from unsloth.chat_templates import train_on_responses_only\n", "import torch"]}, {"cell_type": "code", "execution_count": null, "id": "fa085cba-d1ab-4a9e-8928-b7e4b34caf24", "metadata": {}, "outputs": [], "source": ["# Load model\n", "max_seq_length = 2048\n", "model, tokenizer = FastLanguageModel.from_pretrained(\n", "    model_name=\"unsloth/Llama-3.2-3B-Instruct\",\n", "    max_seq_length=max_seq_length,\n", "    dtype=None,\n", "    load_in_4bit=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "26efb0a8-35d6-401e-8d94-5895d3f66d64", "metadata": {}, "outputs": [], "source": ["model = FastLanguageModel.get_peft_model(\n", "    model, r=16, target_modules=[\"q_proj\", \"k_proj\", \"v_proj\", \"o_proj\", \"gate_proj\", \"up_proj\", \"down_proj\"],\n", "    lora_alpha=16, lora_dropout=0, bias=\"none\", use_gradient_checkpointing=\"unsloth\", random_state=3407\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "1ead1947-8273-40d0-b250-ce35c1eda6e5", "metadata": {}, "outputs": [], "source": ["# Load dataset\n", "dataset = load_dataset('csv', data_files='path/to/your/dataset.csv', split='train')"]}, {"cell_type": "code", "execution_count": null, "id": "058526b0-8241-4b1b-b586-e046e79b8a01", "metadata": {}, "outputs": [], "source": ["# Add conversations\n", "def add_conversations(example):\n", "    return {\n", "        \"conversations\": [\n", "            {\"role\": \"user\", \"content\": f\"Summarize the following research article:\\n\\n{example['Document']}\"},\n", "            {\"role\": \"assistant\", \"content\": example['Summary']}\n", "        ]\n", "    }\n", "dataset = dataset.map(add_conversations)"]}, {"cell_type": "code", "execution_count": null, "id": "7b499849-1737-4b5e-982f-fd092a7c182b", "metadata": {}, "outputs": [], "source": ["# Apply chat template\n", "tokenizer = get_chat_template(tokenizer, chat_template=\"llama-3.1\")\n", "def formatting_prompts_func(examples):\n", "    convos = examples[\"conversations\"]\n", "    texts = [tokenizer.apply_chat_template(convo, tokenize=False, add_generation_prompt=False) for convo in convos]\n", "    return {\"text\": texts}\n", "dataset = dataset.map(formatting_prompts_func, batched=True)"]}, {"cell_type": "code", "execution_count": null, "id": "a75f14c3-2969-453f-b0f6-7a53f5e803f2", "metadata": {}, "outputs": [], "source": ["# Set up trainer\n", "trainer = SFTT<PERSON>er(\n", "    model=model,\n", "    tokenizer=tokenizer,\n", "    train_dataset=dataset,\n", "    dataset_text_field=\"text\",\n", "    max_seq_length=max_seq_length,\n", "    data_collator=DataCollatorForSeq2Seq(tokenizer=tokenizer),\n", "    dataset_num_proc=2,\n", "    packing=False,\n", "    args=TrainingArguments(\n", "        per_device_train_batch_size=2,\n", "        gradient_accumulation_steps=4,\n", "        warmup_steps=5,\n", "        max_steps=60,\n", "        learning_rate=2e-4,\n", "        fp16=not torch.cuda.is_bf16_supported(),\n", "        bf16=torch.cuda.is_bf16_supported(),\n", "        logging_steps=1,\n", "        optim=\"adamw_8bit\",\n", "        weight_decay=0.01,\n", "        lr_scheduler_type=\"linear\",\n", "        seed=3407,\n", "        output_dir=\"outputs\",\n", "        report_to=\"none\",\n", "    ),\n", ")\n", "trainer = train_on_responses_only(\n", "    trainer,\n", "    instruction_part=\"<|start_header_id|>user<|end_header_id|>\\n\\n\",\n", "    response_part=\"<|start_header_id|>assistant<|end_header_id|>\\n\\n\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "094a39fd-e8ec-462a-9038-95fc0a19b3a6", "metadata": {}, "outputs": [], "source": ["# Train\n", "trainer.train()"]}, {"cell_type": "code", "execution_count": null, "id": "0fe87e34-9385-4015-9d5a-5862f5c103ab", "metadata": {}, "outputs": [], "source": ["# Inference\n", "FastLanguageModel.for_inference(model)\n", "messages = [\n", "    {\"role\": \"user\", \"content\": \"Summarize the following research article:\\n\\n[i will give it ]\"},\n", "]\n", "inputs = tokenizer.apply_chat_template(messages, tokenize=True, add_generation_prompt=True, return_tensors=\"pt\").to(\"cuda\")\n", "outputs = model.generate(input_ids=inputs, max_new_tokens=256, use_cache=True)\n", "print(tokenizer.batch_decode(outputs)[0])"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 5}