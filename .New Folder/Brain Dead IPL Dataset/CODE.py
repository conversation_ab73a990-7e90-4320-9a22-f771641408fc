# %%
import torch
from transformers import T5ForConditionalGeneration, T5Tokenizer, Seq2SeqTrainingArguments, Seq2SeqTrainer
from datasets import load_dataset, DatasetDict
import pandas as pd
from sklearn.model_selection import train_test_split
from peft import LoraConfig, get_peft_model, TaskType
import evaluate
import numpy as np


# %%
# 1. Load and preprocess dataset
from datasets import Dataset


def load_and_preprocess_data(file_path):
    # Load dataset
    df = pd.read_csv(file_path)
    
    # Clean data
    df = df.dropna()
    df = df[df['abstract'].str.strip().astype(bool)]  # Remove empty abstracts
    
    # Split dataset
    train_df, temp_df = train_test_split(df, test_size=0.2, random_state=42)
    val_df, test_df = train_test_split(temp_df, test_size=0.5, random_state=42)
    
    return DatasetDict({
        'train': Dataset.from_pandas(train_df),
        'val': Dataset.from_pandas(val_df),
        'test': Dataset.from_pandas(test_df)
    })

# %%
# 2. Initialize model and tokenizer
model_name = "t5-base"
tokenizer = T5Tokenizer.from_pretrained(model_name)
model = T5ForConditionalGeneration.from_pretrained(model_name)

# %%
# 3. Tokenization function with research paper specific processing
def preprocess_function(examples):
    inputs = ["summarize: " + doc[:5000] for doc in examples["article"]]  # Truncate long articles
    targets = [abs[:1000] for abs in examples["abstract"]]  # Truncate long abstracts
    
    model_inputs = tokenizer(
        inputs,
        max_length=512,
        truncation=True,
        padding="max_length"
    )
    
    with tokenizer.as_target_tokenizer():
        labels = tokenizer(
            targets,
            max_length=256,
            truncation=True,
            padding="max_length"
        )
    
    model_inputs["labels"] = labels["input_ids"]
    return model_inputs


# 4. Load and process dataset
dataset = load_and_preprocess_data("train.csv")

# %%
# Process all splits
tokenized_datasets = dataset.map(
    preprocess_function,
    batched=True,
    remove_columns=["article", "abstract"]
)


# %%
# 5. Configure LoRA
peft_config = LoraConfig(
    r=32,
    lora_alpha=32,
    target_modules=["q", "v"],  # T5 attention matrices
    lora_dropout=0.05,
    bias="none",
    task_type=TaskType.SEQ_2_SEQ_LM
)


# %%
# 6. Create PEFT model
model = get_peft_model(model, peft_config)
print(f"Trainable parameters: {sum(p.numel() for p in model.parameters() if p.requires_grad)}")

# %%
# Training arguments
training_args = Seq2SeqTrainingArguments(
    output_dir="./t5-summarizer",
    evaluation_strategy="steps",
    max_steps=30000,
    eval_steps=500,
    save_strategy="steps",
    save_steps=500,
    learning_rate=3e-5,
    per_device_train_batch_size=6,
    per_device_eval_batch_size=6,
    gradient_accumulation_steps=4,
    weight_decay=0.01,
    num_train_epochs=3,
    predict_with_generate=True,
    fp16=True,
    report_to="tensorboard",
    logging_steps=100,
    push_to_hub=False,
)

# %%
# 8. Create trainer
trainer = Seq2SeqTrainer(
    model=model,
    args=training_args,
    train_dataset=tokenized_datasets["train"],
    eval_dataset=tokenized_datasets["val"],
    tokenizer=tokenizer,
)

# %%
# 9. Start training
trainer.train()


# %%
model.save_pretrained("./t5-summarizer-final")
tokenizer.save_pretrained("./t5-summarizer-final")

# %%
# Updated evaluation function with sample limit
def evaluate_summarization(model, tokenizer, dataset, max_samples=3):
    rouge = evaluate.load('rouge')
    
    generated_summaries = []
    reference_summaries = []
    
    # Limit to first 3 samples
    for example in dataset.select(range(max_samples)):
        inputs = tokenizer(
            "summarize: " + example["article"][:5000],
            max_length=512,
            truncation=True,
            return_tensors="pt"
        ).to(model.device)
        
        outputs = model.generate(
            input_ids=inputs.input_ids,
            attention_mask=inputs.attention_mask,
            max_length=256,
            num_beams=4,
            early_stopping=True
        )
        
        generated = tokenizer.decode(outputs[0], skip_special_tokens=True)
        generated_summaries.append(generated)
        reference_summaries.append(example["abstract"])
    
    return rouge.compute(
        predictions=generated_summaries,
        references=reference_summaries,
        use_stemmer=True
    )


# %%
# Now run evaluation with only 3 samples
test_results = evaluate_summarization(model, tokenizer, dataset["test"], max_samples=100)
print("Test Results (100 samples):", test_results)

# %%
# 13. Inference function
def generate_research_summary(article, model, tokenizer):
    inputs = tokenizer(
        "summarize: " + article[:5000],
        max_length=512,
        truncation=True,
        return_tensors="pt"
    ).to(model.device)
    
    outputs = model.generate(
            input_ids=inputs.input_ids,
            attention_mask=inputs.attention_mask,
            max_length=256,
            num_beams=4,
            early_stopping=True
        )
    
    return tokenizer.decode(outputs[0], skip_special_tokens=True)

# %%
# Example usage
sample_article = dataset["test"][4]["article"]
summary = generate_research_summary(sample_article, model, tokenizer)
print("\nGenerated Summary:", summary)

# %%



