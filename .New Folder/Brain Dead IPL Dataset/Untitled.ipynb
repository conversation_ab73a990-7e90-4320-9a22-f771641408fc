{"cells": [{"cell_type": "code", "execution_count": 1, "id": "d81fe114-1f75-44c3-8334-e12be06b2fed", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from sklearn.preprocessing import StandardScaler, LabelEncoder\n", "from sklearn.model_selection import train_test_split, GridSearchCV\n", "from sklearn.ensemble import RandomForestClassifier\n", "from sklearn.metrics import accuracy_score, classification_report, confusion_matrix\n", "from sklearn.cluster import KMeans\n", "import xgboost as xgb\n", "from sklearn.ensemble import VotingClassifier\n", "import warnings"]}, {"cell_type": "code", "execution_count": 2, "id": "0502d581-cd1b-4b91-a922-9d69a929b903", "metadata": {}, "outputs": [], "source": ["warnings.filterwarnings(\"ignore\")\n", "\n", "plt.style.use('fivethirtyeight')\n", "sns.set(style=\"whitegrid\")"]}, {"cell_type": "code", "execution_count": 3, "id": "acad0374-4f1a-4a63-9604-44c19149ffef", "metadata": {}, "outputs": [], "source": ["matches = pd.read_csv('matches.csv')\n", "deliveries = pd.read_csv('deliveries.csv')"]}, {"cell_type": "code", "execution_count": 4, "id": "8b1e8013-82e6-4797-8789-884a5a19a7fd", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Matches data shape: (1095, 20)\n", "Deliveries data shape: (260920, 17)\n"]}], "source": ["print(\"Matches data shape:\", matches.shape)\n", "print(\"Deliveries data shape:\", deliveries.shape)"]}, {"cell_type": "code", "execution_count": 6, "id": "23edcb03-ea85-4bf2-818a-a4c8622a7b34", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Matches data head:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>season</th>\n", "      <th>city</th>\n", "      <th>date</th>\n", "      <th>match_type</th>\n", "      <th>player_of_match</th>\n", "      <th>venue</th>\n", "      <th>team1</th>\n", "      <th>team2</th>\n", "      <th>toss_winner</th>\n", "      <th>toss_decision</th>\n", "      <th>winner</th>\n", "      <th>result</th>\n", "      <th>result_margin</th>\n", "      <th>target_runs</th>\n", "      <th>target_overs</th>\n", "      <th>super_over</th>\n", "      <th>method</th>\n", "      <th>umpire1</th>\n", "      <th>umpire2</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>335982</td>\n", "      <td>2007/08</td>\n", "      <td>Bangalore</td>\n", "      <td>2008-04-18</td>\n", "      <td>League</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>M Chinnaswamy Stadium</td>\n", "      <td>Royal Challengers Bangalore</td>\n", "      <td>Kolkata Knight Riders</td>\n", "      <td>Royal Challengers Bangalore</td>\n", "      <td>field</td>\n", "      <td>Kolkata Knight Riders</td>\n", "      <td>runs</td>\n", "      <td>140.0</td>\n", "      <td>223.0</td>\n", "      <td>20.0</td>\n", "      <td>N</td>\n", "      <td>NaN</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td><PERSON><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>335983</td>\n", "      <td>2007/08</td>\n", "      <td>Chandigarh</td>\n", "      <td>2008-04-19</td>\n", "      <td>League</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Punjab Cricket Association Stadium, Mohali</td>\n", "      <td>Kings XI Punjab</td>\n", "      <td>Chennai Super Kings</td>\n", "      <td>Chennai Super Kings</td>\n", "      <td>bat</td>\n", "      <td>Chennai Super Kings</td>\n", "      <td>runs</td>\n", "      <td>33.0</td>\n", "      <td>241.0</td>\n", "      <td>20.0</td>\n", "      <td>N</td>\n", "      <td>NaN</td>\n", "      <td><PERSON></td>\n", "      <td><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>335984</td>\n", "      <td>2007/08</td>\n", "      <td>Delhi</td>\n", "      <td>2008-04-19</td>\n", "      <td>League</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>Delhi Daredevils</td>\n", "      <td>Rajasthan Royals</td>\n", "      <td>Rajasthan Royals</td>\n", "      <td>bat</td>\n", "      <td>Delhi Daredevils</td>\n", "      <td>wickets</td>\n", "      <td>9.0</td>\n", "      <td>130.0</td>\n", "      <td>20.0</td>\n", "      <td>N</td>\n", "      <td>NaN</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>GA Pratapkumar</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>335985</td>\n", "      <td>2007/08</td>\n", "      <td>Mumbai</td>\n", "      <td>2008-04-20</td>\n", "      <td>League</td>\n", "      <td>MV Boucher</td>\n", "      <td>Wankhede Stadium</td>\n", "      <td>Mumbai Indians</td>\n", "      <td>Royal Challengers Bangalore</td>\n", "      <td>Mumbai Indians</td>\n", "      <td>bat</td>\n", "      <td>Royal Challengers Bangalore</td>\n", "      <td>wickets</td>\n", "      <td>5.0</td>\n", "      <td>166.0</td>\n", "      <td>20.0</td>\n", "      <td>N</td>\n", "      <td>NaN</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>335986</td>\n", "      <td>2007/08</td>\n", "      <td>Kolkata</td>\n", "      <td>2008-04-20</td>\n", "      <td>League</td>\n", "      <td><PERSON></td>\n", "      <td>Eden Gardens</td>\n", "      <td>Kolkata Knight Riders</td>\n", "      <td>Deccan Chargers</td>\n", "      <td>Deccan Chargers</td>\n", "      <td>bat</td>\n", "      <td>Kolkata Knight Riders</td>\n", "      <td>wickets</td>\n", "      <td>5.0</td>\n", "      <td>111.0</td>\n", "      <td>20.0</td>\n", "      <td>N</td>\n", "      <td>NaN</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td><PERSON></td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       id   season        city        date match_type player_of_match  \\\n", "0  335982  2007/08   Bangalore  2008-04-18     League     BB McCullum   \n", "1  335983  2007/08  Chandigarh  2008-04-19     League      MEK Hussey   \n", "2  335984  2007/08       Delhi  2008-04-19     League     MF Maharoof   \n", "3  335985  2007/08      Mumbai  2008-04-20     League      MV Boucher   \n", "4  335986  2007/08     Kolkata  2008-04-20     League       DJ <PERSON><PERSON><PERSON>   \n", "\n", "                                        venue                        team1  \\\n", "0                       M Chinnaswamy Stadium  Royal Challengers Bangalore   \n", "1  Punjab Cricket Association Stadium, Mohali              Kings XI Punjab   \n", "2                            Feroz Shah Kotla             Delhi Daredevils   \n", "3                            Wankhede Stadium               Mumbai Indians   \n", "4                                Eden Gardens        Kolkata Knight Riders   \n", "\n", "                         team2                  toss_winner toss_decision  \\\n", "0        Kolkata Knight Riders  Royal Challengers Bangalore         field   \n", "1          Chennai Super Kings          Chennai Super Kings           bat   \n", "2             Rajasthan Royals             Rajasthan Royals           bat   \n", "3  Royal Challengers Bangalore               Mumbai Indians           bat   \n", "4              Deccan Chargers              Deccan Chargers           bat   \n", "\n", "                        winner   result  result_margin  target_runs  \\\n", "0        Kolkata Knight Riders     runs          140.0        223.0   \n", "1          Chennai Super Kings     runs           33.0        241.0   \n", "2             Delhi Daredevils  wickets            9.0        130.0   \n", "3  Royal Challengers Bangalore  wickets            5.0        166.0   \n", "4        Kolkata Knight Riders  wickets            5.0        111.0   \n", "\n", "   target_overs super_over method    umpire1         umpire2  \n", "0          20.0          N    NaN  Asad Rauf     RE <PERSON>  \n", "1          20.0          N    NaN  MR Benson      SL Shastri  \n", "2          20.0          N    NaN  Aleem Dar  GA Pratapkumar  \n", "3          20.0          N    NaN   SJ Davis       DJ <PERSON>  \n", "4          20.0          N    NaN  BF Bowden     K Hariharan  "]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["print(\"\\nMatches data head:\")\n", "matches.head()"]}, {"cell_type": "code", "execution_count": 7, "id": "439194dd-86b3-4d0b-b0b4-a0edbe458af6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Deliveries data head:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>match_id</th>\n", "      <th>inning</th>\n", "      <th>batting_team</th>\n", "      <th>bowling_team</th>\n", "      <th>over</th>\n", "      <th>ball</th>\n", "      <th>batter</th>\n", "      <th>bowler</th>\n", "      <th>non_striker</th>\n", "      <th>batsman_runs</th>\n", "      <th>extra_runs</th>\n", "      <th>total_runs</th>\n", "      <th>extras_type</th>\n", "      <th>is_wicket</th>\n", "      <th>player_dismissed</th>\n", "      <th>dismissal_kind</th>\n", "      <th>fielder</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>335982</td>\n", "      <td>1</td>\n", "      <td>Kolkata Knight Riders</td>\n", "      <td>Royal Challengers Bangalore</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>SC Ganguly</td>\n", "      <td><PERSON></td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>legbyes</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>335982</td>\n", "      <td>1</td>\n", "      <td>Kolkata Knight Riders</td>\n", "      <td>Royal Challengers Bangalore</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td><PERSON></td>\n", "      <td>SC Ganguly</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>335982</td>\n", "      <td>1</td>\n", "      <td>Kolkata Knight Riders</td>\n", "      <td>Royal Challengers Bangalore</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td><PERSON></td>\n", "      <td>SC Ganguly</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>wides</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>335982</td>\n", "      <td>1</td>\n", "      <td>Kolkata Knight Riders</td>\n", "      <td>Royal Challengers Bangalore</td>\n", "      <td>0</td>\n", "      <td>4</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td><PERSON></td>\n", "      <td>SC Ganguly</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>335982</td>\n", "      <td>1</td>\n", "      <td>Kolkata Knight Riders</td>\n", "      <td>Royal Challengers Bangalore</td>\n", "      <td>0</td>\n", "      <td>5</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td><PERSON></td>\n", "      <td>SC Ganguly</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   match_id  inning           batting_team                 bowling_team  over  \\\n", "0    335982       1  Kolkata Knight Riders  Royal Challengers Bangalore     0   \n", "1    335982       1  Kolkata Knight Riders  Royal Challengers Bangalore     0   \n", "2    335982       1  Kolkata Knight Riders  Royal Challengers Bangalore     0   \n", "3    335982       1  Kolkata Knight Riders  Royal Challengers Bangalore     0   \n", "4    335982       1  Kolkata Knight Riders  Royal Challengers Bangalore     0   \n", "\n", "   ball       batter   bowler  non_striker  batsman_runs  extra_runs  \\\n", "0     1   SC Ganguly  P Kumar  BB McCullum             0           1   \n", "1     2  BB McCullum  P Kumar   SC Ganguly             0           0   \n", "2     3  BB McCullum  P Kumar   SC Ganguly             0           1   \n", "3     4  BB McCullum  P Kumar   SC Ganguly             0           0   \n", "4     5  BB McCullum  P Kumar   SC Ganguly             0           0   \n", "\n", "   total_runs extras_type  is_wicket player_dismissed dismissal_kind fielder  \n", "0           1     legbyes          0              NaN            NaN     NaN  \n", "1           0         NaN          0              NaN            NaN     NaN  \n", "2           1       wides          0              NaN            NaN     NaN  \n", "3           0         NaN          0              NaN            NaN     NaN  \n", "4           0         NaN          0              NaN            NaN     NaN  "]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["print(\"\\nDeliveries data head:\")\n", "deliveries.head()"]}, {"cell_type": "code", "execution_count": 8, "id": "233dc8e3-5981-450c-9c3f-4e510e4694f6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Missing values in matches dataset:\n"]}, {"data": {"text/plain": ["id                    0\n", "season                0\n", "city                 51\n", "date                  0\n", "match_type            0\n", "player_of_match       5\n", "venue                 0\n", "team1                 0\n", "team2                 0\n", "toss_winner           0\n", "toss_decision         0\n", "winner                5\n", "result                0\n", "result_margin        19\n", "target_runs           3\n", "target_overs          3\n", "super_over            0\n", "method             1074\n", "umpire1               0\n", "umpire2               0\n", "dtype: int64"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["print(\"\\nMissing values in matches dataset:\")\n", "matches.isnull().sum()"]}, {"cell_type": "code", "execution_count": 9, "id": "ab92e15f-4dd4-468b-af07-5d0468c98320", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Missing values in deliveries dataset:\n"]}, {"data": {"text/plain": ["match_id                 0\n", "inning                   0\n", "batting_team             0\n", "bowling_team             0\n", "over                     0\n", "ball                     0\n", "batter                   0\n", "bowler                   0\n", "non_striker              0\n", "batsman_runs             0\n", "extra_runs               0\n", "total_runs               0\n", "extras_type         246795\n", "is_wicket                0\n", "player_dismissed    247970\n", "dismissal_kind      247970\n", "fielder             251566\n", "dtype: int64"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["print(\"\\nMissing values in deliveries dataset:\")\n", "deliveries.isnull().sum()"]}, {"cell_type": "code", "execution_count": 10, "id": "854596ee-ecb6-4207-a247-9ea30af2b65f", "metadata": {}, "outputs": [], "source": ["# Handle missing values in matches dataset\n", "matches['player_of_match'].fillna('No Player of Match', inplace=True)\n", "matches['city'].fillna('Unknown', inplace=True)\n", "matches['winner'].fillna('No Result', inplace=True)"]}, {"cell_type": "code", "execution_count": 11, "id": "a6f20a00-5e94-4279-9500-c13185231120", "metadata": {}, "outputs": [], "source": ["# Standardize team names \n", "# Handle the Delhi Daredevils to Delhi Capitals name change\n", "matches['team1'] = matches['team1'].replace('Delhi Daredevils', 'Delhi Capitals')\n", "matches['team2'] = matches['team2'].replace('Delhi Daredevils', 'Delhi Capitals')\n", "matches['winner'] = matches['winner'].replace('Delhi Daredevils', 'Delhi Capitals')\n", "matches['toss_winner'] = matches['toss_winner'].replace('Delhi Daredevils', 'Delhi Capitals')"]}, {"cell_type": "code", "execution_count": 12, "id": "e61b31eb-2d82-4d22-a4db-58e2a9912dd0", "metadata": {}, "outputs": [], "source": ["# Handle same for deliveries dataset\n", "deliveries['batting_team'] = deliveries['batting_team'].replace('Delhi Daredevils', 'Delhi Capitals')\n", "deliveries['bowling_team'] = deliveries['bowling_team'].replace('Delhi Daredevils', 'Delhi Capitals')"]}, {"cell_type": "code", "execution_count": 13, "id": "c5dcfb82-6f1c-43ec-8fae-2796f5f80d47", "metadata": {}, "outputs": [], "source": ["# Convert date to datetime format for time-based analysis\n", "matches['date'] = pd.to_datetime(matches['date'])\n", "matches['season'] = matches['date'].dt.year\n", "\n", "# Add a season column to the matches dataset if it doesn't exist\n", "if 'season' not in matches.columns:\n", "    # Extract year from date column to create season\n", "    matches['season'] = matches['date'].dt.year"]}, {"cell_type": "code", "execution_count": 14, "id": "f552c82b-1488-419a-9c1e-aabb953abd77", "metadata": {}, "outputs": [], "source": ["# Extract Features from the datasets\n", "# =================================\n", "\n", "# 1. Team Performance Metrics\n", "# Team win percentages\n", "team_wins = matches['winner'].value_counts().reset_index()\n", "team_wins.columns = ['team', 'wins']"]}, {"cell_type": "code", "execution_count": 16, "id": "62be94fc-b069-4052-99c0-5bb1cb6fc700", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Team Performance Summary:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>team</th>\n", "      <th>total_matches</th>\n", "      <th>wins</th>\n", "      <th>win_percentage</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>Rising Pune Supergiant</td>\n", "      <td>16</td>\n", "      <td>10</td>\n", "      <td>62.50</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>Gujarat Titans</td>\n", "      <td>45</td>\n", "      <td>28</td>\n", "      <td>62.22</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Chennai Super Kings</td>\n", "      <td>238</td>\n", "      <td>138</td>\n", "      <td>57.98</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Mumbai Indians</td>\n", "      <td>261</td>\n", "      <td>144</td>\n", "      <td>55.17</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>Lucknow Super Giants</td>\n", "      <td>44</td>\n", "      <td>24</td>\n", "      <td>54.55</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Kolkata Knight Riders</td>\n", "      <td>251</td>\n", "      <td>131</td>\n", "      <td>52.19</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>Rajasthan Royals</td>\n", "      <td>221</td>\n", "      <td>112</td>\n", "      <td>50.68</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>Sunrisers Hyderabad</td>\n", "      <td>182</td>\n", "      <td>88</td>\n", "      <td>48.35</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Royal Challengers Bangalore</td>\n", "      <td>240</td>\n", "      <td>116</td>\n", "      <td>48.33</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>Royal Challengers Bengaluru</td>\n", "      <td>15</td>\n", "      <td>7</td>\n", "      <td>46.67</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>Kings XI Punjab</td>\n", "      <td>190</td>\n", "      <td>88</td>\n", "      <td>46.32</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Delhi Capitals</td>\n", "      <td>252</td>\n", "      <td>115</td>\n", "      <td>45.63</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>Gujarat Lions</td>\n", "      <td>30</td>\n", "      <td>13</td>\n", "      <td>43.33</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>Punjab Kings</td>\n", "      <td>56</td>\n", "      <td>24</td>\n", "      <td>42.86</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>Kochi <PERSON>rs <PERSON></td>\n", "      <td>14</td>\n", "      <td>6</td>\n", "      <td>42.86</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>Deccan Chargers</td>\n", "      <td>75</td>\n", "      <td>29</td>\n", "      <td>38.67</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>Rising Pune Supergiants</td>\n", "      <td>14</td>\n", "      <td>5</td>\n", "      <td>35.71</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>Pune Warriors</td>\n", "      <td>46</td>\n", "      <td>12</td>\n", "      <td>26.09</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                           team  total_matches  wins  win_percentage\n", "16       Rising Pune Supergiant             16    10           62.50\n", "12               Gujarat Titans             45    28           62.22\n", "1           Chennai Super Kings            238   138           57.98\n", "3                Mumbai Indians            261   144           55.17\n", "10         Lucknow Super Giants             44    24           54.55\n", "4         Kolkata Knight Riders            251   131           52.19\n", "5              Rajasthan Royals            221   112           50.68\n", "7           Sunrisers Hyderabad            182    88           48.35\n", "0   Royal Challengers Bangalore            240   116           48.33\n", "14  Royal Challengers Bengaluru             15     7           46.67\n", "6               Kings XI Punjab            190    88           46.32\n", "2                Delhi Capitals            252   115           45.63\n", "13                Gujarat Lions             30    13           43.33\n", "9                  Punjab Kings             56    24           42.86\n", "17         Kochi Tuskers Kerala             14     6           42.86\n", "8               Deccan Chargers             75    29           38.67\n", "15      Rising Pune Supergiants             14     5           35.71\n", "11                Pune Warriors             46    12           26.09"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["# Total matches played by each team\n", "team1_matches = matches['team1'].value_counts().reset_index()\n", "team1_matches.columns = ['team', 'matches_as_team1']\n", "team2_matches = matches['team2'].value_counts().reset_index()\n", "team2_matches.columns = ['team', 'matches_as_team2']\n", "\n", "team_matches = pd.merge(team1_matches, team2_matches, on='team', how='outer')\n", "team_matches['total_matches'] = team_matches['matches_as_team1'] + team_matches['matches_as_team2']\n", "team_matches.fillna(0, inplace=True)\n", "\n", "# Merge wins and matches\n", "team_performance = pd.merge(team_matches, team_wins, on='team', how='left')\n", "team_performance.fillna(0, inplace=True)  # Teams with 0 wins\n", "team_performance['win_percentage'] = (team_performance['wins'] / team_performance['total_matches'] * 100).round(2)\n", "\n", "print(\"\\nTeam Performance Summary:\")\n", "team_performance[['team', 'total_matches', 'wins', 'win_percentage']].sort_values('win_percentage', ascending=False)"]}, {"cell_type": "code", "execution_count": 17, "id": "3723f34f-73b7-41a3-b1fb-b9f1521bbe54", "metadata": {}, "outputs": [], "source": ["# 2. Batting and Bowling Metrics\n", "\n", "# Calculate Runs per Team\n", "team_runs = deliveries.groupby('batting_team')['total_runs'].sum().reset_index()\n", "team_runs.columns = ['team', 'total_runs']\n", "\n", "# Calculate Balls Faced per Team\n", "team_balls = deliveries.groupby('batting_team').size().reset_index()\n", "team_balls.columns = ['team', 'balls_faced']\n", "\n", "# Merge runs and balls\n", "team_batting = pd.merge(team_runs, team_balls, on='team', how='left')\n", "team_batting['run_rate'] = (team_batting['total_runs'] / (team_batting['balls_faced'] / 6)).round(2)\n", "\n", "# Calculate Economy Rate\n", "team_runs_conceded = deliveries.groupby('bowling_team')['total_runs'].sum().reset_index()\n", "team_runs_conceded.columns = ['team', 'runs_conceded']\n", "\n", "team_balls_bowled = deliveries.groupby('bowling_team').size().reset_index()\n", "team_balls_bowled.columns = ['team', 'balls_bowled']\n", "\n", "team_bowling = pd.merge(team_runs_conceded, team_balls_bowled, on='team', how='left')\n", "team_bowling['economy_rate'] = (team_bowling['runs_conceded'] / (team_bowling['balls_bowled'] / 6)).round(2)"]}, {"cell_type": "code", "execution_count": 18, "id": "2cb6a0e3-bb25-48e9-a0ad-20f275a216b8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Team Batting and Bowling Metrics:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>team</th>\n", "      <th>run_rate</th>\n", "      <th>economy_rate</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>Royal Challengers Bengaluru</td>\n", "      <td>9.67</td>\n", "      <td>9.39</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Gujarat Titans</td>\n", "      <td>8.47</td>\n", "      <td>8.46</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>Punjab Kings</td>\n", "      <td>8.37</td>\n", "      <td>8.52</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>Lucknow Super Giants</td>\n", "      <td>8.34</td>\n", "      <td>8.51</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Gujarat Lions</td>\n", "      <td>8.18</td>\n", "      <td>8.61</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Chennai Super Kings</td>\n", "      <td>8.09</td>\n", "      <td>7.81</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>Mumbai Indians</td>\n", "      <td>8.05</td>\n", "      <td>7.86</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>Royal Challengers Bangalore</td>\n", "      <td>8.02</td>\n", "      <td>8.04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>Kolkata Knight Riders</td>\n", "      <td>8.00</td>\n", "      <td>7.91</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>Sunrisers Hyderabad</td>\n", "      <td>7.99</td>\n", "      <td>8.04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>Kings XI Punjab</td>\n", "      <td>7.97</td>\n", "      <td>8.13</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>Rajasthan Royals</td>\n", "      <td>7.94</td>\n", "      <td>7.98</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Delhi Capitals</td>\n", "      <td>7.91</td>\n", "      <td>8.04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>Rising Pune Supergiants</td>\n", "      <td>7.83</td>\n", "      <td>7.85</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>Rising Pune Supergiant</td>\n", "      <td>7.80</td>\n", "      <td>7.58</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Deccan Chargers</td>\n", "      <td>7.61</td>\n", "      <td>7.71</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>Kochi <PERSON>rs <PERSON></td>\n", "      <td>7.21</td>\n", "      <td>7.38</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>Pune Warriors</td>\n", "      <td>7.01</td>\n", "      <td>7.54</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                           team  run_rate  economy_rate\n", "16  Royal Challengers Bengaluru      9.67          9.39\n", "4                Gujarat Titans      8.47          8.46\n", "11                 Punjab Kings      8.37          8.52\n", "8          Lucknow Super Giants      8.34          8.51\n", "3                 Gujarat Lions      8.18          8.61\n", "0           Chennai Super Kings      8.09          7.81\n", "9                Mumbai Indians      8.05          7.86\n", "15  Royal Challengers Bangalore      8.02          8.04\n", "7         Kolkata Knight Riders      8.00          7.91\n", "17          Sunrisers Hyderabad      7.99          8.04\n", "5               Kings XI Punjab      7.97          8.13\n", "12             Rajasthan Royals      7.94          7.98\n", "2                Delhi Capitals      7.91          8.04\n", "14      Rising Pune Supergiants      7.83          7.85\n", "13       Rising Pune Supergiant      7.80          7.58\n", "1               Deccan Chargers      7.61          7.71\n", "6          Kochi Tuskers Kerala      7.21          7.38\n", "10                Pune Warriors      7.01          7.54"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["# Combine batting and bowling metrics\n", "team_metrics = pd.merge(team_batting, team_bowling, on='team', how='left')\n", "print(\"\\nTeam Batting and Bowling Metrics:\")\n", "team_metrics[['team', 'run_rate', 'economy_rate']].sort_values('run_rate', ascending=False)"]}, {"cell_type": "code", "execution_count": 19, "id": "3b9eacbc-09be-4cad-b990-d7d4949c3d1b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Highest Team Scores:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>batting_team</th>\n", "      <th>total_runs</th>\n", "      <th>date</th>\n", "      <th>venue</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Sunrisers Hyderabad</td>\n", "      <td>287</td>\n", "      <td>2024-04-15</td>\n", "      <td>M Chinnaswamy Stadium, Bengaluru</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Sunrisers Hyderabad</td>\n", "      <td>277</td>\n", "      <td>2024-03-27</td>\n", "      <td><PERSON><PERSON> International Stadium, Uppal, Hyd...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Kolkata Knight Riders</td>\n", "      <td>272</td>\n", "      <td>2024-04-03</td>\n", "      <td>Dr. <PERSON><PERSON><PERSON><PERSON> ACA-VDCA Cricket St...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Sunrisers Hyderabad</td>\n", "      <td>266</td>\n", "      <td>2024-04-20</td>\n", "      <td><PERSON>run <PERSON> Stadium, Delhi</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Royal Challengers Bangalore</td>\n", "      <td>263</td>\n", "      <td>2013-04-23</td>\n", "      <td>M Chinnaswamy Stadium</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                  batting_team  total_runs       date  \\\n", "0          Sunrisers Hyderabad         287 2024-04-15   \n", "1          Sunrisers Hyderabad         277 2024-03-27   \n", "2        Kolkata Knight Riders         272 2024-04-03   \n", "3          Sunrisers Hyderabad         266 2024-04-20   \n", "4  Royal Challengers Bangalore         263 2013-04-23   \n", "\n", "                                               venue  \n", "0                   M Chinnaswamy Stadium, Bengaluru  \n", "1  Rajiv Gandhi International Stadium, Uppal, Hyd...  \n", "2  Dr. <PERSON><PERSON><PERSON><PERSON> ACA-VDCA Cricket St...  \n", "3                        Arun <PERSON> Stadium, Delhi  \n", "4                              M Chinnaswamy Stadium  "]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["# 3. Highest and Lowest Team Scores\n", "\n", "# Calculate highest scores\n", "matches_with_scores = matches.copy()\n", "match_scores = deliveries.groupby(['match_id', 'inning', 'batting_team'])['total_runs'].sum().reset_index()\n", "\n", "highest_scores = match_scores.sort_values('total_runs', ascending=False).head(10)\n", "highest_scores = pd.merge(highest_scores, matches[['id', 'date', 'venue']], left_on='match_id', right_on='id', how='left')\n", "print(\"\\nHighest Team Scores:\")\n", "highest_scores[['batting_team', 'total_runs', 'date', 'venue']].head()"]}, {"cell_type": "code", "execution_count": 20, "id": "ea6aa725-0b8f-405b-bfe6-9142aa80ddd5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Lowest Team Scores:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>batting_team</th>\n", "      <th>total_runs</th>\n", "      <th>date</th>\n", "      <th>venue</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Kings XI Punjab</td>\n", "      <td>2</td>\n", "      <td>2020-09-20</td>\n", "      <td>Dubai International Cricket Stadium</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Royal Challengers Bangalore</td>\n", "      <td>2</td>\n", "      <td>2015-05-17</td>\n", "      <td>M Chinnaswamy Stadium</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Sunrisers Hyderabad</td>\n", "      <td>2</td>\n", "      <td>2020-10-18</td>\n", "      <td><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Kolkata Knight Riders</td>\n", "      <td>3</td>\n", "      <td>2020-10-18</td>\n", "      <td><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Delhi Capitals</td>\n", "      <td>3</td>\n", "      <td>2020-09-20</td>\n", "      <td>Dubai International Cricket Stadium</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                  batting_team  total_runs       date  \\\n", "0              Kings XI Punjab           2 2020-09-20   \n", "1  Royal Challengers Bangalore           2 2015-05-17   \n", "2          Sunrisers Hyderabad           2 2020-10-18   \n", "3        Kolkata Knight Riders           3 2020-10-18   \n", "4               Delhi Capitals           3 2020-09-20   \n", "\n", "                                 venue  \n", "0  Dubai International Cricket Stadium  \n", "1                M Chinnaswamy Stadium  \n", "2                 Sheikh <PERSON> Stadium  \n", "3                 Sheikh <PERSON> Stadium  \n", "4  Dubai International Cricket Stadium  "]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["lowest_scores = match_scores[match_scores['total_runs'] > 0].sort_values('total_runs').head(10)\n", "lowest_scores = pd.merge(lowest_scores, matches[['id', 'date', 'venue']], left_on='match_id', right_on='id', how='left')\n", "print(\"\\nLowest Team Scores:\")\n", "lowest_scores[['batting_team', 'total_runs', 'date', 'venue']].head()"]}, {"cell_type": "code", "execution_count": 21, "id": "972fe8ea-a0db-4cdd-a9a9-e368ce2c9711", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Total Boundaries by Teams:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>team</th>\n", "      <th>total_fours</th>\n", "      <th>total_sixes</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>Mumbai Indians</td>\n", "      <td>3637</td>\n", "      <td>1685</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Chennai Super Kings</td>\n", "      <td>3196</td>\n", "      <td>1509</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>Kolkata Knight Riders</td>\n", "      <td>3461</td>\n", "      <td>1495</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>Royal Challengers Bangalore</td>\n", "      <td>3149</td>\n", "      <td>1488</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Delhi Capitals</td>\n", "      <td>3508</td>\n", "      <td>1351</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>Rajasthan Royals</td>\n", "      <td>3091</td>\n", "      <td>1237</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>Kings XI Punjab</td>\n", "      <td>2631</td>\n", "      <td>1075</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>Sunrisers Hyderabad</td>\n", "      <td>2405</td>\n", "      <td>1042</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>Punjab Kings</td>\n", "      <td>795</td>\n", "      <td>440</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Deccan Chargers</td>\n", "      <td>957</td>\n", "      <td>400</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>Lucknow Super Giants</td>\n", "      <td>577</td>\n", "      <td>332</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Gujarat Titans</td>\n", "      <td>691</td>\n", "      <td>271</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>Pune Warriors</td>\n", "      <td>525</td>\n", "      <td>196</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>Royal Challengers Bengaluru</td>\n", "      <td>229</td>\n", "      <td>165</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Gujarat Lions</td>\n", "      <td>460</td>\n", "      <td>155</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>Rising Pune Supergiant</td>\n", "      <td>197</td>\n", "      <td>89</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>Rising Pune Supergiants</td>\n", "      <td>171</td>\n", "      <td>68</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>Kochi <PERSON>rs <PERSON></td>\n", "      <td>170</td>\n", "      <td>53</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                           team  total_fours  total_sixes\n", "9                Mumbai Indians         3637         1685\n", "0           Chennai Super Kings         3196         1509\n", "7         Kolkata Knight Riders         3461         1495\n", "15  Royal Challengers Bangalore         3149         1488\n", "2                Delhi Capitals         3508         1351\n", "12             Rajasthan Royals         3091         1237\n", "5               Kings XI Punjab         2631         1075\n", "17          Sunrisers Hyderabad         2405         1042\n", "11                 Punjab Kings          795          440\n", "1               Deccan Chargers          957          400\n", "8          Lucknow Super Giants          577          332\n", "4                Gujarat Titans          691          271\n", "10                Pune Warriors          525          196\n", "16  Royal Challengers Bengaluru          229          165\n", "3                 Gujarat Lions          460          155\n", "13       Rising Pune Supergiant          197           89\n", "14      Rising Pune Supergiants          171           68\n", "6          Kochi Tuskers Kerala          170           53"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["# 4. Total 4s and 6s by Teams\n", "fours = deliveries[deliveries['batsman_runs'] == 4].groupby('batting_team').size().reset_index()\n", "fours.columns = ['team', 'total_fours']\n", "\n", "sixes = deliveries[deliveries['batsman_runs'] == 6].groupby('batting_team').size().reset_index()\n", "sixes.columns = ['team', 'total_sixes']\n", "\n", "boundaries = pd.merge(fours, sixes, on='team', how='outer')\n", "boundaries.fillna(0, inplace=True)\n", "print(\"\\nTotal Boundaries by Teams:\")\n", "boundaries.sort_values('total_sixes', ascending=False)"]}, {"cell_type": "code", "execution_count": 22, "id": "4f46c970-571b-4cce-bf07-218fc60158a7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Average Powerplay Score by Teams:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>batting_team</th>\n", "      <th>avg_powerplay_score</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>Royal Challengers Bengaluru</td>\n", "      <td>57.13</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Gujarat Lions</td>\n", "      <td>49.89</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>Punjab Kings</td>\n", "      <td>48.73</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>Rising Pune Supergiant</td>\n", "      <td>47.26</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>Sunrisers Hyderabad</td>\n", "      <td>47.19</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Gujarat Titans</td>\n", "      <td>46.63</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Delhi Capitals</td>\n", "      <td>46.05</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>Kochi <PERSON>rs <PERSON></td>\n", "      <td>45.93</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>Kolkata Knight Riders</td>\n", "      <td>45.56</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>Lucknow Super Giants</td>\n", "      <td>45.55</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>Kings XI Punjab</td>\n", "      <td>45.19</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>Rajasthan Royals</td>\n", "      <td>44.93</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Chennai Super Kings</td>\n", "      <td>44.77</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>Mumbai Indians</td>\n", "      <td>44.71</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>Rising Pune Supergiants</td>\n", "      <td>44.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>Royal Challengers Bangalore</td>\n", "      <td>43.92</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Deccan Chargers</td>\n", "      <td>43.64</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>Pune Warriors</td>\n", "      <td>40.61</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                   batting_team  avg_powerplay_score\n", "16  Royal Challengers Bengaluru                57.13\n", "3                 Gujarat Lions                49.89\n", "11                 Punjab Kings                48.73\n", "13       Rising Pune Supergiant                47.26\n", "17          Sunrisers Hyderabad                47.19\n", "4                Gujarat Titans                46.63\n", "2                Delhi Capitals                46.05\n", "6          Kochi Tuskers Kerala                45.93\n", "7         Kolkata Knight Riders                45.56\n", "8          Lucknow Super Giants                45.55\n", "5               Kings XI Punjab                45.19\n", "12             Rajasthan Royals                44.93\n", "0           Chennai Super Kings                44.77\n", "9                Mumbai Indians                44.71\n", "14      Rising Pune Supergiants                44.00\n", "15  Royal Challengers Bangalore                43.92\n", "1               Deccan Chargers                43.64\n", "10                Pune Warriors                40.61"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["# 5. Powerplay and Death Overs Analysis\n", "\n", "# Powerplay (first 6 overs) analysis\n", "powerplay_runs = deliveries[(deliveries['over'] < 6)].groupby('batting_team')['total_runs'].sum().reset_index()\n", "powerplay_balls = deliveries[(deliveries['over'] < 6)].groupby('batting_team').size().reset_index()\n", "powerplay_balls.columns = ['batting_team', 'balls']\n", "powerplay = pd.merge(powerplay_runs, powerplay_balls, on='batting_team', how='left')\n", "powerplay['avg_powerplay_score'] = (powerplay['total_runs'] * 36 / powerplay['balls']).round(2)\n", "print(\"\\nAverage Powerplay Score by Teams:\")\n", "powerplay[['batting_team', 'avg_powerplay_score']].sort_values('avg_powerplay_score', ascending=False)"]}, {"cell_type": "code", "execution_count": 23, "id": "a432712b-4ddd-4d4b-8faa-ed17c3a636d4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Average Death Overs Score by Teams:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>batting_team</th>\n", "      <th>avg_death_score</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>Royal Challengers Bengaluru</td>\n", "      <td>43.38</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Gujarat Titans</td>\n", "      <td>42.44</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>Rising Pune Supergiants</td>\n", "      <td>41.53</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Chennai Super Kings</td>\n", "      <td>41.07</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>Royal Challengers Bangalore</td>\n", "      <td>40.73</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>Mumbai Indians</td>\n", "      <td>40.32</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>Lucknow Super Giants</td>\n", "      <td>40.23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>Punjab Kings</td>\n", "      <td>38.99</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>Kolkata Knight Riders</td>\n", "      <td>38.46</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Delhi Capitals</td>\n", "      <td>38.30</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>Rising Pune Supergiant</td>\n", "      <td>38.28</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>Rajasthan Royals</td>\n", "      <td>37.65</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>Sunrisers Hyderabad</td>\n", "      <td>37.49</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>Kings XI Punjab</td>\n", "      <td>36.86</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Deccan Chargers</td>\n", "      <td>36.73</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Gujarat Lions</td>\n", "      <td>36.06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>Kochi <PERSON>rs <PERSON></td>\n", "      <td>33.98</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>Pune Warriors</td>\n", "      <td>32.41</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                   batting_team  avg_death_score\n", "16  Royal Challengers Bengaluru            43.38\n", "4                Gujarat Titans            42.44\n", "14      Rising Pune Supergiants            41.53\n", "0           Chennai Super Kings            41.07\n", "15  Royal Challengers Bangalore            40.73\n", "9                Mumbai Indians            40.32\n", "8          Lucknow Super Giants            40.23\n", "11                 Punjab Kings            38.99\n", "7         Kolkata Knight Riders            38.46\n", "2                Delhi Capitals            38.30\n", "13       Rising Pune Supergiant            38.28\n", "12             Rajasthan Royals            37.65\n", "17          Sunrisers Hyderabad            37.49\n", "5               Kings XI Punjab            36.86\n", "1               Deccan Chargers            36.73\n", "3                 Gujarat Lions            36.06\n", "6          Kochi Tuskers Kerala            33.98\n", "10                Pune Warriors            32.41"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["# Death overs (last 4 overs) analysis\n", "death_runs = deliveries[(deliveries['over'] >= 16) & (deliveries['over'] <= 20)].groupby('batting_team')['total_runs'].sum().reset_index()\n", "death_balls = deliveries[(deliveries['over'] >= 16) & (deliveries['over'] <= 20)].groupby('batting_team').size().reset_index()\n", "death_balls.columns = ['batting_team', 'balls']\n", "death = pd.merge(death_runs, death_balls, on='batting_team', how='left')\n", "death['avg_death_score'] = (death['total_runs'] * 24 / death['balls']).round(2)\n", "print(\"\\nAverage Death Overs Score by Teams:\")\n", "death[['batting_team', 'avg_death_score']].sort_values('avg_death_score', ascending=False)"]}, {"cell_type": "code", "execution_count": 24, "id": "4f8cb783-01c1-47ce-b71e-a15d5df2d277", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Average Runs per Over (Sample):\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>batting_team</th>\n", "      <th>over</th>\n", "      <th>total_runs</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Chennai Super Kings</td>\n", "      <td>0</td>\n", "      <td>1252.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Chennai Super Kings</td>\n", "      <td>1</td>\n", "      <td>1608.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Chennai Super Kings</td>\n", "      <td>2</td>\n", "      <td>1838.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Chennai Super Kings</td>\n", "      <td>3</td>\n", "      <td>2010.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Chennai Super Kings</td>\n", "      <td>4</td>\n", "      <td>2093.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>Chennai Super Kings</td>\n", "      <td>5</td>\n", "      <td>2190.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>Chennai Super Kings</td>\n", "      <td>6</td>\n", "      <td>1640.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>Chennai Super Kings</td>\n", "      <td>7</td>\n", "      <td>1689.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>Chennai Super Kings</td>\n", "      <td>8</td>\n", "      <td>1839.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>Chennai Super Kings</td>\n", "      <td>9</td>\n", "      <td>1716.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["          batting_team  over  total_runs\n", "0  Chennai Super Kings     0      1252.0\n", "1  Chennai Super Kings     1      1608.0\n", "2  Chennai Super Kings     2      1838.0\n", "3  Chennai Super Kings     3      2010.0\n", "4  Chennai Super Kings     4      2093.0\n", "5  Chennai Super Kings     5      2190.0\n", "6  Chennai Super Kings     6      1640.0\n", "7  Chennai Super Kings     7      1689.0\n", "8  Chennai Super Kings     8      1839.0\n", "9  Chennai Super Kings     9      1716.0"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["# 6. Average Run per Over\n", "over_runs = deliveries.groupby(['batting_team', 'over'])['total_runs'].sum().reset_index()\n", "avg_over_runs = over_runs.groupby(['batting_team', 'over'])['total_runs'].mean().reset_index()\n", "print(\"\\nAverage Runs per Over (Sample):\")\n", "avg_over_runs.head(10)"]}, {"cell_type": "code", "execution_count": 25, "id": "b452579a-1cf6-4abc-9d0c-f7b8b5a54428", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Top 20 Run Scorers:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>batter</th>\n", "      <th>batsman_runs</th>\n", "      <th>matches</th>\n", "      <th>batting_avg</th>\n", "      <th>strike_rate</th>\n", "      <th>fours</th>\n", "      <th>sixes</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>631</th>\n", "      <td>V <PERSON></td>\n", "      <td>8014</td>\n", "      <td>244</td>\n", "      <td>32.58</td>\n", "      <td>128.51</td>\n", "      <td>708.0</td>\n", "      <td>273.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>512</th>\n", "      <td><PERSON></td>\n", "      <td>6769</td>\n", "      <td>221</td>\n", "      <td>30.49</td>\n", "      <td>123.45</td>\n", "      <td>768.0</td>\n", "      <td>153.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>477</th>\n", "      <td><PERSON><PERSON></td>\n", "      <td>6630</td>\n", "      <td>251</td>\n", "      <td>26.31</td>\n", "      <td>127.92</td>\n", "      <td>599.0</td>\n", "      <td>281.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>147</th>\n", "      <td>DA Warner</td>\n", "      <td>6567</td>\n", "      <td>184</td>\n", "      <td>35.12</td>\n", "      <td>135.43</td>\n", "      <td>663.0</td>\n", "      <td>236.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>546</th>\n", "      <td>SK Raina</td>\n", "      <td>5536</td>\n", "      <td>200</td>\n", "      <td>27.54</td>\n", "      <td>132.54</td>\n", "      <td>506.0</td>\n", "      <td>204.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>374</th>\n", "      <td><PERSON>i</td>\n", "      <td>5243</td>\n", "      <td>228</td>\n", "      <td>23.00</td>\n", "      <td>132.84</td>\n", "      <td>363.0</td>\n", "      <td>252.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>AB de Villiers</td>\n", "      <td>5181</td>\n", "      <td>170</td>\n", "      <td>30.12</td>\n", "      <td>148.58</td>\n", "      <td>414.0</td>\n", "      <td>253.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>124</th>\n", "      <td><PERSON>le</td>\n", "      <td>4997</td>\n", "      <td>141</td>\n", "      <td>34.46</td>\n", "      <td>142.12</td>\n", "      <td>408.0</td>\n", "      <td>359.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>501</th>\n", "      <td><PERSON><PERSON></td>\n", "      <td>4954</td>\n", "      <td>197</td>\n", "      <td>25.02</td>\n", "      <td>126.15</td>\n", "      <td>481.0</td>\n", "      <td>182.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>282</th>\n", "      <td><PERSON><PERSON></td>\n", "      <td>4843</td>\n", "      <td>233</td>\n", "      <td>20.61</td>\n", "      <td>131.35</td>\n", "      <td>466.0</td>\n", "      <td>161.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>289</th>\n", "      <td>KL Rahul</td>\n", "      <td>4689</td>\n", "      <td>122</td>\n", "      <td>37.81</td>\n", "      <td>131.05</td>\n", "      <td>400.0</td>\n", "      <td>187.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50</th>\n", "      <td><PERSON></td>\n", "      <td>4642</td>\n", "      <td>171</td>\n", "      <td>27.15</td>\n", "      <td>120.32</td>\n", "      <td>479.0</td>\n", "      <td>103.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>188</th>\n", "      <td>F <PERSON>is</td>\n", "      <td>4571</td>\n", "      <td>138</td>\n", "      <td>33.12</td>\n", "      <td>133.07</td>\n", "      <td>422.0</td>\n", "      <td>166.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>572</th>\n", "      <td>SV Samson</td>\n", "      <td>4419</td>\n", "      <td>162</td>\n", "      <td>27.28</td>\n", "      <td>135.14</td>\n", "      <td>352.0</td>\n", "      <td>206.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>64</th>\n", "      <td>AT Rayudu</td>\n", "      <td>4348</td>\n", "      <td>185</td>\n", "      <td>23.50</td>\n", "      <td>124.58</td>\n", "      <td>359.0</td>\n", "      <td>173.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>194</th>\n", "      <td><PERSON></td>\n", "      <td>4217</td>\n", "      <td>151</td>\n", "      <td>27.93</td>\n", "      <td>119.67</td>\n", "      <td>492.0</td>\n", "      <td>59.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>564</th>\n", "      <td><PERSON></td>\n", "      <td>3880</td>\n", "      <td>141</td>\n", "      <td>27.13</td>\n", "      <td>134.16</td>\n", "      <td>377.0</td>\n", "      <td>190.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>363</th>\n", "      <td>MK Pandey</td>\n", "      <td>3859</td>\n", "      <td>158</td>\n", "      <td>24.12</td>\n", "      <td>117.37</td>\n", "      <td>335.0</td>\n", "      <td>111.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>530</th>\n", "      <td>SA Yadav</td>\n", "      <td>3594</td>\n", "      <td>135</td>\n", "      <td>26.43</td>\n", "      <td>142.51</td>\n", "      <td>385.0</td>\n", "      <td>130.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>242</th>\n", "      <td><PERSON><PERSON></td>\n", "      <td>3583</td>\n", "      <td>106</td>\n", "      <td>33.49</td>\n", "      <td>142.24</td>\n", "      <td>356.0</td>\n", "      <td>161.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["             batter  batsman_runs  matches  batting_avg  strike_rate  fours  \\\n", "631         V <PERSON><PERSON><PERSON>          8014      244        32.58       128.51  708.0   \n", "512        <PERSON>          6769      221        30.49       123.45  768.0   \n", "477       <PERSON><PERSON>          6630      251        26.31       127.92  599.0   \n", "147       DA Warner          6567      184        35.12       135.43  663.0   \n", "546        SK Raina          5536      200        27.54       132.54  506.0   \n", "374        MS Dhoni          5243      228        23.00       132.84  363.0   \n", "30   AB de Villiers          5181      170        30.12       148.58  414.0   \n", "124        CH Gayle          4997      141        34.46       142.12  408.0   \n", "501      RV Uthappa          4954      197        25.02       126.15  481.0   \n", "282      <PERSON><PERSON> Karthik          4843      233        20.61       131.35  466.0   \n", "289        KL Rahul          4689      122        37.81       131.05  400.0   \n", "50        AM Rahane          4642      171        27.15       120.32  479.0   \n", "188    F <PERSON> Plessis          4571      138        33.12       133.07  422.0   \n", "572       SV Samson          4419      162        27.28       135.14  352.0   \n", "64        AT Rayudu          4348      185        23.50       124.58  359.0   \n", "194       G <PERSON><PERSON><PERSON><PERSON>          4217      151        27.93       119.67  492.0   \n", "564       <PERSON> Watson          3880      141        27.13       134.16  377.0   \n", "363       MK Pandey          3859      158        24.12       117.37  335.0   \n", "530        SA Yadav          3594      135        26.43       142.51  385.0   \n", "242      JC Buttler          3583      106        33.49       142.24  356.0   \n", "\n", "     sixes  \n", "631  273.0  \n", "512  153.0  \n", "477  281.0  \n", "147  236.0  \n", "546  204.0  \n", "374  252.0  \n", "30   253.0  \n", "124  359.0  \n", "501  182.0  \n", "282  161.0  \n", "289  187.0  \n", "50   103.0  \n", "188  166.0  \n", "572  206.0  \n", "64   173.0  \n", "194   59.0  \n", "564  190.0  \n", "363  111.0  \n", "530  130.0  \n", "242  161.0  "]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["# Player Performance Analysis\n", "# ==========================\n", "\n", "# 1. Top Run Scorers\n", "batsman_runs = deliveries.groupby('batter')['batsman_runs'].sum().reset_index()\n", "batsman_matches = deliveries.groupby('batter')['match_id'].nunique().reset_index()\n", "batsman_matches.columns = ['batter', 'matches']\n", "batsman_innings = deliveries.groupby(['batter', 'match_id', 'inning']).size().reset_index()\n", "batsman_innings = batsman_innings.groupby('batter').size().reset_index()\n", "batsman_innings.columns = ['batter', 'innings']\n", "\n", "batsman_balls = deliveries.groupby('batter').size().reset_index()\n", "batsman_balls.columns = ['batter', 'balls_faced']\n", "\n", "batsman_fours = deliveries[deliveries['batsman_runs'] == 4].groupby('batter').size().reset_index()\n", "batsman_fours.columns = ['batter', 'fours']\n", "\n", "batsman_sixes = deliveries[deliveries['batsman_runs'] == 6].groupby('batter').size().reset_index()\n", "batsman_sixes.columns = ['batter', 'sixes']\n", "\n", "# Merge all batsman stats\n", "batsman_stats = pd.merge(batsman_runs, batsman_matches, on='batter', how='left')\n", "batsman_stats = pd.merge(batsman_stats, batsman_innings, on='batter', how='left')\n", "batsman_stats = pd.merge(batsman_stats, batsman_balls, on='batter', how='left')\n", "batsman_stats = pd.merge(batsman_stats, batsman_fours, on='batter', how='left')\n", "batsman_stats = pd.merge(batsman_stats, batsman_sixes, on='batter', how='left')\n", "batsman_stats.fillna(0, inplace=True)\n", "\n", "# Calculate batting average and strike rate\n", "batsman_stats['batting_avg'] = (batsman_stats['batsman_runs'] / batsman_stats['innings']).round(2)\n", "batsman_stats['strike_rate'] = (batsman_stats['batsman_runs'] / batsman_stats['balls_faced'] * 100).round(2)\n", "\n", "top_batsmen = batsman_stats.sort_values('batsman_runs', ascending=False).head(20)\n", "print(\"\\nTop 20 Run Scorers:\")\n", "top_batsmen[['batter', 'batsman_runs', 'matches', 'batting_avg', 'strike_rate', 'fours', 'sixes']]"]}, {"cell_type": "code", "execution_count": 26, "id": "29e1f4ce-50e2-4461-a120-6f9028656b67", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Top 20 Wicket Takers:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>bowler</th>\n", "      <th>wickets</th>\n", "      <th>economy</th>\n", "      <th>bowling_avg</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>445</th>\n", "      <td><PERSON><PERSON></td>\n", "      <td>205.0</td>\n", "      <td>7.74</td>\n", "      <td>22.83</td>\n", "    </tr>\n", "    <tr>\n", "      <th>297</th>\n", "      <td><PERSON></td>\n", "      <td>192.0</td>\n", "      <td>7.98</td>\n", "      <td>26.97</td>\n", "    </tr>\n", "    <tr>\n", "      <th>102</th>\n", "      <td>DJ <PERSON></td>\n", "      <td>183.0</td>\n", "      <td>8.08</td>\n", "      <td>24.24</td>\n", "    </tr>\n", "    <tr>\n", "      <th>61</th>\n", "      <td><PERSON></td>\n", "      <td>181.0</td>\n", "      <td>7.46</td>\n", "      <td>27.91</td>\n", "    </tr>\n", "    <tr>\n", "      <th>379</th>\n", "      <td><PERSON> Narine</td>\n", "      <td>180.0</td>\n", "      <td>6.76</td>\n", "      <td>25.96</td>\n", "    </tr>\n", "    <tr>\n", "      <th>304</th>\n", "      <td><PERSON></td>\n", "      <td>180.0</td>\n", "      <td>6.97</td>\n", "      <td>30.19</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>A <PERSON><PERSON><PERSON></td>\n", "      <td>174.0</td>\n", "      <td>7.30</td>\n", "      <td>24.10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>372</th>\n", "      <td>SL Malinga</td>\n", "      <td>170.0</td>\n", "      <td>7.03</td>\n", "      <td>20.51</td>\n", "    </tr>\n", "    <tr>\n", "      <th>168</th>\n", "      <td><PERSON><PERSON></td>\n", "      <td>168.0</td>\n", "      <td>7.23</td>\n", "      <td>22.86</td>\n", "    </tr>\n", "    <tr>\n", "      <th>319</th>\n", "      <td><PERSON></td>\n", "      <td>160.0</td>\n", "      <td>7.57</td>\n", "      <td>30.73</td>\n", "    </tr>\n", "    <tr>\n", "      <th>140</th>\n", "      <td><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>150.0</td>\n", "      <td>7.04</td>\n", "      <td>27.34</td>\n", "    </tr>\n", "    <tr>\n", "      <th>337</th>\n", "      <td><PERSON></td>\n", "      <td>149.0</td>\n", "      <td>6.91</td>\n", "      <td>22.42</td>\n", "    </tr>\n", "    <tr>\n", "      <th>419</th>\n", "      <td>UT Yadav</td>\n", "      <td>144.0</td>\n", "      <td>8.35</td>\n", "      <td>30.85</td>\n", "    </tr>\n", "    <tr>\n", "      <th>390</th>\n", "      <td><PERSON><PERSON></td>\n", "      <td>137.0</td>\n", "      <td>7.71</td>\n", "      <td>27.43</td>\n", "    </tr>\n", "    <tr>\n", "      <th>139</th>\n", "      <td><PERSON><PERSON></td>\n", "      <td>135.0</td>\n", "      <td>8.45</td>\n", "      <td>23.81</td>\n", "    </tr>\n", "    <tr>\n", "      <th>251</th>\n", "      <td><PERSON><PERSON></td>\n", "      <td>132.0</td>\n", "      <td>8.52</td>\n", "      <td>24.83</td>\n", "    </tr>\n", "    <tr>\n", "      <th>261</th>\n", "      <td><PERSON></td>\n", "      <td>127.0</td>\n", "      <td>8.30</td>\n", "      <td>27.45</td>\n", "    </tr>\n", "    <tr>\n", "      <th>39</th>\n", "      <td><PERSON></td>\n", "      <td>123.0</td>\n", "      <td>7.28</td>\n", "      <td>31.09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>410</th>\n", "      <td><PERSON><PERSON></td>\n", "      <td>121.0</td>\n", "      <td>8.12</td>\n", "      <td>27.16</td>\n", "    </tr>\n", "    <tr>\n", "      <th>185</th>\n", "      <td><PERSON></td>\n", "      <td>120.0</td>\n", "      <td>8.34</td>\n", "      <td>22.10</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["              bowler  wickets  economy  bowling_avg\n", "445        YS Chahal    205.0     7.74        22.83\n", "297        PP Chawla    192.0     7.98        26.97\n", "102         DJ Bravo    183.0     8.08        24.24\n", "61           B <PERSON>    181.0     7.46        27.91\n", "379        SP Narine    180.0     6.76        25.96\n", "304         R <PERSON><PERSON>    180.0     6.97        30.19\n", "7           A <PERSON><PERSON><PERSON>    174.0     7.30        24.10\n", "372       SL Malinga    170.0     7.03        20.51\n", "168        <PERSON><PERSON>    168.0     7.23        22.86\n", "319        RA Jadeja    160.0     7.57        30.73\n", "140  <PERSON><PERSON><PERSON><PERSON>    150.0     7.04        27.34\n", "337      <PERSON>    149.0     6.91        22.42\n", "419         UT Yadav    144.0     8.35        30.85\n", "390   <PERSON><PERSON>    137.0     7.71        27.43\n", "139         <PERSON><PERSON>    135.0     8.45        23.81\n", "251        <PERSON><PERSON>    132.0     8.52        24.83\n", "261   <PERSON>    127.0     8.30        27.45\n", "39          AR Patel    123.0     7.28        31.09\n", "410         TA Boult    121.0     8.12        27.16\n", "185         K Rabada    120.0     8.34        22.10"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["# 2. Top Wicket Takers\n", "# First, identify rows where a wicket fell and it's attributed to the bowler\n", "bowler_wickets = deliveries[deliveries['is_wicket'] == 1].copy()\n", "valid_dismissal_types = ['bowled', 'caught', 'lbw', 'stumped', 'caught and bowled', 'hit wicket']\n", "bowler_wickets = bowler_wickets[bowler_wickets['dismissal_kind'].isin(valid_dismissal_types)]\n", "\n", "# Count wickets\n", "bowler_wicket_counts = bowler_wickets.groupby('bowler').size().reset_index()\n", "bowler_wicket_counts.columns = ['bowler', 'wickets']\n", "\n", "# Calculate economy rate\n", "bowler_runs = deliveries.groupby('bowler')['total_runs'].sum().reset_index()\n", "bowler_balls = deliveries.groupby('bowler').size().reset_index()\n", "bowler_balls.columns = ['bowler', 'balls_bowled']\n", "\n", "bowler_stats = pd.merge(bowler_wicket_counts, bowler_runs, on='bowler', how='outer')\n", "bowler_stats = pd.merge(bowler_stats, bowler_balls, on='bowler', how='left')\n", "bowler_stats.fillna(0, inplace=True)\n", "\n", "bowler_stats['economy'] = (bowler_stats['total_runs'] / (bowler_stats['balls_bowled'] / 6)).round(2)\n", "bowler_stats['bowling_avg'] = (bowler_stats['total_runs'] / bowler_stats['wickets']).round(2)\n", "bowler_stats['bowling_avg'].replace([np.inf, -np.inf], 0, inplace=True)\n", "\n", "top_bowlers = bowler_stats.sort_values('wickets', ascending=False).head(20)\n", "print(\"\\nTop 20 Wicket Takers:\")\n", "top_bowlers[['bowler', 'wickets', 'economy', 'bowling_avg']]"]}, {"cell_type": "code", "execution_count": 27, "id": "eb888747-ca7e-4658-b64c-2f78bf2cb6c0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Top Man of the Match Winners:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>player</th>\n", "      <th>mom_count</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>AB de Villiers</td>\n", "      <td>25</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td><PERSON>le</td>\n", "      <td>22</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td><PERSON><PERSON></td>\n", "      <td>19</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>DA Warner</td>\n", "      <td>18</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>V <PERSON></td>\n", "      <td>18</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td><PERSON>i</td>\n", "      <td>17</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td><PERSON></td>\n", "      <td>16</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td><PERSON><PERSON></td>\n", "      <td>16</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td><PERSON></td>\n", "      <td>16</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td><PERSON></td>\n", "      <td>15</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["           player  mom_count\n", "0  AB de Villiers         25\n", "1        CH Gayle         22\n", "2       R<PERSON> Sharma         19\n", "3       DA Warner         18\n", "4         V Kohli         18\n", "5        MS Dhoni         17\n", "6       SR Watson         16\n", "7       YK Pathan         16\n", "8       RA Jadeja         16\n", "9      AD Russell         15"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["# 3. Man of the Match Analysis\n", "mom_counts = matches['player_of_match'].value_counts().reset_index()\n", "mom_counts.columns = ['player', 'mom_count']\n", "print(\"\\nTop Man of the Match Winners:\")\n", "mom_counts.head(10)"]}, {"cell_type": "code", "execution_count": 29, "id": "97aac393-a704-4a21-b279-ab7ca24ffb05", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Player Clusters based on Batting Average and Bowling Economy:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>batting_avg</th>\n", "      <th>economy</th>\n", "    </tr>\n", "    <tr>\n", "      <th>cluster</th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>22.270104</td>\n", "      <td>0.036458</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>5.827222</td>\n", "      <td>7.788889</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>25.965714</td>\n", "      <td>9.478393</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>15.179733</td>\n", "      <td>8.725067</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         batting_avg   economy\n", "cluster                       \n", "0          22.270104  0.036458\n", "1           5.827222  7.788889\n", "2          25.965714  9.478393\n", "3          15.179733  8.725067"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["# 4. <PERSON><PERSON> and <PERSON> Clusters using K-Means\n", "# Combine batting and bowling stats\n", "player_combined = pd.merge(batsman_stats, bowler_stats, left_on='batter', right_on='bowler', how='outer')\n", "player_combined.fillna(0, inplace=True)\n", "\n", "# Select players with at least 10 matches\n", "min_matches = 10\n", "filtered_players = player_combined[player_combined['matches'] >= min_matches].copy()\n", "\n", "# Prepare data for clustering\n", "# Replace infinite values with NaN\n", "filtered_players[['batting_avg', 'economy']] = filtered_players[['batting_avg', 'economy']].replace([np.inf, -np.inf], np.nan)\n", "# Drop rows with NaN in the clustering features\n", "filtered_players_clean = filtered_players.dropna(subset=['batting_avg', 'economy'])\n", "\n", "# Standardize features\n", "scaler = StandardScaler()\n", "scaled_features = scaler.fit_transform(filtered_players_clean[['batting_avg', 'economy']])\n", "\n", "# Apply K-Means clustering\n", "kmeans = KMeans(n_clusters=4, random_state=42)\n", "clusters = kmeans.fit_predict(scaled_features)\n", "\n", "# Add clusters back to the clean dataframe\n", "filtered_players_clean['cluster'] = clusters\n", "\n", "print(\"\\nPlayer Clusters based on Batting Average and Bowling Economy:\")\n", "filtered_players_clean.groupby('cluster')[['batting_avg', 'economy']].mean()"]}, {"cell_type": "code", "execution_count": 31, "id": "3cf3159f-9389-4f0a-93bf-246129715495", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Top 10 Singles Scorers:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>batter</th>\n", "      <th>singles</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>601</th>\n", "      <td>V <PERSON></td>\n", "      <td>2591</td>\n", "    </tr>\n", "    <tr>\n", "      <th>490</th>\n", "      <td><PERSON></td>\n", "      <td>2102</td>\n", "    </tr>\n", "    <tr>\n", "      <th>459</th>\n", "      <td><PERSON><PERSON></td>\n", "      <td>1996</td>\n", "    </tr>\n", "    <tr>\n", "      <th>519</th>\n", "      <td>SK Raina</td>\n", "      <td>1708</td>\n", "    </tr>\n", "    <tr>\n", "      <th>143</th>\n", "      <td>DA Warner</td>\n", "      <td>1682</td>\n", "    </tr>\n", "    <tr>\n", "      <th>361</th>\n", "      <td><PERSON>i</td>\n", "      <td>1554</td>\n", "    </tr>\n", "    <tr>\n", "      <th>49</th>\n", "      <td><PERSON></td>\n", "      <td>1537</td>\n", "    </tr>\n", "    <tr>\n", "      <th>63</th>\n", "      <td>AT Rayudu</td>\n", "      <td>1495</td>\n", "    </tr>\n", "    <tr>\n", "      <th>280</th>\n", "      <td>KL Rahul</td>\n", "      <td>1464</td>\n", "    </tr>\n", "    <tr>\n", "      <th>273</th>\n", "      <td><PERSON><PERSON></td>\n", "      <td>1464</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         batter  singles\n", "601     V Kohli     2591\n", "490    S Dhawan     2102\n", "459   <PERSON><PERSON>     1996\n", "519    SK Raina     1708\n", "143   DA Warner     1682\n", "361    MS Dhoni     1554\n", "49    AM Rahane     1537\n", "63    AT Rayudu     1495\n", "280    KL Rahul     1464\n", "273  KD Karthik     1464"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["# 5. Top Players in Different Run Categories\n", "top_ones = deliveries[deliveries['batsman_runs'] == 1].groupby('batter').size().reset_index()\n", "top_ones.columns = ['batter', 'singles']\n", "top_ones = top_ones.sort_values('singles', ascending=False).head(10)\n", "\n", "top_twos = deliveries[deliveries['batsman_runs'] == 2].groupby('batter').size().reset_index()\n", "top_twos.columns = ['batter', 'doubles']\n", "top_twos = top_twos.sort_values('doubles', ascending=False).head(10)\n", "\n", "top_fours = deliveries[deliveries['batsman_runs'] == 4].groupby('batter').size().reset_index()\n", "top_fours.columns = ['batter', 'fours']\n", "top_fours = top_fours.sort_values('fours', ascending=False).head(10)\n", "\n", "top_sixes = deliveries[deliveries['batsman_runs'] == 6].groupby('batter').size().reset_index()\n", "top_sixes.columns = ['batter', 'sixes']\n", "top_sixes = top_sixes.sort_values('sixes', ascending=False).head(10)\n", "\n", "print(\"\\nTop 10 Singles Scorers:\")\n", "top_ones"]}, {"cell_type": "code", "execution_count": 32, "id": "26923090-19dc-4b91-93df-0852064b1cfb", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Top 10 Doubles Scorers:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>batter</th>\n", "      <th>doubles</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>490</th>\n", "      <td>V <PERSON></td>\n", "      <td>445</td>\n", "    </tr>\n", "    <tr>\n", "      <th>120</th>\n", "      <td>DA Warner</td>\n", "      <td>370</td>\n", "    </tr>\n", "    <tr>\n", "      <th>297</th>\n", "      <td><PERSON>i</td>\n", "      <td>340</td>\n", "    </tr>\n", "    <tr>\n", "      <th>400</th>\n", "      <td><PERSON></td>\n", "      <td>299</td>\n", "    </tr>\n", "    <tr>\n", "      <th>425</th>\n", "      <td>SK Raina</td>\n", "      <td>271</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>AB de Villiers</td>\n", "      <td>268</td>\n", "    </tr>\n", "    <tr>\n", "      <th>372</th>\n", "      <td><PERSON><PERSON></td>\n", "      <td>263</td>\n", "    </tr>\n", "    <tr>\n", "      <th>222</th>\n", "      <td><PERSON><PERSON></td>\n", "      <td>258</td>\n", "    </tr>\n", "    <tr>\n", "      <th>39</th>\n", "      <td><PERSON></td>\n", "      <td>257</td>\n", "    </tr>\n", "    <tr>\n", "      <th>153</th>\n", "      <td><PERSON></td>\n", "      <td>249</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["             batter  doubles\n", "490         V Kohli      445\n", "120       DA Warner      370\n", "297        MS Dhoni      340\n", "400        S <PERSON><PERSON><PERSON>      299\n", "425        SK Raina      271\n", "21   AB de Villiers      268\n", "372       <PERSON><PERSON>      263\n", "222      <PERSON><PERSON>      258\n", "39        AM Rahane      257\n", "153       G <PERSON><PERSON><PERSON>      249"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["print(\"\\nTop 10 Doubles Scorers:\")\n", "top_twos"]}, {"cell_type": "code", "execution_count": 33, "id": "7e8b2995-a4c8-4568-bd9a-d224a7f770b8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Top 10 Fours Hitters:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>batter</th>\n", "      <th>fours</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>416</th>\n", "      <td><PERSON></td>\n", "      <td>768</td>\n", "    </tr>\n", "    <tr>\n", "      <th>508</th>\n", "      <td>V <PERSON></td>\n", "      <td>708</td>\n", "    </tr>\n", "    <tr>\n", "      <th>119</th>\n", "      <td>DA Warner</td>\n", "      <td>663</td>\n", "    </tr>\n", "    <tr>\n", "      <th>388</th>\n", "      <td><PERSON><PERSON></td>\n", "      <td>599</td>\n", "    </tr>\n", "    <tr>\n", "      <th>441</th>\n", "      <td>SK Raina</td>\n", "      <td>506</td>\n", "    </tr>\n", "    <tr>\n", "      <th>158</th>\n", "      <td><PERSON></td>\n", "      <td>492</td>\n", "    </tr>\n", "    <tr>\n", "      <th>406</th>\n", "      <td><PERSON><PERSON></td>\n", "      <td>481</td>\n", "    </tr>\n", "    <tr>\n", "      <th>43</th>\n", "      <td><PERSON></td>\n", "      <td>479</td>\n", "    </tr>\n", "    <tr>\n", "      <th>230</th>\n", "      <td><PERSON><PERSON></td>\n", "      <td>466</td>\n", "    </tr>\n", "    <tr>\n", "      <th>155</th>\n", "      <td>F <PERSON>is</td>\n", "      <td>422</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["           batter  fours\n", "416      <PERSON> D<PERSON>    768\n", "508       V <PERSON><PERSON><PERSON>    708\n", "119     DA Warner    663\n", "388     <PERSON><PERSON>    599\n", "441      SK Raina    506\n", "158     G Gambhir    492\n", "406    R<PERSON>    481\n", "43      AM <PERSON><PERSON>    479\n", "230    <PERSON><PERSON>rth<PERSON>    466\n", "155  <PERSON> <PERSON>is    422"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["print(\"\\nTop 10 Fours Hitters:\")\n", "top_fours"]}, {"cell_type": "code", "execution_count": 34, "id": "87b637c7-c8de-46b8-aeeb-ab7d55c97b92", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Top 10 Sixes Hitters:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>batter</th>\n", "      <th>sixes</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>84</th>\n", "      <td><PERSON>le</td>\n", "      <td>359</td>\n", "    </tr>\n", "    <tr>\n", "      <th>324</th>\n", "      <td><PERSON><PERSON></td>\n", "      <td>281</td>\n", "    </tr>\n", "    <tr>\n", "      <th>427</th>\n", "      <td>V <PERSON></td>\n", "      <td>273</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>AB de Villiers</td>\n", "      <td>253</td>\n", "    </tr>\n", "    <tr>\n", "      <th>254</th>\n", "      <td><PERSON>i</td>\n", "      <td>252</td>\n", "    </tr>\n", "    <tr>\n", "      <th>98</th>\n", "      <td>DA Warner</td>\n", "      <td>236</td>\n", "    </tr>\n", "    <tr>\n", "      <th>184</th>\n", "      <td>KA Pollard</td>\n", "      <td>224</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td><PERSON></td>\n", "      <td>209</td>\n", "    </tr>\n", "    <tr>\n", "      <th>387</th>\n", "      <td>SV Samson</td>\n", "      <td>206</td>\n", "    </tr>\n", "    <tr>\n", "      <th>365</th>\n", "      <td>SK Raina</td>\n", "      <td>204</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["             batter  sixes\n", "84         CH Gayle    359\n", "324       <PERSON><PERSON>    281\n", "427         V <PERSON>hli    273\n", "14   AB de Villiers    253\n", "254        MS Dhoni    252\n", "98        DA Warner    236\n", "184      KA Pollard    224\n", "23       <PERSON> Russell    209\n", "387       SV Samson    206\n", "365        SK Raina    204"]}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "source": ["print(\"\\nTop 10 Sixes Hitters:\")\n", "top_sixes"]}, {"cell_type": "code", "execution_count": 35, "id": "40c39716-8014-46c9-abff-b2543589e76f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Average Runs per Match per Season:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>season</th>\n", "      <th>avg_runs_per_match</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2008</td>\n", "      <td>309.26</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2009</td>\n", "      <td>286.89</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2010</td>\n", "      <td>314.72</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2011</td>\n", "      <td>289.78</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2012</td>\n", "      <td>303.42</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>2013</td>\n", "      <td>297.39</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>2014</td>\n", "      <td>315.52</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>2015</td>\n", "      <td>311.07</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>2016</td>\n", "      <td>314.37</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>2017</td>\n", "      <td>318.41</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>2018</td>\n", "      <td>331.68</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>2019</td>\n", "      <td>323.90</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>2020</td>\n", "      <td>323.60</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>2021</td>\n", "      <td>310.62</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>2022</td>\n", "      <td>329.66</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>2023</td>\n", "      <td>347.14</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>2024</td>\n", "      <td>365.79</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    season  avg_runs_per_match\n", "0     2008              309.26\n", "1     2009              286.89\n", "2     2010              314.72\n", "3     2011              289.78\n", "4     2012              303.42\n", "5     2013              297.39\n", "6     2014              315.52\n", "7     2015              311.07\n", "8     2016              314.37\n", "9     2017              318.41\n", "10    2018              331.68\n", "11    2019              323.90\n", "12    2020              323.60\n", "13    2021              310.62\n", "14    2022              329.66\n", "15    2023              347.14\n", "16    2024              365.79"]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "source": ["# Seasonal Analysis\n", "# ================\n", "\n", "# 1. Average Runs per Match per Season\n", "match_runs = deliveries.groupby('match_id')['total_runs'].sum().reset_index()\n", "match_runs = pd.merge(match_runs, matches[['id', 'season']], left_on='match_id', right_on='id', how='left')\n", "season_avg_runs = match_runs.groupby('season')['total_runs'].mean().reset_index()\n", "season_avg_runs.columns = ['season', 'avg_runs_per_match']\n", "season_avg_runs['avg_runs_per_match'] = season_avg_runs['avg_runs_per_match'].round(2)\n", "print(\"\\nAverage Runs per Match per Season:\")\n", "season_avg_runs"]}, {"cell_type": "code", "execution_count": 36, "id": "16b23b6a-c349-4f93-a4a6-68e27c532c44", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "200+ Scores per Season:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>season</th>\n", "      <th>200+_scores</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2008</td>\n", "      <td>11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2009</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2010</td>\n", "      <td>9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2011</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2012</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>2013</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>2014</td>\n", "      <td>9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>2015</td>\n", "      <td>7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>2016</td>\n", "      <td>6</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>2017</td>\n", "      <td>10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>2018</td>\n", "      <td>15</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>2019</td>\n", "      <td>11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>2020</td>\n", "      <td>13</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>2021</td>\n", "      <td>9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>2022</td>\n", "      <td>18</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>2023</td>\n", "      <td>37</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>2024</td>\n", "      <td>41</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    season  200+_scores\n", "0     2008           11\n", "1     2009            1\n", "2     2010            9\n", "3     2011            5\n", "4     2012            5\n", "5     2013            4\n", "6     2014            9\n", "7     2015            7\n", "8     2016            6\n", "9     2017           10\n", "10    2018           15\n", "11    2019           11\n", "12    2020           13\n", "13    2021            9\n", "14    2022           18\n", "15    2023           37\n", "16    2024           41"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["# 2. 200+ Targets per Season\n", "high_scoring_innings = match_scores[match_scores['total_runs'] >= 200]\n", "high_scoring_innings = pd.merge(high_scoring_innings, matches[['id', 'season']], left_on='match_id', right_on='id', how='left')\n", "high_scores_per_season = high_scoring_innings.groupby('season').size().reset_index()\n", "high_scores_per_season.columns = ['season', '200+_scores']\n", "print(\"\\n200+ Scores per Season:\")\n", "high_scores_per_season"]}, {"cell_type": "code", "execution_count": 37, "id": "9ea8aa69-e69c-4d72-a459-eac9f28ff7d4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Average Team Score per Season (Sample):\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>season</th>\n", "      <th>team</th>\n", "      <th>avg_score</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2008</td>\n", "      <td>Chennai Super Kings</td>\n", "      <td>157.50</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2008</td>\n", "      <td>Deccan Chargers</td>\n", "      <td>159.21</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2008</td>\n", "      <td>Delhi Capitals</td>\n", "      <td>151.29</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2008</td>\n", "      <td>Kings XI Punjab</td>\n", "      <td>164.27</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2008</td>\n", "      <td>Kolkata Knight Riders</td>\n", "      <td>149.38</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>2008</td>\n", "      <td>Mumbai Indians</td>\n", "      <td>148.57</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>2008</td>\n", "      <td>Rajasthan Royals</td>\n", "      <td>162.56</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>2008</td>\n", "      <td>Royal Challengers Bangalore</td>\n", "      <td>141.64</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>2009</td>\n", "      <td>Chennai Super Kings</td>\n", "      <td>159.36</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>2009</td>\n", "      <td>Deccan Chargers</td>\n", "      <td>150.50</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   season                         team  avg_score\n", "0    2008          Chennai Super Kings     157.50\n", "1    2008              Deccan Chargers     159.21\n", "2    2008               Delhi Capitals     151.29\n", "3    2008              Kings XI Punjab     164.27\n", "4    2008        Kolkata Knight Riders     149.38\n", "5    2008               Mumbai Indians     148.57\n", "6    2008             Rajasthan Royals     162.56\n", "7    2008  Royal Challengers Bangalore     141.64\n", "8    2009          Chennai Super Kings     159.36\n", "9    2009              Deccan Chargers     150.50"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["# 3. Team Performance by Season\n", "team_season_runs = deliveries.groupby(['batting_team', 'match_id'])['total_runs'].sum().reset_index()\n", "team_season_runs = pd.merge(team_season_runs, matches[['id', 'season']], left_on='match_id', right_on='id', how='left')\n", "team_season_avg = team_season_runs.groupby(['season', 'batting_team'])['total_runs'].mean().reset_index()\n", "team_season_avg.columns = ['season', 'team', 'avg_score']\n", "team_season_avg['avg_score'] = team_season_avg['avg_score'].round(2)\n", "print(\"\\nAverage Team Score per Season (Sample):\")\n", "team_season_avg.head(10)"]}, {"cell_type": "code", "execution_count": 38, "id": "e4c55d91-fd1c-4a55-9af9-c4a91bd0915f", "metadata": {}, "outputs": [], "source": ["# Feature Extraction for Machine Learning\n", "# =====================================\n", "\n", "# Create a dataset for predicting match winners\n", "match_features = matches.copy()\n", "\n", "# Add toss features\n", "match_features['toss_win'] = (match_features['toss_winner'] == match_features['winner']).astype(int)\n", "\n", "# Add venue-based features\n", "venue_win_rates = match_features.groupby('venue')['winner'].value_counts().unstack().fillna(0)\n", "for team in venue_win_rates.columns:\n", "    venue_win_rates[f'{team}_win_rate'] = venue_win_rates[team] / venue_win_rates.sum(axis=1)\n", "venue_win_rates = venue_win_rates[[col for col in venue_win_rates.columns if '_win_rate' in col]]\n", "match_features = pd.merge(match_features, venue_win_rates.reset_index(), on='venue', how='left')\n", "\n", "# Add team form (recent performance) - We'll use a simple metric of win % in last 5 matches\n", "def get_team_form(team, date, n=5):\n", "    prev_matches = matches[(matches['date'] < date) & ((matches['team1'] == team) | (matches['team2'] == team))].tail(n)\n", "    if len(prev_matches) == 0:\n", "        return 0.5  # De<PERSON>ult if no previous matches\n", "    \n", "    wins = sum(prev_matches['winner'] == team)\n", "    return wins / len(prev_matches)\n", "\n", "# Add form for both teams (this will be computationally intensive)\n", "# For demo purposes, we'll only calculate this for a subset\n", "match_subset = match_features.sample(frac=0.1, random_state=42)  # 10% sample\n", "match_subset['team1_form'] = match_subset.apply(lambda row: get_team_form(row['team1'], row['date']), axis=1)\n", "match_subset['team2_form'] = match_subset.apply(lambda row: get_team_form(row['team2'], row['date']), axis=1)\n", "\n", "# Head-to-head metrics\n", "def get_head_to_head(team1, team2, date):\n", "    prev_matches = matches[(matches['date'] < date) & \n", "                          (((matches['team1'] == team1) & (matches['team2'] == team2)) | \n", "                           ((matches['team1'] == team2) & (matches['team2'] == team1)))]\n", "    \n", "    if len(prev_matches) == 0:\n", "        return 0.5  # <PERSON><PERSON><PERSON> if no head-to-head history\n", "    \n", "    team1_wins = sum(prev_matches['winner'] == team1)\n", "    return team1_wins / len(prev_matches)\n", "\n", "match_subset['team1_h2h'] = match_subset.apply(lambda row: get_head_to_head(row['team1'], row['team2'], row['date']), axis=1)\n", "\n", "# Add features for home advantage\n", "match_subset['team1_home'] = match_subset.apply(lambda row: 1 if row['venue'].find(row['team1'].split(' ')[-1]) != -1 else 0, axis=1)\n", "match_subset['team2_home'] = match_subset.apply(lambda row: 1 if row['venue'].find(row['team2'].split(' ')[-1]) != -1 else 0, axis=1)\n", "\n", "# Add target variable (winner)\n", "match_subset['team1_win'] = (match_subset['winner'] == match_subset['team1']).astype(int)\n", "\n", "# To ensure we have a complete dataset for the final matches of 2024 and for predicting 2025\n", "# We'll create a more simplified feature set for all matches\n", "simple_features = matches.copy()\n", "simple_features['toss_win'] = (simple_features['toss_winner'] == simple_features['winner']).astype(int)\n", "simple_features['team1_win'] = (simple_features['winner'] == simple_features['team1']).astype(int)\n", "\n", "# One-hot encode categorical variables\n", "teams = pd.get_dummies(simple_features[['team1', 'team2']].stack()).groupby(level=0).sum()\n", "toss_decision = pd.get_dummies(simple_features['toss_decision'], prefix='toss')\n", "venues = pd.get_dummies(simple_features['venue'], prefix='venue')"]}, {"cell_type": "code", "execution_count": 39, "id": "d975d998-c885-4420-9fe4-02354707fdd9", "metadata": {}, "outputs": [], "source": ["# Combine all features\n", "X = pd.concat([teams, toss_decision, simple_features[['toss_win']]], axis=1)\n", "y = simple_features['team1_win']\n", "\n", "# Split the dataset\n", "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)"]}, {"cell_type": "code", "execution_count": 41, "id": "b795d612-fa22-46d8-ae6b-3b4db3d7cf6c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Random Forest Accuracy: 0.684931506849315\n", "              precision    recall  f1-score   support\n", "\n", "           0       0.63      0.70      0.67        98\n", "           1       0.74      0.67      0.70       121\n", "\n", "    accuracy                           0.68       219\n", "   macro avg       0.68      0.69      0.68       219\n", "weighted avg       0.69      0.68      0.69       219\n", "\n"]}], "source": ["# Train the models\n", "# ===============\n", "\n", "# 1. Random Forest Classifier\n", "rf_model = RandomForestClassifier(n_estimators=100, random_state=42)\n", "rf_model.fit(X_train, y_train)\n", "rf_pred = rf_model.predict(X_test)\n", "rf_accuracy = accuracy_score(y_test, rf_pred)\n", "print(\"\\nRandom Forest Accuracy:\", rf_accuracy)\n", "print(classification_report(y_test, rf_pred))"]}, {"cell_type": "code", "execution_count": 42, "id": "eb9af7c6-e4d3-491d-85fc-2aea4c418120", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "XGBoost Accuracy: 0.6894977168949772\n", "              precision    recall  f1-score   support\n", "\n", "           0       0.63      0.74      0.68        98\n", "           1       0.76      0.64      0.70       121\n", "\n", "    accuracy                           0.69       219\n", "   macro avg       0.69      0.69      0.69       219\n", "weighted avg       0.70      0.69      0.69       219\n", "\n"]}], "source": ["# 2. XGBoost Classifier\n", "xgb_model = xgb.XGBClassifier(n_estimators=100, random_state=42)\n", "xgb_model.fit(X_train, y_train)\n", "xgb_pred = xgb_model.predict(X_test)\n", "xgb_accuracy = accuracy_score(y_test, xgb_pred)\n", "print(\"\\nXGBoost Accuracy:\", xgb_accuracy)\n", "print(classification_report(y_test, xgb_pred))"]}, {"cell_type": "code", "execution_count": 43, "id": "8314a982-aa84-4036-87a8-33b4b412ef0d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Ensemble Model Accuracy: 0.6757990867579908\n", "              precision    recall  f1-score   support\n", "\n", "           0       0.62      0.72      0.67        98\n", "           1       0.74      0.64      0.68       121\n", "\n", "    accuracy                           0.68       219\n", "   macro avg       0.68      0.68      0.68       219\n", "weighted avg       0.69      0.68      0.68       219\n", "\n"]}], "source": ["# 3. Ensemble Model (Voting Classifier)\n", "ensemble_model = VotingClassifier(\n", "    estimators=[\n", "        ('rf', RandomForestClassifier(n_estimators=100, random_state=42)),\n", "        ('xgb', xgb.XGBClassifier(n_estimators=100, random_state=42))\n", "    ],\n", "    voting='soft'\n", ")\n", "ensemble_model.fit(X_train, y_train)\n", "ensemble_pred = ensemble_model.predict(X_test)\n", "ensemble_accuracy = accuracy_score(y_test, ensemble_pred)\n", "print(\"\\nEnsemble Model Accuracy:\", ensemble_accuracy)\n", "print(classification_report(y_test, ensemble_pred))"]}, {"cell_type": "code", "execution_count": null, "id": "f31b2ab9-3f49-4b6b-9e2a-da4f86d07782", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "b6860892-d5ee-4974-99e5-e64602fba6aa", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 5}