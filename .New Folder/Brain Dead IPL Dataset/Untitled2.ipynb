{"cells": [{"cell_type": "code", "execution_count": 15, "id": "19bf0c78-f3de-4e2b-b1cd-36bfa609171e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: matplotlib in /home/<USER>/anaconda3/envs/unsloth_env/lib/python3.11/site-packages (3.10.1)\n", "Collecting seaborn\n", "  Using cached seaborn-0.13.2-py3-none-any.whl.metadata (5.4 kB)\n", "Requirement already satisfied: contourpy>=1.0.1 in /home/<USER>/anaconda3/envs/unsloth_env/lib/python3.11/site-packages (from matplotlib) (1.3.1)\n", "Requirement already satisfied: cycler>=0.10 in /home/<USER>/anaconda3/envs/unsloth_env/lib/python3.11/site-packages (from matplotlib) (0.12.1)\n", "Requirement already satisfied: fonttools>=4.22.0 in /home/<USER>/anaconda3/envs/unsloth_env/lib/python3.11/site-packages (from matplotlib) (4.56.0)\n", "Requirement already satisfied: kiwisolver>=1.3.1 in /home/<USER>/anaconda3/envs/unsloth_env/lib/python3.11/site-packages (from matplotlib) (1.4.8)\n", "Requirement already satisfied: numpy>=1.23 in /home/<USER>/anaconda3/envs/unsloth_env/lib/python3.11/site-packages (from matplotlib) (2.2.4)\n", "Requirement already satisfied: packaging>=20.0 in /home/<USER>/anaconda3/envs/unsloth_env/lib/python3.11/site-packages (from matplotlib) (24.2)\n", "Requirement already satisfied: pillow>=8 in /home/<USER>/anaconda3/envs/unsloth_env/lib/python3.11/site-packages (from matplotlib) (11.1.0)\n", "Requirement already satisfied: pyparsing>=2.3.1 in /home/<USER>/anaconda3/envs/unsloth_env/lib/python3.11/site-packages (from matplotlib) (3.2.1)\n", "Requirement already satisfied: python-dateutil>=2.7 in /home/<USER>/anaconda3/envs/unsloth_env/lib/python3.11/site-packages (from matplotlib) (2.9.0.post0)\n", "Requirement already satisfied: pandas>=1.2 in /home/<USER>/anaconda3/envs/unsloth_env/lib/python3.11/site-packages (from seaborn) (2.2.3)\n", "Requirement already satisfied: pytz>=2020.1 in /home/<USER>/anaconda3/envs/unsloth_env/lib/python3.11/site-packages (from pandas>=1.2->seaborn) (2025.1)\n", "Requirement already satisfied: tzdata>=2022.7 in /home/<USER>/anaconda3/envs/unsloth_env/lib/python3.11/site-packages (from pandas>=1.2->seaborn) (2025.1)\n", "Requirement already satisfied: six>=1.5 in /home/<USER>/anaconda3/envs/unsloth_env/lib/python3.11/site-packages (from python-dateutil>=2.7->matplotlib) (1.17.0)\n", "Using cached seaborn-0.13.2-py3-none-any.whl (294 kB)\n", "Installing collected packages: seaborn\n", "Successfully installed seaborn-0.13.2\n", "Note: you may need to restart the kernel to use updated packages.\n"]}], "source": ["%pip install matplotlib seaborn\n", "\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import warnings"]}, {"cell_type": "code", "execution_count": 16, "id": "dc34371f-71f5-4f45-80a7-13dff27218b7", "metadata": {}, "outputs": [], "source": ["warnings.filterwarnings(\"ignore\")\n", "\n", "plt.style.use('fivethirtyeight')\n", "sns.set(style=\"whitegrid\")"]}, {"cell_type": "code", "execution_count": 18, "id": "6220bf7c-1a27-4376-8ac2-1782c4c1326c", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Paper Id</th>\n", "      <th>Paper Title</th>\n", "      <th>Key Words</th>\n", "      <th>Abstract</th>\n", "      <th>Conclusion</th>\n", "      <th>Document</th>\n", "      <th>Paper Type</th>\n", "      <th>Summary</th>\n", "      <th>Topic</th>\n", "      <th>OCR</th>\n", "      <th>labels</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>Multi-document Summarization via Deep Learning...</td>\n", "      <td>Multi-document summarization (MDS), Deep learn...</td>\n", "      <td>Multi-document summarization (MDS) is an effec...</td>\n", "      <td>In this article, we have presented the first c...</td>\n", "      <td>Multi-document Summarization via Deep Learning...</td>\n", "      <td>Text summarization</td>\n", "      <td>This article presents a systematic overview of...</td>\n", "      <td>Natural Language Processing</td>\n", "      <td>lla i aye RR | poe [<PERSON><PERSON> | <PERSON><PERSON>, —+ ar ea...</td>\n", "      <td>Deep Learning and Machine Learning</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>NLP based Machine Learning Approaches for Text...</td>\n", "      <td>Text summarization, Abstractive and extractive...</td>\n", "      <td>Due to the plethora of data available today, t...</td>\n", "      <td>We have seen that due to abundant availability...</td>\n", "      <td>NLP based Machine Learning Approaches for Text...</td>\n", "      <td>Natural Language Processing</td>\n", "      <td>The article discusses the importance of text s...</td>\n", "      <td>Natural Language Processing</td>\n", "      <td>@STOM © Word Vector Embedding kenearest neighb...</td>\n", "      <td>Deep Learning and Machine Learning</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>Abstractive text summarization using LSTM-CNN ...</td>\n", "      <td>Text mining . Abstractive text summarization ....</td>\n", "      <td>Abstractive Text Summarization (ATS), which i...</td>\n", "      <td>In this paper, we develop a novel LSTM-CNN bas...</td>\n", "      <td>Abstractive text summarization using LSTM-CNN ...</td>\n", "      <td>Text summarization</td>\n", "      <td>The article presents a new framework for abstr...</td>\n", "      <td>Natural Language Processing</td>\n", "      <td>encoder decoderWord Merpholosical Coreterence ...</td>\n", "      <td>Deep Learning and Machine Learning</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>DEXPERTS: Decoding-Time Controlled Text Genera...</td>\n", "      <td>Natural language generation, Controlled text g...</td>\n", "      <td>Despite recent advances in natural language\\ng...</td>\n", "      <td>We present DEXPERTS, a method for controlled\\n...</td>\n", "      <td>DEXPERTS: Decoding-Time Controlled Text Genera...</td>\n", "      <td>Text generation</td>\n", "      <td>The paper proposes a method called DEXPERTS fo...</td>\n", "      <td>Natural Language Processing</td>\n", "      <td>reatva star on negative proms oe TT os ee oe S...</td>\n", "      <td>Deep Learning and Machine Learning</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>A Survey of Knowledge-enhanced Text Generation</td>\n", "      <td>text-to-text generation, natural language proc...</td>\n", "      <td>The goal of text-to-text generation is to make...</td>\n", "      <td>In this survey, we present a comprehensive rev...</td>\n", "      <td>A Survey of Knowledge-enhanced Text Generation...</td>\n", "      <td>Text generation</td>\n", "      <td>The paper discusses the challenges in text-to-...</td>\n", "      <td>Natural Language Processing</td>\n", "      <td>(ira =&gt; Generation model =&gt; foam] | Generation...</td>\n", "      <td>Deep Learning and Machine Learning</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Paper Id                                        Paper Title  \\\n", "0         1  Multi-document Summarization via Deep Learning...   \n", "1         2  NLP based Machine Learning Approaches for Text...   \n", "2         3  Abstractive text summarization using LSTM-CNN ...   \n", "3         4  DEXPERTS: Decoding-Time Controlled Text Genera...   \n", "4         5     A Survey of Knowledge-enhanced Text Generation   \n", "\n", "                                           Key Words  \\\n", "0  Multi-document summarization (MDS), Deep learn...   \n", "1  Text summarization, Abstractive and extractive...   \n", "2  Text mining . Abstractive text summarization ....   \n", "3  Natural language generation, Controlled text g...   \n", "4  text-to-text generation, natural language proc...   \n", "\n", "                                            Abstract  \\\n", "0  Multi-document summarization (MDS) is an effec...   \n", "1  Due to the plethora of data available today, t...   \n", "2   Abstractive Text Summarization (ATS), which i...   \n", "3  Despite recent advances in natural language\\ng...   \n", "4  The goal of text-to-text generation is to make...   \n", "\n", "                                          Conclusion  \\\n", "0  In this article, we have presented the first c...   \n", "1  We have seen that due to abundant availability...   \n", "2  In this paper, we develop a novel LSTM-CNN bas...   \n", "3  We present DEXPERTS, a method for controlled\\n...   \n", "4  In this survey, we present a comprehensive rev...   \n", "\n", "                                            Document  \\\n", "0  Multi-document Summarization via Deep Learning...   \n", "1  NLP based Machine Learning Approaches for Text...   \n", "2  Abstractive text summarization using LSTM-CNN ...   \n", "3  DEXPERTS: Decoding-Time Controlled Text Genera...   \n", "4  A Survey of Knowledge-enhanced Text Generation...   \n", "\n", "                    Paper Type  \\\n", "0           Text summarization   \n", "1  Natural Language Processing   \n", "2           Text summarization   \n", "3              Text generation   \n", "4              Text generation   \n", "\n", "                                             Summary  \\\n", "0  This article presents a systematic overview of...   \n", "1  The article discusses the importance of text s...   \n", "2  The article presents a new framework for abstr...   \n", "3  The paper proposes a method called DEXPERTS fo...   \n", "4  The paper discusses the challenges in text-to-...   \n", "\n", "                         Topic  \\\n", "0  Natural Language Processing   \n", "1  Natural Language Processing   \n", "2  Natural Language Processing   \n", "3  Natural Language Processing   \n", "4  Natural Language Processing   \n", "\n", "                                                 OCR  \\\n", "0  lla i aye RR | poe [<PERSON><PERSON> | <PERSON><PERSON>, —+ ar ea...   \n", "1  @STOM © Word Vector Embedding kenearest neighb...   \n", "2  encoder decoderWord Merpholosical Coreterence ...   \n", "3  reatva star on negative proms oe TT os ee oe S...   \n", "4  (ira => Generation model => foam] | Generation...   \n", "\n", "                               labels  \n", "0  Deep Learning and Machine Learning  \n", "1  Deep Learning and Machine Learning  \n", "2  Deep Learning and Machine Learning  \n", "3  Deep Learning and Machine Learning  \n", "4  Deep Learning and Machine Learning  "]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["data = pd.read_csv('Brain_Dead_CompScholar_Dataset.csv')\n", "data.head()"]}, {"cell_type": "code", "execution_count": 19, "id": "2e58ec7e-16bc-4971-8e77-4faf34053ced", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Index(['Paper Id', 'Paper Title', 'Key Words', 'Abstract', 'Conclusion',\n", "       'Document', 'Paper Type', 'Summary', 'Topic', 'OCR', 'labels'],\n", "      dtype='object')\n"]}], "source": ["column_names = data.columns\n", "print(column_names)"]}, {"cell_type": "code", "execution_count": 20, "id": "e9b09059-892c-47e8-890f-cddf5d189c97", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 371 entries, 0 to 370\n", "Data columns (total 11 columns):\n", " #   Column       Non-Null Count  Dtype \n", "---  ------       --------------  ----- \n", " 0   Paper Id     371 non-null    int64 \n", " 1   Paper Title  371 non-null    object\n", " 2   Key Words    371 non-null    object\n", " 3   Abstract     371 non-null    object\n", " 4   Conclusion   371 non-null    object\n", " 5   Document     371 non-null    object\n", " 6   Paper Type   371 non-null    object\n", " 7   Summary      371 non-null    object\n", " 8   Topic        371 non-null    object\n", " 9   OCR          245 non-null    object\n", " 10  labels       371 non-null    object\n", "dtypes: int64(1), object(10)\n", "memory usage: 32.0+ KB\n"]}], "source": ["data.info()"]}, {"cell_type": "code", "execution_count": 21, "id": "369b7ad1-20f4-4b8b-a57c-cbab4d5c900f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Frequency of each Paper Type:\n"]}, {"data": {"text/plain": ["Paper Type\n", "Medical Data Analysis                          76\n", "Deep Learning and Machine Learning             63\n", "Person recognition                             53\n", "sentiment analysis                             46\n", "Text summarization                             27\n", "Facial Emotion Recognition                     25\n", "Emotion detection using normal face            24\n", "Natural Language Processing                    15\n", "Text generation                                14\n", "Artificial Neural Network                      10\n", "Others                                          5\n", "opinion mining                                  2\n", "lexicon-based approach                          1\n", "sentimental analysis on social media review     1\n", "Data mining                                     1\n", "Convolution Neural Network                      1\n", "facial recognition                              1\n", "feature selection                               1\n", "semantic indexing                               1\n", "sentiment classification                        1\n", "text  mining                                    1\n", "computational intelligence                      1\n", "Emotion detection using cartoon face            1\n", "Name: count, dtype: int64"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["paper_type_counts = data['Paper Type'].value_counts()\n", "print(\"\\nFrequency of each Paper Type:\")\n", "paper_type_counts"]}, {"cell_type": "code", "execution_count": 22, "id": "7f06b87b-d5f6-43a9-b653-30e20a50d8b7", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.figure(figsize=(10, 6))\n", "paper_type_counts.plot(kind='bar', color='skyblue')\n", "plt.title('Distribution of Paper Types')\n", "plt.xlabel('Paper Type')\n", "plt.ylabel('Count')\n", "plt.xticks(rotation=45)\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 23, "id": "ed97f5e1-f338-45d9-8514-0ce7d0df9016", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Frequency of each Topic:\n"]}, {"data": {"text/plain": ["Topic\n", "Object and Sentiment Recognition      187\n", "Medical Data Analysis                  92\n", "Natural Language Processing            56\n", "Deep Learning and Machine Learning     36\n", "Name: count, dtype: int64"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["topic_counts = data['Topic'].value_counts()\n", "print(\"\\nFrequency of each Topic:\")\n", "topic_counts"]}, {"cell_type": "code", "execution_count": 24, "id": "eb922a03-9d5a-4e16-aede-3d4d990c88b8", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.figure(figsize=(10, 6))\n", "topic_counts.plot(kind='bar', color='salmon')\n", "plt.title('Distribution of Topics')\n", "plt.xlabel('Topic')\n", "plt.ylabel('Count')\n", "plt.xticks(rotation=45)\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 25, "id": "2b723674-4e96-4e2a-924c-bf81cc6ba748", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Statistics of Abstract Lengths (in words):\n"]}, {"data": {"text/plain": ["count    371.000000\n", "mean     189.142857\n", "std       65.927098\n", "min       55.000000\n", "25%      143.500000\n", "50%      175.000000\n", "75%      229.500000\n", "max      446.000000\n", "Name: abstract_length, dtype: float64"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["data['abstract_length'] = data['Abstract'].apply(lambda x: len(str(x).split()))\n", "print(\"\\nStatistics of Abstract Lengths (in words):\")\n", "data['abstract_length'].describe()"]}, {"cell_type": "code", "execution_count": 26, "id": "68f10917-1a7a-4f24-9afb-eec43d205875", "metadata": {}, "outputs": [{"data": {"image/png": "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*************************************************************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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.figure(figsize=(10, 6))\n", "sns.histplot(data['abstract_length'], bins=20, kde=True, color='green')\n", "plt.title('Distribution of Abstract Lengths (Word Count)')\n", "plt.xlabel('Word Count')\n", "plt.ylabel('Frequency')\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 1, "id": "1692910c-fa57-4198-a492-d094115aa0b1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🦥 Unsloth: Will patch your computer to enable 2x faster free finetuning.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/unsloth_env/lib/python3.11/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n", "WARNING[XFORMERS]: xFormers can't load C++/CUDA extensions. xFormers was built for:\n", "    PyTorch 2.5.1 with CUDA 1201 (you have 2.6.0+cu124)\n", "    Python  3.11.10 (you have 3.11.11)\n", "  Please reinstall xformers (see https://github.com/facebookresearch/xformers#installing-xformers)\n", "  Memory-efficient attention, SwiGLU, sparse and more won't be available.\n", "  Set XFORMERS_MORE_DETAILS=1 for more details\n"]}, {"name": "stdout", "output_type": "stream", "text": ["🦥 Unsloth Zoo will now patch everything to make training faster!\n"]}], "source": ["from unsloth import FastLanguageModel, get_chat_template\n", "from datasets import load_dataset\n", "from trl import SFTTrainer\n", "from transformers import TrainingArguments, DataCollatorForLanguageModeling\n", "from unsloth.chat_templates import train_on_responses_only\n", "import torch\n", "import numpy as np"]}, {"cell_type": "code", "execution_count": 15, "id": "fa085cba-d1ab-4a9e-8928-b7e4b34caf24", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["==((====))==  Unsloth 2025.3.18: Fast Llama patching. Transformers: 4.50.0.\n", "   \\\\   /|    NVIDIA GeForce RTX 4060 Ti. Num GPUs = 1. Max memory: 15.592 GB. Platform: Linux.\n", "O^O/ \\_/ \\    Torch: 2.6.0+cu124. CUDA: 8.9. CUDA Toolkit: 12.4. Triton: 3.2.0\n", "\\        /    Bfloat16 = TRUE. FA [Xformers = None. FA2 = False]\n", " \"-____-\"     Free license: http://github.com/unslothai/unsloth\n", "Unsloth: Fast downloading is enabled - ignore downloading bars which are red colored!\n"]}], "source": ["# Load model\n", "max_seq_length = 2048\n", "model, tokenizer = FastLanguageModel.from_pretrained(\n", "    model_name=\"unsloth/Meta-Llama-3.1-8B-Instruct-bnb-4bit\",\n", "    max_seq_length=max_seq_length,\n", "    dtype=None,\n", "    load_in_4bit=True,\n", ")"]}, {"cell_type": "code", "execution_count": 16, "id": "26efb0a8-35d6-401e-8d94-5895d3f66d64", "metadata": {}, "outputs": [], "source": ["model = FastLanguageModel.get_peft_model(\n", "    model,\n", "    r=16,\n", "    target_modules=[\"q_proj\", \"k_proj\", \"v_proj\", \"o_proj\", \"gate_proj\", \"up_proj\", \"down_proj\"],\n", "    lora_alpha=16,\n", "    lora_dropout=0,\n", "    bias=\"none\",\n", "    use_gradient_checkpointing=\"unsloth\",\n", "    random_state=3407,\n", ")"]}, {"cell_type": "code", "execution_count": 17, "id": "1ead1947-8273-40d0-b250-ce35c1eda6e5", "metadata": {}, "outputs": [{"data": {"text/plain": ["Dataset({\n", "    features: ['article', 'abstract'],\n", "    num_rows: 119924\n", "})"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["# Load and preprocess dataset\n", "def preprocess_dataset(sample):\n", "    \"\"\"Clean and format articles/abstracts\"\"\"\n", "    # Handle missing articles\n", "    if sample[\"article\"] is None: \n", "        sample[\"article\"] = \"No content available\"\n", "    # Remove empty abstracts\n", "    if not sample[\"abstract\"].strip():\n", "        return None\n", "    return sample\n", "\n", "dataset = load_dataset('csv', data_files='train.csv', split='train')\n", "dataset = dataset.map(preprocess_dataset).filter(lambda x: x is not None)\n", "dataset"]}, {"cell_type": "code", "execution_count": 18, "id": "058526b0-8241-4b1b-b586-e046e79b8a01", "metadata": {}, "outputs": [{"data": {"text/plain": ["Dataset({\n", "    features: ['article', 'abstract', 'conversations'],\n", "    num_rows: 119924\n", "})"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["# Create chat format\n", "def add_conversations(example):\n", "    return {\n", "        \"conversations\": [\n", "            {\"role\": \"user\", \"content\": f\"Summarize this research article:\\n\\n{example['article']}\"},\n", "            {\"role\": \"assistant\", \"content\": example['abstract']}\n", "        ]\n", "    }\n", "dataset = dataset.map(add_conversations, num_proc=4)\n", "dataset"]}, {"cell_type": "code", "execution_count": 19, "id": "7b499849-1737-4b5e-982f-fd092a7c182b", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Map (num_proc=4): 100%|██████████| 119924/119924 [00:06<00:00, 17453.33 examples/s]\n"]}, {"data": {"text/plain": ["Dataset({\n", "    features: ['article', 'abstract', 'conversations', 'text'],\n", "    num_rows: 119924\n", "})"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["# Apply chat template\n", "tokenizer = get_chat_template(tokenizer, chat_template=\"llama-3.1\")\n", "def formatting_prompts_func(examples):\n", "    return {\n", "        \"text\": [\n", "            tokenizer.apply_chat_template(convo, tokenize=False, add_generation_prompt=False)\n", "            for convo in examples[\"conversations\"]\n", "        ]\n", "    }\n", "dataset = dataset.map(formatting_prompts_func, batched=True, num_proc=4)\n", "\n", "dataset"]}, {"cell_type": "code", "execution_count": 20, "id": "5f15f66c", "metadata": {}, "outputs": [{"data": {"text/plain": ["Dataset({\n", "    features: ['text'],\n", "    num_rows: 119924\n", "})"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["# Remove original columns\n", "dataset = dataset.remove_columns([\"article\", \"abstract\", \"conversations\"])\n", "dataset"]}, {"cell_type": "code", "execution_count": 21, "id": "ea4ca375", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'text': \"<|begin_of_text|><|start_header_id|>system<|end_header_id|>\\n\\nCutting Knowledge Date: December 2023\\nToday Date: 26 July 2024\\n\\n<|eot_id|><|start_header_id|>user<|end_header_id|>\\n\\nSummarize this research article:\\n\\na recent systematic analysis showed that in 2011 , 314 ( 296 - 331 ) million children younger than 5 years were mildly , moderately or severely stunted and 258 ( 240 - 274 ) million were mildly , moderately or severely underweight in the developing countries . \\n in iran a study among 752 high school girls in sistan and baluchestan showed prevalence of 16.2% , 8.6% and 1.5% , for underweight , overweight and obesity , respectively . \\n the prevalence of malnutrition among elementary school aged children in tehran varied from 6% to 16% . \\n anthropometric study of elementary school students in shiraz revealed that 16% of them suffer from malnutrition and low body weight . \\n snack should have 300 - 400 kcal energy and could provide 5 - 10 g of protein / day . nowadays , school nutrition programs are running as the national programs , world - wide . national school lunch program in the united states \\n there are also some reports regarding school feeding programs in developing countries . in vietnam , \\n school base program showed an improvement in nutrient intakes . in iran a national free food program ( nffp ) \\n is implemented in elementary schools of deprived areas to cover all poor students . however , this program is not conducted in slums and poor areas of the big cities so many malnourished children with low socio - economic situation are not covered by nffp . although the rate of poverty in areas known as deprived is higher than other areas , many students in deprived areas are not actually poor and can afford food . \\n hence , nutritional value of the nffp is lower than the scientific recommended snacks for this age group . \\n furthermore , lack of variety of food packages has decreased the tendency of children toward nffp . on the other hand , \\n the most important one is ministry of education ( moe ) of iran , which is responsible for selecting and providing the packages for targeted schools . \\n the ministry of health ( moh ) is supervising the health situation of students and their health needs . \\n welfare organizations , along with charities , have the indirect effect on nutritional status of students by financial support of their family . \\n provincial governors have also the role of coordinating and supervising all activities of these organizations . \\n parent - teacher association is a community - based institution that participates in school 's policy such as nffp . \\n in addition to these organizations , nutritional literacy of students , their parents and teachers , is a very important issue , which could affect nutritional status of school age children . \\n therefore , the present study was conducted with the aim of improving the nffp , so that by its resources all poor children will be covered even in big cities . \\n moreover , all food packages were replaced by nutritious and diverse packages that were accessible for non - poor children . according to the aim of this study and multiple factors that could affect the problem , \\n public health advocacy has been chosen as the best strategy to deal with this issue . \\n therefore , the present study determines the effects of nutrition intervention in an advocacy process model on the prevalence of underweight in school aged children in the poor area of shiraz , iran . \\n this interventional study has been carried out between 2009 and 2010 in shiraz , iran . \\n this survey was approved by the research committee of shiraz university of medical sciences . in coordination with education organization of fars province \\n two elementary schools and one middle school in the third region of the urban area of shiraz were selected randomly . in those schools all \\n students ( 2897 , 7 - 13 years old ) were screened based on their body mass index ( bmi ) by nutritionists . according to convenience method all \\n students divided to two groups based on their economic situation ; family revenue and head of household 's job and nutrition situation ; the first group were poor and malnourished students and the other group were well nourished or well - off students . \\n for this report , the children 's height and weight were entered into center for disease control and prevention ( cdc ) to calculate bmi and bmi - for - age z - scores based on cdc for diseases control and prevention and growth standards . \\n the significance of the difference between proportions was calculated using two - tailed z - tests for independent proportions . for implementing the interventions , \\n the advocacy process model weight was to the nearest 0.1 kg on a balance scale ( model # seca scale ) . \\n standing height was measured to the nearest 0.1 cm with a wall - mounted stadiometer . \\n advocacy group formation : this step was started with stakeholder analysis and identifying the stakeholders . \\n the team was formed with representatives of all stakeholders include ; education organization , welfare organization , deputy for health of shiraz university , food and cosmetic product supervisory office and several non - governmental organizations and charities . situation analysis : this was carried out by use of existing data such as formal report of organizations , literature review and focus group with experts . \\n the prevalence of malnutrition and its related factors among students was determined and weaknesses and strengths of the nffp were analyzed . \\n accordingly , three sub - groups were established : research and evaluation , education and justification and executive group . designing the strategies : \\n three strategies were identified ; education and justification campaign , nutritional intervention ( providing nutritious , safe and diverse snacks ) and networking . performing the interventions : interventions that were implementing in selected schools were providing a diverse and nutritious snack package along with nutrition education for both groups while the first group ( poor and malnourished students ) was utilized the package free of charge . \\n education and justification intervention : regarding the literature review and expert opinion , an educational group affiliated with the advocacy team has prepared educational booklets about nutritional information for each level ( degree ) . \\n accordingly , education of these booklets has been integrated into regular education of students and they educated and justified for better nutrition life - style . \\n it leads the educational group to hold several meeting with the student 's parents to justify them about the project and its benefit for their children . \\n after these meetings , parental desire for participation in the project illustrated the effectiveness of the justification meeting with them . \\n for educate fifteen talk show programs in tv and radio , 12 published papers in the local newspaper , have implemented to mobilize the community and gain their support . \\n healthy diet , the importance of breakfast and snack in adolescence , wrong food habits among school age children , role of the family to improve food habit of children were the main topics , in which media campaign has focused on . \\n nutritional intervention : the snack basket of the students was replaced with traditional , nutritious and diverse foods . in general , the new snack package in average has provided 380 kcal energy , 15 g protein along with sufficient calcium and iron . \\n low economic and malnourished children were supported by executive group affiliated with advocacy team and the rest of them prepare their snack by themselves . \\n research and evaluation : in this step , the literacy and anthropometric indices ( bmi ) of students were assessed before and after the interventions . \\n the reference for anthropometric measures was the world health organization / national center for health statistics ( who / nchs ) standards and the cut - offs were - two standard deviations ( sd ) from the mean . \\n each student that was malnourished and poor has been taken into account for free food and nutritious snacks . \\n demographic information , height , weight and knowledge of the students were measured by use of a validated and reliable ( cronbach 's alpha was 0.61 ) questionnaire . \\n this project is granted by shiraz university of medical sciences , charities and welfare organization and education organization of fars province . \\n statistical analyses were performed using the statistical package for the social sciences ( spss ) software , version 17.0 ( spss inc . , \\n the results are expressed as mean  sd and proportions as appropriated . in order to determine the effective variables on the malnutrition status \\n paired t test was used to compare the end values with baseline ones in each group . \\n in this project , the who z - score cut - offs used were as follow : using bmi - for - age z - scores ; overweight : > + 1 sd , i.e. , z - score > 1 ( equivalent to bmi 25 kg / m ) , obesity : > + 2 sd ( equivalent to bmi 30 kg / m ) , thinness : < 2 sd and severe thinness : < 3 sd . \\n this interventional study has been carried out between 2009 and 2010 in shiraz , iran . \\n this survey was approved by the research committee of shiraz university of medical sciences . in coordination with education organization of fars province \\n two elementary schools and one middle school in the third region of the urban area of shiraz were selected randomly . in those schools all \\n students ( 2897 , 7 - 13 years old ) were screened based on their body mass index ( bmi ) by nutritionists . according to convenience method all \\n students divided to two groups based on their economic situation ; family revenue and head of household 's job and nutrition situation ; the first group were poor and malnourished students and the other group were well nourished or well - off students . \\n for this report , the children 's height and weight were entered into center for disease control and prevention ( cdc ) to calculate bmi and bmi - for - age z - scores based on cdc for diseases control and prevention and growth standards . \\n the significance of the difference between proportions was calculated using two - tailed z - tests for independent proportions . for implementing the interventions , \\n weight was to the nearest 0.1 kg on a balance scale ( model # seca scale ) . \\n standing height was measured to the nearest 0.1 cm with a wall - mounted stadiometer . \\n advocacy group formation : this step was started with stakeholder analysis and identifying the stakeholders . \\n the team was formed with representatives of all stakeholders include ; education organization , welfare organization , deputy for health of shiraz university , food and cosmetic product supervisory office and several non - governmental organizations and charities . situation analysis : this was carried out by use of existing data such as formal report of organizations , literature review and focus group with experts . \\n the prevalence of malnutrition and its related factors among students was determined and weaknesses and strengths of the nffp were analyzed . \\n accordingly , three sub - groups were established : research and evaluation , education and justification and executive group . designing the strategies : \\n three strategies were identified ; education and justification campaign , nutritional intervention ( providing nutritious , safe and diverse snacks ) and networking . performing the interventions : interventions that were implementing in selected schools were providing a diverse and nutritious snack package along with nutrition education for both groups while the first group ( poor and malnourished students ) was utilized the package free of charge . duration of intervention was 6 months . \\n education and justification intervention : regarding the literature review and expert opinion , an educational group affiliated with the advocacy team has prepared educational booklets about nutritional information for each level ( degree ) . \\n accordingly , education of these booklets has been integrated into regular education of students and they educated and justified for better nutrition life - style . obviously , student 's families had remarkable effect on children 's food habit . \\n it leads the educational group to hold several meeting with the student 's parents to justify them about the project and its benefit for their children . \\n after these meetings , parental desire for participation in the project illustrated the effectiveness of the justification meeting with them . \\n educate fifteen talk show programs in tv and radio , 12 published papers in the local newspaper , have implemented to mobilize the community and gain their support . \\n healthy diet , the importance of breakfast and snack in adolescence , wrong food habits among school age children , role of the family to improve food habit of children were the main topics , in which media campaign has focused on . \\n nutritional intervention : the snack basket of the students was replaced with traditional , nutritious and diverse foods . in general , the new snack package in average has provided 380 kcal energy , 15 g protein along with sufficient calcium and iron . \\n low economic and malnourished children were supported by executive group affiliated with advocacy team and the rest of them prepare their snack by themselves . \\n research and evaluation : in this step , the literacy and anthropometric indices ( bmi ) of students were assessed before and after the interventions . \\n the reference for anthropometric measures was the world health organization / national center for health statistics ( who / nchs ) standards and the cut - offs were - two standard deviations ( sd ) from the mean . \\n each student that was malnourished and poor has been taken into account for free food and nutritious snacks . \\n demographic information , height , weight and knowledge of the students were measured by use of a validated and reliable ( cronbach 's alpha was 0.61 ) questionnaire . \\n this project is granted by shiraz university of medical sciences , charities and welfare organization and education organization of fars province . \\n advocacy group formation : this step was started with stakeholder analysis and identifying the stakeholders . \\n the team was formed with representatives of all stakeholders include ; education organization , welfare organization , deputy for health of shiraz university , food and cosmetic product supervisory office and several non - governmental organizations and charities . \\n situation analysis : this was carried out by use of existing data such as formal report of organizations , literature review and focus group with experts . \\n the prevalence of malnutrition and its related factors among students was determined and weaknesses and strengths of the nffp were analyzed . \\n accordingly , three sub - groups were established : research and evaluation , education and justification and executive group . \\n designing the strategies : three strategies were identified ; education and justification campaign , nutritional intervention ( providing nutritious , safe and diverse snacks ) and networking . \\n performing the interventions : interventions that were implementing in selected schools were providing a diverse and nutritious snack package along with nutrition education for both groups while the first group ( poor and malnourished students ) was utilized the package free of charge . \\n education and justification intervention : regarding the literature review and expert opinion , an educational group affiliated with the advocacy team has prepared educational booklets about nutritional information for each level ( degree ) . \\n accordingly , education of these booklets has been integrated into regular education of students and they educated and justified for better nutrition life - style . obviously , student 's families had remarkable effect on children 's food habit . \\n it leads the educational group to hold several meeting with the student 's parents to justify them about the project and its benefit for their children . \\n after these meetings , parental desire for participation in the project illustrated the effectiveness of the justification meeting with them . \\n educate fifteen talk show programs in tv and radio , 12 published papers in the local newspaper , have implemented to mobilize the community and gain their support . \\n healthy diet , the importance of breakfast and snack in adolescence , wrong food habits among school age children , role of the family to improve food habit of children were the main topics , in which media campaign has focused on . nutritional intervention : the snack basket of the students \\n was replaced with traditional , nutritious and diverse foods . in general , the new snack package in average has provided 380 kcal energy , 15 g protein along with sufficient calcium and iron . \\n low economic and malnourished children were supported by executive group affiliated with advocacy team and the rest of them prepare their snack by themselves . \\n research and evaluation : in this step , the literacy and anthropometric indices ( bmi ) of students were assessed before and after the interventions . \\n the reference for anthropometric measures was the world health organization / national center for health statistics ( who / nchs ) standards and the cut - offs were - two standard deviations ( sd ) from the mean . \\n each student that was malnourished and poor has been taken into account for free food and nutritious snacks . \\n demographic information , height , weight and knowledge of the students were measured by use of a validated and reliable ( cronbach 's alpha was 0.61 ) questionnaire . \\n this project is granted by shiraz university of medical sciences , charities and welfare organization and education organization of fars province . \\n statistical analyses were performed using the statistical package for the social sciences ( spss ) software , version 17.0 ( spss inc . , chicago , il , usa ) . \\n the results are expressed as mean  sd and proportions as appropriated . in order to determine the effective variables on the malnutrition status \\n paired t test was used to compare the end values with baseline ones in each group . \\n two - sided p < 0.05 was considered to be statistically significant . in this project , \\n the who z - score cut - offs used were as follow : using bmi - for - age z - scores ; overweight : > + 1 sd , i.e. , z - score > 1 ( equivalent to bmi 25 kg / m ) , obesity : > + 2 sd ( equivalent to bmi 30 \\n kg / m ) , thinness : < 2 sd and severe thinness : < 3 sd . \\n study population contains 2897 children ; 70.8% were primary school students and 29.2% were secondary school students . \\n 2336 ( 80.5% ) out of total students were well - off and 561 children ( 19.5% ) were indigent . \\n 19.5% of subjects were in case group ( n = 561 ) and 80.5% were in the control group ( n = 2336 ) . \\n the mean of age in welfare group was 10.0  2.3 and 10.5  2.5 in non - welfare group . \\n demographic characteristics of school aged children in shiraz , iran table 2 shows the frequency of subjects in different categories of bmi for age in non - welfare and welfare groups of school aged children separately among boys and girls before and after a nutrition intervention based on advocacy process model in shiraz , iran . \\n the frequency of subjects with bmi lower than < 2 sd decreased significantly after intervention among non - welfare girls ( p < 0.01 ) . \\n however , there were no significant decreases in the frequency of subjects with bmi lower than < 2 sd boys . \\n when we assess the effect of intervention in total population without separating by sex groups , we found no significant change in this population [ table 3 ] . \\n bmi for age for iranian students aged 7 - 14 years based on gender according to who growth standards 2007 bmi for age for iranian students aged 7 - 14 years according to who growth standards 2007 in non - welfare and welfare groups of total population table 4 has shown the prevalence of normal bmi , mild , moderate and severe malnutrition in non - welfare and welfare groups of school aged children separately among boys and girls before and after a nutrition intervention based on advocacy process model . according to this table \\n there were no significant differences in the prevalence of mild , moderate and severe malnutrition among girls and boys . \\n table 4 also shows the mean of all anthropometric indices changed significantly after intervention both among girls and boys . \\n the pre- and post - test education assessment in both groups showed that the student 's average knowledge score has been significantly increased from 12.5  3.2 to 16.8  4.3 ( p < 0.0001 ) . \\n bmi , height and weight in non - welfare and welfare groups of school aged children separately in males and females before and after a nutrition intervention based on advocacy process model in shiraz , iran according to study 's finding the odds ratio ( or ) of sever thinness and thinness in non - welfare compared with welfare is 3.5 ( or = 3.5 , confidence interval [ ci ] = 2.5 - 3.9 , p < 0.001 ) . \\n furthermore , the finding showed or of overweight and obesity in welfare compared to non - welfare is 19.3 ( or = 19.3 , ci = 2.5 - 3.9 , p = 0.04 ) . \\n the result of this community intervention study revealed that nutrition intervention based on advocacy program had been successful to reduce the prevalence of underweight among poor girls . \\n this study shows determinant factor of nutritional status of school age children was their socio - economic level . according to our knowledge , \\n this is the first study , which determines the effect of a community intervention based on advocacy process on the malnutrition indices in a big city ( shiraz ) in iran . \\n the other program in iran ( nffp ) is specified to deprived area and is not conducted in big cities . \\n allocating millions of dollars to nffp by government , selecting the malnourished students through an active screening system at primary and middle schools , paying attention of policy makers to student 's nutrition have provided the opportunity to combat the problem . however , negligence of under - poverty line , providing poor snacks in terms of nutritional value and lack of variety are the main defects of this program . \\n advocacy by definition is a blending of science , ethics and politics for comprehensive approaching health issues . by using advocacy program in california among the high school students for improving their nutrition and physical activity \\n angeles unified school district participants emphasized on nutrition classes for families as well as students in addition to other interventions . in the present study \\n another study revealed that evaluability assessment gave stakeholders the opportunity to reflect on the project and its implementation issues . \\n it seems that in iran , free food program among the students not only is needed in deprived areas , but also it should be performed in big cities such as shiraz . at baseline , \\n no significant difference was founded among wealthy students between the pre- and post - nutritional status intervention . \\n in contrast , the numbers of students who have malnutrition decreased from 44% to 39.4% , which was identified as a significant among impecunious girls students . \\n there was also a significant increase in the proportion of children with bmi that was normal for age ( 2 to + 1 sd ) most of the published community interventions showed better results among females compared with males . \\n this difference in the impact of nutritional interventions between male and female might be related to the different age of puberty in the female population compared to the male population . in the age range of the present study female \\n although , there is no nffp in big cities of iran , there are some programs for improving the nutritional status such as providing free milk in schools . \\n a recent publication has shown that school feeding programs focus on milk supplementation had beneficial effects on the physical function and school performances specifically among girls in iran . \\n the results of the mentioned study showed an improvement in the weight of children , psychological test 's scores and the grade - point average following this school feeding program . \\n the intervention in the present study had focused on the snack intake in the school time . \\n there are some reports regarding the nutrition transition in iran , which shows the importance of nutrition intervention to provide more healthy eating dietary habits among welfare groups of adolescents . \\n hence , nutrition intervention especially in the form of nutrition education is needed in big cities and among welfare children and adolescents . although a study among iranian adolescents showed that dietary behavior of adolescents does not accord to their knowledge , which emphasize on the necessity of community intervention programs . a recent study regarding the major dietary pattern among iranian children showed the presence of four major dietary patterns , in which fast food pattern and sweet pattern as two major dietary patterns can be mentioned among iranian children . in advocacy program audience 's analysis \\n accordingly , one of the prominent strategies in this study was working with media and was meeting with parent - teacher association that both of them were secondary target audiences \\n . we also took into account policy makers in different levels , from national to local as primary audiences . \\n advocacy team had several meetings with management and planning organization at national level and education organization of the fars province as well as principal of the targeted schools . \\n providing nutritious snacks need contribution of private sector such as food industries or factories , but their benefits should be warranted . \\n another choice was community involvement ; which can be achieved by female health volunteers who are working with the health system . \\n advocacy team by using the support of charities and female health volunteers could establish a local factory that produced student 's snacks based on the new definition . however , there are some challenges on the way of expanding this program . \\n mass production of the proposed snacks according to different desires and cultures and getting involvement of food industries with respect to marketing issues is one of those challenges . \\n moreover , providing a supportive environment in order to change the food habits of the students and their parents among the wide range of the population require a sustainable and continuous inter - sector collaboration . \\n although in a limited number of schools , in our study , interventions and advocacy program was successful , expanding this model to another areas around the country depends on convincing the policy makers at national level . in this \\n regard , advocacy team should prepare evidenced based profile and transitional planning to convince the policy makers for improving the rule and regulation of nffp . \\n the same as this study in other studies have also emphasized that there must be efforts to strengthen the capacity within the schools to deal with the nutritional problems either overweight , obesity or malnutrition by using of educational and nutritional intervention . \\n assessing the dietary adherence is very important in nutrition intervention among population . as this population was children and adolescents we had a limitation in the blood sample collection to assess the subject 's dietary adherence . \\n furthermore , this intervention was only focused on the intake of snack in school time and we did not have comprehensive information on the dietary intake of children and adolescents after school all over the day . \\n the investigators propose further investigation in different areas of the country based on socio - cultural differences in order to make necessary modification and adapt this model to other areas . \\n regarding the nutritional needs of the school age children , provision of a good platform for implementing and expanding this efficient model to the whole country based upon the socio - economic situation of each region is advisable to the moh and the moe . \\n community nutrition intervention based on the advocacy process model is effective on reducing the prevalence of underweight specifically among female school aged children .<|eot_id|><|start_header_id|>assistant<|end_header_id|>\\n\\nbackground : the present study was carried out to assess the effects of community nutrition intervention based on advocacy approach on malnutrition status among school - aged children in shiraz , iran.materials and methods : this case - control nutritional intervention has been done between 2008 and 2009 on 2897 primary and secondary school boys and girls ( 7 - 13 years old ) based on advocacy approach in shiraz , iran . \\n the project provided nutritious snacks in public schools over a 2-year period along with advocacy oriented actions in order to implement and promote nutritional intervention . for evaluation of effectiveness of the intervention growth monitoring indices of pre- and post - intervention were statistically compared.results:the frequency of subjects with body mass index lower than 5% decreased significantly after intervention among girls ( p = 0.02 ) . \\n however , there were no significant changes among boys or total population . \\n the mean of all anthropometric indices changed significantly after intervention both among girls and boys as well as in total population . \\n the pre- and post - test education assessment in both groups showed that the student 's average knowledge score has been significantly increased from 12.5  3.2 to 16.8  4.3 ( p < 0.0001).conclusion : this study demonstrates the potential success and scalability of school feeding programs in iran . \\n community nutrition intervention based on the advocacy process model is effective on reducing the prevalence of underweight specifically among female school aged children .<|eot_id|>\"}"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["dataset[0]"]}, {"cell_type": "code", "execution_count": 25, "id": "a75f14c3-2969-453f-b0f6-7a53f5e803f2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Unsloth: We found double BOS tokens - we shall remove one automatically.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Unsloth: Tokenizing [\"text\"] (num_proc=2): 100%|██████████| 119924/119924 [05:37<00:00, 355.33 examples/s]\n"]}], "source": ["trainer = SFTT<PERSON>er(\n", "    model=model,\n", "    tokenizer=tokenizer,\n", "    train_dataset=dataset,\n", "    dataset_text_field=\"text\",  \n", "    max_seq_length=max_seq_length,\n", "    data_collator=DataCollatorForLanguageModeling(tokenizer=tokenizer, mlm=False),\n", "    dataset_num_proc=2,\n", "    packing=False,\n", "    args=TrainingArguments(\n", "        per_device_train_batch_size=4,\n", "        gradient_accumulation_steps=2,\n", "        warmup_steps=5,\n", "        max_steps=12,\n", "        learning_rate=2e-4,\n", "        fp16=not torch.cuda.is_bf16_supported(),\n", "        bf16=torch.cuda.is_bf16_supported(),\n", "        logging_steps=1,\n", "        optim=\"adamw_8bit\",\n", "        weight_decay=0.01,\n", "        lr_scheduler_type=\"linear\",\n", "        seed=3407,\n", "        output_dir=\"outputs\",\n", "        report_to=\"none\",\n", "    ),\n", ")"]}, {"cell_type": "code", "execution_count": 26, "id": "5e49e101", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Map (num_proc=16): 100%|██████████| 119924/119924 [00:13<00:00, 9205.37 examples/s] \n"]}], "source": ["trainer = train_on_responses_only(\n", "    trainer,\n", "    instruction_part=\"<|start_header_id|>user<|end_header_id|>\\n\\n\",\n", "    response_part=\"<|start_header_id|>assistant<|end_header_id|>\\n\\n\",\n", "    force_match=True,\n", ")"]}, {"cell_type": "code", "execution_count": 27, "id": "094a39fd-e8ec-462a-9038-95fc0a19b3a6", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["==((====))==  Unsloth - 2x faster free finetuning | Num GPUs used = 1\n", "   \\\\   /|    Num examples = 119,924 | Num Epochs = 1 | Total steps = 12\n", "O^O/ \\_/ \\    Batch size per device = 4 | Gradient accumulation steps = 2\n", "\\        /    Data Parallel GPUs = 1 | Total batch size (4 x 2 x 1) = 8\n", " \"-____-\"     Trainable parameters = 41,943,040/8,000,000,000 (0.52% trained)\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='12' max='12' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [12/12 04:06, Epoch 0/1]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Step</th>\n", "      <th>Training Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>2.087400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>0.630400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>0.313600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>0.455300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>0.283100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>0.074600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>13.057300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>0.003700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>0.006000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>0.002200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>0.002400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>12</td>\n", "      <td>0.001000</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["TrainOutput(global_step=12, training_loss=1.4097600713527452, metrics={'train_runtime': 269.0928, 'train_samples_per_second': 0.357, 'train_steps_per_second': 0.045, 'total_flos': 8902647402725376.0, 'train_loss': 1.4097600713527452})"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["# Train\n", "trainer.train()"]}, {"cell_type": "code", "execution_count": 29, "id": "347ece87", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The The\n"]}], "source": ["tokenizer = get_chat_template(\n", "    tokenizer,\n", "    chat_template = \"llama-3.1\",\n", ")\n", "\n", "FastLanguageModel.for_inference(model) # Enable native 2x faster inference\n", "\n", "messages = [\n", "    {\"role\": \"user\", \"content\": \"\"\"\n", "Multi-document summarization (MDS) is an effective tool for information aggregation...\n", "NLP based Machine Learning Approaches for Text \n", "SummarizationText summarization, Abstractive and extractive summaries, Query-based summarization, Structured-based and semantic-based approaches, Evaluation metrics (ROGUE score and TF_IDF score), Future directions (GANs and transfer learning),Data abundance and information overloadDue to the plethora of data available today, text \n", "summarization has become very essential to gain just the right \n", "amount of information from huge texts. We see long articles in \n", "news websites, blogs, customers’ review websites, and so on. This \n", "review paper presents various approaches to generate summary \n", "of huge texts. Various papers have been studied for different \n", "methods that have been used so far for text summarization. \n", "Mostly, the methods described in this paper produce Abstractive \n", "(ABS) or Extractive (EXT) summaries of text documents. Query\u0002based summarization techniques are also discussed. The paper \n", "mostly discusses about the structured based and semantic based \n", "approaches for summarization of the text documents. Various \n", "datasets were used to test the summaries produced by these \n", "models, such as the CNN corpus, DUC2000, single and multiple \n", "text documents etc. We have studied these methods and also the \n", "tendencies, achievements, past work and future scope of them in \n", "text summarization as well as other fieldsWe have seen that due to abundant availability of data, text \n", "summarization has a very vital role in saving user’s time, as \n", "well as resources. Text summarization is indeed an important \n", "tool for today. We have seen the use of various algorithms and \n", "methods for this purpose. These methods, in individual and \n", "together give different types of summaries. Their accuracy \n", "score can be compared to find the better and more concise \n", "summaries. For this purpose, ROGUE score has been used \n", "more frequently. Similarly, in some cases TF_IDF scores have \n", "been used too. \n", "The summaries generated using these methods are not \n", "always up to the mark. Sometimes, it’s also irrelevant to the \n", "original document. Therefore, this topic is ongoing and people \n", "have done various works on this. There isn’t any specific \n", "model that generates best summaries. So, for future, the models \n", "discussed can be modified for more accurate summaries. For \n", "e.g., we could use GAN’s and transfer learning. For future, this \n", "can give a way to develop and enhance further ideas for text \n", "summarization. @STOM © Word Vector Embedding kenearest neighbor algorithm © Differential Evolution Algorithm Newtonian Method Artificial Bee Colony Human Learning Algorithm\n", "\"\"\"},\n", "]\n", "\n", "inputs = tokenizer.apply_chat_template(\n", "    messages,\n", "    tokenize = True,\n", "    add_generation_prompt = True, # Must add for generation\n", "    return_tensors = \"pt\",\n", ").to(\"cuda\")\n", "\n", "from transformers import TextStreamer\n", "text_streamer = TextStreamer(tokenizer, skip_prompt = True)\n", "_ = model.generate(input_ids = inputs, streamer = text_streamer, max_new_tokens = 128,\n", "                   use_cache = True, temperature = 1.5, min_p = 0.1)"]}, {"cell_type": "code", "execution_count": null, "id": "d21b8cc3", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "unsloth_env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}