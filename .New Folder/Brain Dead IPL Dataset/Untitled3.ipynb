{"cells": [{"cell_type": "code", "execution_count": 1, "id": "291567a5-10d7-433f-85fe-e346e59ec314", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n"]}, {"cell_type": "code", "execution_count": 3, "id": "ab1ae109-6d2e-4437-b10e-04d46bd50c75", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>article</th>\n", "      <th>abstract</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>a recent systematic analysis showed that in 20...</td>\n", "      <td>background : the present study was carried out...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                             article  \\\n", "0  a recent systematic analysis showed that in 20...   \n", "\n", "                                            abstract  \n", "0  background : the present study was carried out...  "]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["data = pd.read_csv('train.csv')\n", "data.head(1)"]}, {"cell_type": "code", "execution_count": 9, "id": "5f611853-be4c-4284-89be-896218650524", "metadata": {}, "outputs": [{"data": {"text/plain": ["article     2692\n", "abstract       0\n", "dtype: int64"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["data.isnull().sum()"]}, {"cell_type": "code", "execution_count": 11, "id": "17e795e1-c1ef-43e4-a86b-b2844fbba613", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 119924 entries, 0 to 119923\n", "Data columns (total 2 columns):\n", " #   Column    Non-Null Count   Dtype \n", "---  ------    --------------   ----- \n", " 0   article   117232 non-null  object\n", " 1   abstract  119924 non-null  object\n", "dtypes: object(2)\n", "memory usage: 1.8+ MB\n"]}], "source": ["data.info()"]}, {"cell_type": "code", "execution_count": 15, "id": "5460e825-0db6-4431-8350-9f3c2b6a6208", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Statistics of Abstract Lengths (in words):\n"]}, {"data": {"text/plain": ["count    119924.000000\n", "mean        202.235407\n", "std          78.226149\n", "min          42.000000\n", "25%         142.000000\n", "50%         208.000000\n", "75%         262.000000\n", "max         391.000000\n", "Name: abstract_length, dtype: float64"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["data['abstract_length'] = data['abstract'].apply(lambda x: len(str(x).split()))\n", "print(\"\\nStatistics of Abstract Lengths (in words):\")\n", "data['abstract_length'].describe()"]}, {"cell_type": "code", "execution_count": 17, "id": "8deab772-27c9-449a-b117-26aec52c749d", "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAA90AAAJOCAYAAACqS2TfAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjguNCwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8fJSN1AAAACXBIWXMAAA9hAAAPYQGoP6dpAACsU0lEQVR4nOzdd3hUZcLG4d9k0kMSEtIhhCK9dwIiIF2KgopKLyqKq6Kga9l1cVdhxS4qWBClqwgooghIEwi9CBh6CSEESCEhvZ3vj2zmM9JCSHJSnvu65tLMvHPOM8MQ8uS85z0WwzAMRERERERERKTI2ZkdQERERERERKS8UukWERERERERKSYq3SIiIiIiIiLFRKVbREREREREpJiodIuIiIiIiIgUE5VuERERERERkWKi0i0iIiIiIiJSTFS6RURERERERIqJSreIiIiIiIhIMVHpFhEpYl9++SUWi8V2c3Z2JiAggK5duzJ16lQuXLhwxXMmT56MxWK5qf2kpKQwefJk1q9ff1PPu9q+atSoQb9+/W5qOzeyYMEC3nvvvas+ZrFYmDx5cpHur6j9+uuvtG7dGjc3NywWC8uWLbvhc/bv34/FYsHBwYFz585ddUxRvtdbtmxh8uTJXLp0qUi2Vxg//fTTTf1Zjho1ikqVKhVfoFt0vfe0OP6eXM2cOXPw9fXl8uXLADRu3JgGDRpcMW7p0qVYLBZCQ0OveGzu3LlYLBZ++OGHYs3apUsXunTpUqCx6enpfPjhh9x+++14eXnh6OhI1apVGTx4MBs2bCjWnAUVFRXF5MmT2bt37xWP/fOf/6Rly5bk5OSUfDARKdNUukVEisns2bMJCwtj9erVfPTRRzRv3pw33niDBg0asGbNmnxjH374YcLCwm5q+ykpKbz66qs3XboLs6/CuF7pDgsL4+GHHy72DIVlGAaDBw/GwcGBH374gbCwMDp37nzD533++ecAZGVlMWfOnOKOyZYtW3j11VdNL92vvvqqafsvama/pykpKbz00kv8/e9/x93dHYCuXbty6NAhoqOj841dv349bm5u7Ny501bQ//yYnZ0dd9xxR4llv56YmBg6duzIs88+S+PGjfnyyy/59ddfefvtt7FarXTr1o19+/aZHZOoqCheffXVq5buSZMmcfLkSb766quSDyYiZZpKt4hIMWncuDHt27enU6dO3Hvvvbz77rv8/vvvuLm5MWjQIM6fP28bW61aNdq3b1+seVJSUkpsXzfSvn17qlWrZmqG64mKiiIuLo6BAwfSrVs32rdvj5eX13Wfk56ezvz582nWrBlVq1bliy++KKG0BZeammp2BLmBr776itjY2Hy/lOratSvAFb9gW79+PQ8//DAWi4VNmzZd8ViLFi2oXLnyLeVJTU3FMIxb2gbAiBEj2LdvHytXrmTmzJncfffddOrUiQcffJCFCxcSFhZ2w79jZvP09GTYsGH897//LZL3REQqDpVuEZESVL16dd5++20uX77MJ598Yrv/alO+165dS5cuXahSpQouLi5Ur16de++9l5SUFE6dOoWvry8Ar776qm0q+6hRo/Jtb/fu3dx33314eXlRu3bta+4rz9KlS2natCnOzs7UqlWLDz74IN/jeVPnT506le/+9evXY7FYbKWgS5curFixgtOnT+ebap/natPLDxw4wN13342XlxfOzs40b978iiNKeftZuHAhL7/8MkFBQXh4eNC9e3cOHz587Tf+TzZt2kS3bt1wd3fH1dWVDh06sGLFCtvjkydPtv1C4O9//zsWi4UaNWrccLvLli2zlaWRI0dy5MiRK4rQn93ovc7JyeG1116jXr16uLi4ULlyZZo2bcr7779vy/ncc88BULNmTdt7nPdnkDcVesmSJbRo0QJnZ2fbEemPPvqIO+64Az8/P9zc3GjSpAnTpk0jMzPzipwrV66kW7dueHp64urqSoMGDZg6dSqQO1X8o48+Asj35/zXz0dhrFmzhm7duuHh4YGrqysdO3bk119/zTcm77N88OBBHnroITw9PfH392fMmDEkJCTkG3vp0iXGjh2Lt7c3lSpVom/fvpw4cSLfZ/FG7+mf35OWLVvi4uJC/fr1r/gFS0pKCpMmTaJmzZo4Ozvj7e1N69atWbhw4Q1f94wZM+jfv3++stylS5crcsTGxrJ//3769u1Lq1atWLdune2xM2fOcOLECVtZhxt/7uH//36vWrWKMWPG4Ovri6urK+np6RiGwbRp0wgJCcHZ2ZmWLVvy888/3/D1AOzatYuff/6ZsWPHcuedd151TJs2bahevbrt64J8Pyjo96O897Bx48bs2LGDTp064erqSq1atfjvf/9rmy6+fv162rRpA8Do0aNtf/5//l41fPhwjhw5ku/9FhG5EXuzA4iIVDR33XUXVquVjRs3XnPMqVOn6Nu3L506deKLL76gcuXKnD17lpUrV5KRkUFgYCArV66kd+/ejB071nZULK+I5xk0aBAPPvggjz32GMnJydfNtXfvXiZMmMDkyZMJCAhg/vz5PP3002RkZDBp0qSbeo0ff/wxjz76KMePH2fp0qU3HH/48GE6dOiAn58fH3zwAVWqVGHevHmMGjWK8+fP8/zzz+cb/9JLL9GxY0c+//xzEhMT+fvf/07//v0JDw/HarVecz8bNmygR48eNG3alFmzZuHk5MTHH39M//79WbhwIQ888AAPP/wwzZo1Y9CgQTz55JMMGTIEJyenG76GvO0NHTqUuLg4pk6dyqxZs7j99tuvGFuQ93ratGlMnjyZf/zjH9xxxx1kZmZy6NAh27Tnhx9+mLi4OKZPn86SJUsIDAwEoGHDhrb97N69m/DwcP7xj39Qs2ZN3NzcADh+/DhDhgyhZs2aODo6sm/fPl5//XUOHTqUr0DOmjWLRx55hM6dOzNz5kz8/Pw4cuQIBw4cAHLPcU1OTmbx4sX5TlnIy1JY8+bNY8SIEdx999189dVXODg48Mknn9CrVy9++eUXunXrlm/8vffeywMPPMDYsWPZv38/L774IoDtteTk5NC/f3927tzJ5MmTadmyJWFhYfTu3Tvfdgrynu7bt4+JEyfywgsv4O/vz+eff87YsWO57bbbbFO5n332WebOnctrr71GixYtSE5O5sCBA8TGxl73dUdGRrJ//34ef/zxfPd7e3vTtGnTfEVvw4YNWK1WOnToQOfOnVm7dq3tsbxxeaW7IJ/7PxszZgx9+/Zl7ty5JCcn4+DgwKuvvsqrr77K2LFjue+++zhz5gyPPPII2dnZ1KtX77qva9WqVQDcc8891x2X52a/HxRUdHQ0Q4cOZeLEifzrX/9i6dKlvPjiiwQFBTFixAhatmzJ7NmzGT16NP/4xz/o27cvQL5ZOa1ataJSpUqsWLHimr9AEBG5giEiIkVq9uzZBmDs2LHjmmP8/f2NBg0a2L7+17/+Zfz5W/LixYsNwNi7d+81t3Hx4kUDMP71r39d8Vje9l555ZVrPvZnISEhhsViuWJ/PXr0MDw8PIzk5OR8r+3kyZP5xq1bt84AjHXr1tnu69u3rxESEnLV7H/N/eCDDxpOTk5GREREvnF9+vQxXF1djUuXLuXbz1133ZVv3DfffGMARlhY2FX3l6d9+/aGn5+fcfnyZdt9WVlZRuPGjY1q1aoZOTk5hmEYxsmTJw3AePPNN6+7vTynTp0y7OzsjAcffNB2X+fOnQ03NzcjMTEx39iCvtf9+vUzmjdvft39vvnmm1f988jbj9VqNQ4fPnzdbWRnZxuZmZnGnDlzDKvVasTFxRmGYRiXL182PDw8jNtvv932vlzNE088ccXn6XpGjhxpuLm5XfPx5ORkw9vb2+jfv/8VOZs1a2a0bdvWdl/eZ3natGn5xo4fP95wdna25V6xYoUBGDNmzMg3burUqVd8Fm/0njo7OxunT5+23Zeammp4e3sb48aNs93XuHFj45577rn2m3ANX3/9tQEYW7duveKxCRMmGIARFRVlGIZhPPnkk0b79u0NwzCMn376ybBarUZCQoJhGIYxevRow2q12j57Bf3c5/39HjFiRL59x8fHG87OzsbAgQPz3b9582YDMDp37nzd1/XYY48ZgHHo0KECvQ8F/X5wM9+POnfubADGtm3b8o1t2LCh0atXL9vXO3bsMABj9uzZ18zXsWNHo127dgV6LSIihmEYml4uImIC4wbnAzZv3hxHR0ceffRRvvrqK06cOFGo/dx7770FHtuoUSOaNWuW774hQ4aQmJjI7t27C7X/glq7di3dunUjODg43/2jRo0iJSXlioXfBgwYkO/rpk2bAnD69Olr7iM5OZlt27Zx33335Vs922q1Mnz4cCIjIws8Rf2vZs+eTU5ODmPGjLHdN2bMGJKTk/n666+vGF+Q97pt27bs27eP8ePH88svv5CYmHjTuZo2bUrdunWvuH/Pnj0MGDCAKlWqYLVacXBwYMSIEWRnZ3PkyBEgd0GxxMRExo8ff9Mr69+KLVu2EBcXx8iRI8nKyrLdcnJy6N27Nzt27Lhi1sbVPg9paWm2KwXkrYw9ePDgfOMeeuihm87XvHnzfNOgnZ2dqVu3br7PXtu2bfn555954YUXWL9+fYHPpY+KigLAz8/visf+el73+vXrbauG582myJs9s379elq3bo27u3uhPvd//b4RFhZGWloaQ4cOzXd/hw4dCAkJKdBruxk3+/2goAICAmjbtm2++5o2bXrd7xtX4+fnx9mzZwuVQUQqJpVuEZESlpycTGxsLEFBQdccU7t2bdasWYOfnx9PPPEEtWvXpnbt2rbzeQvqZqb5BgQEXPO+G02LvVWxsbFXzZr3Hv11/1WqVMn3dd707+uVm/j4eAzDuKn9FEROTg5ffvklQUFBtGrVikuXLnHp0iW6d++Om5sbs2bNuuI5BXmvX3zxRd566y22bt1Knz59qFKlCt26dWPnzp0Fzna11xoREUGnTp04e/Ys77//Pr/99hs7duywnZud9x5evHgRoMQXvMtbYPC+++7DwcEh3+2NN97AMAzi4uLyPedGn4fY2Fjs7e3x9vbON87f3/+m8/11X3n7+/Nn74MPPuDvf/87y5Yto2vXrnh7e3PPPfdw9OjR6247bxvOzs5XPNa5c2fs7OxYt24dsbGxHDhwwLaivru7Oy1atGD9+vVERERw8uRJW0kvzOf+r2PzHr/e5/Z68n5JcfLkyRuOzdtfUf89hYL92RWEs7OzFiUUkZui0i0iUsJWrFhBdnb2Da9t26lTJ5YvX05CQgJbt24lNDSUCRMmsGjRogLv62aOUP71ckR/vi/vh9W8MpCenp5vXExMTIH3czVVqlS56nWt8478+fj43NL2Aby8vLCzsyvy/axZs4bTp08TFRVFlSpV8PLywsvLi6pVq5KcnMzWrVv5448/8j2nIO+1vb09zz77LLt37yYuLo6FCxdy5swZevXqZVuJ/kau9ue/bNkykpOTWbJkCcOGDeP222+ndevWODo65huXtz5AZGRkgfZVVPL+DKZPn86OHTuuervZslylShWysrKuKOtX+3MoCm5ubrz66qu2y3zNmDGDrVu30r9//+s+L++1/zUn5K6cnVes8y4H1rFjR9vjnTt3Zt26dVecz12Yz/1fPzd5n8nrfW6vp1evXgAFutZ93v4Kkre4vh/dSFxcXJF8TxKRikOlW0SkBEVERDBp0iQ8PT0ZN25cgZ5jtVpp166d7Uhk3vTjghzdvRkHDx684jq5CxYswN3dnZYtWwLYVvH+/fff84374YcfrtjezRxB6tatG2vXrrX9UJ1nzpw5uLq6Fsklztzc3GjXrh1LlizJlysnJ4d58+ZRrVq1q07FvpFZs2ZhZ2fHsmXLbKUn7zZ37lyAK1a3Lsh7/WeVK1fmvvvu44knniAuLs62WnNhPgN5herPi8MZhsFnn32Wb1yHDh3w9PRk5syZ1z0doqg/hx07dqRy5cr88ccftG7d+qq3v/6C4Ebyjgj/dar/1X6BVdSvx9/fn1GjRvHQQw9x+PDh6/7CpH79+kDuQndX07VrV44ePcqCBQto1aqV7TrekPsa9+7dy7Jly3BwcLAV8qL43Ldv3x5nZ2fmz5+f7/4tW7YUaGp2y5Yt6dOnD7Nmzcq34Nuf7dy5k4iICKDg3w9u5vtRQRXkz//EiRP5FtcTEbkRrV4uIlJMDhw4YDsf9cKFC/z222/Mnj0bq9XK0qVLr1hp/M9mzpzJ2rVr6du3L9WrVyctLc1W3Lp37w7kTikNCQnh+++/p1u3bnh7e+Pj41Ogy1tdTVBQEAMGDGDy5MkEBgYyb948Vq9ezRtvvIGrqyuQe1mfevXqMWnSJLKysvDy8mLp0qVXvTRWkyZNWLJkCTNmzKBVq1bY2dnRunXrq+77X//6Fz/++CNdu3bllVdewdvbm/nz57NixQqmTZuGp6dnoV7TX02dOpUePXrQtWtXJk2ahKOjIx9//DEHDhxg4cKFN33ucmxsLN9//z29evXi7rvvvuqYd999lzlz5jB16lQcHByAgr3X/fv3p3HjxrRu3RpfX19Onz7Ne++9R0hICHXq1AFy32OA999/n5EjR+Lg4EC9evXylbG/6tGjB46Ojjz00EM8//zzpKWlMWPGDOLj4/ONq1SpEm+//TYPP/ww3bt355FHHsHf359jx46xb98+Pvzww3wZ3njjDfr06YPVaqVp06bXLcbZ2dksXrz4ivvd3Nzo06cP06dPZ+TIkcTFxXHffffh5+fHxYsX2bdvHxcvXmTGjBnX3PbV9O7dm44dOzJx4kQSExNp1aoVYWFhzJkzBwA7u/8/BlGY9/Sv2rVrR79+/WjatCleXl6Eh4czd+5cQkNDbX++13qei4sLW7duveI8dcgt3W+99RZLly694ooCnTp1AuD777+nQ4cOtpXq4dY/915eXkyaNInXXnuNhx9+mPvvv58zZ87YVt8viDlz5tC7d2/69OnDmDFj6NOnD15eXpw7d47ly5ezcOFCdu3aRfXq1Qv8/eBmvh8VVO3atXFxcWH+/Pk0aNCASpUqERQUlG9q+9GjR3nyyScLvQ8RqYDMXMVNRKQ8yltRN+/m6Oho+Pn5GZ07dzamTJliXLhw4Yrn/HVF8bCwMGPgwIFGSEiI4eTkZFSpUsXo3Lmz8cMPP+R73po1a4wWLVoYTk5OBmCMHDky3/YuXrx4w30ZRu6qzH379jUWL15sNGrUyHB0dDRq1KhhvPPOO1c8/8iRI0bPnj0NDw8Pw9fX13jyySdtq0P/ebXguLg447777jMqV65sWCyWfPvkKquu79+/3+jfv7/h6elpODo6Gs2aNbtiBeG8VYm//fbbfPfnrTZ+vRWH8/z222/GnXfeabi5uRkuLi5G+/btjeXLl191ezdavfy9994zAGPZsmXXHDNz5kwDML777jvDMAr+Xr/99ttGhw4dDB8fH8PR0dGoXr26MXbsWOPUqVP5xr344otGUFCQYWdnl+/PIG8/V7N8+XKjWbNmhrOzs1G1alXjueeeM37++ecr/gwNI3dl7LyV2F1dXY2GDRsab7zxhu3x9PR04+GHHzZ8fX1tf85XW/k7z8iRI/P9/fjz7c+r3W/YsMHo27ev4e3tbTg4OBhVq1Y1+vbtm+/P/lqf86utah0XF2eMHj3aqFy5suHq6mr06NHD2Lp1qwEY77///i29p507d863gvcLL7xgtG7d2vDy8jKcnJyMWrVqGc8884wRExNzzfclz/Dhw42GDRte9bHExETD3t7eAIwff/zxisebN29uAMbLL798xWMF+dxf78oLOTk5xtSpU43g4GDD0dHRaNq0qbF8+fIrXvv1pKamGh988IERGhpqeHh4GPb29kZQUJAxaNAgY8WKFfnGFuT7gWEU/PtR586djUaNGl3x/JEjR15xlYWFCxca9evXNxwcHK74XjVr1izDwcHBiI6OLtBrFhExDMOwGMYNltAVERERKYcWLFjA0KFD2bx5Mx06dDA7DpA7zbpNmzZs3bqVdu3amR1H/qJTp05Ur179iqn2IiLXo9ItIiIi5d7ChQs5e/YsTZo0wc7Ojq1bt/Lmm2/SokUL2yXFSosHHniA5ORkfvzxR7OjyJ9s3LiRnj178scff1CrVi2z44hIGaKF1ERERKTcc3d3Z9GiRTzwwAPcddddfPbZZ4waNYrly5ebHe0Kb7/9Nm3atOHy5ctmR5E/iY2NZc6cOSrcInLTdKRbREREREREpJjoSLeIiIiIiIhIMVHpFhERERERESkmKt0iIiIiIiIixcTe7ADlSU5ODlFRUbi7u2OxWMyOIyIiIiIiIsXEMAwuX75MUFAQdnbXPp6t0l2EoqKiCA4ONjuGiIiIiIiIlJAzZ85QrVq1az6u0l2E3N3dgdw33cPDw+Q0IiIiIiIiUlwSExMJDg629cBrUekuQnlTyj08PFS6RUREREREKoAbnVqshdREREREREREiolKt4iIiIiIiEgxUekWERERERERKSYq3SIiIiIiIiLFRKVbREREREREpJiodIuIiIiIiIgUE5VuERERERERkWKi0i0iIiIiIiJSTFS6RURERERERIqJSreIiIiIiIhIMVHpFhERERERESkmKt0iIiIiIiIixUSlW0RERERERKSYqHSLiIiIiIiIFBOVbhEREREREZFiotItIiIiIiIiUkxUukVERERERESKiUq3iIiIiIiISDFR6RYREREREREpJirdIiIiIiIiIsXE3uwAIiIiIkXBMAx2n9vN6YTTXEi+wIXkC8SlxtEqsBX3N7ofZ3tnsyOKiEgFpNItIiIiZd7hmMP87ee/sebEmqs+/swvzzCmxRjGtRpHbe/aJZxOREQqMothGIbZIcqLxMREPD09SUhIwMPDw+w4IiIi5V5yRjKv//Y6b215i8ycTJysTrQMbIl/JX/8XP1wcXBhSfgSziSesT1nVPNRzOg7Q0e+RUTklhS0/6l0FyGVbhERkZKzK2oXg74ZRERCBAB96/Tlgz4fUMurVr5xWTlZ/HT0J2bsnMHKYysBuL367Sx7YBlVXKuUeG4RESkfVLpNoNItIiJSMo7FHSN0VigxKTGEeIbwfu/3GVBvABaL5brP+/XErwz6ZhCJ6YnUrVKXn4f+fEVJFxERKYiC9j+tXi4iIiJlyoXkC/Se15uYlBhaBbZi/+P7ubv+3Tcs3ADdanVj85jNBHsEcyT2CO0/b8+2yG0lkFpERCoqlW4REREpM5Izkum3oB/H449Ts3JNVgxZgbuT+01to7FfY7Y+vJXmAc25mHKRnvN6cjT2aDElFhGRik6lW0RERMqErJwsBi8ezI6oHVRxqcLKYSvxr+RfqG0FuQexcdRGQquFkpieyKBvBpGckVzEiUVERFS6RUREpIx4+deX+enoT7jYu/DjkB+pW6XuLW3P3cmdxYMX4+/mz4ELB3j0x0fRUjciIlLUVLpFRESk1Pv9/O+8HfY2AHMHzqV9tfZFst0g9yC+uf8brBYrC/Yv4MPtHxbJdkVERPKodIuIiEiplmPk8PiKx8k2shnUYBD3Nry3SLd/R8gdvNXzLQCeXfUsmyI2Fen2RUSkYrM3O4CIiIjI9czeM5stZ7bg5uDGe73eK/R2IiIiiImJuepjnRw60TOoJ6uiVjFwwUC+7fItHo4le/lPHx8fqlevXqL7FBGR4qfSLSIiIqVWTEoMz695HoB/d/03wZ7BhdpOREQE9RvUJzUl9dqDHIFHIcYnhq6vdYUVhdpVobm4unAo/JCKt4hIOaPSLSIiIqXW31f/nbjUOJr6N+Wpdk8VejsxMTGkpqQy8KWB+Ib4XnNcVHoUP8b9CG3g7t534+9YuNXRb9bF0xdZOmUpMTExKt0iIuWMSreIiIiUSpsiNvHF3i8AmNF3BvZ2t/5ji2+IL4F1A6/5eCCBnDl0hn3n97E1bSuPNnoUO4uWwBERkcLTvyIiIiJS6hiGwaRVkwB4uMXDdAjuUGL77lGrBy72LpxPPs+2yG0ltl8RESmfVLpFRESk1Fl3ah3bzm7D2d6Z1+58rUT37eboRvda3W05EtISSnT/IiJSvqh0i4iISKkz5bcpQO5Rbv9KJXNe9Z+1CGhBsEcwmTmZrDy2ssT3LyIi5YdKt4iIiJQq2yK38evJX7G3s2dSh0mmZLBYLPSr2w87ix2HYg9xIv6EKTlERKTsU+kWERGRUmXqpqkADGs6jJDKIabl8HPzo3VQawB+PfkrhmGYlkVERMourV4uIiIitywiIoKYmJhb3s6xxGN8f/h7LFjoV7kfu3fvLoJ0EB4eXqjn3VH9DvZG7yXqchThMeE09G1YJHlERKTiUOkWERGRWxIREUH9BvVJTUm99Y0NApqCcdDgvsn33fr2/iIpKemmxrs5uhFaLZQNpzew9uRa6vvU1yXERETkpqh0i4iIyC2JiYkhNSWVgS8NxDfEt9DbScxK5OuLX2NgMKjzIHy6+xRZxqPbjrLui3WkpaXd9HNDq4WyI2oHsamx7Dm3h1ZBrYosl4iIlH8q3SIiIlIkfEN8CawbWOjn7zqyCwOD2l61adKoSREmg5iIwk99d7J3olP1Tvxy/Bc2nN5AU/+mOFgdijCdiIiUZ5ofJSIiIqZLz0rn9/O/A3B79dtNTnOl1kGt8XTy5HLGZbad3WZ2HBERKUNUukVERMR0+y/sJzMnEx9XH0I8zVux/Frs7ezpWqMrAJvPbCY1swjOXxcRkQpBpVtERERMt/tc7irlLQNbYrFYTE5zdU38m+Dn5kdaVhrbz243O46IiJQRKt0iIiJiqqjLUZxLOofVYqWZfzOz41yTncWOO6rfAcDWs1tJz0o3OZGIiJQFKt0iIiJiqryj3A18G+Dq4Gpymutr4NuAKi5VSMtKY0fUDrPjiIhIGaDSLSIiIqbJyM5g/4X9ALQMaGlymhuzs9jRqXonAMIiw8jIzjA5kYiIlHYq3SIiImKaAxcOkJGdgbeLNzUq1zA7ToE08W9CZefKpGSm2I7Si4iIXItKt4iIiJjGtoBaQOldQO2v7Cx2tsuabTmzhaycLJMTiYhIaabSLSIiIqaITorm7OWz2FnsaB7Q3Ow4N6WZfzM8nDy4nHGZPdF7zI4jIiKlmEq3iIiImCLvKHf9KvVxc3QzOc3Nsbezp2NwRwA2R2wmOyfb5EQiIlJaqXSLiIhIicvKyfr/BdQCS/8CalfTIqAFbg5uJKQn2F6LiIjIX6l0i4iISIk7HnectKw0KjlWoqZXTbPjFIqD1YH21doDued2G4ZhciIRESmNTC3dGzdupH///gQFBWGxWFi2bFm+xw3DYPLkyQQFBeHi4kKXLl04ePBgvjHp6ek8+eST+Pj44ObmxoABA4iMjMw3Jj4+nuHDh+Pp6YmnpyfDhw/n0qVL+cZERETQv39/3Nzc8PHx4amnniIjQ5cBERERKQ4HLhwAoJFvI+wsZfcYQOug1jhaHbmYcpFjccfMjiMiIqWQqf/KJScn06xZMz788MOrPj5t2jTeeecdPvzwQ3bs2EFAQAA9evTg8uXLtjETJkxg6dKlLFq0iE2bNpGUlES/fv3Izv7/c6uGDBnC3r17WblyJStXrmTv3r0MHz7c9nh2djZ9+/YlOTmZTZs2sWjRIr777jsmTpxYfC9eRESkgsrIzuBw7GEAmvg1MTnNrXG2d7ZNj98SucXkNCIiUhrZm7nzPn360KdPn6s+ZhgG7733Hi+//DKDBg0C4KuvvsLf358FCxYwbtw4EhISmDVrFnPnzqV79+4AzJs3j+DgYNasWUOvXr0IDw9n5cqVbN26lXbt2gHw2WefERoayuHDh6lXrx6rVq3ijz/+4MyZMwQFBQHw9ttvM2rUKF5//XU8PDxK4N0QERGpGA7HHiYzJxMvZy+C3IPMjnPL2ldtz7bIbZy6dIqoy1Hl4jWJiEjRKbXzuU6ePEl0dDQ9e/a03efk5ETnzp3ZsiX3N8m7du0iMzMz35igoCAaN25sGxMWFoanp6etcAO0b98eT0/PfGMaN25sK9wAvXr1Ij09nV27dl0zY3p6OomJifluIiIicn15U8sb+zUuM9fmvh5PZ08a+zUGIOxMmMlpRESktCm1pTs6OhoAf3//fPf7+/vbHouOjsbR0REvL6/rjvHz87ti+35+fvnG/HU/Xl5eODo62sZczdSpU23niXt6ehIcHHyTr1JERKRiSc1MtZ37nFdUy4MOwR0AOHjxIJfSLpkbRkRESpVSW7rz/PU34IZh3PC34n8dc7XxhRnzVy+++CIJCQm225kzZ66bS0REpKILjwknx8jBz80PP7crfyleVgVUCqCWVy0MDMIidbRbRET+X6kt3QEBAQBXHGm+cOGC7ah0QEAAGRkZxMfHX3fM+fPnr9j+xYsX8435637i4+PJzMy84gj4nzk5OeHh4ZHvJiIiItf256nl5U2HarlHu/ec20NqZqrJaUREpLQotaW7Zs2aBAQEsHr1att9GRkZbNiwgQ4dcv9Ra9WqFQ4ODvnGnDt3jgMHDtjGhIaGkpCQwPbt221jtm3bRkJCQr4xBw4c4Ny5c7Yxq1atwsnJiVatWhXr6xQREakokjKSOHXpFACNfctf6a7lVQt/N38yczLZGbXT7DgiIlJKmFq6k5KS2Lt3L3v37gVyF0/bu3cvERERWCwWJkyYwJQpU1i6dCkHDhxg1KhRuLq6MmTIEAA8PT0ZO3YsEydO5Ndff2XPnj0MGzaMJk2a2FYzb9CgAb179+aRRx5h69atbN26lUceeYR+/fpRr149AHr27EnDhg0ZPnw4e/bs4ddff2XSpEk88sgjOnotIiJSRA5eOIiBQTX3ani5eN34CWWMxWKxndu97ew2snKyTE4kIiKlgamXDNu5cyddu3a1ff3ss88CMHLkSL788kuef/55UlNTGT9+PPHx8bRr145Vq1bh7u5ue867776Lvb09gwcPJjU1lW7duvHll19itVptY+bPn89TTz1lW+V8wIAB+a4NbrVaWbFiBePHj6djx464uLgwZMgQ3nrrreJ+C0RERCqM8jy1PE8j30b8evJXEtMT+f3877ZreIuISMVlaunu0qULhmFc83GLxcLkyZOZPHnyNcc4Ozszffp0pk+ffs0x3t7ezJs377pZqlevzo8//njDzCIiInLzEtISiLwcCUBD34Ympyk+Vjsr7aq2Y/WJ1YRFhtEioEW5uCyaiIgUXqk9p1tERETKj0OxhwCo7lEddyf3G4wu21oFtsLJ6kRMSgxHYo+YHUdEREym0i0iIiLF7tDF3NJd36e+yUmKn5O9E62Cchdi3RK5xeQ0IiJiNpVuERERKVYpmSmcTjgNVIzSDdCuajvsLHZEJEQQmRhpdhwRETGRSreIiIgUq8MxhzEwCKgUUC5XLb8aDycPmvg1ASDsTJjJaURExEwq3SIiIlKsDsVUnKnlf5Z3+bDwmHDiUuNMTiMiImZR6RYREZFik56VzvH44wA08GlgcpqS5efmx23et2FgEBapo90iIhWVSreIiIgUm2Nxx8g2svF28cbX1dfsOCWuQ7Xco917o/eSnJFschoRETGDSreIiIgUmz9PLa+I16uuUbkGQe5BZOVksSNqh9lxRETEBCrdIiIiUiyycrI4Epd7neqKNrU8j8VisR3t3n52O5nZmSYnEhGRkqbSLSIiIsXiZPxJMrIzqORYiaruVc2OY5oGvg3wcvYiNSuVPdF7zI4jIiIlTKVbREREikV4TDhQcaeW57Gz2NG+WnsAwiLDyDFyTE4kIiIlSaVbREREilyOkcPh2MNAxZ1a/mctAlrgYu/CpbRLhF8MNzuOiIiUIJVuERERKXKRiZGkZKbgbO9MiGeI2XFM52B1oG3VtgBsPrMZwzBMTiQiIiVFpVtERESKXN5R7jredbDaWU1OUzq0CWqDvZ0955LOcerSKbPjiIhICVHpFhERkSJ3JDZ31fK6VeqanKT0cHN0o3lAcwC2nNlibhgRESkxKt0iIiJSpGJTYolJicHOYsdt3reZHadUCa0WigULx+KPcT7pvNlxRESkBKh0i4iISJHKO8od4hmCs72zyWlKF28Xbxr6NgRgS6SOdouIVAQq3SIiIlKkNLX8+joEdwDgwIUDJKQlmJxGRESKm0q3iIiIFJnUzFROJ5wGoF6VeianKZ2C3IOoUbkGOUYOW89uNTuOiIgUM5VuERERKTLH4o5hYODr6ouXi5fZcUqtDtVyj3bvPrebtKw0k9OIiEhxUukWERGRIpN3qTBNLb++27xvw8/Nj4zsDHZG7TQ7joiIFCOVbhERESkS2UY2x+KOAZpafiMWi8V2tHvb2W1kG9kmJxIRkeKi0i0iIiJFIjojmvTsdFwdXKnqUdXsOKVeY7/GeDh5kJSRxNHUo2bHERGRYqLSLSIiIkXidFruAmp1q9TFzqIfMW7EamelXdV2AOxL2gcWkwOJiEix0L+IIiIiUiROp/9/6ZaCaRXYChd7FxKyE6CR2WlERKQ4qHSLiIjIrfOFy9mXsVqs1PaqbXaaMsPJ3on21drnfnEH5Bg55gYSEZEip9ItIiIit+5/66bV8qqFo9XR3CxlTNuqbXGwOIAfrI9eb3YcEREpYirdIiIicuv+V7o1tfzmOds709itMQCfH/kcwzBMTiQiIkVJpVtERERuSWx6LFTL/X+V7sJp4tYEMuBw4mF+Pvaz2XFERKQIqXSLiIjILdl0fhNYwMfeBw8nD7PjlEnOds6wI/f//7PxPzraLSJSjqh0i4iIyC3ZeH4jACHOISYnKePCwMnOia2RW1l7cq3ZaUREpIiodIuIiEihpWWlsfXiVkCl+5YlwcCQgQC8uuFVHe0WESknVLpFRESk0NaeXEtadhokQBX7KmbHKfNG1h6Jk9WJ3yJ+Y82JNWbHERGRIqDSLSIiIoX2w+Efcv/nCFgsFnPDlAN+Ln483vpxAF5e+7KOdouIlAMq3SIiIlIohmGw/Mjy3C8Om5ulPHnh9hdwdXBlR9SO/39/RUSkzFLpFhGRUutk/EneCXuH4UuH88+1/2TRgUXsP7+fjOwMs6MJsPvcbqIuR+FidYFTZqcpP/wr+fN0u6cB+Oe6f5Jj5JicSEREboW92QFERKR8i4iIICYmpsDjL6ZdZGnEUtadW8eRxCNXHeNg58C9IffyWL3HcHdwv6V8Pj4+VK9e/Za2UVHlTS0P9Q1lbZZW2y5KkzpM4qMdH/H7+d/55uA3PNj4QbMjiYhIIal0i4hIsYmIiKB+g/qkpqQW7AnBwANApf99nQOcJvcoqgfgB/hCpnMmi04uYtH+RbAa+B0o5KmvLq4uHAo/pOJdCHlTnzsFdGItKt1FydvFm0mhk3hl/Sv8a/2/uK/hfdjb6cc2EZGySN+9RUSk2MTExJCaksrAlwbiG+J73bFHUo6wMWEjOeTgZe9FU7emhDiH4FzVGTr8/zjDMDibcZbNCZtJqJQAA8F/sD+dK3emsn3lm8p38fRFlk5ZSkxMjEr3TTqTcIY90XuwYKGTXyez45RLT7d/mve3vc+R2CPM3TeX0S1Gmx1JREQKQaVbRESKnW+IL4F1A6/6WI6Rw68nfmVLwhYA6vvUZ2D9gThaHa+5vSCCaJnTkq2RW9lwegPnM8/z46UfGdZkGIHuV9+PFK0fj/wIQIfgDng5eZmcpnzycPLghdtf4LnVzzF5w2QeavIQzvbOZscSEZGbpIXURETENIZhsOzQMrZE5hbuTtU7Mbjh4OsW7jxWOysdq3fkb23/RpB7ECmZKXy17yvOJJwp7tgC/HAk93zu/nX7m5ykfBvfZjxV3asSkRDB+1vfNzuOiIgUgkq3iIiYZkfUDvZf2I+dxY5BDQZxZ807b/pazx5OHoxoOoLqntVJz05n7u9zORF/opgSC8Dl9MusPZl7DveAegNMTlO+uTq4MrXbVABe/+11LiRfMDmRiIjcLJVuERExRdTlKFYdXwVAj1o9aOLXpNDbcrJ3YliTYdT2qk1mTiYL9i/gSOzVVz6XW7f6xGoysjO4zfs26vvUNztOuTe06VBaBbbicsZlJq+fbHYcERG5SSrdIiJS4tKy0vj2j2/JNrKp71OfdlXb3fI2HawOPNj4Qer71CfbyGbxH4uJToougrTyV3mXCutft/9Nz0yQm2dnsePtnm8D8MmuT/jj4h8mJxIRkZuh0i0iIiXKMAx+OPwDl9IuUdm5MnfXu7vIipu9nT33N7yfWpVrkZmTyaIDi0jOSC6SbUuu7JxsVhxdAWhqeUnqXKMz99S/hxwjh+dWP2d2HBERuQkq3SIiUqK2R20nPCYcO4sd9zW4r8hXY7az2HFfw/vwdvEmIT2Bb/74huyc7CLdR0W2NXIrMSkxVHauTMfgjmbHqVCmdZ+GvZ09Px39idXHV5sdR0RECkilW0RESkxCWgJrTqwBoGetnlT1qFos+3FxcOHBRg/iZHUiIiGCn479hGEYxbKviiZvavldde7CwepgcpqKpU6VOvytzd8AmLhqIlk5WSYnEhGRglDpFhGRErP21FqycrKo7lGdtlXbFuu+fN18ubfBvQDsPrebHVE7inV/FcXyI8sBGFBXU8vN8M/O/8TbxZv9F/bzwbYPzI4jIiIFoNItIiIlIupyFL+f/x2AnrV7lsgCXHWq1KF7re4ArDq+Sgur3aJjcccIjwnH3s6e3rf1NjtOheTt4s207tMAeGXdK7ouvYhIGaDSLSIixc4wDNvlwZr4NSm2aeVX06FaB+pVqUe2kc134d+RmZ1ZYvsub5Yfzj3K3TmkM57OnianqbhGtxhNx+COJGcm8/TKp82OIyIiN6DSLSIixe50+mlOJ5zG3s6ebjW7lei+LRYLA+oNoJJjJWJSYlh1YlWJ7r88+eFI7vncWrXcXHYWO2b2m4m9nT1LDy21/TJERERKJ5VuEREpXlbYlrgNgPbV2ptyhNTVwZV76t0DwM6onRyOOVziGcq6+NR4fjv9G5B7fW4xV2O/xkwMnQjA337+my6NJyJSiql0i4hI8WoNCdkJuDm4cXvw7abFqO1dm/bV2gO5R2yTMpJMy1IW/XT0J7KNbBr5NqKmV02z4wjwzzv+SYhnCBEJEfx7w7/NjiMiIteg0i0iIsUmJSsFOuf+f5caXXCydzI1T7ea3fB38yclM4XvD32vy4jdhMXhiwEY1GCQyUkkj5ujGx/e9SEAb4e9zc6onSYnEhGRq1HpFhGRYrMsYhm4gofVg5aBLc2Og72dPfc2uBd7O3uOxR/jcKqmmRfE5fTL/Hz0ZwDua3ifyWnkz/rV7cfgRoPJNrIZvnQ4KZkpZkcSEZG/UOkWEZFikZmdyfwT8wFoVqkZdpbS8U+Or5svXWp0AWBr4lbwMDdPWfDT0Z9Iz06njncdmvg1MTuO/MWMvjMIrBTIoZhDvLDmBbPjiIjIX5SOn4BERKTc+frg10SnRkMS1HGpY3acfEKrhVLVvSoZRgb0Q9PMbyBvavl9De8rkeury83xdvFm9t2zAZi+fbrt8nwiIlI6qHSLiEiRMwyDaZun5X6xFewt9uYG+gs7ix1317sbO+ygLqyIXGF2pFIrOSOZFUdy3x9NLS+9et3WiyfaPAHA6O9HE5caZ3IiERHJo9ItIiJFbuWxley/sB9XqyuU0rWdfN18aeXeCoC3Dr7FucvnTE5UOv187GdSs1KpWbkmLQJamB1HrmNaj2nUrVKXqMtRjF8xXjM4RERKCZVuEREpcm9sfgOAQSGDIM3kMNfRzK0ZRMHlzMs8vuJxlZSrWPyHppaXFa4OrswbOA+rxcrXB7/mk12fmB1JRERQ6RYRkSK2LXIbG05vwMHOgSG1hpgd57rsLHbwfe709+8Pf8+3f3xrdqRSJTUzlR+P/AjA/Q3vNzmNFESbqm14/c7XAXjq56fYHLHZ5EQiIqLSLSIiRWraltxzuYc2HYq/i7/JaQrgPIyuMxqAJ39+ktiUWJMDlR6/HP+F5MxkqntWp3VQa7PjSAE93/F57m94P5k5mdz37X1EXY4yO5KISIWm0i0iIkXmRPwJloYvBWBS6CST0xTcmNvG0MCnAReSLzBx1USz45QatqnlDTS1vCyxWCx8cfcXNPFrQnRSNPd+cy/pWelmxxIRqbBUukVEpMjM3DkTA4OetXvSyK+R2XEKzNHqyKwBs7Bg4at9X+mSS0B6Vjo/HP4B0KrlZVElx0osfWAplZ0rszVyK0/+/KTZkUREKiyVbhERKRJpWWl8secLANuli8qS0OBQ/tb2bwCM+3EcSRlJJicy16rjq7iccZmq7lVpV62d2XGkEGp712bhvQuxYOGz3Z/x303/NTuSiEiFpNItIiJF4tuD3xKbGkuwRzB96/Q1O06hTOk2heqe1Tl16RT/XPtPs+OYav7++UDuUW47i35cKKt639abt3u+DcCLv77Ixzs+NjmRiEjFo39FRUSkSMzYOQOAca3GYbWzmpymcCo5VuKTfrmXWXp/2/tsi9xmciJzXEq7xLJDywAY3nS4uWHklj0T+gz/6PQPAJ746Qnm7ptrciIRkYpFpVtERG7Z3ui9hEWGYW9nz9iWY82Oc0t639abYU2HYWDw8PKHycjOMDtSiVv8x2LSs9Np6NuQloEtzY4jReDfXf/NU22fAmD096NtCx6KiEjxU+kWEZFbNmNH7lHuexvcS0ClAJPT3Lp3e72Lj6sPBy4cqJDnwc7ZNweAEU1HaNXycsJisfBu73cZ3Xw02UY2D373IEvCl5gdS0SkQlDpFhGRW5KQlsC8/fMAGN9mvMlpioaPqw8f9P4AgNc2vsbBCwdNTlRyTsaf5LeI37BgYVjTYWbHkSJkZ7Hjs/6fcX/D+8nIzuC+b+7j3bB3MQzD7GgiIuWavdkBRESkbJuzbw4pmSk08m1Ep+qdzI5TZB5s/CDz989nxdEVPLz8YTaN3lRmz1W/GfN+z/0FSvda3anqUdXkNFLUrHZWFt67ED83Pz7a8RHPrnqWE/EneK/3ewX6fEdERBATE1MCSQvPx8eH6tWrmx1DRMRGpVtERArNMAzbAmqPt368XE1FtlgszOg7g0YfN2Jr5FY+2vERT7V7yuxYxcowDOb8/r+p5c1GmJxGiovVzsr0PtOp5VWLSasm8eGODzmdcJr5g+bj7uR+zedFRERQv0F9UlNSSzDtzXNxdeFQ+CEVbxEpNVS6RUSk0H6L+I3wmHDcHNwY3qz8rXId7BnMf7v/lyd+eoKXfn2Ju+vdTUjlELNjFZutkVs5FncMNwc3BtYfaHYcKUYWi4VnQ58lxDOEYUuHsfzIcprNbMaX93zJHSF3XPU5MTExpKakMvClgfiG+JZw4oK5ePoiS6csJSYmRqVbREoNlW4RESm0WXtmAfBQ44fwcPIwOU3xeKz1Yyw8sJBNEZsY9+M4fh76c7k6ov9neQuo3dfwPtwc3UxOUzGFh4eX6P5qUpOZ7Wfy4q4XOXnpJF2+7MKw2sN4vN7jOFmdrprNN8SXwLqBJZpTRKQsU+kWEZFCuZR2iW8PfgvAwy0fNjlN8bGz2PF5/89pNrMZvxz/hXm/zyuXR/XTs9JZdHARoKnlZkiKSwJg2DCTFq9zAnqB0dJg7vG5zA2bCyuA01cOTUpKKul0IiJlmkq3iIgUysL9C0nNSqWRbyPaVm1rdpxiVc+nHq90foWX177MhF8m0Ou2Xvi5+Zkdq0j9eORHLqVdItgjmC41upgdp8JJS0oDoOv4rtRpVse0HKfTTrMxYSOpfqkwGqo6VqWNexv8HP04uu0o675YR1pammn5RETKIpVuEREplLyp5Q+3fLjcTrf+s+c6PMc3B79h3/l9PL3yaRbeu9DsSEVq5q6ZAAxrOgw7i64oahavql6mTt0OJJAmGU1Yf3o9u8/t5mzGWc7GnqVelXoEBAWYlktEpCzTv6oiInLT9kbvZde5XTjYOVSYazk7WB34fMDn2FnsWHRgET8e+dHsSEXmj4t/sObEGuwsdoxrNc7sOGIyN0c3+tbpy9/a/I1m/s2wYOFw7GE22G+Av8Hh7MMkpieaHVNEpMwo1Ue6s7KymDx5MvPnzyc6OprAwEBGjRrFP/7xD+zscn9fYBgGr776Kp9++inx8fG0a9eOjz76iEaNGtm2k56ezqRJk1i4cCGpqal069aNjz/+mGrVqtnGxMfH89RTT/HDDz8AMGDAAKZPn07lypVL9DWLiJQFs3bnHuUe2GAgPq4+Jqe5dQVdvMoOO4bWGsrc43MZu3Qs33T5BneHa19iqagU93WHp2+bDsA99e8p16uzy83xcvHinvr30DG4I1sit7D/3H6yfbI5mHOQP7b+QbBHMLW9a1PbqzaB7oGaISEicg2lunS/8cYbzJw5k6+++opGjRqxc+dORo8ejaenJ08//TQA06ZN45133uHLL7+kbt26vPbaa/To0YPDhw/j7p77g9CECRNYvnw5ixYtokqVKkycOJF+/fqxa9curFYrAEOGDCEyMpKVK1cC8OijjzJ8+HCWL19uzosXESmlUjNTmbd/HgBjW4w1Oc2tKdTiVQ7A43DB+wJdpnaB74slWj7Fed3h+NR427W5n2pbvq9DLoXj6+bL3fXuJvhMMMtXLKfKwCrEGrFEJEYQkRjBulPrcLF3IaRyCNXcq1HNoxpB7kE4WB3Mji4iUiqU6tIdFhbG3XffTd++fQGoUaMGCxcuZOfOnUDuUe733nuPl19+mUGDBgHw1Vdf4e/vz4IFCxg3bhwJCQnMmjWLuXPn0r17dwDmzZtHcHAwa9asoVevXoSHh7Ny5Uq2bt1Ku3btAPjss88IDQ3l8OHD1KtXz4RXLyJSOi09tJRLaZcI8Qyhe63uZse5JYVdvOpcxjmWxy6HFtDzzp7UcK5RTAmL/7rDX+z5gpTMFJr6N73m9ZlFABxwgL3Q+f7OBLcM5ljcMY7HH+dk/ElSs1I5FHOIQzGHgNxV//3d/KnmUc1283L2qhDrP4iI/FWpLt233347M2fO5MiRI9StW5d9+/axadMm3nvvPQBOnjxJdHQ0PXv2tD3HycmJzp07s2XLFsaNG8euXbvIzMzMNyYoKIjGjRuzZcsWevXqRVhYGJ6enrbCDdC+fXs8PT3ZsmXLNUt3eno66enptq8TE3V+k4iUf5/v/hyA0c1Hl5vppDe7eFUggcSeiGXLmS1sStpEk/pNqORYqRgTFo/snGw+3PEhkHuUW4VICqqyc2VaB7WmdVBrsnOyibocRURiBGcTz3Im8QxJGUmcSzrHuaRz7IjaAYCrg6vtSHg1j2pU9aiKo9XR5FciIlL8SnXp/vvf/05CQgL169fHarWSnZ3N66+/zkMPPQRAdHQ0AP7+/vme5+/vz+nTp21jHB0d8fLyumJM3vOjo6Px87vy0i9+fn62MVczdepUXn311cK/QBGRMuZ43HHWnVqHBQujW4w2O46putboyvG445xPPs/yI8t5sNGDZa60Lj+ynFOXTlHFpQpDmgwxO46UUVY7K8GewQR7BgO5MxET0xOJTIy03c4lnSMlM4UjcUc4Enck93kWK7W8atHAtwH1qtTD1cHVzJchIlJsSnXp/vrrr5k3bx4LFiygUaNG7N27lwkTJhAUFMTIkSNt4/76Q45hGDf8weevY642/kbbefHFF3n22WdtXycmJhIcHHzD1yUiUlZ9secLAHrW7kl1z+Jb2KsssLezZ2D9gXy2+zOOxB5hT/QeWga2NDvWTflg2wcAPNrqUVwcXExOI+WFxWLB09kTT2dPGvnlLmyblZNFdFJ0viKekJ7A0bijHI07igULtbxq0bZqW+p41ylzv8ASEbmeUl26n3vuOV544QUefPBBAJo0acLp06eZOnUqI0eOJCAg93qReSub57lw4YLt6HdAQAAZGRnEx8fnO9p94cIFOnToYBtz/vz5K/Z/8eLFK46i/5mTkxNOTk63/kJFRMqArJwsvtz3JZB7bW4B/0r+dK3RlTUn1/DL8V+oUbkG3i7eZscqkP3n97Pu1DqsFiuPt37c7DhSztnb2dumlUPugY2LKRcJjwkn/GI455PPczz+OMfjj1PFpQrtqrWjuX9zLcYmIuVCqT4ZLyUlxXZpsDxWq5WcnBwAatasSUBAAKtXr7Y9npGRwYYNG2yFulWrVjg4OOQbc+7cOQ4cOGAbExoaSkJCAtu3b7eN2bZtGwkJCbYxIiIV3cpjK4m6HIWPqw8D6g0wO06pERocSnXP6mRkZ/Bd+Hdk5WSZHalA3g57G4BBDQbZpgWLlBSLxYKfmx+dQzrzWOvHeLLtk3So1gEnqxOxqbH8dPQn3t36LjuidpBj5JgdV0TklpTqI939+/fn9ddfp3r16jRq1Ig9e/bwzjvvMGbMGCD3G/aECROYMmUKderUoU6dOkyZMgVXV1eGDMk9N83T05OxY8cyceJEqlSpgre3N5MmTaJJkya21cwbNGhA7969eeSRR/jkk0+A3EuG9evXTyuXi4j8z6w9udfmHtF0hBY/+hM7ix2D6g/ik12fEHU5itUnVtPntj5mx7quPy7+wdzf5wIwqcMkk9OIgLeLNz1q9+COkDvYe34v2yK3EZ8Wz09Hf2L3ud3cddtd+uWQiJRZpbp0T58+nX/+85+MHz+eCxcuEBQUxLhx43jllVdsY55//nlSU1MZP3488fHxtGvXjlWrVtmu0Q3w7rvvYm9vz+DBg0lNTaVbt258+eWXtmt0A8yfP5+nnnrKtsr5gAED+PDDD0vuxYqIlGLRSdEsP7wcgLEty/a1uYuDp7Mn99S/h4UHFrL97HZqeNaggW8Ds2Nd0z/W/oMcI4eB9QfStmpbs+OI2DjZO9GuajvaBLVhZ9RO1p5cS3RSNF/s/YJm/s3oVbuX1h8QkTKnVJdud3d33nvvPdslwq7GYrEwefJkJk+efM0xzs7OTJ8+nenTp19zjLe3N/PmzbuFtCIi5ddXe78i28gmtFooDX0bmh2nVKpbpS4dqnVgS+QWvj/8PQGVAvBy8brxE0vY1sitLD20FDuLHa/f+brZcUSuys5iR9uqbWnk24g1J9ewN3ov+87v43TCaQY3HEyge8Ev8SciYrZSfU63iIiYzzAM29TysS10lPt67qx5J9U8qpGenc7i8MWl7vxuwzB4Yc0LAIxqNqpUH40XAXBzdOPuenczpvkYvJy9uJR2iVl7ZrH73G4MwzA7nohIgah0i4jIdf0W8RtH445SybESDzR+wOw4pZrVzsp9De7Dxd6FqMtR/HT0p1JVDFYdX8WG0xtwsjoxuctks+OIFFiwZzCPtHyEulXqkm1ks/zIcn44/AOZ2ZlmRxMRuSGVbhERua68o9wPNnqQSo6VTE5T+nk6ezKw/kAsWNgTvYetkVvNjgRAjpHDC7/mHuX+W9u/aVEqKXNcHFx4sNGDdKvZDQsW9p7fy4L9C0jPSjc7mojIdal0i4jINSWkJfDtwW8BLaB2M+pUqUPP2rkLc646sYrDMYdNTgTfHPyGvdF78XDy4MXbXzQ7jkihWCwWbq9+O8ObDsfR6siphFPM2z+PtKw0s6OJiFyTSreIiFzTgv0LSM1KpZFvI9pVbWd2nDKlXdV2tAxsCcCSQ0s4n3TetCwxKTE8+8uzADzX4TmquFYxLYtIUajpVZMRTUfgbO9MZGIkc/bNISUzxexYIiJXpdItIiJXZRgGn+z6BICHWz6MxWIxOVHZYrFYuOu2u6hZuSYZ2RksPLCQpIykEs9hGAaPLn+Uc0nnqO9Tn2dDny3xDCLFoapHVUY2G4mrgyvnks7x1b6vSMlW8RaR0kelW0RErmrb2W3sO78PZ3tnRjYbaXacMslqZ+X+hvdTxaUKCekJzP19LskZySWa4Ys9X7D00FIc7BxYMGgBrg6uJbp/keIUUCmAUc1GUcmxEheSL7AybiU4mJ1KRCQ/lW4REbmqvKPcgxsNLpXXmy4rXBxcGNJkiK0UzPl9TokV76OxR3l65dMAvHbna7QIbFEi+xUpSb5uvoxuPhpXB1dismLgXsg2ss2OJSJio9ItIiJXuJR2ia8PfA3AuFbjTE5T9nm7eDOy2ch8xbu4zz/NzM5k6JKhJGcm06VGFyaGTizW/YmYydvFmwcbPYgVK9SH9w6+Z3YkEREblW4REbnC3H1zSc1KpbFfY0KrhZodp1zwcfXJX7yLeeGnf6z9BzuidlDZuTJz7pmD1c5abPsSKQ2CPYPpUrkLAAtOLuCj7R+ZG0hE5H9UukVEJB/DMJi5ayYAj7V6TAuoFaE/F+/zyef5fPfnRCdFF+k+DMPgpV9fYtqWaQDM7DtT1+SWCqO2S21Yk/v/T618ip+P/mxuIBERwN7sACIiUngRERHExMQU6Tb3xO7hj4t/4Gx1prHRmN27dxd6W+Hh4UWYrHzIK97z988nPi2eWXtm0a9uP5r5N7vlbRuGwYSVE/hg+wcAvNXjLR5o/MAtb1ekTNkEd4++m+/PfM+wpcPY99g+qnlUMzuViFRgKt0iImVUREQE9RvUJzUltWg3PBBoBmk70ujyzy5FssmkpJK/VFZp5uPqw6MtH2VJ+BKOxR9j2aFlRCZG0rt270JPA8/OyeaxHx/j8z2fA/DxXR/zeJvHizK2SJnxYtMXOZt9lp1ROxm6ZChrR6zVKRYiYhqVbhGRMiomJobUlFQGvjQQ3xDfItlmWk4a88/PJ5ts7ulxD359/W5pe0e3HWXdF+tIS0srknzlSd6q5htOb2DD6Q3sjNrJyfiTdK3RlYa+DW9qWv/R2KNMXDWR5UeWY2ex44sBXzCyuS7zJhWXg50DC+9dSItPWrDx9EZe2/ga/+ryL7NjiUgFpdItIlLG+Yb4Elg3sEi2FXYmjGyyCagUQNNGTW/5fO6YiKKd+l7eWCwWutToQpB7EMsOLSM2NZbF4YsJOBNA1xpdqeNd57rPP5t4ln9v+Dez9swi28jG3s6eBYMWcH+j+0voFYiUXrd538aMvjMYvnQ4/974b+6seSedQjqZHUtEKiCVbhERASDHyGF71HYAWge11gJqJahulbo83e5pwiLDCIsMIzopmoUHFuLp5EkVSxUIzT3XPutsFifiT3A87jiHYw/z7R/fkpaVO4ugz219mNJtCs0Dmpv7YkRKkWFNh7H6xGrm7JvD0CVD2fvYXrxdvM2OJSIVjEq3iIgAcDj2MJfSLuFi70JTv6Zmx6lwnOyd6FKjC22rtmXzmc1sP7udhPQEEkiAXvDwlodhy5XP6xjckandpuoInsg1fNjnQ8LOhHE07ijjfhzHt/d/a3YkEalgVLpFRASAbZHbAGgV2AoHq4PJaSouVwdXetTqwR3V7yDqchThJ8PZsX0Hfi38sNpbqe1dm9peubfQ4FC61eymWQki1+Hu5M7CexfSflZ7Fv+xmGWHlnFP/XvMjiUiFYhKt4iIEJ0UzemE09hZ7GhTtY3ZcYTcI981vWrifNGZHV/v4Ofnf6Zly5ZmxxIpk1oFtWJS6CT+u/m/PPHTE9xZ8048nDzMjiUiFYRKt4iI2I5yN/RpqB9ES6nSfM3z0pxNJM8rnV9hcfhijsUd48U1L/JR34/MjiQiFYRKt4hIBZeUkcT+C/sBaFetnclp5K+S4nKvcT5s2DCTk9yYrscupZmLgwuf9PuEbnO6MWPnDIY2HUqH4A5mxxKRCkClW0SkgtsVtYtsI5uq7lWp5lHN7DjyF2lJuauTdx3flTrNrn8JMbPoeuxSVtxZ805GNx/N7L2zeWT5I+x+dDdO9k5mxxKRck6lW0SkAsvKyWJH1A5AR7lLO6+qXkV2PfaipuuxS1nyVs+3+PHIj/xx8Q/e2PwGr3R+xexIIlLO2ZkdQEREzHPw4kGSM5Nxd3SnoU9Ds+OIiBQ7bxdv3u/9PgBTfpvC6UunTU4kIuWdSreISAVlGAabIzYD0KZqG6x2VpMTiYiUjAcbP0iXGl1Iz07nhV9fMDuOiJRzKt0iIhVUeEw4F1Mu4mR1ok2QLhMmIhWHxWLh3V7vYsHCogOL2HJmi9mRRKQcU+kWEamADMNg4+mNALSr2g5ne2eTE4mIlKzmAc0Z02IMAM/88gw5Ro7JiUSkvFLpFhGpgI7EHuF88nkcrY60r9be7DgiIqZ47c7XqORYie1nt7Ng/wKz44hIOaXSLSJSwRiGwcaI3KPcbYLa4OLgYnIiERFzBFQK4KXbXwLgxV9fJCUzxeREIlIeqXSLiFQwx+OPE3U5Cns7e0KrhZodR0TEVM+EPkOIZwiRiZG8teUts+OISDmk0i0iUoH8+Vzu1kGtcXN0MzmRiIi5nO2dmdZjGgBvbH6D6KRokxOJSHmj0i0iUoGcunSKM4lnsFqsdKjWwew4IiKlwv0N76dd1XakZKYw9bepZscRkXJGpVtEpIIwDIO1p9YC0DKwJe5O7iYnEhEpHSwWC6/d+RoAM3fNJCIhwuREIlKeqHSLiFQQBy8eJDIxEgc7BzpV72R2HBGRUqVbzW50qdGFjOwM/rPhP2bHEZFyRKVbRKQCyMzOZM2JNQB0rN5RR7lFRP7CYrHw+p2vAzB772yOxh41OZGIlBcq3SIiFcDWyK0kpCfg4eShc7lFRK6hQ3AH7qpzF9lGNpM3TDY7joiUEyrdIiLlXFJGEpvObAJyp086WB1MTiQiUnq91jX33O6F+xey//x+k9OISHmg0i0iUs6tPbmWjOwMqrpXpYlfE7PjiIiUai0CW3Bfw/swMPjnun+aHUdEygGVbhGRciw6KZo90XsA6FW7FxaLxeREIiKl37+7/Bs7ix3fH/6e3ed2mx1HRMo4lW4RkXLKMAx+PvozAI19GxPsGWxyIhGRsqGBbwMebPwgAK//9rrJaUSkrFPpFhEpp7ae3UpEYgSOVke61+pudhwRkTLlpdtfAmBJ+BIOXDhgchoRKctUukVEyqGYlBjWnlwLQM9aPfF09jQ5kYhI2dLIrxH3NrgXgCm/TTE5jYiUZSrdIiLlTI6Rw7JDy8jKyaK2V21aBrY0O5KISJn0cqeXAfj64NcciT1ichoRKatUukVEypnNZzZz9vJZnKxODKg3QIuniYgUUovAFvSr248cI4epm6aaHUdEyiiVbhGRcuR80nnWn1oPQJ/b+uDh5GFuIBGRMu4fnf4BwNx9czl16ZS5YUSkTLI3O4CIiBSNjOwMlhxaQo6RQ70q9Wjq39TsSCIipggPDy+ybTngQDufdmyL2cakZZN4qelLt7xNHx8fqlevXgTpRKQsUOkWESkHDMNgSfgSLiRfwM3BjX51+2lauYhUOElxSQAMGzasaDccAoyG745/x3dPfQeXb21zLq4uHAo/pOItUkGodIuIlANrT67lcOxhrBYrDzZ+kEqOlcyOJCJS4tKS0gDoOr4rdZrVKdJt/xDzA9FE0/SVprT3aF/o7Vw8fZGlU5YSExOj0i1SQah0i4iUcUdTjrIpYRMAA+oNoJpHNZMTiYiYy6uqF4F1A4t0m128u7Do4CIOpx3mruZ34WTvVKTbF5HySwupiYiUZdVgY8JGADoGd9R53CIixaRulbr4uPqQnp3OrnO7zI4jImWISreISBl1JOEIPATZZFOvSj261exmdiQRkXLLYrEQWi0UgK2RW8nOyTY5kYiUFSrdIiJl0I6zOxgXNg7cwMfBh4H1B2rhNBGRYtbUvymVHCtxOeMy+y/sNzuOiJQRKt0iImXM5ojNdJ/bncTMRDgDfb376txCEZESYG9nT7uq7QAIiwzDMAyTE4lIWaDSLSJShqw7uY5e83qRmJ5IS++WMBec7FS4RURKSuug1jhaHbmQfIFjccfMjiMiZYBKt4hIGWAYBh9t/4je83uTnJlMj1o9+KDdB5BhdjIRkYrF2d6ZloEtAdhyZovJaUSkLFDpFhEp5S6nX+ah7x7ibz//jYzsDO5tcC8/PPQDLvYuZkcTEamQ2ldtj53FjlMJp4i6HGV2HBEp5VS6RURKsd/P/07rz1rz9cGvsbez552e7/Dt/d/ibO9sdjQRkQrL09mTxr6NAR3tFpEbU+kWESmFUjJTeGXdK7T9rC1HYo9QzaMaG0Zt4JnQZ7RKuYhIKdAhuAMAf1z8g/jUeJPTiEhpptItIlKKGIbBskPLaPhRQ/6z8T+kZ6dzV5272DNuj+0HPBERMZ9/JX9qe9XGwCAsMszsOCJSiql0i4iUElsjt9Jnfh8Gfj2Q0wmnCfYI5rvB3/HjQz/i4+pjdjwREfmLvF+G7oneQ0pmislpRKS0sjc7gIhUTBEREcTExJgd47p8fHyoXr16se7DMAxWn1jN1E1TWX9qPQCOVkcmhU7ipU4v4eboVqz7FxGRwqtZuSYBlQKITopmx9kddK7R2exIIlIKFap0nzx5kpo1axZ1FhGpICIiIqjfoD6pKalmR7kuF1cXDoUfKpbinZieyNcHvmbmrpnsPrcbAHs7e4Y3Hc5LnV7iNu/binyfIiJStCwWCx2DO/Jd+Hdsj9pOh+AOOFgdzI4lIqVMoUr3bbfdxh133MHYsWO57777cHbWKroiUnAxMTGkpqQy8KWB+Ib4mh3nqi6evsjSKUuJiYkpstKdY+Sw8fRGZu+dzbcHvyU1K/eXDi72Ljza6lEmhk4k2DO4SPYlIiIlo6FvQ349+SuX0i6x9/xe2gS1MTuSiJQyhSrd+/bt44svvmDixIn87W9/44EHHmDs2LG0bdu2qPOJSDnmG+JLYN1As2MUq4zsDNafWs+S8CV8f/h7opOibY/V96nPmOZjGNV8FL5upfOXDyIicn12FjvaV23PyuMrCTsTRqvAVthZtGySiPy/QpXuxo0b88477zBt2jSWL1/Ol19+ye23306dOnUYO3Ysw4cPx9dXP0CKSMV0Iv4Eq4+vZvWJ1aw5sYaE9ATbYx5OHgxuOJgxLcbQvlp7Xf5LRKQcaBHYgg2nNxCfFs+hmEM09G1odiQRKUVuaSE1e3t7Bg4cyF133cXHH3/Miy++yKRJk3jxxRd54IEHeOONNwgMLN9HsUSkYssxcjgcc5jNZzaz+cxmNp7eyIn4E/nG+Lv5c3e9uxnUYBBda3bF0epoUloRESkOjlZHWge15reI3wiLDFPpFpF8bql079y5ky+++IJFixbh5ubGpEmTGDt2LFFRUbzyyivcfffdbN++vaiyikgFlWPkkJieSGJ6IjlGDoZhYGBgGAZO9k442zvbbvZ2xXdRhozsDI7EHmFf9D72Ru9l3/l97Dq3i7jUuHzj7O3sCa0WSo9aPehRuwdtgtpgtbMWWy4RETFf26pt2XJmC5GJkZxJOKM1OkTEplA/nb7zzjvMnj2bw4cPc9dddzFnzhzuuusu7Oxyz1+pWbMmn3zyCfXr1y/SsCJS/mXnZBOZGMnx+OOcSTzDpbRLJKQlYGAU6PmOVkfcHNxwc3DD1dHV9v9ujm64OrjibO+Mg50DDlYHHK2OWLCQbWSTY+SQY+SQkZ1BSmYK0cnR0Bn+s+8/JOxP4ET8Cc4kniHHyLliny72LrSt2paOwR3pENyBO0LuwN3JvajfGhERKcUqOVaiiX8T9kbvJSwyTKVbRGwKVbpnzJjBmDFjGD16NAEBAVcdU716dWbNmnVL4USkYsibor3/wn5OxJ8gPTv9ijFWixV3J3fs7eyxYLGdC52RnUFaVhppWWm2rzOyM4hPi7/1YF1hWcSyfHe5O7rTLKAZzfyb0TygOc0DmtPMv5kuESMiIoRWC2Vv9F4OxRwiPjUeLxcvsyOJSClQqNJ99OjRG45xdHRk5MiRhdm8iFQQWUYWO6N2EhYZlm+Ktou9C7W9a1Ozck18XH2o7FwZd0f36y46ZhgG6dnpJGckk5KZQnJmMskZybn//dP/Z2RnkJmdSWZOJhnZGUDuyrN5Nwc7B9wc3LCkWzj520kG9hxIi5AWVHOrRjW3ang7eufPEQ37o/cX23t0PeHh4absV0RErs7PzY/aXrU5Hn+crWe30ue2PmZHEpFSoFCle/bs2VSqVIn7778/3/3ffvstKSkpKtsicl1ZOVnQHhZeWEhqdO61qp3tnWkV2IoGPg0IdA+86cutWCwW23ndVahyyxmPbj3KyRUnWbpiKUtZesvbK05JSUlmRxARkf8JrRbK8fjj7Dm3hy4hXXBxcDE7koiYrFCl+7///S8zZ8684n4/Pz8effRRlW4RuaZ90fsYtWkU9IbUnFQ8nTwJrRZKi8AWpWpV77Sk3OnqXcd3pU6zOianubqj246y7ot1pKWlmR1FRET+p5ZXLfzd/DmffJ5d53Zxe/XbzY4kIiYrVOk+ffo0NWvWvOL+kJAQIiIibjmUiJQ/aVlp/GfDf5i2ZVruke406OTfiS7Nutz0Ue2S5FXVi8C6pfPShzERMWZHEBGRv7BYLLSv1p7vD3/P9rPbCa0WqitYiFRwhfpJ18/Pj99///2K+/ft20eVKrc+rVNEypcT8Sdo81kbpmyaQlZOFncG3AkfQgPXBqW6cIuIiBRGE78mVHKsxOWMyxy4eMDsOCJiskL9tPvggw/y1FNPsW7dOrKzs8nOzmbt2rU8/fTTPPjgg0WdUUTKsLUn19LmszYcuHAAfzd/vhv8HW+2eRN0GrKIiJRTVjsrbau2BSDsTBiGUbDLXopI+VSo0v3aa6/Rrl07unXrhouLCy4uLvTs2ZM777yTKVOmFHVGESmDDMPgw+0f0nNuT+JS42gT1Ibd43YzqMEgs6OJiIgUu9aBrXGwc+B88nlOXjppdhwRMVGhzul2dHTk66+/5j//+Q/79u3DxcWFJk2aEBISUtT5RKQMysrJ4okVT/Dp7k8BGN50OJ/2/xRne2eTk4mIiJQMFwcXmgc0Z0fUDsIiw6jlVcvsSCJikls6mbJu3brcf//99OvXr9gK99mzZxk2bBhVqlTB1dWV5s2bs2vXLtvjhmEwefJkgoKCcHFxoUuXLhw8eDDfNtLT03nyySfx8fHBzc2NAQMGEBkZmW9MfHw8w4cPx9PTE09PT4YPH86lS5eK5TWJlGcZ2RkM+W4In+7+FDuLHW/1eIuv7vlKhVtERCqc9tXaA3As7hgXky+anEZEzFKo0p2dnc2sWbMYMmQI3bt3584778x3Kyrx8fF07NgRBwcHfv75Z/744w/efvttKleubBszbdo03nnnHT788EN27NhBQEAAPXr04PLly7YxEyZMYOnSpSxatIhNmzaRlJREv379yM7Oto0ZMmQIe/fuZeXKlaxcuZK9e/cyfPjwInstIhVBWlYa935zL9/+8S0Odg4svn8xEztMxGKxmB1NRESkxHm7eFPfpz4AYZFhJqcREbMUanr5008/zZdffknfvn1p3Lhxsf1A/cYbbxAcHMzs2bNt99WoUcP2/4Zh8N577/Hyyy8zaFDueaJfffUV/v7+LFiwgHHjxpGQkMCsWbOYO3cu3bt3B2DevHkEBwezZs0aevXqRXh4OCtXrmTr1q20a9cOgM8++4zQ0FAOHz5MvXr1iuX1iZQnyRnJ3PP1Paw5sQZne2eWPrCU3rf1NjuWiIiIqUKrhXIo5hC/n/+dO2sW3cEpESk7ClW6Fy1axDfffMNdd91V1Hny+eGHH+jVqxf3338/GzZsoGrVqowfP55HHnkEgJMnTxIdHU3Pnj1tz3FycqJz585s2bKFcePGsWvXLjIzM/ONCQoKonHjxmzZsoVevXoRFhaGp6enrXADtG/fHk9PT7Zs2aLSLXIDSRlJ9Jnfh00Rm3BzcOPHIT/SpUYXs2OJiIiYLtgjmKruVTl7+Sw7zu6gPvXNjiQiJaxQ08sdHR257bbbijrLFU6cOMGMGTOoU6cOv/zyC4899hhPPfUUc+bMASA6OhoAf3//fM/z9/e3PRYdHY2joyNeXl7XHePn53fF/v38/GxjriY9PZ3ExMR8N5GKJj0rnUFfD2JTxCY8nTxZPXy1CreIiMj/WCwWQoNDAdgRtYMsI8vkRCJS0gpVuidOnMj7779f7NcczMnJoWXLlkyZMoUWLVowbtw4HnnkEWbMmJFv3F+ntxuGccMp738dc7XxN9rO1KlTbQuveXp6EhwcXJCXJVJuZOVkMWTJEFafWI2bgxsrh620/WAhIiIiuRr4NKCyc2VSs1I5knLE7DgiUsIKVbo3bdrE/PnzqV27Nv3792fQoEH5bkUlMDCQhg0b5ruvQYMGREREABAQEABwxdHoCxcu2I5+BwQEkJGRQXx8/HXHnD9//or9X7x48Yqj6H/24osvkpCQYLudOXPmJl+hSNmVY+Tw6PJHWRK+BEerI98/+L1tlVYRERH5f3YWO9pVzT2NcX/yftD6oiIVSqFKd+XKlRk4cCCdO3fGx8cn39FeT0/PIgvXsWNHDh8+nO++I0eO2C5PVrNmTQICAli9erXt8YyMDDZs2ECHDh0AaNWqFQ4ODvnGnDt3jgMHDtjGhIaGkpCQwPbt221jtm3bRkJCgm3M1Tg5OeHh4ZHvJlIRGIbBpFWTmL13NnYWOxbdu4hutbqZHUtERKTUahHQAierEwnZCVDX7DQiUpIKtZDan1cTL07PPPMMHTp0YMqUKQwePJjt27fz6aef8umnnwK5U8InTJjAlClTqFOnDnXq1GHKlCm4uroyZMgQADw9PRk7diwTJ06kSpUqeHt7M2nSJJo0aWJbzbxBgwb07t2bRx55hE8++QSARx99lH79+mkRNZGreG3ja7y79V0AvhjwBQMbDDQ5kYiISOnmZO9Eq6BWbDmzBXQmlkiFUqgj3QBZWVmsWbOGTz75xHZN7KioKJKSkoosXJs2bVi6dCkLFy6kcePG/Oc//+G9995j6NChtjHPP/88EyZMYPz48bRu3ZqzZ8+yatUq3N3dbWPeffdd7rnnHgYPHkzHjh1xdXVl+fLlWK1W25j58+fTpEkTevbsSc+ePWnatClz584tstciUl5M3zadV9a/AsB7vd5jZPORJicSEREpG9pVbYcFC9SAPy79YXYcESkhhTrSffr0aXr37k1ERATp6en06NEDd3d3pk2bRlpaGjNnziyygP369aNfv37XfNxisTB58mQmT558zTHOzs5Mnz6d6dOnX3OMt7c38+bNu5WoIuXe3H1zeWrlUwD8q/O/eLr90yYnEhERKTs8nDyo7VKbY6nHmHdiHsPuHGZ2JBEpAYU60v3000/TunVr4uPjcXFxsd0/cOBAfv311yILJyKlx/eHvmf096MBeLrd0/yr879MTiQiIlL2NHVrCsCaqDVEJESYnEZESkKhVy//xz/+gaOjY777Q0JCOHv2bJEEE5HSY+3JtQxePJhsI5tRzUfxTq93bnhZPhEREbmSj4MPnIBsI5sPtn1gdhwRKQGFKt05OTlkZ2dfcX9kZGS+c6lFpOzbfnY7AxYOICM7g4H1B/JZ/8+wsxR6OQgREREJy/3PZ7s/IzE90dwsIlLsCvWTc48ePXjvvfdsX1ssFpKSkvjXv/7FXXfdVVTZRMRkBy4coM/8PiRnJtOtZjcW3LsAe7tCLQUhIiIieY5BjUo1SExP5PPdn5udRkSKWaFK97vvvsuGDRto2LAhaWlpDBkyhBo1anD27FneeOONos4oIiY4EX+CnnN7EpcaR7uq7Vj24DKc7Z3NjiUiIlL2GTC0Vu7VeN7f9j5ZOVkmBxKR4lSo0h0UFMTevXuZNGkS48aNo0WLFvz3v/9lz549+Pn5FXVGESlhUZej6DG3B+eSztHYrzE/Df2JSo6VzI4lIiJSbvSt1hdfV18iEiJY/Mdis+OISDEq9DxRFxcXxowZw5gxY4oyj4iYLDopmju/upMT8Seo5VWLVcNW4e3ibXYsERGRcsXJ6sQTbZ5g8obJvB32Ng80ekCLlIqUU4Uq3XPmzLnu4yNGjChUGBEpOhEREcTExNzUc+LS4xi3ZRwnkk7g7+zP+y3f59zRc5zjXJFmCw8PL9LtiYiIlEXj24znv5v/y86onfwW8Rt3hNxhdiQRKQaFKt1PP/10vq8zMzNJSUnB0dERV1dXlW4Rk0VERFC/QX1SU1IL/iRXYCTgDyTC+ffP0z++fzElzJWUlFSs2xcRESnNfN18GdF0BJ/u/pS3w95W6RYppwpVuuPj46+47+jRozz++OM899xztxxKRG5NTEwMqSmpDHxpIL4hvjccn5aTxorYFcRmxeJq50q/2v2o/N/KxZbv6LajrPtiHWlpacW2DxERkbLgmdBn+HT3pyw/vJwjsUeoW6Wu2ZFEpIgV2bV/6tSpw3//+1+GDRvGoUOHimqzInILfEN8CawbeN0xSRlJLPt9GbFZsbg5uDGq+Sh8XH2KNVdMxM1NexcRESmv6vvUp1/dfvx45EfeDXuXGf1mmB1JRIpYoVYvvxar1UpUVFRRblJEilFCWgKz987mQvIFKjlWYkSzEcVeuEVERCS/iaETAfhq31fEpOgX0yLlTaGOdP/www/5vjYMg3PnzvHhhx/SsWPHIgkmIsUrNiWWOb/PITE9EU8nT0Y0G6FVykVEREzQOaQzLQNbsvvcbmbsmME/O//T7EgiUoQKVbrvueeefF9bLBZ8fX258847efvtt4sil4gUo+ikaOb9Po/kzGSquFRhRLMReDh5mB1LRESkQrJYLEwMncjQJUOZvn06kzpMwsXBxexYIlJECjW9PCcnJ98tOzub6OhoFixYQGDg9c8fFRFzHYs7xuy9s0nOTCagUgCjm49W4RYRETHZ4EaDCfEM4WLKRWbvnW12HBEpQkV6TreIlG67onaxYP8CMrIzqOFZg5HNRuLm6GZ2LBERkQrP3s6eSR0mAfDWlrfIyskyOZGIFJVCTS9/9tlnCzz2nXfeKcwuRKQIGYbBryd/ZfOZzQA09W/KgLoDsNpZTU4mIiIieca0GMOrG17l5KWTfHvwWx5q8pDZkUSkCBSqdO/Zs4fdu3eTlZVFvXr1ADhy5AhWq5WWLVvaxlkslqJJKSKFlpGdwbJDywiPCQdyF2vpHNJZfz9FRERKGVcHV55q+xSvrH+FNza/wYONH9S/1yLlQKFKd//+/XF3d+err77Cy8sLgPj4eEaPHk2nTp2YOHFikYYUkcJJzEpk2Z5lXEi+gJ3FjgF1B9AsoJnZsUREROQanmj7BG9sfoN95/fxy/Ff6H1bb7MjicgtKtQ53W+//TZTp061FW4ALy8vXnvtNa1eLlJa1IYlMUts1+Ae1WyUCreIiEgp5+3izaOtHgXgjc1vmJxGRIpCoUp3YmIi58+fv+L+CxcucPny5VsOJSKFl2Pk8MXRL2AoZBgZVHOvxqMtHyXYM9jsaCIiIlIAz4Y+i4OdA+tPrWdb5Daz44jILSpU6R44cCCjR49m8eLFREZGEhkZyeLFixk7diyDBg0q6owiUkBxqXHcvehuPjr0EdhBfZf6jGw+Encnd7OjiYiISAFV86jG0KZDAR3tFikPClW6Z86cSd++fRk2bBghISGEhIQwdOhQ+vTpw8cff1zUGUWkAHZG7aTVp6348ciPONo5wg/QybMT9naFWrpBRERETPR8h+cBWHZoGYdiDpmcRkRuRaFKt6urKx9//DGxsbG2lczj4uL4+OOPcXPTNX9FSpJhGMzcOZOOX3Tk1KVT1PKqxezbZ8NuXUFARESkrGrg24C7692NgcGbm980O46I3IJCle48586d49y5c9StWxc3NzcMwyiqXCJSAMkZyYxYNoLHVzxORnYGd9e7m12P7qK+Z32zo4mIiMgteuH2FwCY+/tcIhMjTU4jIoVVqNIdGxtLt27dqFu3LnfddRfnzp0D4OGHH9blwkRKyJHYI7Sf1Z55v8/DarEyrfs0lj6wlMrOlc2OJiIiIkWgfbX23BFyB5k5mbwb9q7ZcUSkkApVup955hkcHByIiIjA1dXVdv8DDzzAypUriyyciFzdkvAltP60NQcuHMDfzZ9fR/zKcx2f03RyERGRcuaFjrlHuz/d/SlxqXEmpxGRwihU6V61ahVvvPEG1apVy3d/nTp1OH36dJEEE5ErZeVk8dyq57j3m3u5nHGZTtU7sWfcHjrX6Gx2NBERESkGvW/rTVP/piRlJPHxDi1YLFIWFap0Jycn5zvCnScmJgYnJ6dbDiUiVzp3+Rzd5nTjrbC3AJgUOolfR/xKoHugyclERESkuFgsFv7e8e8AfLDtA1IyU0xOJCI3q1Cl+4477mDOnDm2ry0WCzk5Obz55pt07dq1yMKJSK6NpzfS8tOWbDy9EXdHdxbfv5g3e76Jg9XB7GgiIiJSzAY3GkyNyjW4mHKR2Xtmmx1HRG5SoS7g++abb9KlSxd27txJRkYGzz//PAcPHiQuLo7NmzcXdUaRUiciIoKYmJgS2dfiU4uZdmAa2UY2td1r82brNwlJC2H37t3XfE54eHiJZBMREZHiZ29nz6TQSfzt57/xVthbPNrqUf3iXaQMKVTpbtiwIb///jszZszAarWSnJzMoEGDeOKJJwgM1FRXKd8iIiKo36A+qSmpxbsjO6A30PZ/X/8Ox5cfZ1DmoAJvIikpqTiSiYiISAkb3WI0/974b05dOsW83+cxusVosyOJSAHddOnOzMykZ8+efPLJJ7z66qvFkUmkVIuJiSE1JZWBLw3EN8S3WPaRnpPOmvg1nM04C0Bb97Y069kMS6+CrU5+dNtR1n2xjrS0tGLJJyIiIiXL1cGV5zo8x3Orn2PKpikMbzYce7tCHT8TkRJ2039THRwcOHDggC5NJBWeb4gvgXWLfmZHXGoci/cvJi4jDgc7BwY1GER9n/o3tY2YiJKZ+i4iIiIl57HWj/HG5jc4FneMRQcWMazpMLMjiUgBFGohtREjRjBr1qyiziJS4UUmRjJrzyziUuPwdPJkTIsxN124RUREpHyq5FiJiaETAXht42tk52SbnEhECqJQc1IyMjL4/PPPWb16Na1bt8bNzS3f4++8806RhBOpSA7HHGZx+GKycrIIrBTIkCZDqORYyexYIiIiUgwKu+hpR8eOeDh4cDj2MNNWTKNX1V5FnAx8fHyoXr16kW9XpKK6qdJ94sQJatSowYEDB2jZsiUAR44cyTdG085Fbt7OqJ38dPQnDAxu87qN+xvdj6PV0exYIiIiUsSS4nIXOR027Bamht8B3AkvrXyJl2a8BEbRZMvj4urCofBDKt4iReSmSnedOnU4d+4c69atA+CBBx7ggw8+wN/fv1jCiZR3hmGw4fQGNpzeAECLgBb0rdMXq53V5GQiIiJSHNKSchc57Tq+K3Wa1SnUNtJz0ll4YSEZfhl0f7c7tVxqFVm+i6cvsnTKUmJiYlS6RYrITZVuw8j/a7Sff/6Z5OTkIg0kUlEYhsHK4yvZfnY7AJ1DOtM5pLNmi4iIiFQAXlW9bmlB1vbO7dl4eiP7M/fToWkH/fwgUooVaiG1PH8t4SJSMDlGDt8f/t5WuPvc1ocuNbroH0wREREpkPZV2+NodeR88nkOxx42O46IXMdNHem2WCxXlAKVBClOhmFw8tJJMrMzsbezx2pnxdXBFT83P7OjFVpWThaL/1jM4djDWLBwT/17aOrf1OxYIiIiUoa4OLjQtmpbNkVsYuPpjdSrUk8/l4uUUjc9vXzUqFE4OTkBkJaWxmOPPXbF6uVLliwpuoRSYZ26dIox349h3al1VzzWpUYXJneeTOcanU1IVnjpWeksOriIU5dOYW9nz30N7qOeTz2zY4mIiEgZFFotlG2R2ziXdI6jcUepW6Wu2ZFE5CpuqnSPHDky39e3tOqiyDUYhsGnuz5l0upJJGUkYW9nTyXHSmTlZJGdk01aVhrrT62ny6kudK3RlVe7vEqnkE5mx76hlMwU5u+fT9TlKBytjjzU+CFqVK5hdiwREREpo1wdXGkT1IYtkVvYeHojdbzr6Gi3SCl0U6V79uzZxZVDBICoy1GMXDaSNSfWANCpeidm3z2b2t61bWPOJJxh6qapfL77c9adWse6L9cxpMkQPu33KW6ObtfatKkS0xOZ+/tcYlJicHVwZWiToQS5B5kdS0RERMq40OBQtkdt5+zls5yIP5HvZyYRKR1uaSE1kaKUmZ1J/4X9WXNiDc72zrzb613Wj1p/xT8ewZ7BfNz3Y449dYzHWj2G1WJlwf4FtPu8HUdij1xj6+aJTYnliz1fEJMSg4eTB6Obj1bhFhERkSJRybESrQNbA7Dh9AYtdCxSCql0S6kxbfM0dp/bjZezF3vG7WFC+wnYWa79Ea3uWZ0Z/WawftR6AioFcPDiQVp/2pql4UtLMPX1nU08yxd7vyAhPYEqLlUY03wMPq4+ZscSERGRcqRDcAesFitnEs9w6tIps+OIyF+odEupcODCAV7d8CoA0/tMp75P/QI/9/bqt7P70d10qt6JyxmXGfTNIF7+9WVyjJziilsgR2OP8tW+r0jJTCHIPYjRzUfj6expaiYREREpf9yd3GkV1ArIPdotIqWLSreYLisni1HLRpGZk8mAegMY0mTITW8j0D2QX0f8yrPtnwVgyqYpPLD4AVIyU4o6boHsi97HooOLyMzJpLZXbUY2G1lqzzcXERGRsq9jcEesFiunE05zMv6k2XFE5E9UusV0b25+k13ndlHZuTIz+84s9KqbDlYH3u71Nl/d8xUOdg4s/mMxXb7sQnRSdBEnvjbDMFh/aj3LDi8jx8ihqV9THmr8EI5WxxLLICIiIhWPh5MHLQNbArDu1Dqd2y1Siqh0i6kOXjjI5A2TAfig9wcEugfe8jZHNBvBmhFr8HbxZkfUDtp93o7fz/9+y9u9kfSsdL4++LVtWldotVDuqX8PVjtrse9bREREpFP1Ttjb2XMm8QzH4o6ZHUdE/kelW0z15M9PkpGdQb+6/RjWtOiu+35HyB1se3gbdavUJSIhgvaft+fLvV8W2fb/KiYlhs92f8bh2MNYLVYG1BtAz9o9da1MERERKTHuTu60DWoLwNpTa3W0W6SUUOkW0xy4cIB1p9ZhtVj56K6Piryg3uZ9G2Fjw+hZuyepWamM/n40o5aNIjkjucj2YRgG+8/v57PdnxGbGou7ozujm4+mRUCLItuHiIiISEF1rN4RR6sj0UnRhMeEmx1HRFDpFhPN2DEDgHvq30N1z+rFsg9vF29+Hvozr3V9DTuLHV/t+4q2n7flwIUDt7ztxKxE5u+fz5JDS8jIziDEM4RHWz1KVY+qRZBcRERE5Oa5OrjSvlp7ANafWm/61VxERKVbTHI5/TJzfp8DwPg244t1X3YWO16+42XWjlhLYKVA/rj4B81nNueRHx7hTMKZm95eZk4m3A7fXvyW4/HHsVqsdKnRheFNh1PJsVIxvAIRERGRggutFoqzvTMXUy4WyYEGEbk1Kt1iinm/zyMpI4l6VerRtUbXEtln5xqd2fvYXu6pfw/ZRjaf7/mcOtPr8OwvzxZohfNDMYd4fvXz9F3TF7pDNtnUrFyTx1s/TueQzlowTUREREoFZ3tnOgZ3BHKPdmfnZJucSKRiszc7gFQ8hmHw8c6Pgdyj3CW52Jifmx9LH1jKljNbeOnXl9hwegPvbn2Xd7e+S32f+nQM7kiH4A6EeIZwIfkC55PPcyH5AutPrScsMuz/N3QZulTrwh1N79BiaSIiIlLqtK3alq2RW4lPi2dP9B5aB7U2O5JIhaXSLSVuU8QmDlw4gKuDKyOajTAlQ4fgDqwbuY7VJ1bzyrpX2HZ2G4diDnEo5hCz9sy66nOsFit31bmLzh6dmTRgEnVn1FXhFhERkVLJ0erIHSF38POxn1l/aj1N/ZviaHU0O5ZIhaTSLSUu7yj30CZDqexc2bQcFouFnrV70rN2T2JSYtgauZXNEZvZErmFmJQY/Nz88HPzw9/Nn9petRncaDCB7oHs3r0btCaJiIiIlHKtAlvZjnZvjdzKHSF3mB1JpEJS6ZYSFZ0UzXd/fAcU/wJqN8PH1Yd+dfvRr24/s6OIiIiIFAmrnZU7a97Jd+HfsfnMZloFtsLN0c3sWCIVjkq3lKhZu2eRmZNJaLVQmgc0v+qYiIgIYmJiSjbYTQgP1zUvRUREpGxo5NuILWe2cC7pHBsjNtLntj5mRxKpcFS6pcQYhsHnez4Hrn2UOyIigvoN6pOaklqS0QolKSnJ7AgiIiIi12WxWOheqztzf5/LzqidtK/aHi8XL7NjiVQoKt1SYvZf2M+pS6dwtndmUINBVx0TExNDakoqA18aiG+IbwknLJij246y7ot1pKWlmR1FRERE5IZqedWitldtjscfZ92pddf8OUxEiodKt5SYHw7/AED3Wt1xdXC97ljfEF8C6waWRKybFhNReqe+i4iIiFxNt5rdOB5/nP0X9hNaLZRA99L5c5ZIeWRndgCpOJYfWQ7AgLoDTE4iIiIiUrEEugfSxK8JAL8c/wXDMExOJFJxqHRLiYhOimb72e0AWiFcRERExATdanbD3s6e0wmnORR7yOw4IhWGSreUiBVHVgDQOqi1pjOJiIiImMDT2ZPQaqEArD6+muycbJMTiVQMKt1SIn44kns+t6aWi4iIiJinY3BHKjlWIj4t3jYLUUSKl0q3FLvUzFRWH18NQP96/U1OIyIiIlJxOdk70bVGVwA2nN5ASmaKyYlEyj+Vbil2a0+uJTUrlWCPYJr5NzM7joiIiEiF1jygOf5u/qRnp7P+1Hqz44iUeyrdUuzyVi3vV7cfFovF5DQiIiIiFZudxY5etXsBsDNqJxeTL5qcSKR8U+mWYmUYxv9fKqyezucWERERKQ1qetWkXpV6GBisPLZSlxATKUYq3VKsdp/bTdTlKNwc3OhSo4vZcURERETkf3rW7onVYuXEpRMcitElxESKi0q3FKu8o9w9a/fE2d7Z5DQiIiIiksfbxZsOwR0A+OX4L2RmZ5qcSKR8UumWYpVXuvvX1arlIiIiIqVNp+qd8HTyJCE9gU1nNpkdR6RcUumWYhOTEsPuc7sBuKvOXSanEREREZG/crA60LN2TwA2R2wmMSvR5EQi5Y9KtxSb307/BkAj30b4V/I3OY2IiIiIXE0DnwbUrFyTbCObsMQws+OIlDsq3VJsNpzeAMAdIXeYnERERERErsVisdDntj7YWew4nX4a6pidSKR8UemWYrPx9EYAOod0NjmJiIiIiFyPr5sv7aq2y/3iLkjNSjU3kEg5UqZK99SpU7FYLEyYMMF2n2EYTJ48maCgIFxcXOjSpQsHDx7M97z09HSefPJJfHx8cHNzY8CAAURGRuYbEx8fz/Dhw/H09MTT05Phw4dz6dKlEnhV5VNCWgJ7o/cC0Cmkk7lhREREROSGutTogpudG3jB50c/NzuOSLlRZkr3jh07+PTTT2natGm++6dNm8Y777zDhx9+yI4dOwgICKBHjx5cvnzZNmbChAksXbqURYsWsWnTJpKSkujXrx/Z2dm2MUOGDGHv3r2sXLmSlStXsnfvXoYPH15ir6+82RSxCQOD27xvI8g9yOw4IiIiInIDjlZHOnp2BGDu8bnsP7/f5EQi5UOZKN1JSUkMHTqUzz77DC8vL9v9hmHw3nvv8fLLLzNo0CAaN27MV199RUpKCgsWLAAgISGBWbNm8fbbb9O9e3datGjBvHnz2L9/P2vWrAEgPDyclStX8vnnnxMaGkpoaCifffYZP/74I4cPHzblNZd1mlouIiIiUvbUcK4B4ZBtZDPux3HkGDlmRxIp88pE6X7iiSfo27cv3bt3z3f/yZMniY6OpmfPnrb7nJyc6Ny5M1u2bAFg165dZGZm5hsTFBRE48aNbWPCwsLw9PSkXbt2tjHt27fH09PTNuZq0tPTSUxMzHeTXBsjcku3FlETERERKWN+BlerK2GRYXy26zOz04iUeaW+dC9atIjdu3czderUKx6Ljo4GwN8//+Wo/P39bY9FR0fj6OiY7wj51cb4+fldsX0/Pz/bmKuZOnWq7RxwT09PgoODb+7FlVPJGcnsjNoJqHSLiIiIlDmJML7+eAD+vubvRCdd++dhEbmxUl26z5w5w9NPP828efNwdna+5jiLxZLva8Mwrrjvr/465mrjb7SdF198kYSEBNvtzJkz191nRREWGUZWThbVPatTo3INs+OIiIiIyE0aXHMwrQJbkZCewFM/P2V2HJEyrVSX7l27dnHhwgVatWqFvb099vb2bNiwgQ8++AB7e3vbEe6/Ho2+cOGC7bGAgAAyMjKIj4+/7pjz589fsf+LFy9ecRT9z5ycnPDw8Mh3E9hwStfnFhERESnLrBYrn/X/DKvFyrd/fMuS8CVmRxIps0p16e7WrRv79+9n7969tlvr1q0ZOnQoe/fupVatWgQEBLB69WrbczIyMtiwYQMdOnQAoFWrVjg4OOQbc+7cOQ4cOGAbExoaSkJCAtu3b7eN2bZtGwkJCbYxUnB553NrETURERGRsqtFYAteuP0FAMavGE9sSqzJiUTKJnuzA1yPu7s7jRs3znefm5sbVapUsd0/YcIEpkyZQp06dahTpw5TpkzB1dWVIUOGAODp6cnYsWOZOHEiVapUwdvbm0mTJtGkSRPbwmwNGjSgd+/ePPLII3zyyScAPProo/Tr14969eqV4Csu+9Ky0tgWuQ3QkW4RERGRsu6fd/yTJeFLCI8J55lfnmHOwDlmRxIpc0r1ke6CeP7555kwYQLjx4+ndevWnD17llWrVuHu7m4b8+6773LPPfcwePBgOnbsiKurK8uXL8dqtdrGzJ8/nyZNmtCzZ0969uxJ06ZNmTt3rhkvqUzbfnY76dnp+Lv5U8e7jtlxREREROQWONk7Mfvu2dhZ7Jj7+1xWHFlhdiSRMqdUH+m+mvXr1+f72mKxMHnyZCZPnnzN5zg7OzN9+nSmT59+zTHe3t7MmzeviFJWXLbrc9fofMPF7ERERESk9GtXrR3Ptn+Wt8LeYtyP4zg4/iCezp5mxxIpM8r8kW4pXfJK9x3VNbVcREREpLz4d9d/U8e7Dmcvn+WZX54xO45ImaLSLUUmMzuTLWe2ALlHukVERESkfHBxcGHWgFlYsDB772y+P/S92ZFEygyVbiky+y/sJzkzmcrOlWno29DsOCIiIiJShDqFdGJi6EQAHln+CBeSL5icSKRsUOmWIrP9bO4l19pWbYudRR8tERERkfLmP3f+h8Z+jbmYcpFHlz+KYRhmRxIp9dSMpMhsO5t7qbC2QW1NTiIiIiIixcHZ3pl5A+fhYOfA94e/58u9X5odSaTUU+mWIvPnI90iIiIiUj41C2jGv7v+G4CnVz7NqUunzA0kUsqpdEuRSExPJPxiOKDSLSIiIlLePdfhOToGd+RyxmVGLB1Bdk622ZFESi2VbikSO6N2YmAQ4hmCfyV/s+OIiIiISDGy2lmZM3AO7o7u/BbxG6//9vr/tXfn8VXUh/rHn0lO9oSQkJ0kgBDWQPSChaBCgogLyCpCLRRvK7YVuCK2ttrbn9QqeLUXuVVRay0qsiMoUgVBAogYhACKEBYlhC0hBLLvy/z+QE4NawIJc07O5+0rL5I5cyZP+PpVHmbmO1ZHAhwWpRuNgkvLAQAAXMsNQTdozuA5kqQ/b/yzNh/ZbHEiwDFRutEozpXu3q17W5wEAAAA18u4HuM0vsd41Zq1euD9B5RXlmd1JMDhULrRKOwrl3OmGwAAwKW8es+r6hDcQUcLj2riRxN5jBhwHpvVAXB9HTlyRLm5uY16zJyyHJ0oOiF3w11uJ9204/SOqz5Wenp6IyYDAABAUwvwCtDCUQuV+Fai3k9/X2/ueFMP93zY6liAw6B0u5AjR46oc5fOKista9wDd5Y0VqrJqtGtvW9tlEMWFxc3ynEAAADQ9HpF9dLM22fqd2t/p0dXP6o+0X3UI7yH1bEAh0DpdiG5ubkqKy3TiKdGKLRNaKMdd2vhVn1d8rU6t+usfm/0u6ZjHdx6UCn/TFF5eXkjpQMAAMD1MC1xmtZnrNcn332i+5bcp+0Pb1cLrxZWxwIsR+l2QaFtQhXZMbLRjlfwdYEkKS4mTpGR13bc3CONe+k7AAAArg83w03zRszTTW/cpINnDuqhlQ9p8X2LZRiG1dEAS7GQGq5JrVmrE0UnJEmtA1pbnAYAAABWauXbSktGL5GHm4eW7l2qV756xepIgOUo3bgmuaW5qqyplIebh0L9Gu+SdQAAADinPtF99OIdL0qSHv/0cfujZQFXRenGNTledFySFBUQJTeDf50AAAAg/Vfv/9KoLqNUVVul0UtHK7eUWwjhumhJuCbHC8+Wbi4tBwAAwDmGYeitoW8pLjhORwqOaOyysaqurbY6FmAJSjeuybkz3a1bULoBAADwb4HegVoxZoX8PPz0WcZneuqzp6yOBFiC0o2rVlVTpZPFJyVxphsAAAAX6hbWTXOHzZUkvbjlRS3+drHFiYDrj9KNq5ZdnC1Tpvw8/HgGIwAAAC5qdLfReqLvE5KkX6z8hXaf3G1xIuD6onTjqmUVZ0k6u4gaz18EAADApTx3+3MaeMNAlVaVasTiETpTdsbqSMB1Y7M6AJzXudId4R9hcRIAAAA0pvT09EY/5pNxT2pv1l59n/e97vrnXXq598vycPO4qmOFhIQoNja2kRMCTYPSjauWXZwtSYoMiLQ4CQAAABpD8ZliSdK4ceOa5huESfqltC13m/r8uY+06uoO4+Pro33p+yjecAqUblyV6tpq5ZTkSJIi/SndAAAAzUF5cbkkKfmRZMUlxDXJ98gsz9SavDVSL6nvgL6K94tv0PtPZZ7SihkrlJubS+mGU6B046rklOSo1qyVj81HgV6BVscBAABAIwpqHaTIjk1zYiVSkao5WqN1h9bpy8IvdUObG9Q+uH2TfC/AEbCQGq7Kufu5I/0jWUQNAAAADdI3uq9uDL9Rpkwt3btUuaW5VkcCmgylG1clq+iHRdQCWEQNAAAADWMYhgZ3HKzYFrGqqKnQwm8XqqyqzOpYQJOgdOOq2BdR435uAAAAXAWbm033d7tfgV6BOlN2Rkv2LlFNbY3VsYBGR+lGg9XU1lC6AQAAcM38PP300/ifytPdU4fzD+uT7z6RaZpWxwIaFaUbDZZbmqsas0ae7p4K9gm2Og4AAACcWLh/uEZ1GSVJSstK01cnvrI4EdC4KN1oMBZRAwAAQGPq2KqjBt4wUJK05rs1+u7MdxYnAhoPpRsNdu7S8gh/FlEDAABA4+gb3Vc3Rvx7RfOTxSetjgQ0Cko3Gsx+pjuA+7kBAADQOAzD0JC4IWoT2EaVNZVa+O1CFVcWWx0LuGaUbjSIaZosogYAAIAm4e7mrjHdxijYJ1gFFQVa9O0iVdVUWR0LuCaUbjTImbIzqqyplM3NphDfEKvjAAAAoJnx8fDRA/EPyMfmo+NFx/XBvg9Y0RxOjdKNBjl3aXmEX4TcDP71AQAAQONr5dtKY7qNkZvhpr25e/VZxmdWRwKuGq0JDZJV9EPpDmARNQAAADSdNi3baGinoZKkL45+oZ1ZOy1OBFwdSjca5MePCwMAAACaUkJ4gvrF9pMkrTq4Shl5GRYnAhqO0o16M02T0g0AAIDrKqltkuJD41Vr1mrJ3iXKr863OhLQIJRu1FtBRYHKq8vlZrgpzC/M6jgAAABwAYZhaFjnYYpuEa3y6nKtPrNa8rU6FVB/lG7U27n7ucP9wuXu5m5xGgAAALgKm5tNY7uNVUvvliqsKZTGSJU1lVbHAuqF0o16s69c7s8iagAAALi+/Dz99ED8A/I0PKU20jNfP8OjxOAUKN2oN+7nBgAAgJVC/UI1MGigVCt9cvwT/WXTX6yOBFwRpRv1Ypqm/fLyyABKNwAAAKwR7RUtrTr7+dMbntaC3QusDQRcAaUb9VJcWaySqhIZMhTuF251HAAAALiyHdLP2/9ckvSfH/6nvjjyhcWBgEujdKNezl1aHuIbIg93D4vTAAAAwNVN6TJFwzsPV2VNpYYvHq5DeYesjgRcFKUb9cKl5QAAAHAkboab3hvxnnpG9lRuaa4GLxisvLI8q2MBF6B0o15YRA0AAACOxs/TTyt/ulLRLaK1L3ef7lt6n6pqqqyOBdRB6Ua9ULoBAADgiKICorTqp6vk7+mv9Rnr9Zt//YZHicGhULpxRSWVJSqsKJTEM7oBAADgeBIiErRo1CK5GW56a+db+t8v/9fqSIAdpRtXlF2cLUkK9gmWl83L4jQAAADAhQZ3HKzZd86WJP1+3e+19vu11gYCfkDpxhVxaTkAAACcweSfTNYvbvyFas1ajX1/rDLyMqyOBFC6cWXnznRTugEAAODIDMPQq4Nf1U9a/0Rnys5oxOIRKq0qtToWXBylG1dkP9PN48IAAADg4Lxt3nr//vcV5hemr09+rYdWPsTCarAUpRuXVV5drjNlZySxiBoAAACcQ3SLaC0dvVQ2N5sWfrtQs76cZXUkuDBKNy7r3KXlgV6B8vXwtTgNAAAAUD/92vTTS3e+JEl6Yt0TWndoncWJ4Koo3bgsLi0HAACAs5p08yQ9eOODZxdWWzZWh/MPWx0JLojSjcvKLmIRNQAAADgnwzD02uDX1Cuql06XnWZhNViC0o3L4nFhAAAAcGbeNm8tv3+5Qn1DtSt7lyZ+NJGF1XBdUbpxSZU1lcotzZXE5eUAAABwXjGBMVo6eqncDXct2L1As1NnWx0JLoTSjUs6WXxSpkz5e/rL39Pf6jgAAADAVevftr9m3Xl2FfPfrf2dthzdYnEiuAqb1QHguLi0HAAAAI4qPT29we+5xXaL7oy6U2tOrNHIBSM1v998BXkFNUE6KSQkRLGxsU1ybDgXSjcuKauIlcsBAADgWIrPFEuSxo0bd3UH8JT0sHQy5KQGvjpQWiCpCW7x9vH10b70fRRvULpxaSeKT0iSovyjLE4CAAAAnFVeXC5JSn4kWXEJcVd1jNNVp/VB7geqiavRzX+9WTf539SYEXUq85RWzFih3NxcSjco3bi4qpoqnSo5JYkz3QAAAHA8Qa2DFNnx6v6cGqlIVWZV6qMDH2l70XZ1vaGr2rZs27gBgR+wkBouKrs4W6ZM+Xn4KcAzwOo4AAAAQKO6KeImJYQnyJSp99PfV3FlsdWR0ExRunFR5xZRiwqIkmEYFqcBAAAAGpdhGLon7h6F+oaquLJYK9JXqNastToWmiFKNy6KRdQAAADQ3Hm6e2p019HycPPQofxD2pS5yepIaIYo3bgoFlEDAACAKwj1C9XgjoMlSRszN+pQ3iGLE6G5oXTjAiyiBgAAAFeSEJ6gmyLOrmC+PH25iiqKLE6E5oTSjQuwiBoAAABczd0d7la4X7hKqkr0fvr73N+NRkPpxgVYRA0AAACuxsPdQ6O7jpanu6cyCzK1MXOj1ZHQTFC6cQH7Imr+XFoOAAAA19HKt5WGdBwiSdqUuUkZeRkWJ0JzQOnGBeyLqAWwiBoAAABcS/ew7v++v3vfcp7fjWtG6UYdLKIGAAAAV3d3h7v//fzufStkmqbVkeDEKN2og0XUAAAA4Oo83D10X9f7ZHOz6VDeIW0+utnqSHBiDl26Z86cqZtvvlkBAQEKCwvT8OHDtX///jr7mKap6dOnKyoqSj4+PkpKStKePXvq7FNRUaEpU6YoJCREfn5+Gjp0qI4dO1Znn7y8PI0fP16BgYEKDAzU+PHjlZ+f39Q/osNhETUAAABACvML0z0d7pEkpWSk6EjBEYsTwVk5dOneuHGjJk2apNTUVK1du1bV1dUaNGiQSkpK7Pu88MILmjVrll555RVt27ZNERERuuOOO1RU9O9n602dOlUrVqzQokWLtHnzZhUXF2vIkCGqqamx7/PAAw9o165dWr16tVavXq1du3Zp/Pjx1/XndQQsogYAAACcdWPEjeoe1l2mTL2f/r5Kq0qtjgQnZLM6wOWsXr26ztdz585VWFiY0tLS1K9fP5mmqdmzZ+uPf/yjRo4cKUl65513FB4ergULFuhXv/qVCgoK9NZbb2nevHkaOHCgJOm9995TTEyM1q1bpzvvvFPp6elavXq1UlNT1bt3b0nSm2++qcTERO3fv1+dOnW6vj+4hVhEDQAAADjLMAwNjhus40XHdabsjD7c96HGxo/lilA0iEOf6T5fQUGBJCk4OFiSlJGRoezsbA0aNMi+j5eXl/r3768tW7ZIktLS0lRVVVVnn6ioKMXHx9v3+fLLLxUYGGgv3JLUp08fBQYG2ve5mIqKChUWFtb5cGYsogYAAADU5WXz0uiuo+VuuOvAmQNKPZ5qdSQ4Gacp3aZpatq0abr11lsVHx8vScrOzpYkhYeH19k3PDzc/lp2drY8PT0VFBR02X3CwsIu+J5hYWH2fS5m5syZ9nvAAwMDFRMTc/U/oANgETUAAADgQhH+Ebqz/Z2SpHWH1ul44XGLE8GZOE3pnjx5sr755hstXLjwgtfOv7zDNM0rXvJx/j4X2/9Kx3nyySdVUFBg/zh69OiVfgyHxiJqAAAAwMX1iuqlLiFdVGvWaln6MpVXl1sdCU7CKUr3lClTtHLlSqWkpCg6Otq+PSIiQpIuOBudk5NjP/sdERGhyspK5eXlXXafkydPXvB9T506dcFZ9B/z8vJSixYt6nw4s3N/Y8f93AAAAEBdhmFoaKehaundUvnl+fpo/0c8vxv14tCl2zRNTZ48WcuXL9f69evVrl27Oq+3a9dOERERWrt2rX1bZWWlNm7cqL59+0qSevbsKQ8Pjzr7ZGVl6dtvv7Xvk5iYqIKCAn311Vf2fbZu3aqCggL7Pq7gWNHZx6hFt4i+wp4AAACA6/G2eeu+LvfJzXDT3ty92p613epIcAIOvXr5pEmTtGDBAn344YcKCAiwn9EODAyUj4+PDMPQ1KlTNWPGDMXFxSkuLk4zZsyQr6+vHnjgAfu+v/zlL/X444+rVatWCg4O1m9/+1t1797dvpp5ly5ddNddd2nixIl64403JEkPP/ywhgwZ4jIrl5dWlepM2RlJUuuA1hanAQAAABxT6xatNbDdQH166FOt+W6NYlrEKMI/wupYcGAOfab7tddeU0FBgZKSkhQZGWn/WLx4sX2fJ554QlOnTtUjjzyiXr166fjx4/r0008VEPDvhcBeeuklDR8+XPfff79uueUW+fr66qOPPpK7u7t9n/nz56t79+4aNGiQBg0apB49emjevHnX9ee10rlLy1v5tJKPh4/FaQAAAADH1Se6jzoGd1SNWaNle5eporrC6khwYA59prs+90gYhqHp06dr+vTpl9zH29tbL7/8sl5++eVL7hMcHKz33nvvamI2C1xaDgAAANSPYRga1nmY3kh7Q6fLTuvjgx9reOfhLEaMi3LoM924fs6d6ebScgAAAODKfD18NarLKBky9E3ON9qZvdPqSHBQlG7INE0dLzpbujnTDQAAANRPbGCsktsmS5I+Pvixsouzr/AOuCJKN3S67LTKq8tlc7MpzC/M6jgAAACA07g19lbFBcepxqzRkj1LeH43LkDpho4Vnr2fO8o/Su5u7lfYGwAAAMA5hmFoROcRCvQKVF55nj7Y9wHP70YdlG7YLy1v3YL7uQEAAICG8vHw0f3d7pe74a79p/frm5JvrI4EB0Lphv1MN/dzAwAAAFcnKiBKd3W4S5L0VdFXUqzFgeAwKN0urqqmSieLT0qidAMAAADXomdkT3UP6y5TpjRayi3PtToSHACl28WdKDohU6YCPAPUwquF1XEAAAAAp2UYhoZ0HKIgW5AUIP1xxx9VXVttdSxYjNLt4o4VcWk5AAAA0Fg83T11R9AdUqW0/fR2PZ3ytNWRYDFKt4s7XvjDImoBLKIGAAAANIaWtpbSh2c/n7F5hlYdWGVpHliL0u3iWEQNAAAAaAJ7pDHtxkiSxq8Yr4y8DIsDwSqUbhdWWFGoosoiGTIUGRBpdRwAAACgWXms62Pq3bq38svzNXLJSJVWlVodCRagdLuwc2e5w/3D5enuaXEaAAAAoHnxcPPQ0tFLFeobql3Zu/SrVb+SaZpWx8J1Rul2YedKN/dzAwAAAE0jJjBGS0Yvkbvhrve+eU8vf/Wy1ZFwnVG6XVhmQaYkKTYw1uIkAAAAQPOV1DZJfx30V0nStDXTtClzk8WJcD1Rul1URXWFsoqyJEltAttYnAYAAABo3h7t/age6P6AaswajV462n7VKZo/SreLOlp4VKZMBXkHKdA70Oo4AAAAQLNmGIbevPdNJYQnKKckR/ctuU8V1RVWx8J1QOl2UYfzD0uS2rTkLDcAAABwPfh6+Gr5mOUK8g7S1uNbNeWTKVZHwnVA6XZRmfln7+fm0nIAAADg+rkh6AYtHLVQhgy9ueNN/T3t71ZHQhOjdLugqtoqnSg+IUlq27KttWEAAAAAF3Nnhzv13IDnJEmTP56s1GOpFidCU6J0u6CTVSdVa9Yq0CtQLb1bWh0HAAAAcDl/uPUPGtllpKpqqzRqyShlF2dbHQlNhNLtgrIqf1i1nPu5AQAAAEsYhqG3h72tLiFddKLohO5fer+qaqqsjoUmQOl2QVkVZ0t328C21gYBAAAAXFiAV4BWjFmhFl4t9PmRz/X4p49bHQlNgNLtamxSTlWOJM50AwAAAFbrFNJJ80bMkyS9/NXLevfrdy1OhMZG6XY10VKtahXgGaAg7yCr0wAAAAAub2inofp//f6fJOlXq36lHVk7LE6ExkTpdjVtf/ilZVsZhmFpFAAAAABnPZ30tAbHDVZ5dblGLB6h3NJcqyOhkVC6XU3bs7/wfG4AAADAcbgZbnpv5HvqENxBRwqOaMyyMaqurbY6FhoBpduFVNRUSNFnP+d+bgAAAMCxtPRuqRVjVsjPw0/rM9bribVPWB0JjYDS7UL25O+RbJKPm49a+bSyOg4AAACA88SHxevdEWcXU3sp9SXN+3qexYlwrSjdLiTtdJokKdIzkvu5AQAAAAc1sstI/fdt/y1JmvjRRG0/sd3iRLgWlG4XsuP02VUQozyjLE4CAAAA4HL+nPxnDek4RBU1FRqxeIROFp+0OhKuEqXbhfwp4U/SCinWO9bqKAAAAAAuw81w03sj3lOnVp10rPCYRi8draqaKqtj4SpQul1IlG+U9LXk7+5vdRQAAAAAVxDoHagPxn6gAM8AfX7kcz225jGrI+Eq2KwOAAAAAADNUXp6eqMc55mEZzRt2zS9uu1VBVcGa3js8EY5bkhIiGJjuQq2qVG6AQAAAKARFZ8pliSNGzeu8Q7aT9IA6S9pf9FfpvxFOnbth/Tx9dG+9H0U7yZG6QYAAACARlReXC5JSn4kWXEJcY1yTNM0tTZ/rQ6XH5bvw74aETJCfu5+V328U5mntGLGCuXm5lK6mxilGwAAAACaQFDrIEV2jGy0442tHqu3dr6lU6WntLFsoybcOEE2Nyqdo2MhNQAAAABwAl42L42NHytvm7eOFR3Txwc/lmmaVsfCFVC6AQAAAMBJBPsEa1SXUTJkaGf2Tm3P2m51JFwBpRsAAAAAnEiH4A66vd3tkqTV361WZn6mxYlwOZRuAAAAAHAyfWP6Kj40XrVmrZbsXaKC8gKrI+ESKN0AAAAA4GQMw9DQTkMV4R+h0qpSLd6zWFU1VVbHwkVQugEAAADACXm4e2hMtzHysfkoqzhLqw6sYmE1B0TpBgAAAAAn1dK7pUZ3HS1Dhr7J+UZbj2+1OhLOQ+kGAAAAACfWLqidBrUfJEn69PtPdSjvkMWJ8GOUbgAAAABwcr1b91ZCeIJMmVq2d5nyyvKsjoQfULoBAAAAwMkZhqEhHYcoKiBKZdVlWrxnsSprKq2OBVG6AQAAAKBZsLnZNKbbGPl5+OlkyUktT1+uWrPW6lguj9INAAAAAM1EC68WGtNtjNwNd+0/vV9rD621OpLLo3QDAAAAQDMSExij4Z2HS5JSj6Uq7USatYFcHKUbAAAAAJqZ+LB4JbVNkiT96+C/WNHcQpRuAAAAAGiG+sX2U4+wHjJlasmeJcopybE6kkuidAMAAABAM2QYhu7tdK9iW8SqoqZC83fPV0F5gdWxXA6lGwAAAACaKZubTWPjxyrEN0SFFYWav3u+yqrKrI7lUijdAAAAANCM+Xj4aFz3cQrwDNCp0lNatGeRqs1qq2O5DEo3AAAAADRzgd6B+ln3n8nL3UtHCo5off56ybA6lWugdAMAAACACwj3D9fY+LFyN9x1uPywNFSqNWutjtXsUboBAAAAwEW0bdlWo7qMkiFDukn6n93/I9M0rY7VrFG6AQAAAMCFdAntouSWyZIpLctcpmlrplG8mxClGwAAAABcTAefDtLKs5/P3jpbT332FMW7iVC6AQAAAMAV7ZR+3/33kqTnv3heT372JMW7CVC6AQAAAMBF3d/2fs0aNEuS9D9f/I8e+dcjLK7WyCjdAAAAAODCHkt8TG8MeUOGDL2e9rrGrxivqpoqq2M1G5RuAAAAAHBxD/d8WAtGLZDNzaYFuxdo5JKRKqsqszpWs0DpBgAAAABobPxYfTj2Q3nbvLXqwColv5OsrKIsq2M5PUo3AAAAAECSdE/cPVozbo2CvIO09fhW/eQfP9GOrB1Wx3JqlG4AAAAAgF2/Nv301cSv1Dmks44VHtOt/7xVS/cstTqW06J0AwAAAADq6BDcQam/TNVdHe5SWXWZ7l92v/6w7g8ssHYVKN0AAAAAgAsEegfqo59+pKm9p0o6+0ixW/55i7478521wZwMpRsAAAAAcFE2N5teuuslLRu9TEHeQdp2YptufP1Gvb3rbZmmaXU8p0DpBgAAAABc1qiuo/T1r79WUtsklVSV6D8//E8NXzxch/MPWx3N4VG6AQAAAABXFBMYo3Xj12nGgBmyudm0cv9KdX21q57b9JwqqiusjuewKN0AAAAAgHpxd3PXk7c9qV2/2qX+bfqrrLpM/53y3+rxeg+tOrCKS84vgtINAAAAAGiQbmHdlDIhRfNHzleEf4QOnD6gexfeq5/84yeU7/NQugEAAAAADWYYhh7o/oD2TdqnJ/o+IV8PX20/sV33LrxXN795s5btXcYjxiTZrA4AAAAAALBGenp6oxxnTKsxGpQ8SPO+n6clh5coLStNo5eOViuvVhoaM1Qj2oxQa9/WDTpmSEiIYmNjGyWflSjdAAAAAOBiis8US5LGjRvX+Af3ldRH0n9Ip3Vac7+bq7nfzZUyJKVL2i+p4MqH8fH10b70fU5fvCnd55kzZ45efPFFZWVlqVu3bpo9e7Zuu+02q2MBAAAAQKMpLy6XJCU/kqy4hLgm+R61Zq0Olx/WvtJ9OlZ5TGqnsx/3SCG2EMV6xyrSM1JhHmHycPOo895Tmae0YsYK5ebmUrqbk8WLF2vq1KmaM2eObrnlFr3xxhu6++67tXfvXqcfaAAAAAA4X1DrIEV2jGyy47dWa92iW5Rfnq/0U+nad3qfjhYcVW51rnKLcyVJboabIv0jFd0iWuH+4Qr3C1eQGdRkma43SvePzJo1S7/85S/10EMPSZJmz56tNWvW6LXXXtPMmTMtTgcAAAAAzqmld0slxiQqMSZRJZUlOnD6gDLyM3Sk4IgKKgp0vOi4jhcdt+9vyJAmS5+f/Fz/of+wMPm1o3T/oLKyUmlpafrDH/5QZ/ugQYO0ZcsWi1IBAAAAQPPi5+mnmyJv0k2RN0mS8svzdaTgiE4UnVBOSY5OlpxUaVWpFCJ5uXtZnPbaUbp/kJubq5qaGoWHh9fZHh4eruzs7Iu+p6KiQhUVFfavCwrOrgZQWFjYdEGvQXHx2cUSThw4ocqySovTXNypzFNnf804pUy/TIvTXBwZr52j55PI2FgcPaOj55PI2FgcPaOj55PI2FgcPaOj55PI2FgcLWPgD/90UReZvqaOHzuude+vU9vb2jpsvzqX60rPJDdMnlouSTpx4oRat26tLVu2KDEx0b79ueee07x587Rv374L3jN9+nT9+c9/vp4xAQAAAAAO5OjRo4qOjr7k65zp/kFISIjc3d0vOKudk5Nzwdnvc5588klNmzbN/nVtba3OnDmjVq1ayTCMJs1rtcLCQsXExOjo0aNq0aKF1XHQCBjT5ocxbV4Yz+aHMW1+GNPmhzFtXhp7PE3TVFFRkaKioi67H6X7B56enurZs6fWrl2rESNG2LevXbtWw4YNu+h7vLy85OVV9x6Dli1bNmVMh9OiRQv+A9TMMKbND2PavDCezQ9j2vwwps0PY9q8NOZ4BgYGXnEfSvePTJs2TePHj1evXr2UmJiov//97zpy5Ih+/etfWx0NAAAAAOCEKN0/MmbMGJ0+fVrPPPOMsrKyFB8fr48//lht2rSxOhoAAAAAwAlRus/zyCOP6JFHHrE6hsPz8vLS008/fcHl9XBejGnzw5g2L4xn88OYNj+MafPDmDYvVo0nq5cDAAAAANBE3KwOAAAAAABAc0XpBgAAAACgiVC6AQAAAABoIpRuXNL06dNlGEadj4iICPvrpmlq+vTpioqKko+Pj5KSkrRnzx4LE+N8mzZt0r333quoqCgZhqEPPvigzuv1GcOKigpNmTJFISEh8vPz09ChQ3Xs2LHr+FPgx640pg8++OAF87ZPnz519mFMHcfMmTN18803KyAgQGFhYRo+fLj2799fZx/mqXOpz5gyT53La6+9ph49etif65uYmKhPPvnE/jpz1LlcaTyZn85v5syZMgxDU6dOtW+zep5SunFZ3bp1U1ZWlv1j9+7d9tdeeOEFzZo1S6+88oq2bdumiIgI3XHHHSoqKrIwMX6spKRECQkJeuWVVy76en3GcOrUqVqxYoUWLVqkzZs3q7i4WEOGDFFNTc31+jHwI1caU0m666676szbjz/+uM7rjKnj2LhxoyZNmqTU1FStXbtW1dXVGjRokEpKSuz7ME+dS33GVGKeOpPo6Gg9//zz2r59u7Zv364BAwZo2LBh9j+wM0edy5XGU2J+OrNt27bp73//u3r06FFnu+Xz1AQu4emnnzYTEhIu+lptba0ZERFhPv/88/Zt5eXlZmBgoPn6669fp4RoCEnmihUr7F/XZwzz8/NNDw8Pc9GiRfZ9jh8/brq5uZmrV6++btlxceePqWma5oQJE8xhw4Zd8j2MqWPLyckxJZkbN240TZN52hycP6amyTxtDoKCgsx//OMfzNFm4tx4mibz05kVFRWZcXFx5tq1a83+/fubjz76qGmajvH/Us5047IOHjyoqKgotWvXTmPHjtWhQ4ckSRkZGcrOztagQYPs+3p5eal///7asmWLVXHRAPUZw7S0NFVVVdXZJyoqSvHx8YyzA9uwYYPCwsLUsWNHTZw4UTk5OfbXGFPHVlBQIEkKDg6WxDxtDs4f03OYp86ppqZGixYtUklJiRITE5mjTu788TyH+emcJk2apMGDB2vgwIF1tjvCPLVd8xHQbPXu3VvvvvuuOnbsqJMnT+rZZ59V3759tWfPHmVnZ0uSwsPD67wnPDxcmZmZVsRFA9VnDLOzs+Xp6amgoKAL9jn3fjiWu+++W6NHj1abNm2UkZGhP/3pTxowYIDS0tLk5eXFmDow0zQ1bdo03XrrrYqPj5fEPHV2FxtTiXnqjHbv3q3ExESVl5fL399fK1asUNeuXe1/GGeOOpdLjafE/HRWixYt0o4dO7Rt27YLXnOE/5dSunFJd999t/3z7t27KzExUe3bt9c777xjX1DCMIw67zFN84JtcGxXM4aMs+MaM2aM/fP4+Hj16tVLbdq00b/+9S+NHDnyku9jTK03efJkffPNN9q8efMFrzFPndOlxpR56nw6deqkXbt2KT8/X++//74mTJigjRs32l9njjqXS41n165dmZ9O6OjRo3r00Uf16aefytvb+5L7WTlPubwc9ebn56fu3bvr4MGD9lXMz/+bn5ycnAv+FgmOqT5jGBERocrKSuXl5V1yHzi2yMhItWnTRgcPHpTEmDqqKVOmaOXKlUpJSVF0dLR9O/PUeV1qTC+Geer4PD091aFDB/Xq1UszZ85UQkKC/u///o856qQuNZ4Xw/x0fGlpacrJyVHPnj1ls9lks9m0ceNG/e1vf5PNZrOPi5XzlNKNequoqFB6eroiIyPVrl07RUREaO3atfbXKysrtXHjRvXt29fClKiv+oxhz5495eHhUWefrKwsffvtt4yzkzh9+rSOHj2qyMhISYypozFNU5MnT9by5cu1fv16tWvXrs7rzFPnc6UxvRjmqfMxTVMVFRXM0Wbi3HheDPPT8d1+++3avXu3du3aZf/o1auXfvazn2nXrl264YYbrJ+n17wUG5qtxx9/3NywYYN56NAhMzU11RwyZIgZEBBgHj582DRN03z++efNwMBAc/ny5ebu3bvNn/70p2ZkZKRZWFhocXKcU1RUZO7cudPcuXOnKcmcNWuWuXPnTjMzM9M0zfqN4a9//WszOjraXLdunbljxw5zwIABZkJCglldXW3Vj+XSLjemRUVF5uOPP25u2bLFzMjIMFNSUszExESzdevWjKmD+s1vfmMGBgaaGzZsMLOysuwfpaWl9n2Yp87lSmPKPHU+Tz75pLlp0yYzIyPD/Oabb8ynnnrKdHNzMz/99FPTNJmjzuZy48n8bD5+vHq5aVo/TynduKQxY8aYkZGRpoeHhxkVFWWOHDnS3LNnj/312tpa8+mnnzYjIiJMLy8vs1+/fubu3bstTIzzpaSkmJIu+JgwYYJpmvUbw7KyMnPy5MlmcHCw6ePjYw4ZMsQ8cuSIBT8NTPPyY1paWmoOGjTIDA0NNT08PMzY2FhzwoQJF4wXY+o4LjaWksy5c+fa92GeOpcrjSnz1Pn84he/MNu0aWN6enqaoaGh5u23324v3KbJHHU2lxtP5mfzcX7ptnqeGqZpmtd+vhwAAAAAAJyPe7oBAAAAAGgilG4AAAAAAJoIpRsAAAAAgCZC6QYAAAAAoIlQugEAAAAAaCKUbgAAAAAAmgilGwAAAACAJkLpBgAAAACgiVC6AQDAFbVt21azZ8+2OgYAAE6H0g0AgIN7/fXXFRAQoOrqavu24uJieXh46Lbbbquz7+effy7DMHTgwIHrHVOFhYX64x//qM6dO8vb21sREREaOHCgli9fLtM0r2sW/pIAAOAobFYHAAAAl5ecnKzi4mJt375dffr0kXS2XEdERGjbtm0qLS2Vr6+vJGnDhg2KiopSx44dG/x9ampqZBiG3Nwa/nfy+fn5uvXWW1VQUKBnn31WN998s2w2mzZu3KgnnnhCAwYMUMuWLRt8XAAAnB1nugEAcHCdOnVSVFSUNmzYYN+2YcMGDRs2TO3bt9eWLVvqbE9OTpYk5eXl6ec//7mCgoLk6+uru+++WwcPHrTv+/bbb6tly5ZatWqVunbtKi8vL2VmZionJ0f33nuvfHx81K5dO82fP/+KGZ966ikdPnxYW7du1YQJE9S1a1d17NhREydO1K5du+Tv71+vTNOnT9eNN95Y59izZ89W27Zt7V8/+OCDGj58uP76178qMjJSrVq10qRJk1RVVSVJSkpKUmZmph577DEZhiHDMOr9ew0AQGOjdAMA4ASSkpKUkpJi/zolJUVJSUnq37+/fXtlZaW+/PJLe+l+8MEHtX37dq1cuVJffvmlTNPUPffcYy+nklRaWqqZM2fqH//4h/bs2aOwsDA9+OCDOnz4sNavX69ly5Zpzpw5ysnJuWS22tpaLVq0SD/72c8UFRV1wev+/v6y2Wz1zlQfKSkp+v7775WSkqJ33nlHb7/9tt5++21J0vLlyxUdHa1nnnlGWVlZysrKatCxAQBoTFxeDgCAE0hKStJjjz2m6upqlZWVaefOnerXr59qamr0t7/9TZKUmpqqsrIyJScn6+DBg1q5cqW++OIL9e3bV5I0f/58xcTE6IMPPtDo0aMlSVVVVZozZ44SEhIkSQcOHNAnn3yi1NRU9e7dW5L01ltvqUuXLpfMlpubq7y8PHXu3PmyP0N9M9VHUFCQXnnlFbm7u6tz584aPHiwPvvsM02cOFHBwcFyd3dXQECAIiIi6n1MAACaAme6AQBwAsnJySopKdG2bdv0+eefq2PHjgoLC1P//v21bds2lZSUaMOGDYqNjdUNN9yg9PR02Ww2e3GWpFatWqlTp05KT0+3b/P09FSPHj3sX597X69evezbOnfufNn7sc8tknaly7jrm6k+unXrJnd3d/vXkZGRlz0bDwCAVTjTDQCAE+jQoYOio6OVkpKivLw89e/fX5IUERGhdu3a6YsvvlBKSooGDBggSZdcLdw0zTrl2MfHp87X9S3QPxYaGqqgoKArFuf6ZHJzc7tgv4tdeu7h4VHna8MwVFtbW+/MAABcL5zpBgDASSQnJ2vDhg3asGGDkpKS7Nv79++vNWvWKDU11X4/d9euXVVdXa2tW7fa9zt9+rQOHDhw2UvFu3Tpourqam3fvt2+bf/+/crPz7/ke9zc3DRmzBjNnz9fJ06cuOD1kpISVVdX1ytTaGiosrOz6xTvXbt2XfJ7X4qnp6dqamoa/D4AABobpRsAACeRnJyszZs3a9euXfYz3dLZ0v3mm2+qvLzcXrrj4uI0bNgwTZw4UZs3b9bXX3+tcePGqXXr1ho2bNglv0enTp101113aeLEidq6davS0tL00EMPycfH57LZZsyYoZiYGPXu3Vvvvvuu9u7dq4MHD+qf//ynbrzxRhUXF9crU1JSkk6dOqUXXnhB33//vV599VV98sknDf69atu2rTZt2qTjx48rNze3we8HAKCxULoBAHASycnJKisrU4cOHRQeHm7f3r9/fxUVFal9+/aKiYmxb587d6569uypIUOGKDExUaZp6uOPP77g0uzzzZ07VzExMerfv79Gjhyphx9+WGFhYZd9T1BQkFJTUzVu3Dg9++yzuummm3Tbbbdp4cKFevHFFxUYGFivTF26dNGcOXP06quvKiEhQV999ZV++9vfNvj36plnntHhw4fVvn17hYaGNvj9AAA0FsO81A1WAAAAAADgmnCmGwAAAACAJkLpBgAAAACgiVC6AQAAAABoIpRuAAAAAACaCKUbAAAAAIAmQukGAAAAAKCJULoBAAAAAGgilG4AAAAAAJoIpRsAAAAAgCZC6QYAAAAAoIlQugEAAAAAaCKUbgAAAAAAmsj/B+ysYnUwNQbzAAAAAElFTkSuQmCC", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.figure(figsize=(10, 6))\n", "sns.histplot(data['abstract_length'], bins=20, kde=True, color='green')\n", "plt.title('Distribution of Abstract Lengths (Word Count)')\n", "plt.xlabel('Word Count')\n", "plt.ylabel('Frequency')\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 19, "id": "51e5dd33-2209-47ee-ae53-b8b539de0b68", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Statistics of Abstract Lengths (in words):\n"]}, {"data": {"text/plain": ["count    119924.000000\n", "mean        202.235407\n", "std          78.226149\n", "min          42.000000\n", "25%         142.000000\n", "50%         208.000000\n", "75%         262.000000\n", "max         391.000000\n", "Name: abstract_length, dtype: float64"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["data['article_length'] = data['article'].apply(lambda x: len(str(x).split()))\n", "print(\"\\nStatistics of Abstract Lengths (in words):\")\n", "data['abstract_length'].describe()"]}, {"cell_type": "code", "execution_count": 20, "id": "12d9f86a-34b3-4b07-9875-60a4cee71d9d", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.figure(figsize=(10, 6))\n", "sns.histplot(data['article_length'], bins=20, kde=True, color='green')\n", "plt.title('Distribution of Abstract Lengths (Word Count)')\n", "plt.xlabel('Word Count')\n", "plt.ylabel('Frequency')\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "bc149a8c-9bce-4cab-89f5-246ae571d2b5", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.2"}}, "nbformat": 4, "nbformat_minor": 5}