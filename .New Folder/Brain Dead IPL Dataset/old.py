from unsloth import FastLanguageModel, get_chat_template
from datasets import load_dataset
from trl import SFTTrainer
from transformers import TrainingArguments, DataCollatorForLanguageModeling
from unsloth.chat_templates import train_on_responses_only
import torch

# Load model
max_seq_length = 2048
model, tokenizer = FastLanguageModel.from_pretrained(
    model_name="unsloth/Llama-3.2-3B-Instruct",
    max_seq_length=max_seq_length,
    dtype=None,
    load_in_4bit=True,
)

# Configure LoRA
model = FastLanguageModel.get_peft_model(
    model,
    r=16,
    target_modules=["q_proj", "k_proj", "v_proj", "o_proj", "gate_proj", "up_proj", "down_proj"],
    lora_alpha=16,
    lora_dropout=0,
    bias="none",
    use_gradient_checkpointing="unsloth",
    random_state=3407,
)

# Load dataset
dataset = load_dataset('csv', data_files='Brain_Dead_CompScholar_Dataset.csv', split='train')

# Add conversations format
def add_conversations(example):
    return {
        "conversations": [
            {"role": "user", "content": f"Summarize the following research article:\n\n{example['Document']}"},
            {"role": "assistant", "content": example['Summary']}
        ]
    }
dataset = dataset.map(add_conversations)

# Apply chat template
tokenizer = get_chat_template(tokenizer, chat_template="llama-3.1")
def formatting_prompts_func(examples):
    convos = examples["conversations"]
    texts = [tokenizer.apply_chat_template(convo, tokenize=False, add_generation_prompt=False) for convo in convos]
    return {"text": texts}
dataset = dataset.map(formatting_prompts_func, batched=True)

# Remove unnecessary columns (keep only text)
dataset = dataset.remove_columns([
    "conversations", "Paper Id", "Paper Title", "Key Words", "Abstract", 
    "Conclusion", "Document", "Paper Type", "Summary", "Topic", "OCR", "labels"
])

# Set up trainer with fixes
trainer = SFTTrainer(
    model=model,
    tokenizer=tokenizer,
    train_dataset=dataset,
    dataset_text_field="text",  # Critical fix!
    max_seq_length=max_seq_length,
    data_collator=DataCollatorForLanguageModeling(tokenizer=tokenizer, mlm=False),
    dataset_num_proc=2,
    packing=False,
    args=TrainingArguments(
        per_device_train_batch_size=2,
        gradient_accumulation_steps=4,
        warmup_steps=5,
        max_steps=60,
        learning_rate=2e-4,
        fp16=not torch.cuda.is_bf16_supported(),
        bf16=torch.cuda.is_bf16_supported(),
        logging_steps=1,
        optim="adamw_8bit",
        weight_decay=0.01,
        lr_scheduler_type="linear",
        seed=3407,
        output_dir="outputs",
        report_to="none",
    ),
)

# Apply training on responses only
trainer = train_on_responses_only(
    trainer,
    instruction_part="<|start_header_id|>user<|end_header_id|>\n\n",
    response_part="<|start_header_id|>assistant<|end_header_id|>\n\n",
)

# Train the model
trainer.train()

# Inference example
FastLanguageModel.for_inference(model)
messages = [
    {"role": "user", "content": "Summarize the following research article:\n\n[Your research article text here]"},
]
inputs = tokenizer.apply_chat_template(messages, tokenize=True, add_generation_prompt=True, return_tensors="pt").to("cuda")
outputs = model.generate(input_ids=inputs, max_new_tokens=256, use_cache=True)
print(tokenizer.batch_decode(outputs)[0])