# %%
%pip install matplotlib seaborn

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import warnings

# %%
warnings.filterwarnings("ignore")

plt.style.use('fivethirtyeight')
sns.set(style="whitegrid")

# %%
data = pd.read_csv('Brain_Dead_CompScholar_Dataset.csv')
data.head()

# %%
column_names = data.columns
print(column_names)

# %%
data.info()

# %%
paper_type_counts = data['Paper Type'].value_counts()
print("\nFrequency of each Paper Type:")
paper_type_counts

# %%
plt.figure(figsize=(10, 6))
paper_type_counts.plot(kind='bar', color='skyblue')
plt.title('Distribution of Paper Types')
plt.xlabel('Paper Type')
plt.ylabel('Count')
plt.xticks(rotation=45)
plt.tight_layout()
plt.show()

# %%
topic_counts = data['Topic'].value_counts()
print("\nFrequency of each Topic:")
topic_counts

# %%
plt.figure(figsize=(10, 6))
topic_counts.plot(kind='bar', color='salmon')
plt.title('Distribution of Topics')
plt.xlabel('Topic')
plt.ylabel('Count')
plt.xticks(rotation=45)
plt.tight_layout()
plt.show()

# %%
data['abstract_length'] = data['Abstract'].apply(lambda x: len(str(x).split()))
print("\nStatistics of Abstract Lengths (in words):")
data['abstract_length'].describe()

# %%
plt.figure(figsize=(10, 6))
sns.histplot(data['abstract_length'], bins=20, kde=True, color='green')
plt.title('Distribution of Abstract Lengths (Word Count)')
plt.xlabel('Word Count')
plt.ylabel('Frequency')
plt.tight_layout()
plt.show()

# %%
from unsloth import FastLanguageModel, get_chat_template
from datasets import load_dataset
from trl import SFTTrainer
from transformers import TrainingArguments, DataCollatorForLanguageModeling
from unsloth.chat_templates import train_on_responses_only
import torch
import numpy as np

# %%
# Load model
max_seq_length = 2048
model, tokenizer = FastLanguageModel.from_pretrained(
    model_name="unsloth/Meta-Llama-3.1-8B-bnb-4bit",
    max_seq_length=max_seq_length,
    dtype=None,
    load_in_4bit=True,
)

# %%
model = FastLanguageModel.get_peft_model(
    model,
    r=16,
    target_modules=["q_proj", "k_proj", "v_proj", "o_proj", "gate_proj", "up_proj", "down_proj"],
    lora_alpha=16,
    lora_dropout=0,
    bias="none",
    use_gradient_checkpointing="unsloth",
    random_state=3407,
)

# %%
# Load dataset
dataset = load_dataset('csv', data_files='Brain_Dead_CompScholar_Dataset.csv', split='train')
dataset

# %%
# Add conversations
def add_conversations(example):
    return {
        "conversations": [
            {"role": "user", "content": f"Summarize the following research article:\n\n{example['Document']}"},
            {"role": "assistant", "content": example['Summary']}
        ]
    }
dataset = dataset.map(add_conversations)
dataset

# %%
# Apply chat template
tokenizer = get_chat_template(tokenizer, chat_template="llama-3.1")
def formatting_prompts_func(examples):
    convos = examples["conversations"]
    texts = [tokenizer.apply_chat_template(convo, tokenize=False, add_generation_prompt=False) for convo in convos]
    return {"text": texts}
dataset = dataset.map(formatting_prompts_func, batched=True)
dataset

# %%
'''# Remove unnecessary columns (keep only text)
dataset = dataset.remove_columns([
    "conversations", "Paper Id", "Paper Title", "Key Words", "Abstract", 
    "Conclusion", "Document", "Paper Type", "Summary", "Topic", "OCR", "labels"
])'''
dataset

# %%
# Set up trainer
trainer = SFTTrainer(
    model=model,
    tokenizer=tokenizer,
    train_dataset=dataset,
    dataset_text_field="text",  # ✅ Critical fix
    max_seq_length=max_seq_length,
    data_collator=DataCollatorForLanguageModeling(tokenizer=tokenizer, mlm=False),
    dataset_num_proc=2,
    packing=False,
    args=TrainingArguments(
        per_device_train_batch_size=4,
        gradient_accumulation_steps=2,
        warmup_steps=5,
        max_steps=60,
        learning_rate=2e-4,
        fp16=not torch.cuda.is_bf16_supported(),
        bf16=torch.cuda.is_bf16_supported(),
        logging_steps=1,
        optim="adamw_8bit",
        weight_decay=0.01,
        lr_scheduler_type="linear",
        seed=3407,
        output_dir="outputs",
        report_to="none",
    ),
)

# %%
# Apply training on responses only
trainer = train_on_responses_only(
    trainer,
    instruction_part="<|start_header_id|>user<|end_header_id|>\n\n",
    response_part="<|start_header_id|>assistant<|end_header_id|>\n\n",
)

# %%
# Train
trainer.train()

# %%
# Inference
FastLanguageModel.for_inference(model)
messages = [
    {"role": "user", "content": '''Summarize the following research article:\n\n[NLP based Machine Learning Approaches for Text 
SummarizationText summarization, Abstractive and extractive summaries, Query-based summarization, Structured-based and semantic-based approaches, Evaluation metrics (ROGUE score and TF_IDF score), Future directions (GANs and transfer learning),Data abundance and information overloadDue to the plethora of data available today, text 
summarization has become very essential to gain just the right 
amount of information from huge texts. We see long articles in 
news websites, blogs, customers’ review websites, and so on. This 
review paper presents various approaches to generate summary 
of huge texts. Various papers have been studied for different 
methods that have been used so far for text summarization. 
Mostly, the methods described in this paper produce Abstractive 
(ABS) or Extractive (EXT) summaries of text documents. Querybased summarization techniques are also discussed. The paper 
mostly discusses about the structured based and semantic based 
approaches for summarization of the text documents. Various 
datasets were used to test the summaries produced by these 
models, such as the CNN corpus, DUC2000, single and multiple 
text documents etc. We have studied these methods and also the 
tendencies, achievements, past work and future scope of them in 
text summarization as well as other fieldsWe have seen that due to abundant availability of data, text 
summarization has a very vital role in saving user’s time, as 
well as resources. Text summarization is indeed an important 
tool for today. We have seen the use of various algorithms and 
methods for this purpose. These methods, in individual and 
together give different types of summaries. Their accuracy 
score can be compared to find the better and more concise 
summaries. For this purpose, ROGUE score has been used 
more frequently. Similarly, in some cases TF_IDF scores have 
been used too. 
The summaries generated using these methods are not 
always up to the mark. Sometimes, it’s also irrelevant to the 
original document. Therefore, this topic is ongoing and people 
have done various works on this. There isn’t any specific 
model that generates best summaries. So, for future, the models 
discussed can be modified for more accurate summaries. For 
e.g., we could use GAN’s and transfer learning. For future, this 
can give a way to develop and enhance further ideas for text 
summarization. @STOM © Word Vector Embedding kenearest neighbor algorithm © Differential Evolution Algorithm Newtonian Method Artificial Bee Colony Human Learning Algorithm ]'''},
]
inputs = tokenizer.apply_chat_template(messages, tokenize=True, add_generation_prompt=True, return_tensors="pt").to("cuda")
outputs = model.generate(input_ids=inputs, max_new_tokens=256, use_cache=True)
print(tokenizer.batch_decode(outputs)[0])

# %%
def generate_summary(text, model, tokenizer, max_new_tokens=256):
    """
    Generate a summary for a research article using the fine-tuned model.
    
    Args:
        text (str): Input research article text to summarize
        model: Fine-tuned Unsloth model
        tokenizer: Model tokenizer
        max_new_tokens (int): Maximum length of summary to generate
    
    Returns:
        str: Generated summary
    """
    # Format the input using the same chat template as training
    messages = [
        {"role": "user", "content": f"Summarize the following research article:\n\n{text}"},
        {"role": "assistant", "content": ""},
    ]
    
    # Tokenize and prepare inputs
    inputs = tokenizer.apply_chat_template(
        messages,
        tokenize=True,
        add_generation_prompt=True,
        return_tensors="pt",
    ).to("cuda")
    
    # Generate summary
    with torch.inference_mode():
        outputs = model.generate(
            input_ids=inputs,
            max_new_tokens=max_new_tokens,
            pad_token_id=tokenizer.pad_token_id,
            temperature=0.7,
            repetition_penalty=1.2,
            use_cache=True,
        )
    
    # Decode and clean output
    full_response = tokenizer.batch_decode(outputs, skip_special_tokens=False)[0]
    
    # Extract only the assistant's response
    try:
        response_start = full_response.rfind("<|start_header_id|>assistant<|end_header_id|>") + len("<|start_header_id|>assistant<|end_header_id|>")
        response = full_response[response_start:].split("<|eot_id|>")[0].strip()
    except:
        response = full_response.split("assistant\n\n")[-1].split("<|eot_id|>")[0].strip()
    
    return response

# %%
research_article = """
Multi-document summarization (MDS) is an effective tool for information aggregation...
NLP based Machine Learning Approaches for Text 
SummarizationText summarization, Abstractive and extractive summaries, Query-based summarization, Structured-based and semantic-based approaches, Evaluation metrics (ROGUE score and TF_IDF score), Future directions (GANs and transfer learning),Data abundance and information overloadDue to the plethora of data available today, text 
summarization has become very essential to gain just the right 
amount of information from huge texts. We see long articles in 
news websites, blogs, customers’ review websites, and so on. This 
review paper presents various approaches to generate summary 
of huge texts. Various papers have been studied for different 
methods that have been used so far for text summarization. 
Mostly, the methods described in this paper produce Abstractive 
(ABS) or Extractive (EXT) summaries of text documents. Querybased summarization techniques are also discussed. The paper 
mostly discusses about the structured based and semantic based 
approaches for summarization of the text documents. Various 
datasets were used to test the summaries produced by these 
models, such as the CNN corpus, DUC2000, single and multiple 
text documents etc. We have studied these methods and also the 
tendencies, achievements, past work and future scope of them in 
text summarization as well as other fieldsWe have seen that due to abundant availability of data, text 
summarization has a very vital role in saving user’s time, as 
well as resources. Text summarization is indeed an important 
tool for today. We have seen the use of various algorithms and 
methods for this purpose. These methods, in individual and 
together give different types of summaries. Their accuracy 
score can be compared to find the better and more concise 
summaries. For this purpose, ROGUE score has been used 
more frequently. Similarly, in some cases TF_IDF scores have 
been used too. 
The summaries generated using these methods are not 
always up to the mark. Sometimes, it’s also irrelevant to the 
original document. Therefore, this topic is ongoing and people 
have done various works on this. There isn’t any specific 
model that generates best summaries. So, for future, the models 
discussed can be modified for more accurate summaries. For 
e.g., we could use GAN’s and transfer learning. For future, this 
can give a way to develop and enhance further ideas for text 
summarization. @STOM © Word Vector Embedding kenearest neighbor algorithm © Differential Evolution Algorithm Newtonian Method Artificial Bee Colony Human Learning Algorithm
"""

summary = generate_summary(research_article, model, tokenizer)
print("Generated Summary:")
print(summary)

# %%
from unsloth.chat_templates import get_chat_template

tokenizer = get_chat_template(
    tokenizer,
    chat_template = "llama-3.1",
)
FastLanguageModel.for_inference(model) # Enable native 2x faster inference

messages = [
    {"role": "user", "content": """
Multi-document summarization (MDS) is an effective tool for information aggregation...
NLP based Machine Learning Approaches for Text 
SummarizationText summarization, Abstractive and extractive summaries, Query-based summarization, Structured-based and semantic-based approaches, Evaluation metrics (ROGUE score and TF_IDF score), Future directions (GANs and transfer learning),Data abundance and information overloadDue to the plethora of data available today, text 
summarization has become very essential to gain just the right 
amount of information from huge texts. We see long articles in 
news websites, blogs, customers’ review websites, and so on. This 
review paper presents various approaches to generate summary 
of huge texts. Various papers have been studied for different 
methods that have been used so far for text summarization. 
Mostly, the methods described in this paper produce Abstractive 
(ABS) or Extractive (EXT) summaries of text documents. Querybased summarization techniques are also discussed. The paper 
mostly discusses about the structured based and semantic based 
approaches for summarization of the text documents. Various 
datasets were used to test the summaries produced by these 
models, such as the CNN corpus, DUC2000, single and multiple 
text documents etc. We have studied these methods and also the 
tendencies, achievements, past work and future scope of them in 
text summarization as well as other fieldsWe have seen that due to abundant availability of data, text 
summarization has a very vital role in saving user’s time, as 
well as resources. Text summarization is indeed an important 
tool for today. We have seen the use of various algorithms and 
methods for this purpose. These methods, in individual and 
together give different types of summaries. Their accuracy 
score can be compared to find the better and more concise 
summaries. For this purpose, ROGUE score has been used 
more frequently. Similarly, in some cases TF_IDF scores have 
been used too. 
The summaries generated using these methods are not 
always up to the mark. Sometimes, it’s also irrelevant to the 
original document. Therefore, this topic is ongoing and people 
have done various works on this. There isn’t any specific 
model that generates best summaries. So, for future, the models 
discussed can be modified for more accurate summaries. For 
e.g., we could use GAN’s and transfer learning. For future, this 
can give a way to develop and enhance further ideas for text 
summarization. @STOM © Word Vector Embedding kenearest neighbor algorithm © Differential Evolution Algorithm Newtonian Method Artificial Bee Colony Human Learning Algorithm
"""},
]
inputs = tokenizer.apply_chat_template(
    messages,
    tokenize = True,
    add_generation_prompt = True, # Must add for generation
    return_tensors = "pt",
).to("cuda")

outputs = model.generate(input_ids = inputs, max_new_tokens = 64, use_cache = True,
                         temperature = 1.5, min_p = 0.1)
tokenizer.batch_decode(outputs)

# %%
FastLanguageModel.for_inference(model) # Enable native 2x faster inference

messages = [
    {"role": "user", "content": """
Multi-document summarization (MDS) is an effective tool for information aggregation...
NLP based Machine Learning Approaches for Text 
SummarizationText summarization, Abstractive and extractive summaries, Query-based summarization, Structured-based and semantic-based approaches, Evaluation metrics (ROGUE score and TF_IDF score), Future directions (GANs and transfer learning),Data abundance and information overloadDue to the plethora of data available today, text 
summarization has become very essential to gain just the right 
amount of information from huge texts. We see long articles in 
news websites, blogs, customers’ review websites, and so on. This 
review paper presents various approaches to generate summary 
of huge texts. Various papers have been studied for different 
methods that have been used so far for text summarization. 
Mostly, the methods described in this paper produce Abstractive 
(ABS) or Extractive (EXT) summaries of text documents. Querybased summarization techniques are also discussed. The paper 
mostly discusses about the structured based and semantic based 
approaches for summarization of the text documents. Various 
datasets were used to test the summaries produced by these 
models, such as the CNN corpus, DUC2000, single and multiple 
text documents etc. We have studied these methods and also the 
tendencies, achievements, past work and future scope of them in 
text summarization as well as other fieldsWe have seen that due to abundant availability of data, text 
summarization has a very vital role in saving user’s time, as 
well as resources. Text summarization is indeed an important 
tool for today. We have seen the use of various algorithms and 
methods for this purpose. These methods, in individual and 
together give different types of summaries. Their accuracy 
score can be compared to find the better and more concise 
summaries. For this purpose, ROGUE score has been used 
more frequently. Similarly, in some cases TF_IDF scores have 
been used too. 
The summaries generated using these methods are not 
always up to the mark. Sometimes, it’s also irrelevant to the 
original document. Therefore, this topic is ongoing and people 
have done various works on this. There isn’t any specific 
model that generates best summaries. So, for future, the models 
discussed can be modified for more accurate summaries. For 
e.g., we could use GAN’s and transfer learning. For future, this 
can give a way to develop and enhance further ideas for text 
summarization. @STOM © Word Vector Embedding kenearest neighbor algorithm © Differential Evolution Algorithm Newtonian Method Artificial Bee Colony Human Learning Algorithm
"""},
]
inputs = tokenizer.apply_chat_template(
    messages,
    tokenize = True,
    add_generation_prompt = True, # Must add for generation
    return_tensors = "pt",
).to("cuda")

from transformers import TextStreamer
text_streamer = TextStreamer(tokenizer, skip_prompt = True)
_ = model.generate(input_ids = inputs, streamer = text_streamer, max_new_tokens = 128,
                   use_cache = True, temperature = 1.5, min_p = 0.1)

# %%


# %%



