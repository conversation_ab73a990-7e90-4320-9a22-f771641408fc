{"best_global_step": null, "best_metric": null, "best_model_checkpoint": null, "epoch": 7.4224937623952405, "eval_steps": 500, "global_step": 29000, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.025590173373424605, "grad_norm": 4.753851890563965, "learning_rate": 2.9902000000000003e-05, "loss": 10.6702, "step": 100}, {"epoch": 0.05118034674684921, "grad_norm": 0.5756664872169495, "learning_rate": 2.9802e-05, "loss": 4.4997, "step": 200}, {"epoch": 0.07677052012027381, "grad_norm": 0.2663511633872986, "learning_rate": 2.9702e-05, "loss": 2.9626, "step": 300}, {"epoch": 0.10236069349369842, "grad_norm": 0.24154841899871826, "learning_rate": 2.9602000000000002e-05, "loss": 2.7629, "step": 400}, {"epoch": 0.12795086686712304, "grad_norm": 0.23158356547355652, "learning_rate": 2.9502000000000003e-05, "loss": 2.6645, "step": 500}, {"epoch": 0.12795086686712304, "eval_loss": 2.4020445346832275, "eval_runtime": 221.361, "eval_samples_per_second": 52.959, "eval_steps_per_second": 8.827, "step": 500}, {"epoch": 0.15354104024054763, "grad_norm": 0.22826851904392242, "learning_rate": 2.9402e-05, "loss": 2.5832, "step": 600}, {"epoch": 0.17913121361397225, "grad_norm": 0.18610233068466187, "learning_rate": 2.9302e-05, "loss": 2.5753, "step": 700}, {"epoch": 0.20472138698739684, "grad_norm": 0.18514497578144073, "learning_rate": 2.9202000000000003e-05, "loss": 2.5317, "step": 800}, {"epoch": 0.23031156036082145, "grad_norm": 0.18375949561595917, "learning_rate": 2.9102e-05, "loss": 2.4968, "step": 900}, {"epoch": 0.2559017337342461, "grad_norm": 0.17921075224876404, "learning_rate": 2.9002e-05, "loss": 2.4757, "step": 1000}, {"epoch": 0.2559017337342461, "eval_loss": 2.2502689361572266, "eval_runtime": 223.1246, "eval_samples_per_second": 52.54, "eval_steps_per_second": 8.757, "step": 1000}, {"epoch": 0.28149190710767064, "grad_norm": 0.18879684805870056, "learning_rate": 2.8902000000000002e-05, "loss": 2.4605, "step": 1100}, {"epoch": 0.30708208048109525, "grad_norm": 0.17951032519340515, "learning_rate": 2.8802e-05, "loss": 2.4131, "step": 1200}, {"epoch": 0.3326722538545199, "grad_norm": 0.18215124309062958, "learning_rate": 2.8702e-05, "loss": 2.425, "step": 1300}, {"epoch": 0.3582624272279445, "grad_norm": 0.26605862379074097, "learning_rate": 2.8602e-05, "loss": 2.3733, "step": 1400}, {"epoch": 0.38385260060136905, "grad_norm": 0.17822515964508057, "learning_rate": 2.8502e-05, "loss": 2.3886, "step": 1500}, {"epoch": 0.38385260060136905, "eval_loss": 2.1878559589385986, "eval_runtime": 223.8519, "eval_samples_per_second": 52.369, "eval_steps_per_second": 8.729, "step": 1500}, {"epoch": 0.4094427739747937, "grad_norm": 0.17791827023029327, "learning_rate": 2.8402e-05, "loss": 2.3718, "step": 1600}, {"epoch": 0.4350329473482183, "grad_norm": 0.18324200809001923, "learning_rate": 2.8302e-05, "loss": 2.355, "step": 1700}, {"epoch": 0.4606231207216429, "grad_norm": 0.18813034892082214, "learning_rate": 2.8202000000000002e-05, "loss": 2.3448, "step": 1800}, {"epoch": 0.48621329409506747, "grad_norm": 0.23821766674518585, "learning_rate": 2.8102e-05, "loss": 2.3486, "step": 1900}, {"epoch": 0.5118034674684921, "grad_norm": 0.19892555475234985, "learning_rate": 2.8002e-05, "loss": 2.3572, "step": 2000}, {"epoch": 0.5118034674684921, "eval_loss": 2.142894983291626, "eval_runtime": 224.2571, "eval_samples_per_second": 52.275, "eval_steps_per_second": 8.713, "step": 2000}, {"epoch": 0.5373936408419167, "grad_norm": 0.17738273739814758, "learning_rate": 2.7902e-05, "loss": 2.3506, "step": 2100}, {"epoch": 0.5629838142153413, "grad_norm": 0.17706769704818726, "learning_rate": 2.7802e-05, "loss": 2.323, "step": 2200}, {"epoch": 0.5885739875887659, "grad_norm": 0.20125770568847656, "learning_rate": 2.7703e-05, "loss": 2.3461, "step": 2300}, {"epoch": 0.6141641609621905, "grad_norm": 0.18118518590927124, "learning_rate": 2.7603000000000003e-05, "loss": 2.2957, "step": 2400}, {"epoch": 0.6397543343356151, "grad_norm": 0.3740713894367218, "learning_rate": 2.7504e-05, "loss": 2.3211, "step": 2500}, {"epoch": 0.6397543343356151, "eval_loss": 2.113314151763916, "eval_runtime": 222.7593, "eval_samples_per_second": 52.626, "eval_steps_per_second": 8.772, "step": 2500}, {"epoch": 0.6653445077090397, "grad_norm": 0.17435073852539062, "learning_rate": 2.7405e-05, "loss": 2.326, "step": 2600}, {"epoch": 0.6909346810824644, "grad_norm": 0.20933973789215088, "learning_rate": 2.7305e-05, "loss": 2.2776, "step": 2700}, {"epoch": 0.716524854455889, "grad_norm": 0.25296923518180847, "learning_rate": 2.7205e-05, "loss": 2.257, "step": 2800}, {"epoch": 0.7421150278293135, "grad_norm": 0.17340640723705292, "learning_rate": 2.7106e-05, "loss": 2.2839, "step": 2900}, {"epoch": 0.7677052012027381, "grad_norm": 0.177804097533226, "learning_rate": 2.7006000000000002e-05, "loss": 2.2613, "step": 3000}, {"epoch": 0.7677052012027381, "eval_loss": 2.085970401763916, "eval_runtime": 229.9063, "eval_samples_per_second": 50.99, "eval_steps_per_second": 8.499, "step": 3000}, {"epoch": 0.7932953745761627, "grad_norm": 0.19031216204166412, "learning_rate": 2.6906000000000003e-05, "loss": 2.2705, "step": 3100}, {"epoch": 0.8188855479495873, "grad_norm": 0.1980268508195877, "learning_rate": 2.6806e-05, "loss": 2.2788, "step": 3200}, {"epoch": 0.844475721323012, "grad_norm": 0.18740500509738922, "learning_rate": 2.6706e-05, "loss": 2.2375, "step": 3300}, {"epoch": 0.8700658946964366, "grad_norm": 0.18730807304382324, "learning_rate": 2.6606000000000002e-05, "loss": 2.2258, "step": 3400}, {"epoch": 0.8956560680698612, "grad_norm": 0.20442073047161102, "learning_rate": 2.6506e-05, "loss": 2.2342, "step": 3500}, {"epoch": 0.8956560680698612, "eval_loss": 2.0702037811279297, "eval_runtime": 225.6991, "eval_samples_per_second": 51.941, "eval_steps_per_second": 8.658, "step": 3500}, {"epoch": 0.9212462414432858, "grad_norm": 0.4441717267036438, "learning_rate": 2.6407e-05, "loss": 2.249, "step": 3600}, {"epoch": 0.9468364148167104, "grad_norm": 0.19290386140346527, "learning_rate": 2.6307e-05, "loss": 2.2287, "step": 3700}, {"epoch": 0.9724265881901349, "grad_norm": 0.19023343920707703, "learning_rate": 2.6207e-05, "loss": 2.2551, "step": 3800}, {"epoch": 0.9980167615635596, "grad_norm": 0.1917504072189331, "learning_rate": 2.6107e-05, "loss": 2.2101, "step": 3900}, {"epoch": 1.0237988612372848, "grad_norm": 0.19124191999435425, "learning_rate": 2.6007e-05, "loss": 2.2011, "step": 4000}, {"epoch": 1.0237988612372848, "eval_loss": 2.0542263984680176, "eval_runtime": 226.9771, "eval_samples_per_second": 51.648, "eval_steps_per_second": 8.609, "step": 4000}, {"epoch": 1.0493890346107095, "grad_norm": 0.19315069913864136, "learning_rate": 2.5907e-05, "loss": 2.2175, "step": 4100}, {"epoch": 1.074979207984134, "grad_norm": 0.2106466144323349, "learning_rate": 2.5807e-05, "loss": 2.2564, "step": 4200}, {"epoch": 1.1005693813575588, "grad_norm": 0.19504307210445404, "learning_rate": 2.5707e-05, "loss": 2.2185, "step": 4300}, {"epoch": 1.1261595547309833, "grad_norm": 0.2419312596321106, "learning_rate": 2.5607e-05, "loss": 2.2058, "step": 4400}, {"epoch": 1.151749728104408, "grad_norm": 0.19168978929519653, "learning_rate": 2.5506999999999998e-05, "loss": 2.2081, "step": 4500}, {"epoch": 1.151749728104408, "eval_loss": 2.042915105819702, "eval_runtime": 224.4172, "eval_samples_per_second": 52.238, "eval_steps_per_second": 8.707, "step": 4500}, {"epoch": 1.1773399014778325, "grad_norm": 0.2023511826992035, "learning_rate": 2.5409e-05, "loss": 2.2592, "step": 4600}, {"epoch": 1.202930074851257, "grad_norm": 0.2343863844871521, "learning_rate": 2.5309e-05, "loss": 2.1985, "step": 4700}, {"epoch": 1.2285202482246818, "grad_norm": 0.19939404726028442, "learning_rate": 2.5209000000000002e-05, "loss": 2.2138, "step": 4800}, {"epoch": 1.2541104215981063, "grad_norm": 0.19754478335380554, "learning_rate": 2.5109e-05, "loss": 2.193, "step": 4900}, {"epoch": 1.279700594971531, "grad_norm": 0.1893143504858017, "learning_rate": 2.5009e-05, "loss": 2.1987, "step": 5000}, {"epoch": 1.279700594971531, "eval_loss": 2.0335378646850586, "eval_runtime": 224.115, "eval_samples_per_second": 52.308, "eval_steps_per_second": 8.719, "step": 5000}, {"epoch": 1.3052907683449555, "grad_norm": 0.19188202917575836, "learning_rate": 2.491e-05, "loss": 2.1874, "step": 5100}, {"epoch": 1.3308809417183802, "grad_norm": 0.21117514371871948, "learning_rate": 2.4809999999999998e-05, "loss": 2.195, "step": 5200}, {"epoch": 1.3564711150918047, "grad_norm": 0.20251992344856262, "learning_rate": 2.471e-05, "loss": 2.1981, "step": 5300}, {"epoch": 1.3820612884652292, "grad_norm": 0.20328077673912048, "learning_rate": 2.461e-05, "loss": 2.189, "step": 5400}, {"epoch": 1.407651461838654, "grad_norm": 0.7629321217536926, "learning_rate": 2.4509999999999997e-05, "loss": 2.2051, "step": 5500}, {"epoch": 1.407651461838654, "eval_loss": 2.026211977005005, "eval_runtime": 227.854, "eval_samples_per_second": 51.45, "eval_steps_per_second": 8.576, "step": 5500}, {"epoch": 1.4332416352120785, "grad_norm": 0.18914197385311127, "learning_rate": 2.4410000000000002e-05, "loss": 2.1704, "step": 5600}, {"epoch": 1.4588318085855032, "grad_norm": 0.19079270958900452, "learning_rate": 2.4310000000000003e-05, "loss": 2.2022, "step": 5700}, {"epoch": 1.4844219819589277, "grad_norm": 0.21317286789417267, "learning_rate": 2.4210000000000004e-05, "loss": 2.2147, "step": 5800}, {"epoch": 1.5100121553323524, "grad_norm": 0.20680701732635498, "learning_rate": 2.411e-05, "loss": 2.1966, "step": 5900}, {"epoch": 1.535602328705777, "grad_norm": 0.21282564103603363, "learning_rate": 2.4010000000000002e-05, "loss": 2.1919, "step": 6000}, {"epoch": 1.535602328705777, "eval_loss": 2.0173869132995605, "eval_runtime": 225.4006, "eval_samples_per_second": 52.01, "eval_steps_per_second": 8.669, "step": 6000}, {"epoch": 1.5611925020792015, "grad_norm": 0.2471049576997757, "learning_rate": 2.3910000000000003e-05, "loss": 2.1884, "step": 6100}, {"epoch": 1.5867826754526262, "grad_norm": 0.20167547464370728, "learning_rate": 2.381e-05, "loss": 2.1729, "step": 6200}, {"epoch": 1.612372848826051, "grad_norm": 0.20009011030197144, "learning_rate": 2.371e-05, "loss": 2.1674, "step": 6300}, {"epoch": 1.6379630221994754, "grad_norm": 0.19291473925113678, "learning_rate": 2.3610000000000003e-05, "loss": 2.1845, "step": 6400}, {"epoch": 1.6635531955729, "grad_norm": 0.19921448826789856, "learning_rate": 2.351e-05, "loss": 2.1748, "step": 6500}, {"epoch": 1.6635531955729, "eval_loss": 2.0108907222747803, "eval_runtime": 222.2723, "eval_samples_per_second": 52.742, "eval_steps_per_second": 8.791, "step": 6500}, {"epoch": 1.6891433689463247, "grad_norm": 0.22676610946655273, "learning_rate": 2.341e-05, "loss": 2.1721, "step": 6600}, {"epoch": 1.7147335423197492, "grad_norm": 0.22116273641586304, "learning_rate": 2.3310000000000002e-05, "loss": 2.1754, "step": 6700}, {"epoch": 1.7403237156931737, "grad_norm": 0.22229152917861938, "learning_rate": 2.321e-05, "loss": 2.1951, "step": 6800}, {"epoch": 1.7659138890665984, "grad_norm": 0.20546956360340118, "learning_rate": 2.311e-05, "loss": 2.1547, "step": 6900}, {"epoch": 1.7915040624400231, "grad_norm": 0.2199781835079193, "learning_rate": 2.301e-05, "loss": 2.1411, "step": 7000}, {"epoch": 1.7915040624400231, "eval_loss": 2.004756212234497, "eval_runtime": 224.6605, "eval_samples_per_second": 52.181, "eval_steps_per_second": 8.698, "step": 7000}, {"epoch": 1.8170942358134476, "grad_norm": 0.20468665659427643, "learning_rate": 2.2910000000000003e-05, "loss": 2.1887, "step": 7100}, {"epoch": 1.8426844091868722, "grad_norm": 0.23942163586616516, "learning_rate": 2.281e-05, "loss": 2.1611, "step": 7200}, {"epoch": 1.8682745825602969, "grad_norm": 0.21715989708900452, "learning_rate": 2.271e-05, "loss": 2.1756, "step": 7300}, {"epoch": 1.8938647559337216, "grad_norm": 0.21172544360160828, "learning_rate": 2.2610000000000002e-05, "loss": 2.1685, "step": 7400}, {"epoch": 1.919454929307146, "grad_norm": 0.22683756053447723, "learning_rate": 2.2511e-05, "loss": 2.2064, "step": 7500}, {"epoch": 1.919454929307146, "eval_loss": 1.9994099140167236, "eval_runtime": 224.344, "eval_samples_per_second": 52.255, "eval_steps_per_second": 8.71, "step": 7500}, {"epoch": 1.9450451026805706, "grad_norm": 0.24993334710597992, "learning_rate": 2.2411e-05, "loss": 2.1726, "step": 7600}, {"epoch": 1.9706352760539954, "grad_norm": 0.1965254694223404, "learning_rate": 2.2311e-05, "loss": 2.1446, "step": 7700}, {"epoch": 1.9962254494274199, "grad_norm": 0.2313179224729538, "learning_rate": 2.2212e-05, "loss": 2.1686, "step": 7800}, {"epoch": 2.0220075491011453, "grad_norm": 0.26516228914260864, "learning_rate": 2.2112e-05, "loss": 2.1447, "step": 7900}, {"epoch": 2.0475977224745696, "grad_norm": 0.2155138999223709, "learning_rate": 2.2012000000000002e-05, "loss": 2.1736, "step": 8000}, {"epoch": 2.0475977224745696, "eval_loss": 1.9949309825897217, "eval_runtime": 224.3692, "eval_samples_per_second": 52.249, "eval_steps_per_second": 8.709, "step": 8000}, {"epoch": 2.0731878958479943, "grad_norm": 0.19144293665885925, "learning_rate": 2.1912000000000003e-05, "loss": 2.1449, "step": 8100}, {"epoch": 2.098778069221419, "grad_norm": 0.21554319560527802, "learning_rate": 2.1813e-05, "loss": 2.1576, "step": 8200}, {"epoch": 2.1243682425948434, "grad_norm": 0.19917719066143036, "learning_rate": 2.1714e-05, "loss": 2.1805, "step": 8300}, {"epoch": 2.149958415968268, "grad_norm": 0.19365336000919342, "learning_rate": 2.1614e-05, "loss": 2.1577, "step": 8400}, {"epoch": 2.175548589341693, "grad_norm": 0.2057916522026062, "learning_rate": 2.1513999999999998e-05, "loss": 2.1723, "step": 8500}, {"epoch": 2.175548589341693, "eval_loss": 1.9940115213394165, "eval_runtime": 223.8307, "eval_samples_per_second": 52.374, "eval_steps_per_second": 8.73, "step": 8500}, {"epoch": 2.2011387627151175, "grad_norm": 0.22104643285274506, "learning_rate": 2.1414e-05, "loss": 2.1592, "step": 8600}, {"epoch": 2.226728936088542, "grad_norm": 0.2044217735528946, "learning_rate": 2.1314000000000003e-05, "loss": 2.165, "step": 8700}, {"epoch": 2.2523191094619666, "grad_norm": 0.21229423582553864, "learning_rate": 2.1215000000000003e-05, "loss": 2.1793, "step": 8800}, {"epoch": 2.2779092828353913, "grad_norm": 0.21348711848258972, "learning_rate": 2.1115e-05, "loss": 2.2009, "step": 8900}, {"epoch": 2.303499456208816, "grad_norm": 0.2102176547050476, "learning_rate": 2.1015e-05, "loss": 2.2133, "step": 9000}, {"epoch": 2.303499456208816, "eval_loss": 2.0150187015533447, "eval_runtime": 223.2036, "eval_samples_per_second": 52.522, "eval_steps_per_second": 8.754, "step": 9000}, {"epoch": 2.3290896295822403, "grad_norm": 0.18749666213989258, "learning_rate": 2.0916e-05, "loss": 2.2728, "step": 9100}, {"epoch": 2.354679802955665, "grad_norm": 0.2203253209590912, "learning_rate": 2.0816e-05, "loss": 2.3781, "step": 9200}, {"epoch": 2.3802699763290898, "grad_norm": 0.23275883495807648, "learning_rate": 2.0716e-05, "loss": 2.5139, "step": 9300}, {"epoch": 2.405860149702514, "grad_norm": 0.24789957702159882, "learning_rate": 2.0616e-05, "loss": 2.6493, "step": 9400}, {"epoch": 2.431450323075939, "grad_norm": 0.24137485027313232, "learning_rate": 2.0517e-05, "loss": 2.7991, "step": 9500}, {"epoch": 2.431450323075939, "eval_loss": 2.5544681549072266, "eval_runtime": 224.4636, "eval_samples_per_second": 52.227, "eval_steps_per_second": 8.705, "step": 9500}, {"epoch": 2.4570404964493635, "grad_norm": 0.1963021159172058, "learning_rate": 2.0417e-05, "loss": 3.062, "step": 9600}, {"epoch": 2.4826306698227882, "grad_norm": 0.2234545797109604, "learning_rate": 2.0317000000000002e-05, "loss": 3.4332, "step": 9700}, {"epoch": 2.5082208431962125, "grad_norm": 0.20345501601696014, "learning_rate": 2.0217000000000003e-05, "loss": 3.6968, "step": 9800}, {"epoch": 2.5338110165696373, "grad_norm": 0.21047823131084442, "learning_rate": 2.0117e-05, "loss": 3.947, "step": 9900}, {"epoch": 2.559401189943062, "grad_norm": 0.23330891132354736, "learning_rate": 2.0017e-05, "loss": 4.2978, "step": 10000}, {"epoch": 2.559401189943062, "eval_loss": 3.8411977291107178, "eval_runtime": 228.6355, "eval_samples_per_second": 51.274, "eval_steps_per_second": 8.546, "step": 10000}, {"epoch": 2.5849913633164867, "grad_norm": 0.21581970155239105, "learning_rate": 1.9917000000000003e-05, "loss": 4.5868, "step": 10100}, {"epoch": 2.610581536689911, "grad_norm": 0.23405511677265167, "learning_rate": 1.9817e-05, "loss": 4.9614, "step": 10200}, {"epoch": 2.6361717100633357, "grad_norm": 0.030552025884389877, "learning_rate": 1.9719e-05, "loss": 5.4304, "step": 10300}, {"epoch": 2.6617618834367605, "grad_norm": 0.025583995506167412, "learning_rate": 1.9619e-05, "loss": 5.5038, "step": 10400}, {"epoch": 2.6873520568101847, "grad_norm": 0.026089264079928398, "learning_rate": 1.9519e-05, "loss": 5.6402, "step": 10500}, {"epoch": 2.6873520568101847, "eval_loss": 4.950906753540039, "eval_runtime": 223.8042, "eval_samples_per_second": 52.381, "eval_steps_per_second": 8.731, "step": 10500}, {"epoch": 2.7129422301836095, "grad_norm": 0.02316790074110031, "learning_rate": 1.9419e-05, "loss": 5.7169, "step": 10600}, {"epoch": 2.738532403557034, "grad_norm": 0.022907713428139687, "learning_rate": 1.9319000000000003e-05, "loss": 5.7248, "step": 10700}, {"epoch": 2.7641225769304585, "grad_norm": 0.026960760354995728, "learning_rate": 1.9219000000000004e-05, "loss": 5.6982, "step": 10800}, {"epoch": 2.789712750303883, "grad_norm": 0.0202326700091362, "learning_rate": 1.9119e-05, "loss": 5.7752, "step": 10900}, {"epoch": 2.815302923677308, "grad_norm": 0.009199456311762333, "learning_rate": 1.902e-05, "loss": 5.7992, "step": 11000}, {"epoch": 2.815302923677308, "eval_loss": 5.001400470733643, "eval_runtime": 224.1821, "eval_samples_per_second": 52.292, "eval_steps_per_second": 8.716, "step": 11000}, {"epoch": 2.8408930970507327, "grad_norm": 0.01363667193800211, "learning_rate": 1.8920000000000002e-05, "loss": 5.7428, "step": 11100}, {"epoch": 2.866483270424157, "grad_norm": 0.01416891161352396, "learning_rate": 1.8820999999999998e-05, "loss": 5.7845, "step": 11200}, {"epoch": 2.8920734437975817, "grad_norm": 0.01302664540708065, "learning_rate": 1.8721e-05, "loss": 5.7124, "step": 11300}, {"epoch": 2.9176636171710064, "grad_norm": 0.0, "learning_rate": 1.8621e-05, "loss": 5.7217, "step": 11400}, {"epoch": 2.943253790544431, "grad_norm": 0.0, "learning_rate": 1.8522e-05, "loss": 5.774, "step": 11500}, {"epoch": 2.943253790544431, "eval_loss": 4.992717742919922, "eval_runtime": 224.3033, "eval_samples_per_second": 52.264, "eval_steps_per_second": 8.711, "step": 11500}, {"epoch": 2.9688439639178554, "grad_norm": 0.0, "learning_rate": 1.8424e-05, "loss": 5.834, "step": 11600}, {"epoch": 2.99443413729128, "grad_norm": 0.0, "learning_rate": 1.8324e-05, "loss": 5.7494, "step": 11700}, {"epoch": 3.0202162369650054, "grad_norm": 0.0, "learning_rate": 1.8225000000000003e-05, "loss": 5.7759, "step": 11800}, {"epoch": 3.04580641033843, "grad_norm": 0.0, "learning_rate": 1.8125e-05, "loss": 5.6959, "step": 11900}, {"epoch": 3.071396583711855, "grad_norm": 0.0, "learning_rate": 1.8026e-05, "loss": 5.7715, "step": 12000}, {"epoch": 3.071396583711855, "eval_loss": 4.992106914520264, "eval_runtime": 223.9786, "eval_samples_per_second": 52.34, "eval_steps_per_second": 8.724, "step": 12000}, {"epoch": 3.096986757085279, "grad_norm": 0.0, "learning_rate": 1.7926e-05, "loss": 5.7467, "step": 12100}, {"epoch": 3.122576930458704, "grad_norm": 0.0, "learning_rate": 1.7826e-05, "loss": 5.7762, "step": 12200}, {"epoch": 3.1481671038321286, "grad_norm": 0.0, "learning_rate": 1.7727e-05, "loss": 5.8443, "step": 12300}, {"epoch": 3.173757277205553, "grad_norm": 0.0, "learning_rate": 1.7627000000000003e-05, "loss": 5.7069, "step": 12400}, {"epoch": 3.1993474505789776, "grad_norm": 0.0, "learning_rate": 1.7527000000000003e-05, "loss": 5.7455, "step": 12500}, {"epoch": 3.1993474505789776, "eval_loss": 4.991365432739258, "eval_runtime": 223.3047, "eval_samples_per_second": 52.498, "eval_steps_per_second": 8.75, "step": 12500}, {"epoch": 3.2249376239524024, "grad_norm": 0.0, "learning_rate": 1.7427e-05, "loss": 5.7664, "step": 12600}, {"epoch": 3.2505277973258266, "grad_norm": 0.0, "learning_rate": 1.7327000000000002e-05, "loss": 5.7077, "step": 12700}, {"epoch": 3.2761179706992514, "grad_norm": 0.0, "learning_rate": 1.7227000000000003e-05, "loss": 5.7252, "step": 12800}, {"epoch": 3.301708144072676, "grad_norm": 0.0, "learning_rate": 1.7127e-05, "loss": 5.759, "step": 12900}, {"epoch": 3.327298317446101, "grad_norm": 0.0, "learning_rate": 1.7027e-05, "loss": 5.722, "step": 13000}, {"epoch": 3.327298317446101, "eval_loss": 4.990636825561523, "eval_runtime": 225.256, "eval_samples_per_second": 52.043, "eval_steps_per_second": 8.675, "step": 13000}, {"epoch": 3.352888490819525, "grad_norm": 0.0, "learning_rate": 1.6927000000000002e-05, "loss": 5.7486, "step": 13100}, {"epoch": 3.37847866419295, "grad_norm": 0.0, "learning_rate": 1.6827e-05, "loss": 5.7277, "step": 13200}, {"epoch": 3.4040688375663746, "grad_norm": 0.0, "learning_rate": 1.6729e-05, "loss": 5.8116, "step": 13300}, {"epoch": 3.4296590109397993, "grad_norm": 0.0, "learning_rate": 1.6630000000000002e-05, "loss": 5.7287, "step": 13400}, {"epoch": 3.4552491843132236, "grad_norm": 0.0, "learning_rate": 1.6530000000000003e-05, "loss": 5.7296, "step": 13500}, {"epoch": 3.4552491843132236, "eval_loss": 4.989909648895264, "eval_runtime": 226.6463, "eval_samples_per_second": 51.724, "eval_steps_per_second": 8.621, "step": 13500}, {"epoch": 3.4808393576866483, "grad_norm": 0.0, "learning_rate": 1.643e-05, "loss": 5.7655, "step": 13600}, {"epoch": 3.506429531060073, "grad_norm": 0.0, "learning_rate": 1.633e-05, "loss": 5.7713, "step": 13700}, {"epoch": 3.5320197044334973, "grad_norm": 0.0, "learning_rate": 1.6230000000000002e-05, "loss": 5.7146, "step": 13800}, {"epoch": 3.557609877806922, "grad_norm": 0.0, "learning_rate": 1.6131e-05, "loss": 5.7853, "step": 13900}, {"epoch": 3.583200051180347, "grad_norm": 0.0, "learning_rate": 1.6032e-05, "loss": 5.7468, "step": 14000}, {"epoch": 3.583200051180347, "eval_loss": 4.989182472229004, "eval_runtime": 222.9661, "eval_samples_per_second": 52.577, "eval_steps_per_second": 8.764, "step": 14000}, {"epoch": 3.608790224553771, "grad_norm": 0.0, "learning_rate": 1.5932000000000003e-05, "loss": 5.7183, "step": 14100}, {"epoch": 3.634380397927196, "grad_norm": 0.0, "learning_rate": 1.5833000000000002e-05, "loss": 5.8023, "step": 14200}, {"epoch": 3.6599705713006205, "grad_norm": 0.0, "learning_rate": 1.5733e-05, "loss": 5.6904, "step": 14300}, {"epoch": 3.6855607446740453, "grad_norm": 0.0, "learning_rate": 1.5635000000000002e-05, "loss": 5.8115, "step": 14400}, {"epoch": 3.71115091804747, "grad_norm": 0.0, "learning_rate": 1.5535000000000003e-05, "loss": 5.6931, "step": 14500}, {"epoch": 3.71115091804747, "eval_loss": 4.988505840301514, "eval_runtime": 223.5698, "eval_samples_per_second": 52.436, "eval_steps_per_second": 8.74, "step": 14500}, {"epoch": 3.7367410914208943, "grad_norm": 0.0, "learning_rate": 1.5435e-05, "loss": 5.7353, "step": 14600}, {"epoch": 3.762331264794319, "grad_norm": 0.0, "learning_rate": 1.5335e-05, "loss": 5.691, "step": 14700}, {"epoch": 3.7879214381677437, "grad_norm": 0.0, "learning_rate": 1.5235e-05, "loss": 5.7517, "step": 14800}, {"epoch": 3.813511611541168, "grad_norm": 0.0, "learning_rate": 1.5135999999999999e-05, "loss": 5.8165, "step": 14900}, {"epoch": 3.8391017849145928, "grad_norm": 0.0, "learning_rate": 1.5036e-05, "loss": 5.7104, "step": 15000}, {"epoch": 3.8391017849145928, "eval_loss": 4.987791538238525, "eval_runtime": 224.086, "eval_samples_per_second": 52.315, "eval_steps_per_second": 8.72, "step": 15000}, {"epoch": 3.8646919582880175, "grad_norm": 0.0, "learning_rate": 1.4938e-05, "loss": 5.7666, "step": 15100}, {"epoch": 3.8902821316614418, "grad_norm": 0.0, "learning_rate": 1.4838e-05, "loss": 5.765, "step": 15200}, {"epoch": 3.9158723050348665, "grad_norm": 0.0, "learning_rate": 1.4740000000000001e-05, "loss": 5.8038, "step": 15300}, {"epoch": 3.9414624784082912, "grad_norm": 0.0, "learning_rate": 1.464e-05, "loss": 5.7335, "step": 15400}, {"epoch": 3.967052651781716, "grad_norm": 0.0, "learning_rate": 1.4541e-05, "loss": 5.7987, "step": 15500}, {"epoch": 3.967052651781716, "eval_loss": 4.987320899963379, "eval_runtime": 223.833, "eval_samples_per_second": 52.374, "eval_steps_per_second": 8.73, "step": 15500}, {"epoch": 3.9926428251551407, "grad_norm": 0.0, "learning_rate": 1.4441000000000001e-05, "loss": 5.7063, "step": 15600}, {"epoch": 4.0184249248288655, "grad_norm": 0.0, "learning_rate": 1.4341e-05, "loss": 5.7569, "step": 15700}, {"epoch": 4.044015098202291, "grad_norm": 0.0, "learning_rate": 1.4241000000000001e-05, "loss": 5.7898, "step": 15800}, {"epoch": 4.069605271575715, "grad_norm": 0.0, "learning_rate": 1.4141e-05, "loss": 5.7589, "step": 15900}, {"epoch": 4.095195444949139, "grad_norm": 0.0, "learning_rate": 1.4041000000000002e-05, "loss": 5.7084, "step": 16000}, {"epoch": 4.095195444949139, "eval_loss": 4.986889362335205, "eval_runtime": 223.3375, "eval_samples_per_second": 52.49, "eval_steps_per_second": 8.749, "step": 16000}, {"epoch": 4.120785618322564, "grad_norm": 0.0, "learning_rate": 1.3942e-05, "loss": 5.7629, "step": 16100}, {"epoch": 4.146375791695989, "grad_norm": 0.0, "learning_rate": 1.3841999999999999e-05, "loss": 5.7509, "step": 16200}, {"epoch": 4.171965965069413, "grad_norm": 0.0, "learning_rate": 1.3742e-05, "loss": 5.751, "step": 16300}, {"epoch": 4.197556138442838, "grad_norm": 0.0, "learning_rate": 1.3642e-05, "loss": 5.7641, "step": 16400}, {"epoch": 4.223146311816262, "grad_norm": 0.0, "learning_rate": 1.3542000000000002e-05, "loss": 5.7662, "step": 16500}, {"epoch": 4.223146311816262, "eval_loss": 4.986449241638184, "eval_runtime": 231.6677, "eval_samples_per_second": 50.603, "eval_steps_per_second": 8.434, "step": 16500}, {"epoch": 4.248736485189687, "grad_norm": 0.0, "learning_rate": 1.3442000000000001e-05, "loss": 5.744, "step": 16600}, {"epoch": 4.274326658563112, "grad_norm": 0.0, "learning_rate": 1.3342e-05, "loss": 5.736, "step": 16700}, {"epoch": 4.299916831936536, "grad_norm": 0.0, "learning_rate": 1.3242000000000001e-05, "loss": 5.7039, "step": 16800}, {"epoch": 4.325507005309961, "grad_norm": 0.0, "learning_rate": 1.3142e-05, "loss": 5.7037, "step": 16900}, {"epoch": 4.351097178683386, "grad_norm": 0.0, "learning_rate": 1.3042000000000002e-05, "loss": 5.7264, "step": 17000}, {"epoch": 4.351097178683386, "eval_loss": 4.986025810241699, "eval_runtime": 226.0397, "eval_samples_per_second": 51.863, "eval_steps_per_second": 8.645, "step": 17000}, {"epoch": 4.37668735205681, "grad_norm": 0.0, "learning_rate": 1.2942e-05, "loss": 5.6633, "step": 17100}, {"epoch": 4.402277525430235, "grad_norm": 0.0, "learning_rate": 1.2842e-05, "loss": 5.727, "step": 17200}, {"epoch": 4.427867698803659, "grad_norm": 0.0, "learning_rate": 1.2743000000000001e-05, "loss": 5.7611, "step": 17300}, {"epoch": 4.453457872177084, "grad_norm": 0.0, "learning_rate": 1.2644e-05, "loss": 5.7269, "step": 17400}, {"epoch": 4.479048045550509, "grad_norm": 0.0, "learning_rate": 1.2544e-05, "loss": 5.7519, "step": 17500}, {"epoch": 4.479048045550509, "eval_loss": 4.985595226287842, "eval_runtime": 228.7088, "eval_samples_per_second": 51.257, "eval_steps_per_second": 8.544, "step": 17500}, {"epoch": 4.504638218923933, "grad_norm": 0.0, "learning_rate": 1.2446e-05, "loss": 5.8389, "step": 17600}, {"epoch": 4.530228392297358, "grad_norm": 0.0, "learning_rate": 1.2346e-05, "loss": 5.774, "step": 17700}, {"epoch": 4.555818565670783, "grad_norm": 0.0, "learning_rate": 1.2246000000000001e-05, "loss": 5.7211, "step": 17800}, {"epoch": 4.581408739044207, "grad_norm": 0.0, "learning_rate": 1.2146e-05, "loss": 5.6947, "step": 17900}, {"epoch": 4.606998912417632, "grad_norm": 0.0, "learning_rate": 1.2046000000000001e-05, "loss": 5.7418, "step": 18000}, {"epoch": 4.606998912417632, "eval_loss": 4.985158920288086, "eval_runtime": 222.9095, "eval_samples_per_second": 52.591, "eval_steps_per_second": 8.766, "step": 18000}, {"epoch": 4.632589085791056, "grad_norm": 0.0, "learning_rate": 1.1946e-05, "loss": 5.7137, "step": 18100}, {"epoch": 4.658179259164481, "grad_norm": 0.0, "learning_rate": 1.1846e-05, "loss": 5.719, "step": 18200}, {"epoch": 4.683769432537906, "grad_norm": 0.0, "learning_rate": 1.1746e-05, "loss": 5.7194, "step": 18300}, {"epoch": 4.70935960591133, "grad_norm": 0.0, "learning_rate": 1.1647e-05, "loss": 5.8005, "step": 18400}, {"epoch": 4.734949779284754, "grad_norm": 0.0, "learning_rate": 1.1547000000000001e-05, "loss": 5.7097, "step": 18500}, {"epoch": 4.734949779284754, "eval_loss": 4.984707355499268, "eval_runtime": 224.0656, "eval_samples_per_second": 52.319, "eval_steps_per_second": 8.721, "step": 18500}, {"epoch": 4.7605399526581795, "grad_norm": 0.0, "learning_rate": 1.1447e-05, "loss": 5.7036, "step": 18600}, {"epoch": 4.786130126031604, "grad_norm": 0.0, "learning_rate": 1.1347e-05, "loss": 5.7, "step": 18700}, {"epoch": 4.811720299405028, "grad_norm": 0.0, "learning_rate": 1.1247000000000001e-05, "loss": 5.714, "step": 18800}, {"epoch": 4.837310472778453, "grad_norm": 0.0, "learning_rate": 1.1147e-05, "loss": 5.6909, "step": 18900}, {"epoch": 4.862900646151878, "grad_norm": 0.0, "learning_rate": 1.1047000000000001e-05, "loss": 5.6518, "step": 19000}, {"epoch": 4.862900646151878, "eval_loss": 4.984291076660156, "eval_runtime": 225.0969, "eval_samples_per_second": 52.08, "eval_steps_per_second": 8.681, "step": 19000}, {"epoch": 4.888490819525302, "grad_norm": 0.0, "learning_rate": 1.0948000000000001e-05, "loss": 5.7741, "step": 19100}, {"epoch": 4.914080992898727, "grad_norm": 0.0, "learning_rate": 1.0848e-05, "loss": 5.7286, "step": 19200}, {"epoch": 4.939671166272151, "grad_norm": 0.0, "learning_rate": 1.0748000000000001e-05, "loss": 5.7548, "step": 19300}, {"epoch": 4.9652613396455765, "grad_norm": 0.0, "learning_rate": 1.0648e-05, "loss": 5.7145, "step": 19400}, {"epoch": 4.990851513019001, "grad_norm": 0.0, "learning_rate": 1.0548000000000001e-05, "loss": 5.7291, "step": 19500}, {"epoch": 4.990851513019001, "eval_loss": 4.983855247497559, "eval_runtime": 222.3984, "eval_samples_per_second": 52.712, "eval_steps_per_second": 8.786, "step": 19500}, {"epoch": 5.016633612692726, "grad_norm": 0.0, "learning_rate": 1.0448e-05, "loss": 5.7125, "step": 19600}, {"epoch": 5.042223786066151, "grad_norm": 0.0, "learning_rate": 1.0348e-05, "loss": 5.7136, "step": 19700}, {"epoch": 5.067813959439575, "grad_norm": 0.0, "learning_rate": 1.0248e-05, "loss": 5.7391, "step": 19800}, {"epoch": 5.093404132813, "grad_norm": 0.0, "learning_rate": 1.0148e-05, "loss": 5.7564, "step": 19900}, {"epoch": 5.1189943061864245, "grad_norm": 0.0, "learning_rate": 1.0048000000000001e-05, "loss": 5.6655, "step": 20000}, {"epoch": 5.1189943061864245, "eval_loss": 4.983415126800537, "eval_runtime": 218.4204, "eval_samples_per_second": 53.672, "eval_steps_per_second": 8.946, "step": 20000}, {"epoch": 5.144584479559849, "grad_norm": 0.0, "learning_rate": 9.949e-06, "loss": 5.7176, "step": 20100}, {"epoch": 5.170174652933274, "grad_norm": 0.0, "learning_rate": 9.849e-06, "loss": 5.7257, "step": 20200}, {"epoch": 5.195764826306698, "grad_norm": 0.0, "learning_rate": 9.749000000000001e-06, "loss": 5.7372, "step": 20300}, {"epoch": 5.2213549996801225, "grad_norm": 0.0, "learning_rate": 9.649e-06, "loss": 5.7125, "step": 20400}, {"epoch": 5.246945173053548, "grad_norm": 0.0, "learning_rate": 9.549000000000001e-06, "loss": 5.7168, "step": 20500}, {"epoch": 5.246945173053548, "eval_loss": 4.982964515686035, "eval_runtime": 228.1822, "eval_samples_per_second": 51.376, "eval_steps_per_second": 8.563, "step": 20500}, {"epoch": 5.272535346426972, "grad_norm": 0.0, "learning_rate": 9.450000000000001e-06, "loss": 5.7571, "step": 20600}, {"epoch": 5.298125519800396, "grad_norm": 0.0, "learning_rate": 9.35e-06, "loss": 5.7156, "step": 20700}, {"epoch": 5.323715693173821, "grad_norm": 0.0, "learning_rate": 9.250000000000001e-06, "loss": 5.7351, "step": 20800}, {"epoch": 5.349305866547246, "grad_norm": 0.0, "learning_rate": 9.15e-06, "loss": 5.7174, "step": 20900}, {"epoch": 5.374896039920671, "grad_norm": 0.0, "learning_rate": 9.050000000000001e-06, "loss": 5.7496, "step": 21000}, {"epoch": 5.374896039920671, "eval_loss": 4.9825310707092285, "eval_runtime": 224.9224, "eval_samples_per_second": 52.12, "eval_steps_per_second": 8.687, "step": 21000}, {"epoch": 5.400486213294095, "grad_norm": 0.0, "learning_rate": 8.951e-06, "loss": 5.7617, "step": 21100}, {"epoch": 5.4260763866675195, "grad_norm": 0.0, "learning_rate": 8.851e-06, "loss": 5.7595, "step": 21200}, {"epoch": 5.451666560040945, "grad_norm": 0.0, "learning_rate": 8.751000000000001e-06, "loss": 5.7126, "step": 21300}, {"epoch": 5.477256733414369, "grad_norm": 0.0, "learning_rate": 8.652e-06, "loss": 5.7817, "step": 21400}, {"epoch": 5.502846906787793, "grad_norm": 0.0, "learning_rate": 8.552e-06, "loss": 5.7281, "step": 21500}, {"epoch": 5.502846906787793, "eval_loss": 4.9821696281433105, "eval_runtime": 222.0014, "eval_samples_per_second": 52.806, "eval_steps_per_second": 8.802, "step": 21500}, {"epoch": 5.528437080161218, "grad_norm": 0.0, "learning_rate": 8.452e-06, "loss": 5.7404, "step": 21600}, {"epoch": 5.554027253534643, "grad_norm": 0.0, "learning_rate": 8.353e-06, "loss": 5.8455, "step": 21700}, {"epoch": 5.579617426908067, "grad_norm": 0.0, "learning_rate": 8.253e-06, "loss": 5.7459, "step": 21800}, {"epoch": 5.605207600281492, "grad_norm": 0.0, "learning_rate": 8.153e-06, "loss": 5.7468, "step": 21900}, {"epoch": 5.630797773654916, "grad_norm": 0.0, "learning_rate": 8.053e-06, "loss": 5.6886, "step": 22000}, {"epoch": 5.630797773654916, "eval_loss": 4.981864929199219, "eval_runtime": 223.2302, "eval_samples_per_second": 52.515, "eval_steps_per_second": 8.753, "step": 22000}, {"epoch": 5.656387947028341, "grad_norm": 0.0, "learning_rate": 7.954e-06, "loss": 5.7613, "step": 22100}, {"epoch": 5.681978120401766, "grad_norm": 0.0, "learning_rate": 7.854e-06, "loss": 5.7077, "step": 22200}, {"epoch": 5.70756829377519, "grad_norm": 0.0, "learning_rate": 7.754e-06, "loss": 5.7176, "step": 22300}, {"epoch": 5.733158467148614, "grad_norm": 0.0, "learning_rate": 7.654e-06, "loss": 5.6911, "step": 22400}, {"epoch": 5.75874864052204, "grad_norm": 0.0, "learning_rate": 7.554000000000001e-06, "loss": 5.7136, "step": 22500}, {"epoch": 5.75874864052204, "eval_loss": 4.981537342071533, "eval_runtime": 224.9833, "eval_samples_per_second": 52.106, "eval_steps_per_second": 8.685, "step": 22500}, {"epoch": 5.784338813895464, "grad_norm": 0.0, "learning_rate": 7.454e-06, "loss": 5.7335, "step": 22600}, {"epoch": 5.809928987268889, "grad_norm": 0.0, "learning_rate": 7.354e-06, "loss": 5.6962, "step": 22700}, {"epoch": 5.835519160642313, "grad_norm": 0.0, "learning_rate": 7.2560000000000005e-06, "loss": 5.7795, "step": 22800}, {"epoch": 5.861109334015738, "grad_norm": 0.0, "learning_rate": 7.156000000000001e-06, "loss": 5.6694, "step": 22900}, {"epoch": 5.886699507389163, "grad_norm": 0.0, "learning_rate": 7.056e-06, "loss": 5.7236, "step": 23000}, {"epoch": 5.886699507389163, "eval_loss": 4.98120641708374, "eval_runtime": 223.1171, "eval_samples_per_second": 52.542, "eval_steps_per_second": 8.758, "step": 23000}, {"epoch": 5.912289680762587, "grad_norm": 0.0, "learning_rate": 6.957e-06, "loss": 5.7843, "step": 23100}, {"epoch": 5.937879854136012, "grad_norm": 0.0, "learning_rate": 6.857000000000001e-06, "loss": 5.7308, "step": 23200}, {"epoch": 5.9634700275094366, "grad_norm": 0.0, "learning_rate": 6.758e-06, "loss": 5.7193, "step": 23300}, {"epoch": 5.989060200882861, "grad_norm": 0.0, "learning_rate": 6.6580000000000005e-06, "loss": 5.7721, "step": 23400}, {"epoch": 6.0148423005565865, "grad_norm": 0.0, "learning_rate": 6.558e-06, "loss": 5.7304, "step": 23500}, {"epoch": 6.0148423005565865, "eval_loss": 4.980889797210693, "eval_runtime": 225.9889, "eval_samples_per_second": 51.874, "eval_steps_per_second": 8.646, "step": 23500}, {"epoch": 6.040432473930011, "grad_norm": 0.0, "learning_rate": 6.458e-06, "loss": 5.7112, "step": 23600}, {"epoch": 6.066022647303435, "grad_norm": 0.0, "learning_rate": 6.358e-06, "loss": 5.7413, "step": 23700}, {"epoch": 6.09161282067686, "grad_norm": 0.0, "learning_rate": 6.258e-06, "loss": 5.6959, "step": 23800}, {"epoch": 6.117202994050285, "grad_norm": 0.0, "learning_rate": 6.158e-06, "loss": 5.7705, "step": 23900}, {"epoch": 6.14279316742371, "grad_norm": 0.0, "learning_rate": 6.058e-06, "loss": 5.7454, "step": 24000}, {"epoch": 6.14279316742371, "eval_loss": 4.980568885803223, "eval_runtime": 224.2924, "eval_samples_per_second": 52.267, "eval_steps_per_second": 8.712, "step": 24000}, {"epoch": 6.168383340797134, "grad_norm": 0.0, "learning_rate": 5.958e-06, "loss": 5.7263, "step": 24100}, {"epoch": 6.193973514170558, "grad_norm": 0.0, "learning_rate": 5.8580000000000005e-06, "loss": 5.7077, "step": 24200}, {"epoch": 6.2195636875439835, "grad_norm": 0.0, "learning_rate": 5.759e-06, "loss": 5.7606, "step": 24300}, {"epoch": 6.245153860917408, "grad_norm": 0.0, "learning_rate": 5.6589999999999995e-06, "loss": 5.7321, "step": 24400}, {"epoch": 6.270744034290832, "grad_norm": 0.0, "learning_rate": 5.559e-06, "loss": 5.6805, "step": 24500}, {"epoch": 6.270744034290832, "eval_loss": 4.980257987976074, "eval_runtime": 224.318, "eval_samples_per_second": 52.261, "eval_steps_per_second": 8.711, "step": 24500}, {"epoch": 6.296334207664257, "grad_norm": 0.0, "learning_rate": 5.459e-06, "loss": 5.7171, "step": 24600}, {"epoch": 6.3219243810376815, "grad_norm": 0.0, "learning_rate": 5.36e-06, "loss": 5.755, "step": 24700}, {"epoch": 6.347514554411106, "grad_norm": 0.0, "learning_rate": 5.261e-06, "loss": 5.7693, "step": 24800}, {"epoch": 6.373104727784531, "grad_norm": 0.0, "learning_rate": 5.161e-06, "loss": 5.7311, "step": 24900}, {"epoch": 6.398694901157955, "grad_norm": 0.0, "learning_rate": 5.0609999999999995e-06, "loss": 5.7147, "step": 25000}, {"epoch": 6.398694901157955, "eval_loss": 4.979949474334717, "eval_runtime": 223.986, "eval_samples_per_second": 52.338, "eval_steps_per_second": 8.724, "step": 25000}, {"epoch": 6.4242850745313795, "grad_norm": 0.0, "learning_rate": 4.962e-06, "loss": 5.6876, "step": 25100}, {"epoch": 6.449875247904805, "grad_norm": 0.0, "learning_rate": 4.862e-06, "loss": 5.7477, "step": 25200}, {"epoch": 6.475465421278229, "grad_norm": 0.0, "learning_rate": 4.762e-06, "loss": 5.7339, "step": 25300}, {"epoch": 6.501055594651653, "grad_norm": 0.0, "learning_rate": 4.6620000000000004e-06, "loss": 5.7208, "step": 25400}, {"epoch": 6.5266457680250785, "grad_norm": 0.0, "learning_rate": 4.562e-06, "loss": 5.7118, "step": 25500}, {"epoch": 6.5266457680250785, "eval_loss": 4.9796366691589355, "eval_runtime": 216.4412, "eval_samples_per_second": 54.163, "eval_steps_per_second": 9.028, "step": 25500}, {"epoch": 6.552235941398503, "grad_norm": 0.0, "learning_rate": 4.4629999999999995e-06, "loss": 5.7889, "step": 25600}, {"epoch": 6.577826114771928, "grad_norm": 0.0, "learning_rate": 4.363e-06, "loss": 5.6818, "step": 25700}, {"epoch": 6.603416288145352, "grad_norm": 0.0, "learning_rate": 4.265e-06, "loss": 5.7944, "step": 25800}, {"epoch": 6.6290064615187765, "grad_norm": 0.0, "learning_rate": 4.1660000000000004e-06, "loss": 5.7591, "step": 25900}, {"epoch": 6.654596634892202, "grad_norm": 0.0, "learning_rate": 4.0660000000000005e-06, "loss": 5.7403, "step": 26000}, {"epoch": 6.654596634892202, "eval_loss": 4.979309558868408, "eval_runtime": 219.4419, "eval_samples_per_second": 53.422, "eval_steps_per_second": 8.904, "step": 26000}, {"epoch": 6.680186808265626, "grad_norm": 0.0, "learning_rate": 3.966000000000001e-06, "loss": 5.7051, "step": 26100}, {"epoch": 6.70577698163905, "grad_norm": 0.0, "learning_rate": 3.8669999999999996e-06, "loss": 5.7268, "step": 26200}, {"epoch": 6.731367155012475, "grad_norm": 0.0, "learning_rate": 3.7669999999999997e-06, "loss": 5.7448, "step": 26300}, {"epoch": 6.7569573283859, "grad_norm": 0.0, "learning_rate": 3.6690000000000004e-06, "loss": 5.7434, "step": 26400}, {"epoch": 6.782547501759325, "grad_norm": 0.0, "learning_rate": 3.569e-06, "loss": 5.7243, "step": 26500}, {"epoch": 6.782547501759325, "eval_loss": 4.978987216949463, "eval_runtime": 222.4626, "eval_samples_per_second": 52.696, "eval_steps_per_second": 8.783, "step": 26500}, {"epoch": 6.808137675132749, "grad_norm": 0.0, "learning_rate": 3.469e-06, "loss": 5.7302, "step": 26600}, {"epoch": 6.833727848506173, "grad_norm": 0.0, "learning_rate": 3.371e-06, "loss": 5.8287, "step": 26700}, {"epoch": 6.859318021879599, "grad_norm": 0.0, "learning_rate": 3.272e-06, "loss": 5.778, "step": 26800}, {"epoch": 6.884908195253023, "grad_norm": 0.0, "learning_rate": 3.172e-06, "loss": 5.6777, "step": 26900}, {"epoch": 6.910498368626447, "grad_norm": 0.0, "learning_rate": 3.0720000000000004e-06, "loss": 5.7183, "step": 27000}, {"epoch": 6.910498368626447, "eval_loss": 4.978641510009766, "eval_runtime": 222.7693, "eval_samples_per_second": 52.624, "eval_steps_per_second": 8.771, "step": 27000}, {"epoch": 6.936088541999872, "grad_norm": 0.0, "learning_rate": 2.9729999999999997e-06, "loss": 5.7354, "step": 27100}, {"epoch": 6.961678715373297, "grad_norm": 0.0, "learning_rate": 2.8750000000000004e-06, "loss": 5.7836, "step": 27200}, {"epoch": 6.987268888746721, "grad_norm": 0.0, "learning_rate": 2.775e-06, "loss": 5.7826, "step": 27300}, {"epoch": 7.013050988420447, "grad_norm": 0.0, "learning_rate": 2.6750000000000002e-06, "loss": 5.6769, "step": 27400}, {"epoch": 7.038641161793871, "grad_norm": 0.0, "learning_rate": 2.576e-06, "loss": 5.7644, "step": 27500}, {"epoch": 7.038641161793871, "eval_loss": 4.978581428527832, "eval_runtime": 222.9556, "eval_samples_per_second": 52.58, "eval_steps_per_second": 8.764, "step": 27500}, {"epoch": 7.064231335167296, "grad_norm": 0.0, "learning_rate": 2.476e-06, "loss": 5.7039, "step": 27600}, {"epoch": 7.08982150854072, "grad_norm": 0.0, "learning_rate": 2.376e-06, "loss": 5.7613, "step": 27700}, {"epoch": 7.115411681914145, "grad_norm": 0.0, "learning_rate": 2.276e-06, "loss": 5.6998, "step": 27800}, {"epoch": 7.14100185528757, "grad_norm": 0.0, "learning_rate": 2.176e-06, "loss": 5.7776, "step": 27900}, {"epoch": 7.166592028660994, "grad_norm": 0.0, "learning_rate": 2.076e-06, "loss": 5.7071, "step": 28000}, {"epoch": 7.166592028660994, "eval_loss": 4.978581428527832, "eval_runtime": 223.9969, "eval_samples_per_second": 52.336, "eval_steps_per_second": 8.723, "step": 28000}, {"epoch": 7.192182202034418, "grad_norm": 0.0, "learning_rate": 1.9760000000000002e-06, "loss": 5.6995, "step": 28100}, {"epoch": 7.2177723754078436, "grad_norm": 0.0, "learning_rate": 1.876e-06, "loss": 5.7395, "step": 28200}, {"epoch": 7.243362548781268, "grad_norm": 0.0, "learning_rate": 1.776e-06, "loss": 5.7536, "step": 28300}, {"epoch": 7.268952722154693, "grad_norm": 0.0, "learning_rate": 1.6760000000000001e-06, "loss": 5.7137, "step": 28400}, {"epoch": 7.294542895528117, "grad_norm": 0.0, "learning_rate": 1.576e-06, "loss": 5.6777, "step": 28500}, {"epoch": 7.294542895528117, "eval_loss": 4.978581428527832, "eval_runtime": 218.2871, "eval_samples_per_second": 53.704, "eval_steps_per_second": 8.952, "step": 28500}, {"epoch": 7.320133068901542, "grad_norm": 0.0, "learning_rate": 1.4760000000000001e-06, "loss": 5.675, "step": 28600}, {"epoch": 7.345723242274967, "grad_norm": 0.0, "learning_rate": 1.376e-06, "loss": 5.7253, "step": 28700}, {"epoch": 7.371313415648391, "grad_norm": 0.0, "learning_rate": 1.277e-06, "loss": 5.7058, "step": 28800}, {"epoch": 7.396903589021815, "grad_norm": 0.0, "learning_rate": 1.1770000000000001e-06, "loss": 5.6938, "step": 28900}, {"epoch": 7.4224937623952405, "grad_norm": 0.0, "learning_rate": 1.079e-06, "loss": 5.8629, "step": 29000}, {"epoch": 7.4224937623952405, "eval_loss": 4.978581428527832, "eval_runtime": 221.3469, "eval_samples_per_second": 52.962, "eval_steps_per_second": 8.828, "step": 29000}], "logging_steps": 100, "max_steps": 30000, "num_input_tokens_seen": 0, "num_train_epochs": 8, "save_steps": 500, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 4.314273833036022e+17, "train_batch_size": 6, "trial_name": null, "trial_params": null}