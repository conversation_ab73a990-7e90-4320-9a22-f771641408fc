{"cells": [{"cell_type": "code", "execution_count": 45, "id": "ef04bcdc", "metadata": {}, "outputs": [], "source": ["import torch\n", "from transformers import T5ForConditionalGeneration, T5Tokenizer, Seq2SeqTrainingArguments, Seq2SeqTrainer\n", "from datasets import load_dataset, DatasetDict\n", "import pandas as pd\n", "from sklearn.model_selection import train_test_split\n", "from peft import LoraConfig, get_peft_model, TaskType\n", "import evaluate\n", "import numpy as np\n", "from peft import PeftModel, PeftConfig\n"]}, {"cell_type": "code", "execution_count": null, "id": "65a595e3", "metadata": {}, "outputs": [], "source": ["from datasets import Dataset\n", "\n", "\n", "def load_and_preprocess_data(file_path):\n", "    df = pd.read_csv(file_path)\n", "    \n", "    df = df.dropna()\n", "    df = df[df['abstract'].str.strip().astype(bool)]  \n", "    \n", "    train_df, temp_df = train_test_split(df, test_size=0.2, random_state=42)\n", "    val_df, test_df = train_test_split(temp_df, test_size=0.5, random_state=42)\n", "    \n", "    return DatasetDict({\n", "        'train': Dataset.from_pandas(train_df),\n", "        'val': Dataset.from_pandas(val_df),\n", "        'test': Dataset.from_pandas(test_df)\n", "    })"]}, {"cell_type": "code", "execution_count": null, "id": "dfc4e811", "metadata": {}, "outputs": [], "source": ["\n", "model_name = \"t5-base\"\n", "tokenizer = T5Tokenizer.from_pretrained(model_name)\n", "model = T5ForConditionalGeneration.from_pretrained(model_name)"]}, {"cell_type": "code", "execution_count": null, "id": "15035349", "metadata": {}, "outputs": [], "source": ["\n", "def preprocess_function(examples):\n", "    inputs = [\"summarize: \" + doc[:5000] for doc in examples[\"article\"]] \n", "    targets = [abs[:1000] for abs in examples[\"abstract\"]]  \n", "    \n", "    model_inputs = tokenizer(\n", "        inputs,\n", "        max_length=512,\n", "        truncation=True,\n", "        padding=\"max_length\"\n", "    )\n", "    \n", "    with tokenizer.as_target_tokenizer():\n", "        labels = tokenizer(\n", "            targets,\n", "            max_length=256,\n", "            truncation=True,\n", "            padding=\"max_length\"\n", "        )\n", "    \n", "    model_inputs[\"labels\"] = labels[\"input_ids\"]\n", "    return model_inputs\n", "\n", "\n", "dataset = load_and_preprocess_data(\"train.csv\")"]}, {"cell_type": "code", "execution_count": null, "id": "a39dd74e", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Map:   0%|          | 0/93785 [00:00<?, ? examples/s]/home/<USER>/anaconda3/envs/unsloth_env/lib/python3.11/site-packages/transformers/tokenization_utils_base.py:3980: UserWarning: `as_target_tokenizer` is deprecated and will be removed in v5 of Transformers. You can tokenize your labels by using the argument `text_target` of the regular `__call__` method (either in the same call as your input texts if you use the same keyword arguments, or in a separate call.\n", "  warnings.warn(\n", "Map: 100%|██████████| 93785/93785 [03:58<00:00, 393.48 examples/s]\n", "Map: 100%|██████████| 11723/11723 [00:29<00:00, 393.46 examples/s]\n", "Map: 100%|██████████| 11724/11724 [00:29<00:00, 399.54 examples/s]\n"]}], "source": ["\n", "tokenized_datasets = dataset.map(\n", "    preprocess_function,\n", "    batched=True,\n", "    remove_columns=[\"article\", \"abstract\"]\n", ")\n"]}, {"cell_type": "code", "execution_count": 12, "id": "026dacbb", "metadata": {}, "outputs": [], "source": ["# 5. Configure <PERSON>\n", "peft_config = LoraConfig(\n", "    r=32,\n", "    lora_alpha=32,\n", "    target_modules=[\"q\", \"v\"],  # T5 attention matrices\n", "    lora_dropout=0.05,\n", "    bias=\"none\",\n", "    task_type=TaskType.SEQ_2_SEQ_LM\n", ")\n"]}, {"cell_type": "code", "execution_count": 13, "id": "fc39e334", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Trainable parameters: 3538944\n"]}], "source": ["# 6. Create PEFT model\n", "model = get_peft_model(model, peft_config)\n", "print(f\"Trainable parameters: {sum(p.numel() for p in model.parameters() if p.requires_grad)}\")"]}, {"cell_type": "code", "execution_count": 14, "id": "057db903", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/unsloth_env/lib/python3.11/site-packages/transformers/training_args.py:1611: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead\n", "  warnings.warn(\n"]}], "source": ["# Training arguments\n", "training_args = Seq2SeqTrainingArguments(\n", "    output_dir=\"./t5-summarizer\",\n", "    evaluation_strategy=\"steps\",\n", "    max_steps=30000,\n", "    eval_steps=500,\n", "    save_strategy=\"steps\",\n", "    save_steps=500,\n", "    learning_rate=3e-5,\n", "    per_device_train_batch_size=6,\n", "    per_device_eval_batch_size=6,\n", "    gradient_accumulation_steps=4,\n", "    weight_decay=0.01,\n", "    num_train_epochs=3,\n", "    predict_with_generate=True,\n", "    fp16=True,\n", "    report_to=\"tensorboard\",\n", "    logging_steps=100,\n", "    push_to_hub=False,\n", ")"]}, {"cell_type": "code", "execution_count": 15, "id": "4254f831", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_6491/206718748.py:2: FutureWarning: `tokenizer` is deprecated and will be removed in version 5.0.0 for `Seq2SeqTrainer.__init__`. Use `processing_class` instead.\n", "  trainer = Seq2SeqTrainer(\n", "No label_names provided for model class `PeftModelForSeq2SeqLM`. Since `PeftModel` hides base models input arguments, if label_names is not given, label_names can't be set automatically within `Trainer`. Note that empty label_names list will be used instead.\n"]}], "source": ["# 8. Create trainer\n", "trainer = Seq2SeqTrainer(\n", "    model=model,\n", "    args=training_args,\n", "    train_dataset=tokenized_datasets[\"train\"],\n", "    eval_dataset=tokenized_datasets[\"val\"],\n", "    tokenizer=tokenizer,\n", ")"]}, {"cell_type": "code", "execution_count": 16, "id": "d894fa1d", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Passing a tuple of `past_key_values` is deprecated and will be removed in Transformers v4.48.0. You should pass an instance of `EncoderDecoderCache` instead, e.g. `past_key_values=EncoderDecoderCache.from_legacy_cache(past_key_values)`.\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='30000' max='30000' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [30000/30000 13:25:19, Epoch 7/8]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Step</th>\n", "      <th>Training Loss</th>\n", "      <th>Validation Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>500</td>\n", "      <td>2.664500</td>\n", "      <td>2.402045</td>\n", "    </tr>\n", "    <tr>\n", "      <td>1000</td>\n", "      <td>2.475700</td>\n", "      <td>2.250269</td>\n", "    </tr>\n", "    <tr>\n", "      <td>1500</td>\n", "      <td>2.388600</td>\n", "      <td>2.187856</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2000</td>\n", "      <td>2.357200</td>\n", "      <td>2.142895</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2500</td>\n", "      <td>2.321100</td>\n", "      <td>2.113314</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3000</td>\n", "      <td>2.261300</td>\n", "      <td>2.085970</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3500</td>\n", "      <td>2.234200</td>\n", "      <td>2.070204</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4000</td>\n", "      <td>2.201100</td>\n", "      <td>2.054226</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4500</td>\n", "      <td>2.208100</td>\n", "      <td>2.042915</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5000</td>\n", "      <td>2.198700</td>\n", "      <td>2.033538</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5500</td>\n", "      <td>2.205100</td>\n", "      <td>2.026212</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6000</td>\n", "      <td>2.191900</td>\n", "      <td>2.017387</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6500</td>\n", "      <td>2.174800</td>\n", "      <td>2.010891</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7000</td>\n", "      <td>2.141100</td>\n", "      <td>2.004756</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7500</td>\n", "      <td>2.206400</td>\n", "      <td>1.999410</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8000</td>\n", "      <td>2.173600</td>\n", "      <td>1.994931</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8500</td>\n", "      <td>2.172300</td>\n", "      <td>1.994012</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9000</td>\n", "      <td>2.213300</td>\n", "      <td>2.015019</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9500</td>\n", "      <td>2.799100</td>\n", "      <td>2.554468</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10000</td>\n", "      <td>4.297800</td>\n", "      <td>3.841198</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10500</td>\n", "      <td>5.640200</td>\n", "      <td>4.950907</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11000</td>\n", "      <td>5.799200</td>\n", "      <td>5.001400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11500</td>\n", "      <td>5.774000</td>\n", "      <td>4.992718</td>\n", "    </tr>\n", "    <tr>\n", "      <td>12000</td>\n", "      <td>5.771500</td>\n", "      <td>4.992107</td>\n", "    </tr>\n", "    <tr>\n", "      <td>12500</td>\n", "      <td>5.745500</td>\n", "      <td>4.991365</td>\n", "    </tr>\n", "    <tr>\n", "      <td>13000</td>\n", "      <td>5.722000</td>\n", "      <td>4.990637</td>\n", "    </tr>\n", "    <tr>\n", "      <td>13500</td>\n", "      <td>5.729600</td>\n", "      <td>4.989910</td>\n", "    </tr>\n", "    <tr>\n", "      <td>14000</td>\n", "      <td>5.746800</td>\n", "      <td>4.989182</td>\n", "    </tr>\n", "    <tr>\n", "      <td>14500</td>\n", "      <td>5.693100</td>\n", "      <td>4.988506</td>\n", "    </tr>\n", "    <tr>\n", "      <td>15000</td>\n", "      <td>5.710400</td>\n", "      <td>4.987792</td>\n", "    </tr>\n", "    <tr>\n", "      <td>15500</td>\n", "      <td>5.798700</td>\n", "      <td>4.987321</td>\n", "    </tr>\n", "    <tr>\n", "      <td>16000</td>\n", "      <td>5.708400</td>\n", "      <td>4.986889</td>\n", "    </tr>\n", "    <tr>\n", "      <td>16500</td>\n", "      <td>5.766200</td>\n", "      <td>4.986449</td>\n", "    </tr>\n", "    <tr>\n", "      <td>17000</td>\n", "      <td>5.726400</td>\n", "      <td>4.986026</td>\n", "    </tr>\n", "    <tr>\n", "      <td>17500</td>\n", "      <td>5.751900</td>\n", "      <td>4.985595</td>\n", "    </tr>\n", "    <tr>\n", "      <td>18000</td>\n", "      <td>5.741800</td>\n", "      <td>4.985159</td>\n", "    </tr>\n", "    <tr>\n", "      <td>18500</td>\n", "      <td>5.709700</td>\n", "      <td>4.984707</td>\n", "    </tr>\n", "    <tr>\n", "      <td>19000</td>\n", "      <td>5.651800</td>\n", "      <td>4.984291</td>\n", "    </tr>\n", "    <tr>\n", "      <td>19500</td>\n", "      <td>5.729100</td>\n", "      <td>4.983855</td>\n", "    </tr>\n", "    <tr>\n", "      <td>20000</td>\n", "      <td>5.665500</td>\n", "      <td>4.983415</td>\n", "    </tr>\n", "    <tr>\n", "      <td>20500</td>\n", "      <td>5.716800</td>\n", "      <td>4.982965</td>\n", "    </tr>\n", "    <tr>\n", "      <td>21000</td>\n", "      <td>5.749600</td>\n", "      <td>4.982531</td>\n", "    </tr>\n", "    <tr>\n", "      <td>21500</td>\n", "      <td>5.728100</td>\n", "      <td>4.982170</td>\n", "    </tr>\n", "    <tr>\n", "      <td>22000</td>\n", "      <td>5.688600</td>\n", "      <td>4.981865</td>\n", "    </tr>\n", "    <tr>\n", "      <td>22500</td>\n", "      <td>5.713600</td>\n", "      <td>4.981537</td>\n", "    </tr>\n", "    <tr>\n", "      <td>23000</td>\n", "      <td>5.723600</td>\n", "      <td>4.981206</td>\n", "    </tr>\n", "    <tr>\n", "      <td>23500</td>\n", "      <td>5.730400</td>\n", "      <td>4.980890</td>\n", "    </tr>\n", "    <tr>\n", "      <td>24000</td>\n", "      <td>5.745400</td>\n", "      <td>4.980569</td>\n", "    </tr>\n", "    <tr>\n", "      <td>24500</td>\n", "      <td>5.680500</td>\n", "      <td>4.980258</td>\n", "    </tr>\n", "    <tr>\n", "      <td>25000</td>\n", "      <td>5.714700</td>\n", "      <td>4.979949</td>\n", "    </tr>\n", "    <tr>\n", "      <td>25500</td>\n", "      <td>5.711800</td>\n", "      <td>4.979637</td>\n", "    </tr>\n", "    <tr>\n", "      <td>26000</td>\n", "      <td>5.740300</td>\n", "      <td>4.979310</td>\n", "    </tr>\n", "    <tr>\n", "      <td>26500</td>\n", "      <td>5.724300</td>\n", "      <td>4.978987</td>\n", "    </tr>\n", "    <tr>\n", "      <td>27000</td>\n", "      <td>5.718300</td>\n", "      <td>4.978642</td>\n", "    </tr>\n", "    <tr>\n", "      <td>27500</td>\n", "      <td>5.764400</td>\n", "      <td>4.978581</td>\n", "    </tr>\n", "    <tr>\n", "      <td>28000</td>\n", "      <td>5.707100</td>\n", "      <td>4.978581</td>\n", "    </tr>\n", "    <tr>\n", "      <td>28500</td>\n", "      <td>5.677700</td>\n", "      <td>4.978581</td>\n", "    </tr>\n", "    <tr>\n", "      <td>29000</td>\n", "      <td>5.862900</td>\n", "      <td>4.978581</td>\n", "    </tr>\n", "    <tr>\n", "      <td>29500</td>\n", "      <td>5.706800</td>\n", "      <td>4.978581</td>\n", "    </tr>\n", "    <tr>\n", "      <td>30000</td>\n", "      <td>5.721800</td>\n", "      <td>4.978581</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["TrainOutput(global_step=30000, training_loss=4.636019141133627, metrics={'train_runtime': 48321.2446, 'train_samples_per_second': 14.9, 'train_steps_per_second': 0.621, 'total_flos': 4.463032919402742e+17, 'train_loss': 4.636019141133627, 'epoch': 7.678395496129486})"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["# 9. Start training\n", "trainer.train()\n"]}, {"cell_type": "code", "execution_count": 20, "id": "6f270357", "metadata": {}, "outputs": [], "source": ["# 13. Inference function\n", "def generate_research_summary(article, model, tokenizer):\n", "    inputs = tokenizer(\n", "        \"summarize: \" + article[:5000],\n", "        max_length=512,\n", "        truncation=True,\n", "        return_tensors=\"pt\"\n", "    ).to(model.device)\n", "    \n", "    outputs = model.generate(\n", "            input_ids=inputs.input_ids,\n", "            attention_mask=inputs.attention_mask,\n", "            max_length=256,\n", "            num_beams=4,\n", "            early_stopping=True\n", "        )\n", "    \n", "    return tokenizer.decode(outputs[0], skip_special_tokens=True)"]}, {"cell_type": "code", "execution_count": 46, "id": "7dbccacc", "metadata": {}, "outputs": [], "source": ["\n", "def load_saved_model(model_path):\n", "    base_model = T5ForConditionalGeneration.from_pretrained(\"t5-base\")\n", "    \n", "    model = PeftModel.from_pretrained(base_model, model_path)\n", "    \n", "    tokenizer = T5Tokenizer.from_pretrained(model_path)\n", "    \n", "    return model, tokenizer\n", "\n", "def evaluate_summarization(model, tokenizer, dataset, max_samples=3):\n", "    rouge = evaluate.load('rouge')\n", "    bleu = evaluate.load('bleu')\n", "    \n", "    generated_summaries = []\n", "    reference_summaries = []\n", "    \n", "    for example in dataset.select(range(max_samples)):\n", "        inputs = tokenizer(\n", "            \"summarize: \" + example[\"Abstract\"][:5000],\n", "            max_length=512,\n", "            truncation=True,\n", "            return_tensors=\"pt\"\n", "        ).to(model.device)\n", "        \n", "        outputs = model.generate(\n", "            input_ids=inputs.input_ids,\n", "            attention_mask=inputs.attention_mask,\n", "            max_length=256,\n", "            num_beams=4,\n", "            early_stopping=True\n", "        )\n", "\n", "        generated = tokenizer.decode(outputs[0], skip_special_tokens=True)\n", "        generated_summaries.append(generated)\n", "        reference_summaries.append([example[\"Abstract\"]])  # Wrap in list for BLEU\n", "\n", "    results = {}\n", "    \n", "    rouge_scores = rouge.compute(\n", "        predictions=generated_summaries,\n", "        references=[ref[0] for ref in reference_summaries],\n", "        use_stemmer=True\n", "    )\n", "    results.update(rouge_scores)\n", "\n", "    bleu_scores = bleu.compute(\n", "        predictions=generated_summaries,\n", "        references=reference_summaries,\n", "        max_order=4\n", "    )\n", "    results.update(bleu_scores)\n", "    \n", "    return results"]}, {"cell_type": "code", "execution_count": null, "id": "02f8f0b2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Final Evaluation Results:\n", "ROUGE-1: 0.6030\n", "ROUGE-2: 0.5850\n", "ROUGE-L: 0.5962\n", "BLEU: 0.2361\n", "BLEU-1: 0.9810\n", "BLEU-2: 0.9495\n", "BLEU-3: 0.9244\n", "BLEU-4: 0.9016\n"]}], "source": ["\n", "model_path = \"./checkpoint-8500\"\n", "model, tokenizer = load_saved_model(model_path)\n", "model = model.to(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "    \n", "dataset = load_dataset('csv', data_files='Brain_Dead_CompScholar_Dataset.csv')['train']\n", "\n", "test_results = evaluate_summarization(model, tokenizer, dataset, max_samples=300)\n", "\n", "print(\"\\nFinal Evaluation Results:\")\n", "print(f\"ROUGE-1: {test_results['rouge1']:.4f}\")\n", "print(f\"ROUGE-2: {test_results['rouge2']:.4f}\")\n", "print(f\"ROUGE-L: {test_results['rougeL']:.4f}\")\n", "print(f\"BLEU: {test_results['bleu']:.4f}\")\n", "print(f\"BLEU-1: {test_results['precisions'][0]:.4f}\")\n", "print(f\"BLEU-2: {test_results['precisions'][1]:.4f}\")\n", "print(f\"BLEU-3: {test_results['precisions'][2]:.4f}\")\n", "print(f\"BLEU-4: {test_results['precisions'][3]:.4f}\")"]}, {"cell_type": "code", "execution_count": 44, "id": "f6765392", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Generated Summary: Abstractive Text Summarization (ATS), which is the task of constructing summary sentences by merging facts from different source sentences and condensing them into a shorter representation while preserving information content and overall meaning. In this paper, we propose an LSTM-CNN based ATS framework (ATSDL) that can construct new sentences by exploring more fine-grained fragments than sentences, namely, semantic phrases. Experimental results on the datasets CNN and DailyMail show that our ATSDL framework outperforms the state-the-art models in terms of both semantics and syntactic structure, and achieves competitive results on manual linguistic quality evaluation.\n"]}], "source": ["sample_article = dataset[2][\"Abstract\"]\n", "summary = generate_research_summary(sample_article, model, tokenizer)\n", "print(\"\\nGenerated Summary:\", summary)"]}, {"cell_type": "code", "execution_count": null, "id": "c47e8c27", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "unsloth_env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}