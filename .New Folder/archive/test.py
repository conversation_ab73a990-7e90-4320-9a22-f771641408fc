import os
import csv
from pathlib import Path

def process_images_and_csv(image_folder, input_csv, output_csv):

    try:
        with open(input_csv, 'r') as f:
            reader = csv.DictReader(f)
            data = list(reader)
        print(f"Successfully read {len(data)} rows from {input_csv}")
    except Exception as e:
        print(f"Error reading input CSV file: {e}")
        return

    if not os.path.isdir(image_folder):
        print(f"Error: The folder '{image_folder}' does not exist.")
        return

    image_files = [f for f in os.listdir(image_folder) if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
    print(f"Found {len(image_files)} image files in {image_folder}")
    if not image_files:
        print("No image files found. Checking for nested directories...")
        for root, dirs, files in os.walk(image_folder):
            for file in files:
                if file.lower().endswith(('.jpg', '.jpeg', '.png')):
                    full_path = os.path.join(root, file)
                    rel_path = os.path.relpath(full_path, image_folder)
                    image_files.append(rel_path)
        print(f"Found {len(image_files)} image files after checking nested directories")

    if not image_files:
        print("Error: No image files found in the specified folder or its subdirectories")
        return

    # Sorting image files and createing a mapping of id to image filename
    sorted_image_files = sorted(image_files)
    id_to_image = {str(i+1): img for i, img in enumerate(sorted_image_files)}
    
    print(f"First 5 image files: {sorted_image_files[:5]}")
    print(f"Last 5 image files: {sorted_image_files[-5:]}")

    processed_data = []
    skipped_rows = 0

    # Processing each row in the CSV
    for row in data:
        if 'id' not in row or 'count' not in row:
            print(f"Error: CSV file does not contain 'id' or 'count' columns. Columns found: {', '.join(row.keys())}")
            return
        
        id_value = row['id']
        if id_value in id_to_image:
            processed_data.append({
                'image': id_to_image[id_value],
                'count': row['count']
            })
        else:
            skipped_rows += 1
            if skipped_rows <= 5 or skipped_rows >= len(data) - 5:
                print(f"Warning: No matching image found for ID {id_value}. Skipping this row.")

    # Writeing the processed data to the output CSV file
    try:
        with open(output_csv, 'w', newline='') as f:
            writer = csv.DictWriter(f, fieldnames=['image', 'count'])
            writer.writeheader()
            writer.writerows(processed_data)
        print(f"Processed data has been written to {output_csv}")
        print(f"Number of rows written: {len(processed_data)}")
        print(f"Number of rows skipped: {skipped_rows}")
    except Exception as e:
        print(f"Error writing to output CSV file: {e}")


image_folder = 'frames'  
input_csv = 'labels.csv'
output_csv = 'output.csv'

process_images_and_csv(image_folder, input_csv, output_csv)


