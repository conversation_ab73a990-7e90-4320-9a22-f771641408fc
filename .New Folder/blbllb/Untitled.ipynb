{"cells": [{"cell_type": "code", "execution_count": 16, "id": "d30aca58-881d-4008-9c58-b2e36167a3d6", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import glob"]}, {"cell_type": "code", "execution_count": 28, "id": "9902dad0-b7c5-48de-838e-f379bb2bb8ab", "metadata": {}, "outputs": [], "source": ["file_paths = [\"Count1.xlsx\", \"Count.xlsx\", \"image_data.xlsx\", \"image_n_data.xlsx\"]"]}, {"cell_type": "code", "execution_count": 30, "id": "a6485a54-1742-4e39-86eb-e943775c55d4", "metadata": {}, "outputs": [], "source": ["dataframes = []\n", "\n", "for file_path in file_paths:\n", "    df = pd.read_excel(file_path)\n", "    dataframes.append(df)"]}, {"cell_type": "code", "execution_count": 32, "id": "488cf6e5-c081-4d0c-98c1-e797c0b28b93", "metadata": {}, "outputs": [], "source": ["merged_df = pd.concat(dataframes, ignore_index=True)"]}, {"cell_type": "code", "execution_count": 34, "id": "48a5d3d9-d5ee-442c-a3ab-1b8bd5da9a38", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Files merged successfully into 'labels.csv'.\n"]}], "source": ["merged_df.to_csv(\"labels.csv\", index=False)\n", "\n", "print(\"Files merged successfully into 'labels.csv'.\")"]}, {"cell_type": "code", "execution_count": 36, "id": "1d107bda-7311-4f7b-b6e5-e041e7340bc0", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Image name</th>\n", "      <th>Count</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>frame1</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>frame2</td>\n", "      <td>7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>frame3</td>\n", "      <td>7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>frame 4</td>\n", "      <td>10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>frame5</td>\n", "      <td>8</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>frame6</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>frame7</td>\n", "      <td>7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>frame8</td>\n", "      <td>6</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>frame9</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>frame10</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>frame10</td>\n", "      <td>59</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>frame11</td>\n", "      <td>101</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>frame12</td>\n", "      <td>175</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>frame13</td>\n", "      <td>189</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>frame14</td>\n", "      <td>100</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>frame15</td>\n", "      <td>150</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>frame16</td>\n", "      <td>120</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>frame17</td>\n", "      <td>140</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>frame18</td>\n", "      <td>120</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>frame19</td>\n", "      <td>110</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>IMG-20241012-WA0050.jpg</td>\n", "      <td>60</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>IMG-20241012-WA0052.jpg</td>\n", "      <td>106</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>IMG-20241012-WA0054.jpg</td>\n", "      <td>97</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>IMG-20241012-WA0056.jpg</td>\n", "      <td>72</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>IMG-20241012-WA0058.jpg</td>\n", "      <td>76</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>IMG-20241012-WA0060.jpg</td>\n", "      <td>77</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>IMG-20241012-WA0062.jpg</td>\n", "      <td>89</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>IMG-20241012-WA0064.jpg</td>\n", "      <td>90</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>IMG-20241012-WA0066.jpg</td>\n", "      <td>109</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>frame1</td>\n", "      <td>42</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>frame2</td>\n", "      <td>102</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31</th>\n", "      <td>frame3</td>\n", "      <td>97</td>\n", "    </tr>\n", "    <tr>\n", "      <th>32</th>\n", "      <td>frame4</td>\n", "      <td>144</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33</th>\n", "      <td>frame5</td>\n", "      <td>107</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34</th>\n", "      <td>frame6</td>\n", "      <td>95</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35</th>\n", "      <td>frame7</td>\n", "      <td>44</td>\n", "    </tr>\n", "    <tr>\n", "      <th>36</th>\n", "      <td>frame8</td>\n", "      <td>39</td>\n", "    </tr>\n", "    <tr>\n", "      <th>37</th>\n", "      <td>frame9</td>\n", "      <td>43</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                 Image name  Count\n", "0                    frame1      2\n", "1                    frame2      7\n", "2                    frame3      7\n", "3                   frame 4     10\n", "4                    frame5      8\n", "5                    frame6      3\n", "6                    frame7      7\n", "7                    frame8      6\n", "8                    frame9      3\n", "9                   frame10      3\n", "10                  frame10     59\n", "11                  frame11    101\n", "12                  frame12    175\n", "13                  frame13    189\n", "14                  frame14    100\n", "15                  frame15    150\n", "16                  frame16    120\n", "17                  frame17    140\n", "18                  frame18    120\n", "19                  frame19    110\n", "20  IMG-20241012-WA0050.jpg     60\n", "21  IMG-20241012-WA0052.jpg    106\n", "22  IMG-20241012-WA0054.jpg     97\n", "23  IMG-20241012-WA0056.jpg     72\n", "24  IMG-20241012-WA0058.jpg     76\n", "25  IMG-20241012-WA0060.jpg     77\n", "26  IMG-20241012-WA0062.jpg     89\n", "27  IMG-20241012-WA0064.jpg     90\n", "28  IMG-20241012-WA0066.jpg    109\n", "29                   frame1     42\n", "30                   frame2    102\n", "31                   frame3     97\n", "32                   frame4    144\n", "33                   frame5    107\n", "34                   frame6     95\n", "35                   frame7     44\n", "36                   frame8     39\n", "37                   frame9     43"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["merged_df"]}, {"cell_type": "code", "execution_count": null, "id": "5641ab8d-42ab-4b95-bfd9-b099f43af2b1", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 5}