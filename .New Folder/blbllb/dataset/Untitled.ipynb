{"cells": [{"cell_type": "code", "execution_count": 15, "id": "ad5e002f-2733-4c7e-99ab-92e9fdc91e7d", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from sklearn.metrics import mean_absolute_error, mean_squared_error\n", "\n", "def calculate_similarity_score(original_csv, predicted_csv, output_csv=None):\n", "    \"\"\"\n", "    Calculate similarity score between original and predicted person counts.\n", "    \n", "    Parameters:\n", "    - original_csv: str, path to the CSV file with original counts\n", "    - predicted_csv: str, path to the CSV file with predicted counts\n", "    - output_csv: str, optional path to save the result CSV with similarity scores\n", "\n", "    Returns:\n", "    - A dictionary with MAE, MSE, and Average Similarity Score\n", "    \"\"\"\n", "    # Load the CSV files\n", "    original_data = pd.read_csv(original_csv)\n", "    predicted_data = pd.read_csv(predicted_csv)\n", "    \n", "    # Rename columns to avoid conflicts during merge\n", "    original_data = original_data.rename(columns={\"Count\": \"Count_original\"})\n", "    predicted_data = predicted_data.rename(columns={\"Count\": \"Count_predicted\"})\n", "    \n", "    # Merge the data on \"Image name\" to align original and predicted values\n", "    data = pd.merge(original_data, predicted_data, on=\"Image name\")\n", "    \n", "    # Calculate the absolute differences between original and predicted person counts\n", "    data['error'] = abs(data['Count_original'] - data['Count_predicted'])\n", "    \n", "    # Calculate Mean Absolute Error (MAE)\n", "    mae = mean_absolute_error(data['Count_original'], data['Count_predicted'])\n", "    \n", "    # Calculate Mean Squared Error (MSE)\n", "    mse = mean_squared_error(data['Count_original'], data['Count_predicted'])\n", "    \n", "    # Calculate a custom similarity score (1 - normalized error)\n", "    data['similarity_score'] = 1 - (data['error'] / data['Count_original'].max())\n", "    \n", "    # Calculate the average similarity score\n", "    average_similarity_score = data['similarity_score'].mean()\n", "    \n", "    # Save the results with similarity scores for each image if output_csv is specified\n", "    if output_csv:\n", "        data.to_csv(output_csv, index=False)\n", "    \n", "    # Return the results as a dictionary\n", "    return {\n", "        \"Mean Absolute Error (MAE)\": mae,\n", "        \"Mean Squared Error (MSE)\": mse,\n", "        \"Average Similarity Score\": average_similarity_score\n", "    }\n"]}, {"cell_type": "code", "execution_count": 19, "id": "4e7c9cd6-f132-41b0-943c-932b6d695b81", "metadata": {}, "outputs": [], "source": ["original_data = pd.DataFrame({\n", "    \"Image name\": [\"img1.jpg\", \"img2.jpg\", \"img3.jpg\", \"img4.jpg\", \"img5.jpg\"],\n", "    \"Count\": [10, 20, 15, 25, 90]\n", "})\n", "\n", "predicted_data = pd.DataFrame({\n", "    \"Image name\": [\"img1.jpg\", \"img2.jpg\", \"img3.jpg\", \"img4.jpg\", \"img5.jpg\"],\n", "    \"Count\": [9, 18, 16, 27, 90]\n", "})\n", "\n", "original_data.to_csv(\"original_counts.csv\", index=False)\n", "predicted_data.to_csv(\"predicted_counts.csv\", index=False)"]}, {"cell_type": "code", "execution_count": 21, "id": "44e7500e-f5fb-486f-9b67-cfad55803101", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'Mean Absolute Error (MAE)': 1.2, 'Mean Squared Error (MSE)': 2.0, 'Average Similarity Score': 0.9866666666666667}\n"]}], "source": ["result = calculate_similarity_score(\"original_counts.csv\", \"predicted_counts.csv\", \"similarity_scores.csv\")\n", "print(result)"]}, {"cell_type": "code", "execution_count": 23, "id": "79f3c720-8e50-4e53-a653-d918cddc859c", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Image name</th>\n", "      <th>Count_original</th>\n", "      <th>Count_predicted</th>\n", "      <th>error</th>\n", "      <th>similarity_score</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>img1.jpg</td>\n", "      <td>10</td>\n", "      <td>9</td>\n", "      <td>1</td>\n", "      <td>0.988889</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>img2.jpg</td>\n", "      <td>20</td>\n", "      <td>18</td>\n", "      <td>2</td>\n", "      <td>0.977778</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>img3.jpg</td>\n", "      <td>15</td>\n", "      <td>16</td>\n", "      <td>1</td>\n", "      <td>0.988889</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>img4.jpg</td>\n", "      <td>25</td>\n", "      <td>27</td>\n", "      <td>2</td>\n", "      <td>0.977778</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>img5.jpg</td>\n", "      <td>90</td>\n", "      <td>90</td>\n", "      <td>0</td>\n", "      <td>1.000000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  Image name  Count_original  Count_predicted  error  similarity_score\n", "0   img1.jpg              10                9      1          0.988889\n", "1   img2.jpg              20               18      2          0.977778\n", "2   img3.jpg              15               16      1          0.988889\n", "3   img4.jpg              25               27      2          0.977778\n", "4   img5.jpg              90               90      0          1.000000"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["df = pd.read_csv('similarity_scores.csv')\n", "df"]}, {"cell_type": "code", "execution_count": null, "id": "782f2117-adee-4487-b68d-f2b088a9f01c", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 5}