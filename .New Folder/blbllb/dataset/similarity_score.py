import pandas as pd
from sklearn.metrics import mean_absolute_error, mean_squared_error

def calculate_similarity_score(original_csv, predicted_csv, output_csv=None):
    """
    Calculate similarity score between original and predicted person counts.
    
    Parameters:
    - original_csv: str, path to the CSV file with original counts
    - predicted_csv: str, path to the CSV file with predicted counts
    - output_csv: str, optional path to save the result CSV with similarity scores

    Returns:
    - A dictionary with MAE, MSE, and Average Similarity Score
    """

    original_data = pd.read_csv(original_csv)
    predicted_data = pd.read_csv(predicted_csv)
    
    original_data = original_data.rename(columns={"Count": "Count_original"})
    predicted_data = predicted_data.rename(columns={"Count": "Count_predicted"})
    
    data = pd.merge(original_data, predicted_data, on="Image name")
    
    data['error'] = abs(data['Count_original'] - data['Count_predicted'])
    
    mae = mean_absolute_error(data['Count_original'], data['Count_predicted'])
    
    mse = mean_squared_error(data['Count_original'], data['Count_predicted'])
    
    data['similarity_score'] = 1 - (data['error'] / data['Count_original'].max())
    
    average_similarity_score = data['similarity_score'].mean()
    
    if output_csv:
        data.to_csv(output_csv, index=False)
    
    return {
        "Mean Absolute Error (MAE)": mae,
        "Mean Squared Error (MSE)": mse,
        "Average Similarity Score": average_similarity_score
    }

result = calculate_similarity_score("original_counts.csv", "predicted_counts.csv", "similarity_scores.csv")
print(result)