import cv2
import numpy as np
import pyautogui
import mediapipe as mp
from time import time

class FingerMouseController:
    def __init__(self):
        # Initialize MediaPipe Hands
        self.mp_hands = mp.solutions.hands
        self.hands = self.mp_hands.Hands(
            max_num_hands=1,
            min_detection_confidence=0.7,
            min_tracking_confidence=0.7
        )
        self.mp_draw = mp.solutions.drawing_utils
        
        # Get screen size
        self.screen_width, self.screen_height = pyautogui.size()
        
        # Initialize webcam
        self.cap = cv2.VideoCapture(0)
        
        # Set webcam resolution
        self.cap.set(3, 1280)
        self.cap.set(4, 720)
        
        # Mouse control parameters
        self.smoothening = 5
        self.prev_x, self.prev_y = 0, 0
        self.curr_x, self.curr_y = 0, 0

    def process_frame(self):
        success, img = self.cap.read()
        if not success:
            return False

        # Flip image horizontally for natural movement
        img = cv2.flip(img, 1)
        
        # Convert to RGB for MediaPipe
        img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
        
        # Process hand landmarks
        results = self.hands.process(img_rgb)
        
        if results.multi_hand_landmarks:
            for hand_landmarks in results.multi_hand_landmarks:
                # Draw landmarks for visualization
                self.mp_draw.draw_landmarks(img, hand_landmarks, self.mp_hands.HAND_CONNECTIONS)
                
                # Get index fingertip position
                index_tip = hand_landmarks.landmark[8]
                
                # Convert coordinates to screen position
                self.curr_x = int(np.interp(index_tip.x, [0, 1], [0, self.screen_width]))
                self.curr_y = int(np.interp(index_tip.y, [0, 1], [0, self.screen_height]))
                
                # Smooth movement
                smooth_x = int(self.prev_x + (self.curr_x - self.prev_x) / self.smoothening)
                smooth_y = int(self.prev_y + (self.curr_y - self.prev_y) / self.smoothening)
                
                # Move mouse cursor
                pyautogui.moveTo(smooth_x, smooth_y)
                
                # Update previous positions
                self.prev_x, self.prev_y = smooth_x, smooth_y
                
                # Check for mouse click (thumb and index finger pinch)
                thumb_tip = hand_landmarks.landmark[4]
                if self.calculate_distance(thumb_tip, index_tip) < 0.05:
                    pyautogui.click()
        
        # Display processed frame
        cv2.imshow("Finger Mouse Control", img)
        return True

    def calculate_distance(self, p1, p2):
        return ((p1.x - p2.x) ** 2 + (p1.y - p2.y) ** 2) ** 0.5

    def run(self):
        while True:
            if not self.process_frame():
                break
                
            # Press 'q' to quit
            if cv2.waitKey(1) & 0xFF == ord('q'):
                break
        
        self.cleanup()

    def cleanup(self):
        self.cap.release()
        cv2.destroyAllWindows()

if __name__ == "__main__":
    # Fail-safe for mouse control
    pyautogui.FAILSAFE = True
    
    # Create and run controller
    controller = FingerMouseController()
    controller.run()
