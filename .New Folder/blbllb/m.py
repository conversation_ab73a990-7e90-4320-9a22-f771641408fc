from openpyxl import Workbook

# Create a workbook and select the active worksheet
wb = Workbook()
ws = wb.active

# Set the header for the columns
ws.append(["Image name", "Count"])

# Data to be written to the Excel file
data = [
    ["frame1", 42],
    ["frame2", 102],
    ["frame3", 97],
    ["frame4", 144],
    ["frame5", 107],
    ["frame6", 95],
    ["frame7", 44],
    ["frame8", 39],
    ["frame9", 43]
]

# Add each row of data to the worksheet
for row in data:
    ws.append(row)

# Save the workbook as 'image_data.xlsx'
wb.save("image_n_data.xlsx")
print("Excel file 'image_data.xlsx' created successfully.")
