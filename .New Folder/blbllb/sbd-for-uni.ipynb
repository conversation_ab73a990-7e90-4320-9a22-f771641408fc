{"metadata": {"kernelspec": {"language": "python", "display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python", "version": "3.10.14", "mimetype": "text/x-python", "codemirror_mode": {"name": "ipython", "version": 3}, "pygments_lexer": "ipython3", "nbconvert_exporter": "python", "file_extension": ".py"}, "kaggle": {"accelerator": "nvidiaTeslaT4", "dataSources": [{"sourceId": 9348354, "sourceType": "datasetVersion", "datasetId": 5666284}], "dockerImageVersionId": 30787, "isInternetEnabled": true, "language": "python", "sourceType": "notebook", "isGpuEnabled": true}}, "nbformat_minor": 4, "nbformat": 4, "cells": [{"cell_type": "code", "source": "%%capture\n!pip install pyav", "metadata": {"execution": {"iopub.status.busy": "2024-11-14T13:14:09.896051Z", "iopub.execute_input": "2024-11-14T13:14:09.896762Z", "iopub.status.idle": "2024-11-14T13:14:24.815313Z", "shell.execute_reply.started": "2024-11-14T13:14:09.896712Z", "shell.execute_reply": "2024-11-14T13:14:24.813944Z"}, "trusted": true}, "execution_count": 1, "outputs": []}, {"cell_type": "code", "source": "import os\nimport json\nimport torch\nimport numpy as np\nfrom torch.utils.data import Dataset, DataLoader,random_split\nimport csv\nimport cv2\nfrom torchvision import transforms\nimport numpy as np\nfrom PIL import Image\nimport time \nimport av  # Import PyAV\nimport random", "metadata": {"execution": {"iopub.status.busy": "2024-11-14T13:14:24.817877Z", "iopub.execute_input": "2024-11-14T13:14:24.818296Z", "iopub.status.idle": "2024-11-14T13:14:28.111043Z", "shell.execute_reply.started": "2024-11-14T13:14:24.818249Z", "shell.execute_reply": "2024-11-14T13:14:28.110084Z"}, "trusted": true}, "execution_count": 2, "outputs": []}, {"cell_type": "markdown", "source": "## Dataset dataloader", "metadata": {}}, {"cell_type": "code", "source": "class AutoShotDataset(Dataset):\n    def __init__(self, video_dir, csv_file, cuts_file, snip_length=100, get_vid_extracted_frame_length = 720):\n        self.video_dir = video_dir\n        self.videos = self._load_videos(csv_file)\n        self.cuts = self._load_cuts(cuts_file)\n        self.snip_length = snip_length\n        self.get_vid_extracted_frame_length = get_vid_extracted_frame_length\n\n\n    def _load_videos(self, csv_file):\n        videos = []\n        with open(csv_file, 'r') as f:\n            csv_reader = csv.reader(f)\n            next(csv_reader)\n            for row in csv_reader:\n                videos.append(row[0])  # Assuming the video filename is in the first column\n        return videos\n\n    def _load_cuts(self, cuts_file):\n        with open(cuts_file, 'r') as f:\n            cuts_data = json.load(f)\n\n        cuts = {}\n        for video, cut_list in cuts_data.items():\n            cuts[video] = [tuple(cut) for cut in cut_list]\n        return cuts\n\n    def __len__(self):\n        return len(self.videos)\n    \n    \n    \n    \n    # main get item function of the dataLoader------------------------------------------------------\n    def __getitem__(self, idx):\n        video_name = self.videos[idx]\n        video_path = os.path.join(self.video_dir, video_name)\n        video_cuts = self.cuts.get(video_name, [])\n        \n        # Load the video tensor with a maximum frame length\n        video_tensor = self.get_video(video_path, MAX_FRAMES=self.get_vid_extracted_frame_length)\n        labels = torch.zeros(len(video_tensor), dtype=torch.int32)\n        \n        # Assign labels for specified cuts\n        for start, end in video_cuts:\n            labels[start:end + 1] = 1\n    \n        # Determine random start index and initial snippet\n        if len(video_tensor) > self.snip_length:\n            # 50% chance to reroll if no label 1 is found in the snippet\n            reroll = random.choices([True, False], weights=[4, 1])[0]\n            \n            # Get a random snippet of the specified length\n            rand_idx = random.randint(0, len(video_tensor) - self.snip_length)\n            video_snip = video_tensor[rand_idx:rand_idx + self.snip_length]\n            label_snip = labels[rand_idx:rand_idx + self.snip_length]\n            \n            # Reroll once if reroll is True and no 1 is found in label_snip\n            if reroll and 1 not in label_snip:\n                rand_idx = random.randint(0, len(video_tensor) - self.snip_length)\n                video_snip = video_tensor[rand_idx:rand_idx + self.snip_length]\n                label_snip = labels[rand_idx:rand_idx + self.snip_length]\n        else:\n            # If the video is shorter than snip_length, take the whole video and pad\n            video_snip = video_tensor\n            label_snip = labels\n            \n            # Calculate padding needed\n            pad_len = self.snip_length - len(video_snip)\n            \n            # Repeat the last frame for video padding\n            last_frame = video_snip[-1].unsqueeze(0)  # Add batch dimension to last frame\n            video_snip = torch.cat([video_snip, last_frame.repeat(pad_len, *([1] * (video_snip.dim() - 1)))], dim=0)\n    \n            # Pad labels with the last label value\n            last_label = label_snip[-1].unsqueeze(0)\n            label_snip = torch.cat([label_snip, last_label.repeat(pad_len)], dim=0)\n    \n        return video_snip, label_snip\n\n    \n\n    # loades the video from the dataset and returns as tensors-------------------------------------------------------------\n\n    def get_video(self, video_path,MAX_FRAMES, max_attempts=20):\n        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n        \n        for i in range(max_attempts):\n            container = None\n            try:\n                container = av.open(video_path)\n                frames = []\n                total_frames = container.streams.video[0].frames\n\n                # Transformation pipeline\n                resolution = (224, 224)\n                transform_resize = transforms.Compose([\n                    transforms.Resize(resolution),\n                    transforms.ToTensor(),\n                ])\n\n\n                for frame in container.decode(video=0):\n                    img = frame.to_image()\n                    try:\n                        tensor = transform_resize(img).to(device)\n                        frames.append(tensor)\n                    finally:\n                        # Explicitly close PIL Image\n                        img.close()\n                        \n                        if len(frames) >= MAX_FRAMES:\n#                             print(F\"len of frames exceeded {MAX_FRAMES} video: {video_path}\")\n                            \n                            break\n\n                # Stack frames\n                if not frames:\n                    continue\n\n                video_tensor = torch.stack(frames)\n\n                if video_tensor.size(0) < total_frames - 1 and video_tensor.size(0) < MAX_FRAMES:\n                    print(f\"Expected {total_frames} frames, but extracted {video_tensor.size(0)} frames.\")\n                    print(f\"Attempt {i+1}\")\n                    if i == max_attempts - 1:\n                        return torch.tensor([]).to(device)\n                    continue\n\n                # Move normalization constants to device once\n                mean = torch.tensor([0.485, 0.456, 0.406], device=device).view(1, 3, 1, 1)\n                std = torch.tensor([0.229, 0.224, 0.225], device=device).view(1, 3, 1, 1)\n                video_tensor = (video_tensor - mean) / std\n\n                return video_tensor\n\n            except Exception as e:\n                if i == max_attempts - 1:\n                    raise ValueError(f\"Failed to load video: {video_path}. Error: {str(e)}\")\n            finally:\n                # Always close container\n                if container:\n                    container.close()\n\n        return torch.tensor([]).to(device)\n\n#----------------------------------------------------------------------------------------------------------------------\n            \n# Example usage\nvideo_dir = '/kaggle/input/autoshot_dataset/all the videos'\ncsv_file = '/kaggle/input/autoshot_dataset/cleaned_unique_mp4_files.csv'\ncuts_file = '/kaggle/input/video_cuts.json'\n\ndataset = AutoShotDataset(video_dir, csv_file, cuts_file,snip_length=16,get_vid_extracted_frame_length=720)\nyes = 0\nno = 0\nfor i in range(2):\n    start = time.time()\n    video,label = dataset.__getitem__(i)\n    print(time.time() - start,video.shape)\n    if 1 in label:\n        answer = 'yes' \n        yes += 1\n    else:\n        answer = 'no' \n        no += 1\n    print(\"does it contain a cut?: \",answer)\n    print(label.tolist())\nprint(\"final number of yes and no: yes:\",yes,\"no: \",no)", "metadata": {"_uuid": "8f2839f25d086af736a60e9eeb907d3b93b6e0e5", "_cell_guid": "b1076dfc-b9ad-4769-8c92-a6c4dae69d19", "execution": {"iopub.status.busy": "2024-11-14T13:14:28.112352Z", "iopub.execute_input": "2024-11-14T13:14:28.112766Z", "iopub.status.idle": "2024-11-14T13:15:05.064984Z", "shell.execute_reply.started": "2024-11-14T13:14:28.112733Z", "shell.execute_reply": "2024-11-14T13:15:05.063920Z"}, "trusted": true}, "execution_count": 3, "outputs": [{"name": "stdout", "text": "27.36673617362976 torch.<PERSON><PERSON>([16, 3, 224, 224])\ndoes it contain a cut?:  yes\n[1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n9.52769660949707 torch.<PERSON><PERSON>([16, 3, 224, 224])\ndoes it contain a cut?:  no\n[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\nfinal number of yes and no: yes: 1 no:  1\n", "output_type": "stream"}]}, {"cell_type": "markdown", "source": "## Model ", "metadata": {}}, {"cell_type": "code", "source": "import torch\nfrom torch.optim import Adam<PERSON>\nfrom torchvision.models.video import mvit_v2_s\nimport torch.nn as nn\nimport torch.nn.functional as F\n\nclass VideoClassifier(nn.Module):\n    def __init__(self, num_classes=16, dropout_rate=0.2):\n        super(VideoClassifier, self).__init__()\n        \n        # Load the pretrained MViT-V2-S model\n        self.backbone = mvit_v2_s(weights='DEFAULT')\n        \n        # Get the embedding dimension from MViT\n        hidden_dim = 768  # This is the fixed dimension for MViT-V2-S\n        \n        # Replace the original classifier with our bottleneck and new classifier\n        self.backbone.head = nn.Identity()  # Remove the original classification head\n        \n        # Bottleneck layers with SiLU activation\n        self.bottleneck = nn.Sequential(\n            nn.Linear(hidden_dim, hidden_dim * 4),\n            nn.SiLU(),\n            nn.Dropout(dropout_rate),\n            nn.Linear(hidden_dim * 4, num_classes),  # Classification layer\n            nn.Sigmoid()  # Sigmoid for multi-label classification\n        )\n\n    def forward(self, x):\n        # Expected input shape: (batch_size, num_frames=16, channels=3, height=224, width=224)\n        # Permute to (batch_size, channels, num_frames, height, width)\n        x = x.permute(0, 2, 1, 3, 4)\n        \n        # Get features from backbone\n        features = self.backbone(x)\n        \n        # Pass through bottleneck (includes classification and sigmoid)\n        probabilities = self.bottleneck(features)\n        \n        return probabilities\n\nif __name__ == \"__main__\":\n    # Create sample input (batch_size=2, frames=16, channels=3, height=224, width=224)\n    sample_input = torch.randn(2, 16, 3, 224, 224)\n    \n    # Initialize model\n    model = VideoClassifier(num_classes=16)\n    \n    # Initialize optimizer\n    optimizer = AdamW(model.parameters(), lr=1e-5, weight_decay=0.01)\n    \n    # Move to GPU if available\n    device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n    model = model.to(device)\n    sample_input = sample_input.to(device)\n    \n    # Forward pass\n    outputs = model(sample_input)\n    print(f\"Output shape: {outputs.shape}\")  # Should be (2, 16) for batch_size=2 and num_classes=16\n    print(outputs)\n    print(f\"Output range: ({outputs.min():.3f}, {outputs.max():.3f})\")  # Should be between 0 and 1", "metadata": {"execution": {"iopub.status.busy": "2024-11-14T13:15:05.067473Z", "iopub.execute_input": "2024-11-14T13:15:05.067893Z", "iopub.status.idle": "2024-11-14T13:15:08.206718Z", "shell.execute_reply.started": "2024-11-14T13:15:05.067845Z", "shell.execute_reply": "2024-11-14T13:15:08.205740Z"}, "trusted": true}, "execution_count": 4, "outputs": [{"name": "stderr", "text": "Downloading: \"https://download.pytorch.org/models/mvit_v2_s-ae3be167.pth\" to /root/.cache/torch/hub/checkpoints/mvit_v2_s-ae3be167.pth\n100%|██████████| 132M/132M [00:00<00:00, 186MB/s]  \n", "output_type": "stream"}, {"name": "stdout", "text": "Output shape: torch.Size([2, 16])\ntensor([[0.5002, 0.5033, 0.5006, 0.4922, 0.5013, 0.4951, 0.4992, 0.5067, 0.5004,\n         0.5063, 0.5021, 0.5015, 0.4934, 0.5021, 0.4985, 0.4996],\n        [0.5031, 0.5093, 0.5013, 0.4927, 0.4906, 0.4984, 0.4988, 0.5032, 0.5015,\n         0.5118, 0.4948, 0.5006, 0.4936, 0.5018, 0.4953, 0.4970]],\n       device='cuda:0', grad_fn=<SigmoidBackward0>)\nOutput range: (0.491, 0.512)\n", "output_type": "stream"}]}, {"cell_type": "code", "source": "predicted = (outputs > 0.5).float()\npredicted", "metadata": {"execution": {"iopub.status.busy": "2024-11-14T13:15:08.208000Z", "iopub.execute_input": "2024-11-14T13:15:08.208327Z", "iopub.status.idle": "2024-11-14T13:15:08.219183Z", "shell.execute_reply.started": "2024-11-14T13:15:08.208293Z", "shell.execute_reply": "2024-11-14T13:15:08.217857Z"}, "trusted": true}, "execution_count": 5, "outputs": [{"execution_count": 5, "output_type": "execute_result", "data": {"text/plain": "tensor([[1., 1., 1., 0., 1., 0., 0., 1., 1., 1., 1., 1., 0., 1., 0., 0.],\n        [1., 1., 1., 0., 0., 0., 0., 1., 1., 1., 0., 1., 0., 1., 0., 0.]],\n       device='cuda:0')"}, "metadata": {}}]}, {"cell_type": "code", "source": "from torch.utils.data import DataLoader, random_split\n\ndef create_dataloaders(dataset, batch_size, train_ratio=0.7, val_ratio=0.15):\n    # Split dataset\n    train_size = int(train_ratio * len(dataset))\n    val_size = int(val_ratio * len(dataset))\n    test_size = len(dataset) - train_size - val_size\n    train_dataset, val_dataset, test_dataset = random_split(dataset, [train_size, val_size, test_size])\n\n    # Create DataLoaders\n    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)\n    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)\n    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)\n    \n    return train_loader, val_loader, test_loader", "metadata": {"execution": {"iopub.status.busy": "2024-11-14T13:15:08.220407Z", "iopub.execute_input": "2024-11-14T13:15:08.220686Z", "iopub.status.idle": "2024-11-14T13:15:08.228225Z", "shell.execute_reply.started": "2024-11-14T13:15:08.220648Z", "shell.execute_reply": "2024-11-14T13:15:08.227293Z"}, "trusted": true}, "execution_count": 6, "outputs": []}, {"cell_type": "code", "source": "import torch\nimport gc\nfrom tqdm import tqdm\nfrom torch.optim import Adam<PERSON>\nimport torch.nn as nn\nfrom torch.amp import autocast, GradScaler  # Updated imports\n\ndef train(model, train_loader, val_loader, optimizer, criterion, device, \n          num_epochs=30, patience=5, save_path=\"/kaggle/working/best_model.pth\", \n          mixed_precision=True):\n    \"\"\"\n    Training function with memory optimization and mixed precision training.\n    \n    Args:\n        model: PyTorch model\n        train_loader: Training data loader\n        val_loader: Validation data loader\n        optimizer: PyTorch optimizer\n        criterion: Loss function\n        device: Device to run the model on\n        num_epochs: Number of training epochs\n        patience: Early stopping patience\n        save_path: Path to save the best model\n        mixed_precision: Whether to use mixed precision training\n    \"\"\"\n    # Initialize early stopping parameters\n    best_val_loss = float('inf')\n    epochs_no_improve = 0\n    \n    # Initialize gradient scaler for mixed precision training\n    scaler = GradScaler('cuda') if mixed_precision else None\n    \n    for epoch in range(num_epochs):\n        # Clear memory before each epoch\n        if torch.cuda.is_available():\n            torch.cuda.empty_cache()\n        gc.collect()\n        \n        model.train()\n        train_loss = 0\n        \n        # Training phase\n        train_pbar = tqdm(train_loader, desc=f\"Epoch [{epoch+1}/{num_epochs}] - Training\")\n        for inputs, labels in train_pbar:\n            # Move data to device and convert to appropriate dtype\n            inputs = inputs.to(device, non_blocking=True).float()\n            labels = labels.to(device, non_blocking=True).float()\n            \n            # Mixed precision training\n            if mixed_precision:\n                with autocast('cuda'):\n                    outputs = model(inputs)\n                    loss = criterion(outputs.float(), labels)\n                \n                # Scale gradients and optimize\n                scaler.scale(loss).backward()\n                scaler.step(optimizer)\n                scaler.update()\n            else:\n                outputs = model(inputs)\n                loss = criterion(outputs.float(), labels)\n                \n                # Standard optimization\n                loss.backward()\n                optimizer.step()\n            \n            optimizer.zero_grad(set_to_none=True)  # More efficient than zero_grad()\n            \n            train_loss += loss.item()\n            \n            # Update progress bar\n            train_pbar.set_postfix({'train_loss': f'{loss.item():.4f}'})\n            \n            # Clear memory after each batch\n            del inputs, labels, outputs, loss\n        \n        # Validation phase\n        model.eval()\n        val_loss = 0\n        val_pbar = tqdm(val_loader, desc=f\"Epoch [{epoch+1}/{num_epochs}] - Validation\")\n        \n        with torch.no_grad():\n            for inputs, labels in val_pbar:\n                inputs = inputs.to(device, non_blocking=True).float()\n                labels = labels.to(device, non_blocking=True).float()\n                \n                if mixed_precision:\n                    with autocast('cuda'):\n                        outputs = model(inputs)\n                        loss = criterion(outputs.float(), labels)\n                else:\n                    outputs = model(inputs)\n                    loss = criterion(outputs.float(), labels)\n                \n                val_loss += loss.item()\n                val_pbar.set_postfix({'val_loss': f'{loss.item():.4f}'})\n                \n                # Clear memory after each batch\n                del inputs, labels, outputs, loss\n        \n        # Calculate average losses\n        train_loss /= len(train_loader)\n        val_loss /= len(val_loader)\n        \n        print(f\"Epoch [{epoch+1}/{num_epochs}], Train Loss: {train_loss:.4f}, Validation Loss: {val_loss:.4f}\")\n        \n        # Early stopping and model saving\n        if val_loss < best_val_loss:\n            best_val_loss = val_loss\n            epochs_no_improve = 0\n            # Save model state\n            torch.save({\n                'epoch': epoch,\n                'model_state_dict': model.state_dict(),\n                'optimizer_state_dict': optimizer.state_dict(),\n                'train_loss': train_loss,\n                'val_loss': val_loss,\n            }, save_path)\n            print(\"Model saved!\")\n        else:\n            epochs_no_improve += 1\n            if epochs_no_improve == patience:\n                print(\"Early stopping triggered!\")\n                break\n        \n        # Clear memory after each epoch\n        if torch.cuda.is_available():\n            torch.cuda.empty_cache()\n        gc.collect()\n\n    return best_val_loss", "metadata": {"execution": {"iopub.status.busy": "2024-11-14T13:15:08.229528Z", "iopub.execute_input": "2024-11-14T13:15:08.229864Z", "iopub.status.idle": "2024-11-14T13:15:08.250337Z", "shell.execute_reply.started": "2024-11-14T13:15:08.229832Z", "shell.execute_reply": "2024-11-14T13:15:08.249478Z"}, "trusted": true}, "execution_count": 7, "outputs": []}, {"cell_type": "code", "source": "def test(model, test_loader, criterion, device):\n    model.eval()\n    test_loss = 0\n    correct = 0\n    total = 0\n    \n    with torch.no_grad():\n        for inputs, labels in tqdm(test_loader, desc=\"Testing\", ):\n            inputs, labels = inputs.to(device).float(), labels.to(device).float()\n            outputs = model(inputs)\n            outputs = (outputs > 0.5).float()\n            loss = criterion(outputs, labels)\n            test_loss += loss.item()\n            \n            # Calculate accuracy\n            _, predicted = torch.max(outputs, 1)\n            total += labels.size(0)\n            correct += (predicted == labels).sum().item()\n    \n    test_loss /= len(test_loader)\n    accuracy = correct / total * 100\n    print(f\"Test Loss: {test_loss:.4f}, Test Accuracy: {accuracy:.2f}%\")\n    return accuracy", "metadata": {"execution": {"iopub.status.busy": "2024-11-14T13:15:08.251494Z", "iopub.execute_input": "2024-11-14T13:15:08.251873Z", "iopub.status.idle": "2024-11-14T13:15:08.263833Z", "shell.execute_reply.started": "2024-11-14T13:15:08.251832Z", "shell.execute_reply": "2024-11-14T13:15:08.262981Z"}, "trusted": true}, "execution_count": 8, "outputs": []}, {"cell_type": "code", "source": "# Define parameters\nbatch_size = 8\nlearning_rate = 1e-5\nweight_decay = 0.0\nnum_classes = 16\nnum_epochs = 30\npatience = 5  # Early stopping patience\n\n\n\n# Initialize dataset and dataloaders\ndataset = AutoShotDataset(video_dir, csv_file, cuts_file, snip_length=16, get_vid_extracted_frame_length=720)\ntrain_loader, val_loader, test_loader = create_dataloaders(dataset, batch_size)\n\n\n\n# Initialize model, optimizer, criterion\nmodel = VideoClassifier(num_classes=num_classes)\n\n\ndevice = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n# Wrap the model with DataParallel to use multiple GPUs\nif torch.cuda.device_count() > 1:\n    model = nn.DataParallel(model)\n\n\n\nmodel = model.to(device)\noptimizer = AdamW(model.parameters(), lr=learning_rate, weight_decay=weight_decay)\ncriterion = nn.CrossEntropyLoss()", "metadata": {"execution": {"iopub.status.busy": "2024-11-14T13:15:08.265065Z", "iopub.execute_input": "2024-11-14T13:15:08.265352Z", "iopub.status.idle": "2024-11-14T13:15:09.115643Z", "shell.execute_reply.started": "2024-11-14T13:15:08.265323Z", "shell.execute_reply": "2024-11-14T13:15:09.114579Z"}, "trusted": true}, "execution_count": 9, "outputs": []}, {"cell_type": "code", "source": "# Train the model\ntrain(model, train_loader, val_loader, optimizer, criterion, device, num_epochs=num_epochs, patience=patience)", "metadata": {"execution": {"iopub.status.busy": "2024-11-13T21:06:36.033085Z", "iopub.execute_input": "2024-11-13T21:06:36.033556Z"}, "collapsed": true, "jupyter": {"outputs_hidden": true}, "trusted": true}, "execution_count": null, "outputs": [{"name": "stderr", "text": "Epoch [1/30] - Training:   0%|          | 0/57 [00:00<?, ?it/s]/opt/conda/lib/python3.10/site-packages/torch/nn/parallel/parallel_apply.py:79: FutureWarning: `torch.cuda.amp.autocast(args...)` is deprecated. Please use `torch.amp.autocast('cuda', args...)` instead.\n  with torch.cuda.device(device), torch.cuda.stream(stream), autocast(enabled=autocast_enabled):\nEpoch [1/30] - Training: 100%|██████████| 57/57 [1:33:07<00:00, 98.03s/it, train_loss=-0.0000]\nEpoch [1/30] - Validation: 100%|██████████| 13/13 [20:44<00:00, 95.71s/it, val_loss=-0.0000] \n", "output_type": "stream"}, {"name": "stdout", "text": "Epoch [1/30], Train Loss: 2.6024, Validation Loss: 1.6253\nModel saved!\n", "output_type": "stream"}, {"name": "stderr", "text": "Epoch [2/30] - Training: 100%|██████████| 57/57 [1:33:16<00:00, 98.18s/it, train_loss=1.1021]  \nEpoch [2/30] - Validation: 100%|██████████| 13/13 [20:59<00:00, 96.87s/it, val_loss=-0.0000] \n", "output_type": "stream"}, {"name": "stdout", "text": "Epoch [2/30], Train Loss: 2.1232, Validation Loss: 1.1739\nModel saved!\n", "output_type": "stream"}, {"name": "stderr", "text": "Epoch [3/30] - Training: 100%|██████████| 57/57 [1:34:19<00:00, 99.29s/it, train_loss=1.1102]  \nEpoch [3/30] - Validation: 100%|██████████| 13/13 [20:29<00:00, 94.55s/it, val_loss=-0.0000] \n", "output_type": "stream"}, {"name": "stdout", "text": "Epoch [3/30], Train Loss: 2.3433, Validation Loss: 2.5319\n", "output_type": "stream"}, {"name": "stderr", "text": "Epoch [4/30] - Training:  39%|███▊      | 22/57 [37:53<58:10, 99.74s/it, train_loss=-0.0000]  ", "output_type": "stream"}]}, {"cell_type": "code", "source": "# Load best model for testing, handling DataParallel key prefix if necessary\nstate_dict = torch.load(\"/kaggle/working/best_model.pth\",weights_only=True)\nif 'module.' in list(state_dict.keys())[0]:  # Check if DataParallel was used\n    state_dict = {k.replace('module.', ''): v for k, v in state_dict.items()}\nmodel.load_state_dict(state_dict)\n\n\n\n# Test the model\naccuracy = test(model, test_loader, criterion, device)\nprint(f\"Final Test Accuracy: {accuracy:.2f}%\")", "metadata": {"execution": {"iopub.status.busy": "2024-11-14T13:16:59.014645Z", "iopub.execute_input": "2024-11-14T13:16:59.015102Z", "iopub.status.idle": "2024-11-14T13:16:59.831306Z", "shell.execute_reply.started": "2024-11-14T13:16:59.015062Z", "shell.execute_reply": "2024-11-14T13:16:59.829705Z"}, "trusted": true}, "execution_count": 12, "outputs": [{"traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mRuntimeError\u001b[0m                              <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[12], line 5\u001b[0m\n\u001b[1;32m      3\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mmodule.\u001b[39m\u001b[38;5;124m'\u001b[39m \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mlist\u001b[39m(state_dict\u001b[38;5;241m.\u001b[39mkeys())[\u001b[38;5;241m0\u001b[39m]:  \u001b[38;5;66;03m# Check if DataParallel was used\u001b[39;00m\n\u001b[1;32m      4\u001b[0m     state_dict \u001b[38;5;241m=\u001b[39m {k\u001b[38;5;241m.\u001b[39mreplace(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mmodule.\u001b[39m\u001b[38;5;124m'\u001b[39m, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124m'\u001b[39m): v \u001b[38;5;28;01mfor\u001b[39;00m k, v \u001b[38;5;129;01min\u001b[39;00m state_dict\u001b[38;5;241m.\u001b[39mitems()}\n\u001b[0;32m----> 5\u001b[0m \u001b[43mmodel\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mload_state_dict\u001b[49m\u001b[43m(\u001b[49m\u001b[43mstate_dict\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m      9\u001b[0m \u001b[38;5;66;03m# Test the model\u001b[39;00m\n\u001b[1;32m     10\u001b[0m accuracy \u001b[38;5;241m=\u001b[39m test(model, test_loader, criterion, device)\n", "File \u001b[0;32m/opt/conda/lib/python3.10/site-packages/torch/nn/modules/module.py:2215\u001b[0m, in \u001b[0;36mModule.load_state_dict\u001b[0;34m(self, state_dict, strict, assign)\u001b[0m\n\u001b[1;32m   2210\u001b[0m         error_msgs\u001b[38;5;241m.\u001b[39minsert(\n\u001b[1;32m   2211\u001b[0m             \u001b[38;5;241m0\u001b[39m, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mMissing key(s) in state_dict: \u001b[39m\u001b[38;5;132;01m{}\u001b[39;00m\u001b[38;5;124m. \u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;241m.\u001b[39mformat(\n\u001b[1;32m   2212\u001b[0m                 \u001b[38;5;124m'\u001b[39m\u001b[38;5;124m, \u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;241m.\u001b[39mjoin(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mk\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m'\u001b[39m \u001b[38;5;28;01mfor\u001b[39;00m k \u001b[38;5;129;01min\u001b[39;00m missing_keys)))\n\u001b[1;32m   2214\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mlen\u001b[39m(error_msgs) \u001b[38;5;241m>\u001b[39m \u001b[38;5;241m0\u001b[39m:\n\u001b[0;32m-> 2215\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mRuntimeError\u001b[39;00m(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mError(s) in loading state_dict for \u001b[39m\u001b[38;5;132;01m{}\u001b[39;00m\u001b[38;5;124m:\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;130;01m\\t\u001b[39;00m\u001b[38;5;132;01m{}\u001b[39;00m\u001b[38;5;124m'\u001b[39m\u001b[38;5;241m.\u001b[39mformat(\n\u001b[1;32m   2216\u001b[0m                        \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m\u001b[38;5;18m__class__\u001b[39m\u001b[38;5;241m.\u001b[39m\u001b[38;5;18m__name__\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;130;01m\\t\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;241m.\u001b[39mjoin(error_msgs)))\n\u001b[1;32m   2217\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m _IncompatibleKeys(missing_keys, unexpected_keys)\n", "\u001b[0;31mRuntimeError\u001b[0m: Error(s) in loading state_dict for DataParallel:\n\tMissing key(s) in state_dict: \"module.backbone.conv_proj.weight\", \"module.backbone.conv_proj.bias\", \"module.backbone.pos_encoding.class_token\", \"module.backbone.blocks.0.norm1.weight\", \"module.backbone.blocks.0.norm1.bias\", \"module.backbone.blocks.0.norm2.weight\", \"module.backbone.blocks.0.norm2.bias\", \"module.backbone.blocks.0.attn.rel_pos_h\", \"module.backbone.blocks.0.attn.rel_pos_w\", \"module.backbone.blocks.0.attn.rel_pos_t\", \"module.backbone.blocks.0.attn.qkv.weight\", \"module.backbone.blocks.0.attn.qkv.bias\", \"module.backbone.blocks.0.attn.project.0.weight\", \"module.backbone.blocks.0.attn.project.0.bias\", \"module.backbone.blocks.0.attn.pool_q.pool.weight\", \"module.backbone.blocks.0.attn.pool_q.norm_act.0.weight\", \"module.backbone.blocks.0.attn.pool_q.norm_act.0.bias\", \"module.backbone.blocks.0.attn.pool_k.pool.weight\", \"module.backbone.blocks.0.attn.pool_k.norm_act.0.weight\", \"module.backbone.blocks.0.attn.pool_k.norm_act.0.bias\", \"module.backbone.blocks.0.attn.pool_v.pool.weight\", \"module.backbone.blocks.0.attn.pool_v.norm_act.0.weight\", \"module.backbone.blocks.0.attn.pool_v.norm_act.0.bias\", \"module.backbone.blocks.0.mlp.0.weight\", \"module.backbone.blocks.0.mlp.0.bias\", \"module.backbone.blocks.0.mlp.3.weight\", \"module.backbone.blocks.0.mlp.3.bias\", \"module.backbone.blocks.1.norm1.weight\", \"module.backbone.blocks.1.norm1.bias\", \"module.backbone.blocks.1.norm2.weight\", \"module.backbone.blocks.1.norm2.bias\", \"module.backbone.blocks.1.attn.rel_pos_h\", \"module.backbone.blocks.1.attn.rel_pos_w\", \"module.backbone.blocks.1.attn.rel_pos_t\", \"module.backbone.blocks.1.attn.qkv.weight\", \"module.backbone.blocks.1.attn.qkv.bias\", \"module.backbone.blocks.1.attn.project.0.weight\", \"module.backbone.blocks.1.attn.project.0.bias\", \"module.backbone.blocks.1.attn.pool_q.pool.weight\", \"module.backbone.blocks.1.attn.pool_q.norm_act.0.weight\", \"module.backbone.blocks.1.attn.pool_q.norm_act.0.bias\", \"module.backbone.blocks.1.attn.pool_k.pool.weight\", \"module.backbone.blocks.1.attn.pool_k.norm_act.0.weight\", \"module.backbone.blocks.1.attn.pool_k.norm_act.0.bias\", \"module.backbone.blocks.1.attn.pool_v.pool.weight\", \"module.backbone.blocks.1.attn.pool_v.norm_act.0.weight\", \"module.backbone.blocks.1.attn.pool_v.norm_act.0.bias\", \"module.backbone.blocks.1.mlp.0.weight\", \"module.backbone.blocks.1.mlp.0.bias\", \"module.backbone.blocks.1.mlp.3.weight\", \"module.backbone.blocks.1.mlp.3.bias\", \"module.backbone.blocks.1.project.weight\", \"module.backbone.blocks.1.project.bias\", \"module.backbone.blocks.2.norm1.weight\", \"module.backbone.blocks.2.norm1.bias\", \"module.backbone.blocks.2.norm2.weight\", \"module.backbone.blocks.2.norm2.bias\", \"module.backbone.blocks.2.attn.rel_pos_h\", \"module.backbone.blocks.2.attn.rel_pos_w\", \"module.backbone.blocks.2.attn.rel_pos_t\", \"module.backbone.blocks.2.attn.qkv.weight\", \"module.backbone.blocks.2.attn.qkv.bias\", \"module.backbone.blocks.2.attn.project.0.weight\", \"module.backbone.blocks.2.attn.project.0.bias\", \"module.backbone.blocks.2.attn.pool_q.pool.weight\", \"module.backbone.blocks.2.attn.pool_q.norm_act.0.weight\", \"module.backbone.blocks.2.attn.pool_q.norm_act.0.bias\", \"module.backbone.blocks.2.attn.pool_k.pool.weight\", \"module.backbone.blocks.2.attn.pool_k.norm_act.0.weight\", \"module.backbone.blocks.2.attn.pool_k.norm_act.0.bias\", \"module.backbone.blocks.2.attn.pool_v.pool.weight\", \"module.backbone.blocks.2.attn.pool_v.norm_act.0.weight\", \"module.backbone.blocks.2.attn.pool_v.norm_act.0.bias\", \"module.backbone.blocks.2.mlp.0.weight\", \"module.backbone.blocks.2.mlp.0.bias\", \"module.backbone.blocks.2.mlp.3.weight\", \"module.backbone.blocks.2.mlp.3.bias\", \"module.backbone.blocks.3.norm1.weight\", \"module.backbone.blocks.3.norm1.bias\", \"module.backbone.blocks.3.norm2.weight\", \"module.backbone.blocks.3.norm2.bias\", \"module.backbone.blocks.3.attn.rel_pos_h\", \"module.backbone.blocks.3.attn.rel_pos_w\", \"module.backbone.blocks.3.attn.rel_pos_t\", \"module.backbone.blocks.3.attn.qkv.weight\", \"module.backbone.blocks.3.attn.qkv.bias\", \"module.backbone.blocks.3.attn.project.0.weight\", \"module.backbone.blocks.3.attn.project.0.bias\", \"module.backbone.blocks.3.attn.pool_q.pool.weight\", \"module.backbone.blocks.3.attn.pool_q.norm_act.0.weight\", \"module.backbone.blocks.3.attn.pool_q.norm_act.0.bias\", \"module.backbone.blocks.3.attn.pool_k.pool.weight\", \"module.backbone.blocks.3.attn.pool_k.norm_act.0.weight\", \"module.backbone.blocks.3.attn.pool_k.norm_act.0.bias\", \"module.backbone.blocks.3.attn.pool_v.pool.weight\", \"module.backbone.blocks.3.attn.pool_v.norm_act.0.weight\", \"module.backbone.blocks.3.attn.pool_v.norm_act.0.bias\", \"module.backbone.blocks.3.mlp.0.weight\", \"module.backbone.blocks.3.mlp.0.bias\", \"module.backbone.blocks.3.mlp.3.weight\", \"module.backbone.blocks.3.mlp.3.bias\", \"module.backbone.blocks.3.project.weight\", \"module.backbone.blocks.3.project.bias\", \"module.backbone.blocks.4.norm1.weight\", \"module.backbone.blocks.4.norm1.bias\", \"module.backbone.blocks.4.norm2.weight\", \"module.backbone.blocks.4.norm2.bias\", \"module.backbone.blocks.4.attn.rel_pos_h\", \"module.backbone.blocks.4.attn.rel_pos_w\", \"module.backbone.blocks.4.attn.rel_pos_t\", \"module.backbone.blocks.4.attn.qkv.weight\", \"module.backbone.blocks.4.attn.qkv.bias\", \"module.backbone.blocks.4.attn.project.0.weight\", \"module.backbone.blocks.4.attn.project.0.bias\", \"module.backbone.blocks.4.attn.pool_q.pool.weight\", \"module.backbone.blocks.4.attn.pool_q.norm_act.0.weight\", \"module.backbone.blocks.4.attn.pool_q.norm_act.0.bias\", \"module.backbone.blocks.4.attn.pool_k.pool.weight\", \"module.backbone.blocks.4.attn.pool_k.norm_act.0.weight\", \"module.backbone.blocks.4.attn.pool_k.norm_act.0.bias\", \"module.backbone.blocks.4.attn.pool_v.pool.weight\", \"module.backbone.blocks.4.attn.pool_v.norm_act.0.weight\", \"module.backbone.blocks.4.attn.pool_v.norm_act.0.bias\", \"module.backbone.blocks.4.mlp.0.weight\", \"module.backbone.blocks.4.mlp.0.bias\", \"module.backbone.blocks.4.mlp.3.weight\", \"module.backbone.blocks.4.mlp.3.bias\", \"module.backbone.blocks.5.norm1.weight\", \"module.backbone.blocks.5.norm1.bias\", \"module.backbone.blocks.5.norm2.weight\", \"module.backbone.blocks.5.norm2.bias\", \"module.backbone.blocks.5.attn.rel_pos_h\", \"module.backbone.blocks.5.attn.rel_pos_w\", \"module.backbone.blocks.5.attn.rel_pos_t\", \"module.backbone.blocks.5.attn.qkv.weight\", \"module.backbone.blocks.5.attn.qkv.bias\", \"module.backbone.blocks.5.attn.project.0.weight\", \"module.backbone.blocks.5.attn.project.0.bias\", \"module.backbone.blocks.5.attn.pool_q.pool.weight\", \"module.backbone.blocks.5.attn.pool_q.norm_act.0.weight\", \"module.backbone.blocks.5.attn.pool_q.norm_act.0.bias\", \"module.backbone.blocks.5.attn.pool_k.pool.weight\", \"module.backbone.blocks.5.attn.pool_k.norm_act.0.weight\", \"module.backbone.blocks.5.attn.pool_k.norm_act.0.bias\", \"module.backbone.blocks.5.attn.pool_v.pool.weight\", \"module.backbone.blocks.5.attn.pool_v.norm_act.0.weight\", \"module.backbone.blocks.5.attn.pool_v.norm_act.0.bias\", \"module.backbone.blocks.5.mlp.0.weight\", \"module.backbone.blocks.5.mlp.0.bias\", \"module.backbone.blocks.5.mlp.3.weight\", \"module.backbone.blocks.5.mlp.3.bias\", \"module.backbone.blocks.6.norm1.weight\", \"module.backbone.blocks.6.norm1.bias\", \"module.backbone.blocks.6.norm2.weight\", \"module.backbone.blocks.6.norm2.bias\", \"module.backbone.blocks.6.attn.rel_pos_h\", \"module.backbone.blocks.6.attn.rel_pos_w\", \"module.backbone.blocks.6.attn.rel_pos_t\", \"module.backbone.blocks.6.attn.qkv.weight\", \"module.backbone.blocks.6.attn.qkv.bias\", \"module.backbone.blocks.6.attn.project.0.weight\", \"module.backbone.blocks.6.attn.project.0.bias\", \"module.backbone.blocks.6.attn.pool_q.pool.weight\", \"module.backbone.blocks.6.attn.pool_q.norm_act.0.weight\", \"module.backbone.blocks.6.attn.pool_q.norm_act.0.bias\", \"module.backbone.blocks.6.attn.pool_k.pool.weight\", \"module.backbone.blocks.6.attn.pool_k.norm_act.0.weight\", \"module.backbone.blocks.6.attn.pool_k.norm_act.0.bias\", \"module.backbone.blocks.6.attn.pool_v.pool.weight\", \"module.backbone.blocks.6.attn.pool_v.norm_act.0.weight\", \"module.backbone.blocks.6.attn.pool_v.norm_act.0.bias\", \"module.backbone.blocks.6.mlp.0.weight\", \"module.backbone.blocks.6.mlp.0.bias\", \"module.backbone.blocks.6.mlp.3.weight\", \"module.backbone.blocks.6.mlp.3.bias\", \"module.backbone.blocks.7.norm1.weight\", \"module.backbone.blocks.7.norm1.bias\", \"module.backbone.blocks.7.norm2.weight\", \"module.backbone.blocks.7.norm2.bias\", \"module.backbone.blocks.7.attn.rel_pos_h\", \"module.backbone.blocks.7.attn.rel_pos_w\", \"module.backbone.blocks.7.attn.rel_pos_t\", \"module.backbone.blocks.7.attn.qkv.weight\", \"module.backbone.blocks.7.attn.qkv.bias\", \"module.backbone.blocks.7.attn.project.0.weight\", \"module.backbone.blocks.7.attn.project.0.bias\", \"module.backbone.blocks.7.attn.pool_q.pool.weight\", \"module.backbone.blocks.7.attn.pool_q.norm_act.0.weight\", \"module.backbone.blocks.7.attn.pool_q.norm_act.0.bias\", \"module.backbone.blocks.7.attn.pool_k.pool.weight\", \"module.backbone.blocks.7.attn.pool_k.norm_act.0.weight\", \"module.backbone.blocks.7.attn.pool_k.norm_act.0.bias\", \"module.backbone.blocks.7.attn.pool_v.pool.weight\", \"module.backbone.blocks.7.attn.pool_v.norm_act.0.weight\", \"module.backbone.blocks.7.attn.pool_v.norm_act.0.bias\", \"module.backbone.blocks.7.mlp.0.weight\", \"module.backbone.blocks.7.mlp.0.bias\", \"module.backbone.blocks.7.mlp.3.weight\", \"module.backbone.blocks.7.mlp.3.bias\", \"module.backbone.blocks.8.norm1.weight\", \"module.backbone.blocks.8.norm1.bias\", \"module.backbone.blocks.8.norm2.weight\", \"module.backbone.blocks.8.norm2.bias\", \"module.backbone.blocks.8.attn.rel_pos_h\", \"module.backbone.blocks.8.attn.rel_pos_w\", \"module.backbone.blocks.8.attn.rel_pos_t\", \"module.backbone.blocks.8.attn.qkv.weight\", \"module.backbone.blocks.8.attn.qkv.bias\", \"module.backbone.blocks.8.attn.project.0.weight\", \"module.backbone.blocks.8.attn.project.0.bias\", \"module.backbone.blocks.8.attn.pool_q.pool.weight\", \"module.backbone.blocks.8.attn.pool_q.norm_act.0.weight\", \"module.backbone.blocks.8.attn.pool_q.norm_act.0.bias\", \"module.backbone.blocks.8.attn.pool_k.pool.weight\", \"module.backbone.blocks.8.attn.pool_k.norm_act.0.weight\", \"module.backbone.blocks.8.attn.pool_k.norm_act.0.bias\", \"module.backbone.blocks.8.attn.pool_v.pool.weight\", \"module.backbone.blocks.8.attn.pool_v.norm_act.0.weight\", \"module.backbone.blocks.8.attn.pool_v.norm_act.0.bias\", \"module.backbone.blocks.8.mlp.0.weight\", \"module.backbone.blocks.8.mlp.0.bias\", \"module.backbone.blocks.8.mlp.3.weight\", \"module.backbone.blocks.8.mlp.3.bias\", \"module.backbone.blocks.9.norm1.weight\", \"module.backbone.blocks.9.norm1.bias\", \"module.backbone.blocks.9.norm2.weight\", \"module.backbone.blocks.9.norm2.bias\", \"module.backbone.blocks.9.attn.rel_pos_h\", \"module.backbone.blocks.9.attn.rel_pos_w\", \"module.backbone.blocks.9.attn.rel_pos_t\", \"module.backbone.blocks.9.attn.qkv.weight\", \"module.backbone.blocks.9.attn.qkv.bias\", \"module.backbone.blocks.9.attn.project.0.weight\", \"module.backbone.blocks.9.attn.project.0.bias\", \"module.backbone.blocks.9.attn.pool_q.pool.weight\", \"module.backbone.blocks.9.attn.pool_q.norm_act.0.weight\", \"module.backbone.blocks.9.attn.pool_q.norm_act.0.bias\", \"module.backbone.blocks.9.attn.pool_k.pool.weight\", \"module.backbone.blocks.9.attn.pool_k.norm_act.0.weight\", \"module.backbone.blocks.9.attn.pool_k.norm_act.0.bias\", \"module.backbone.blocks.9.attn.pool_v.pool.weight\", \"module.backbone.blocks.9.attn.pool_v.norm_act.0.weight\", \"module.backbone.blocks.9.attn.pool_v.norm_act.0.bias\", \"module.backbone.blocks.9.mlp.0.weight\", \"module.backbone.blocks.9.mlp.0.bias\", \"module.backbone.blocks.9.mlp.3.weight\", \"module.backbone.blocks.9.mlp.3.bias\", \"module.backbone.blocks.10.norm1.weight\", \"module.backbone.blocks.10.norm1.bias\", \"module.backbone.blocks.10.norm2.weight\", \"module.backbone.blocks.10.norm2.bias\", \"module.backbone.blocks.10.attn.rel_pos_h\", \"module.backbone.blocks.10.attn.rel_pos_w\", \"module.backbone.blocks.10.attn.rel_pos_t\", \"module.backbone.blocks.10.attn.qkv.weight\", \"module.backbone.blocks.10.attn.qkv.bias\", \"module.backbone.blocks.10.attn.project.0.weight\", \"module.backbone.blocks.10.attn.project.0.bias\", \"module.backbone.blocks.10.attn.pool_q.pool.weight\", \"module.backbone.blocks.10.attn.pool_q.norm_act.0.weight\", \"module.backbone.blocks.10.attn.pool_q.norm_act.0.bias\", \"module.backbone.blocks.10.attn.pool_k.pool.weight\", \"module.backbone.blocks.10.attn.pool_k.norm_act.0.weight\", \"module.backbone.blocks.10.attn.pool_k.norm_act.0.bias\", \"module.backbone.blocks.10.attn.pool_v.pool.weight\", \"module.backbone.blocks.10.attn.pool_v.norm_act.0.weight\", \"module.backbone.blocks.10.attn.pool_v.norm_act.0.bias\", \"module.backbone.blocks.10.mlp.0.weight\", \"module.backbone.blocks.10.mlp.0.bias\", \"module.backbone.blocks.10.mlp.3.weight\", \"module.backbone.blocks.10.mlp.3.bias\", \"module.backbone.blocks.11.norm1.weight\", \"module.backbone.blocks.11.norm1.bias\", \"module.backbone.blocks.11.norm2.weight\", \"module.backbone.blocks.11.norm2.bias\", \"module.backbone.blocks.11.attn.rel_pos_h\", \"module.backbone.blocks.11.attn.rel_pos_w\", \"module.backbone.blocks.11.attn.rel_pos_t\", \"module.backbone.blocks.11.attn.qkv.weight\", \"module.backbone.blocks.11.attn.qkv.bias\", \"module.backbone.blocks.11.attn.project.0.weight\", \"module.backbone.blocks.11.attn.project.0.bias\", \"module.backbone.blocks.11.attn.pool_q.pool.weight\", \"module.backbone.blocks.11.attn.pool_q.norm_act.0.weight\", \"module.backbone.blocks.11.attn.pool_q.norm_act.0.bias\", \"module.backbone.blocks.11.attn.pool_k.pool.weight\", \"module.backbone.blocks.11.attn.pool_k.norm_act.0.weight\", \"module.backbone.blocks.11.attn.pool_k.norm_act.0.bias\", \"module.backbone.blocks.11.attn.pool_v.pool.weight\", \"module.backbone.blocks.11.attn.pool_v.norm_act.0.weight\", \"module.backbone.blocks.11.attn.pool_v.norm_act.0.bias\", \"module.backbone.blocks.11.mlp.0.weight\", \"module.backbone.blocks.11.mlp.0.bias\", \"module.backbone.blocks.11.mlp.3.weight\", \"module.backbone.blocks.11.mlp.3.bias\", \"module.backbone.blocks.12.norm1.weight\", \"module.backbone.blocks.12.norm1.bias\", \"module.backbone.blocks.12.norm2.weight\", \"module.backbone.blocks.12.norm2.bias\", \"module.backbone.blocks.12.attn.rel_pos_h\", \"module.backbone.blocks.12.attn.rel_pos_w\", \"module.backbone.blocks.12.attn.rel_pos_t\", \"module.backbone.blocks.12.attn.qkv.weight\", \"module.backbone.blocks.12.attn.qkv.bias\", \"module.backbone.blocks.12.attn.project.0.weight\", \"module.backbone.blocks.12.attn.project.0.bias\", \"module.backbone.blocks.12.attn.pool_q.pool.weight\", \"module.backbone.blocks.12.attn.pool_q.norm_act.0.weight\", \"module.backbone.blocks.12.attn.pool_q.norm_act.0.bias\", \"module.backbone.blocks.12.attn.pool_k.pool.weight\", \"module.backbone.blocks.12.attn.pool_k.norm_act.0.weight\", \"module.backbone.blocks.12.attn.pool_k.norm_act.0.bias\", \"module.backbone.blocks.12.attn.pool_v.pool.weight\", \"module.backbone.blocks.12.attn.pool_v.norm_act.0.weight\", \"module.backbone.blocks.12.attn.pool_v.norm_act.0.bias\", \"module.backbone.blocks.12.mlp.0.weight\", \"module.backbone.blocks.12.mlp.0.bias\", \"module.backbone.blocks.12.mlp.3.weight\", \"module.backbone.blocks.12.mlp.3.bias\", \"module.backbone.blocks.13.norm1.weight\", \"module.backbone.blocks.13.norm1.bias\", \"module.backbone.blocks.13.norm2.weight\", \"module.backbone.blocks.13.norm2.bias\", \"module.backbone.blocks.13.attn.rel_pos_h\", \"module.backbone.blocks.13.attn.rel_pos_w\", \"module.backbone.blocks.13.attn.rel_pos_t\", \"module.backbone.blocks.13.attn.qkv.weight\", \"module.backbone.blocks.13.attn.qkv.bias\", \"module.backbone.blocks.13.attn.project.0.weight\", \"module.backbone.blocks.13.attn.project.0.bias\", \"module.backbone.blocks.13.attn.pool_q.pool.weight\", \"module.backbone.blocks.13.attn.pool_q.norm_act.0.weight\", \"module.backbone.blocks.13.attn.pool_q.norm_act.0.bias\", \"module.backbone.blocks.13.attn.pool_k.pool.weight\", \"module.backbone.blocks.13.attn.pool_k.norm_act.0.weight\", \"module.backbone.blocks.13.attn.pool_k.norm_act.0.bias\", \"module.backbone.blocks.13.attn.pool_v.pool.weight\", \"module.backbone.blocks.13.attn.pool_v.norm_act.0.weight\", \"module.backbone.blocks.13.attn.pool_v.norm_act.0.bias\", \"module.backbone.blocks.13.mlp.0.weight\", \"module.backbone.blocks.13.mlp.0.bias\", \"module.backbone.blocks.13.mlp.3.weight\", \"module.backbone.blocks.13.mlp.3.bias\", \"module.backbone.blocks.14.norm1.weight\", \"module.backbone.blocks.14.norm1.bias\", \"module.backbone.blocks.14.norm2.weight\", \"module.backbone.blocks.14.norm2.bias\", \"module.backbone.blocks.14.attn.rel_pos_h\", \"module.backbone.blocks.14.attn.rel_pos_w\", \"module.backbone.blocks.14.attn.rel_pos_t\", \"module.backbone.blocks.14.attn.qkv.weight\", \"module.backbone.blocks.14.attn.qkv.bias\", \"module.backbone.blocks.14.attn.project.0.weight\", \"module.backbone.blocks.14.attn.project.0.bias\", \"module.backbone.blocks.14.attn.pool_q.pool.weight\", \"module.backbone.blocks.14.attn.pool_q.norm_act.0.weight\", \"module.backbone.blocks.14.attn.pool_q.norm_act.0.bias\", \"module.backbone.blocks.14.attn.pool_k.pool.weight\", \"module.backbone.blocks.14.attn.pool_k.norm_act.0.weight\", \"module.backbone.blocks.14.attn.pool_k.norm_act.0.bias\", \"module.backbone.blocks.14.attn.pool_v.pool.weight\", \"module.backbone.blocks.14.attn.pool_v.norm_act.0.weight\", \"module.backbone.blocks.14.attn.pool_v.norm_act.0.bias\", \"module.backbone.blocks.14.mlp.0.weight\", \"module.backbone.blocks.14.mlp.0.bias\", \"module.backbone.blocks.14.mlp.3.weight\", \"module.backbone.blocks.14.mlp.3.bias\", \"module.backbone.blocks.14.project.weight\", \"module.backbone.blocks.14.project.bias\", \"module.backbone.blocks.15.norm1.weight\", \"module.backbone.blocks.15.norm1.bias\", \"module.backbone.blocks.15.norm2.weight\", \"module.backbone.blocks.15.norm2.bias\", \"module.backbone.blocks.15.attn.rel_pos_h\", \"module.backbone.blocks.15.attn.rel_pos_w\", \"module.backbone.blocks.15.attn.rel_pos_t\", \"module.backbone.blocks.15.attn.qkv.weight\", \"module.backbone.blocks.15.attn.qkv.bias\", \"module.backbone.blocks.15.attn.project.0.weight\", \"module.backbone.blocks.15.attn.project.0.bias\", \"module.backbone.blocks.15.attn.pool_q.pool.weight\", \"module.backbone.blocks.15.attn.pool_q.norm_act.0.weight\", \"module.backbone.blocks.15.attn.pool_q.norm_act.0.bias\", \"module.backbone.blocks.15.attn.pool_k.pool.weight\", \"module.backbone.blocks.15.attn.pool_k.norm_act.0.weight\", \"module.backbone.blocks.15.attn.pool_k.norm_act.0.bias\", \"module.backbone.blocks.15.attn.pool_v.pool.weight\", \"module.backbone.blocks.15.attn.pool_v.norm_act.0.weight\", \"module.backbone.blocks.15.attn.pool_v.norm_act.0.bias\", \"module.backbone.blocks.15.mlp.0.weight\", \"module.backbone.blocks.15.mlp.0.bias\", \"module.backbone.blocks.15.mlp.3.weight\", \"module.backbone.blocks.15.mlp.3.bias\", \"module.backbone.norm.weight\", \"module.backbone.norm.bias\", \"module.bottleneck.0.weight\", \"module.bottleneck.0.bias\", \"module.bottleneck.3.weight\", \"module.bottleneck.3.bias\". \n\tUnexpected key(s) in state_dict: \"epoch\", \"model_state_dict\", \"optimizer_state_dict\", \"train_loss\", \"val_loss\". "], "ename": "RuntimeError", "evalue": "Error(s) in loading state_dict for DataParallel:\n\tMissing key(s) in state_dict: \"module.backbone.conv_proj.weight\", \"module.backbone.conv_proj.bias\", \"module.backbone.pos_encoding.class_token\", \"module.backbone.blocks.0.norm1.weight\", \"module.backbone.blocks.0.norm1.bias\", \"module.backbone.blocks.0.norm2.weight\", \"module.backbone.blocks.0.norm2.bias\", \"module.backbone.blocks.0.attn.rel_pos_h\", \"module.backbone.blocks.0.attn.rel_pos_w\", \"module.backbone.blocks.0.attn.rel_pos_t\", \"module.backbone.blocks.0.attn.qkv.weight\", \"module.backbone.blocks.0.attn.qkv.bias\", \"module.backbone.blocks.0.attn.project.0.weight\", \"module.backbone.blocks.0.attn.project.0.bias\", \"module.backbone.blocks.0.attn.pool_q.pool.weight\", \"module.backbone.blocks.0.attn.pool_q.norm_act.0.weight\", \"module.backbone.blocks.0.attn.pool_q.norm_act.0.bias\", \"module.backbone.blocks.0.attn.pool_k.pool.weight\", \"module.backbone.blocks.0.attn.pool_k.norm_act.0.weight\", \"module.backbone.blocks.0.attn.pool_k.norm_act.0.bias\", \"module.backbone.blocks.0.attn.pool_v.pool.weight\", \"module.backbone.blocks.0.attn.pool_v.norm_act.0.weight\", \"module.backbone.blocks.0.attn.pool_v.norm_act.0.bias\", \"module.backbone.blocks.0.mlp.0.weight\", \"module.backbone.blocks.0.mlp.0.bias\", \"module.backbone.blocks.0.mlp.3.weight\", \"module.backbone.blocks.0.mlp.3.bias\", \"module.backbone.blocks.1.norm1.weight\", \"module.backbone.blocks.1.norm1.bias\", \"module.backbone.blocks.1.norm2.weight\", \"module.backbone.blocks.1.norm2.bias\", \"module.backbone.blocks.1.attn.rel_pos_h\", \"module.backbone.blocks.1.attn.rel_pos_w\", \"module.backbone.blocks.1.attn.rel_pos_t\", \"module.backbone.blocks.1.attn.qkv.weight\", \"module.backbone.blocks.1.attn.qkv.bias\", \"module.backbone.blocks.1.attn.project.0.weight\", \"module.backbone.blocks.1.attn.project.0.bias\", \"module.backbone.blocks.1.attn.pool_q.pool.weight\", \"module.backbone.blocks.1.attn.pool_q.norm_act.0.weight\", \"module.backbone.blocks.1.attn.pool_q.norm_act.0.bias\", \"module.backbone.blocks.1.attn.pool_k.pool.weight\", \"module.backbone.blocks.1.attn.pool_k.norm_act.0.weight\", \"module.backbone.blocks.1.attn.pool_k.norm_act.0.bias\", \"module.backbone.blocks.1.attn.pool_v.pool.weight\", \"module.backbone.blocks.1.attn.pool_v.norm_act.0.weight\", \"module.backbone.blocks.1.attn.pool_v.norm_act.0.bias\", \"module.backbone.blocks.1.mlp.0.weight\", \"module.backbone.blocks.1.mlp.0.bias\", \"module.backbone.blocks.1.mlp.3.weight\", \"module.backbone.blocks.1.mlp.3.bias\", \"module.backbone.blocks.1.project.weight\", \"module.backbone.blocks.1.project.bias\", \"module.backbone.blocks.2.norm1.weight\", \"module.backbone.blocks.2.norm1.bias\", \"module.backbone.blocks.2.norm2.weight\", \"module.backbone.blocks.2.norm2.bias\", \"module.backbone.blocks.2.attn.rel_pos_h\", \"module.backbone.blocks.2.attn.rel_pos_w\", \"module.backbone.blocks.2.attn.rel_pos_t\", \"module.backbone.blocks.2.attn.qkv.weight\", \"module.backbone.blocks.2.attn.qkv.bias\", \"module.backbone.blocks.2.attn.project.0.weight\", \"module.backbone.blocks.2.attn.project.0.bias\", \"module.backbone.blocks.2.attn.pool_q.pool.weight\", \"module.backbone.blocks.2.attn.pool_q.norm_act.0.weight\", \"module.backbone.blocks.2.attn.pool_q.norm_act.0.bias\", \"module.backbone.blocks.2.attn.pool_k.pool.weight\", \"module.backbone.blocks.2.attn.pool_k.norm_act.0.weight\", \"module.backbone.blocks.2.attn.pool_k.norm_act.0.bias\", \"module.backbone.blocks.2.attn.pool_v.pool.weight\", \"module.backbone.blocks.2.attn.pool_v.norm_act.0.weight\", \"module.backbone.blocks.2.attn.pool_v.norm_act.0.bias\", \"module.backbone.blocks.2.mlp.0.weight\", \"module.backbone.blocks.2.mlp.0.bias\", \"module.backbone.blocks.2.mlp.3.weight\", \"module.backbone.blocks.2.mlp.3.bias\", \"module.backbone.blocks.3.norm1.weight\", \"module.backbone.blocks.3.norm1.bias\", \"module.backbone.blocks.3.norm2.weight\", \"module.backbone.blocks.3.norm2.bias\", \"module.backbone.blocks.3.attn.rel_pos_h\", \"module.backbone.blocks.3.attn.rel_pos_w\", \"module.backbone.blocks.3.attn.rel_pos_t\", \"module.backbone.blocks.3.attn.qkv.weight\", \"module.backbone.blocks.3.attn.qkv.bias\", \"module.backbone.blocks.3.attn.project.0.weight\", \"module.backbone.blocks.3.attn.project.0.bias\", \"module.backbone.blocks.3.attn.pool_q.pool.weight\", \"module.backbone.blocks.3.attn.pool_q.norm_act.0.weight\", \"module.backbone.blocks.3.attn.pool_q.norm_act.0.bias\", \"module.backbone.blocks.3.attn.pool_k.pool.weight\", \"module.backbone.blocks.3.attn.pool_k.norm_act.0.weight\", \"module.backbone.blocks.3.attn.pool_k.norm_act.0.bias\", \"module.backbone.blocks.3.attn.pool_v.pool.weight\", \"module.backbone.blocks.3.attn.pool_v.norm_act.0.weight\", \"module.backbone.blocks.3.attn.pool_v.norm_act.0.bias\", \"module.backbone.blocks.3.mlp.0.weight\", \"module.backbone.blocks.3.mlp.0.bias\", \"module.backbone.blocks.3.mlp.3.weight\", \"module.backbone.blocks.3.mlp.3.bias\", \"module.backbone.blocks.3.project.weight\", \"module.backbone.blocks.3.project.bias\", \"module.backbone.blocks.4.norm1.weight\", \"module.backbone.blocks.4.norm1.bias\", \"module.backbone.blocks.4.norm2.weight\", \"module.backbone.blocks.4.norm2.bias\", \"module.backbone.blocks.4.attn.rel_pos_h\", \"module.backbone.blocks.4.attn.rel_pos_w\", \"module.backbone.blocks.4.attn.rel_pos_t\", \"module.backbone.blocks.4.attn.qkv.weight\", \"module.backbone.blocks.4.attn.qkv.bias\", \"module.backbone.blocks.4.attn.project.0.weight\", \"module.backbone.blocks.4.attn.project.0.bias\", \"module.backbone.blocks.4.attn.pool_q.pool.weight\", \"module.backbone.blocks.4.attn.pool_q.norm_act.0.weight\", \"module.backbone.blocks.4.attn.pool_q.norm_act.0.bias\", \"module.backbone.blocks.4.attn.pool_k.pool.weight\", \"module.backbone.blocks.4.attn.pool_k.norm_act.0.weight\", \"module.backbone.blocks.4.attn.pool_k.norm_act.0.bias\", \"module.backbone.blocks.4.attn.pool_v.pool.weight\", \"module.backbone.blocks.4.attn.pool_v.norm_act.0.weight\", \"module.backbone.blocks.4.attn.pool_v.norm_act.0.bias\", \"module.backbone.blocks.4.mlp.0.weight\", \"module.backbone.blocks.4.mlp.0.bias\", \"module.backbone.blocks.4.mlp.3.weight\", \"module.backbone.blocks.4.mlp.3.bias\", \"module.backbone.blocks.5.norm1.weight\", \"module.backbone.blocks.5.norm1.bias\", \"module.backbone.blocks.5.norm2.weight\", \"module.backbone.blocks.5.norm2.bias\", \"module.backbone.blocks.5.attn.rel_pos_h\", \"module.backbone.blocks.5.attn.rel_pos_w\", \"module.backbone.blocks.5.attn.rel_pos_t\", \"module.backbone.blocks.5.attn.qkv.weight\", \"module.backbone.blocks.5.attn.qkv.bias\", \"module.backbone.blocks.5.attn.project.0.weight\", \"module.backbone.blocks.5.attn.project.0.bias\", \"module.backbone.blocks.5.attn.pool_q.pool.weight\", \"module.backbone.blocks.5.attn.pool_q.norm_act.0.weight\", \"module.backbone.blocks.5.attn.pool_q.norm_act.0.bias\", \"module.backbone.blocks.5.attn.pool_k.pool.weight\", \"module.backbone.blocks.5.attn.pool_k.norm_act.0.weight\", \"module.backbone.blocks.5.attn.pool_k.norm_act.0.bias\", \"module.backbone.blocks.5.attn.pool_v.pool.weight\", \"module.backbone.blocks.5.attn.pool_v.norm_act.0.weight\", \"module.backbone.blocks.5.attn.pool_v.norm_act.0.bias\", \"module.backbone.blocks.5.mlp.0.weight\", \"module.backbone.blocks.5.mlp.0.bias\", \"module.backbone.blocks.5.mlp.3.weight\", \"module.backbone.blocks.5.mlp.3.bias\", \"module.backbone.blocks.6.norm1.weight\", \"module.backbone.blocks.6.norm1.bias\", \"module.backbone.blocks.6.norm2.weight\", \"module.backbone.blocks.6.norm2.bias\", \"module.backbone.blocks.6.attn.rel_pos_h\", \"module.backbone.blocks.6.attn.rel_pos_w\", \"module.backbone.blocks.6.attn.rel_pos_t\", \"module.backbone.blocks.6.attn.qkv.weight\", \"module.backbone.blocks.6.attn.qkv.bias\", \"module.backbone.blocks.6.attn.project.0.weight\", \"module.backbone.blocks.6.attn.project.0.bias\", \"module.backbone.blocks.6.attn.pool_q.pool.weight\", \"module.backbone.blocks.6.attn.pool_q.norm_act.0.weight\", \"module.backbone.blocks.6.attn.pool_q.norm_act.0.bias\", \"module.backbone.blocks.6.attn.pool_k.pool.weight\", \"module.backbone.blocks.6.attn.pool_k.norm_act.0.weight\", \"module.backbone.blocks.6.attn.pool_k.norm_act.0.bias\", \"module.backbone.blocks.6.attn.pool_v.pool.weight\", \"module.backbone.blocks.6.attn.pool_v.norm_act.0.weight\", \"module.backbone.blocks.6.attn.pool_v.norm_act.0.bias\", \"module.backbone.blocks.6.mlp.0.weight\", \"module.backbone.blocks.6.mlp.0.bias\", \"module.backbone.blocks.6.mlp.3.weight\", \"module.backbone.blocks.6.mlp.3.bias\", \"module.backbone.blocks.7.norm1.weight\", \"module.backbone.blocks.7.norm1.bias\", \"module.backbone.blocks.7.norm2.weight\", \"module.backbone.blocks.7.norm2.bias\", \"module.backbone.blocks.7.attn.rel_pos_h\", \"module.backbone.blocks.7.attn.rel_pos_w\", \"module.backbone.blocks.7.attn.rel_pos_t\", \"module.backbone.blocks.7.attn.qkv.weight\", \"module.backbone.blocks.7.attn.qkv.bias\", \"module.backbone.blocks.7.attn.project.0.weight\", \"module.backbone.blocks.7.attn.project.0.bias\", \"module.backbone.blocks.7.attn.pool_q.pool.weight\", \"module.backbone.blocks.7.attn.pool_q.norm_act.0.weight\", \"module.backbone.blocks.7.attn.pool_q.norm_act.0.bias\", \"module.backbone.blocks.7.attn.pool_k.pool.weight\", \"module.backbone.blocks.7.attn.pool_k.norm_act.0.weight\", \"module.backbone.blocks.7.attn.pool_k.norm_act.0.bias\", \"module.backbone.blocks.7.attn.pool_v.pool.weight\", \"module.backbone.blocks.7.attn.pool_v.norm_act.0.weight\", \"module.backbone.blocks.7.attn.pool_v.norm_act.0.bias\", \"module.backbone.blocks.7.mlp.0.weight\", \"module.backbone.blocks.7.mlp.0.bias\", \"module.backbone.blocks.7.mlp.3.weight\", \"module.backbone.blocks.7.mlp.3.bias\", \"module.backbone.blocks.8.norm1.weight\", \"module.backbone.blocks.8.norm1.bias\", \"module.backbone.blocks.8.norm2.weight\", \"module.backbone.blocks.8.norm2.bias\", \"module.backbone.blocks.8.attn.rel_pos_h\", \"module.backbone.blocks.8.attn.rel_pos_w\", \"module.backbone.blocks.8.attn.rel_pos_t\", \"module.backbone.blocks.8.attn.qkv.weight\", \"module.backbone.blocks.8.attn.qkv.bias\", \"module.backbone.blocks.8.attn.project.0.weight\", \"module.backbone.blocks.8.attn.project.0.bias\", \"module.backbone.blocks.8.attn.pool_q.pool.weight\", \"module.backbone.blocks.8.attn.pool_q.norm_act.0.weight\", \"module.backbone.blocks.8.attn.pool_q.norm_act.0.bias\", \"module.backbone.blocks.8.attn.pool_k.pool.weight\", \"module.backbone.blocks.8.attn.pool_k.norm_act.0.weight\", \"module.backbone.blocks.8.attn.pool_k.norm_act.0.bias\", \"module.backbone.blocks.8.attn.pool_v.pool.weight\", \"module.backbone.blocks.8.attn.pool_v.norm_act.0.weight\", \"module.backbone.blocks.8.attn.pool_v.norm_act.0.bias\", \"module.backbone.blocks.8.mlp.0.weight\", \"module.backbone.blocks.8.mlp.0.bias\", \"module.backbone.blocks.8.mlp.3.weight\", \"module.backbone.blocks.8.mlp.3.bias\", \"module.backbone.blocks.9.norm1.weight\", \"module.backbone.blocks.9.norm1.bias\", \"module.backbone.blocks.9.norm2.weight\", \"module.backbone.blocks.9.norm2.bias\", \"module.backbone.blocks.9.attn.rel_pos_h\", \"module.backbone.blocks.9.attn.rel_pos_w\", \"module.backbone.blocks.9.attn.rel_pos_t\", \"module.backbone.blocks.9.attn.qkv.weight\", \"module.backbone.blocks.9.attn.qkv.bias\", \"module.backbone.blocks.9.attn.project.0.weight\", \"module.backbone.blocks.9.attn.project.0.bias\", \"module.backbone.blocks.9.attn.pool_q.pool.weight\", \"module.backbone.blocks.9.attn.pool_q.norm_act.0.weight\", \"module.backbone.blocks.9.attn.pool_q.norm_act.0.bias\", \"module.backbone.blocks.9.attn.pool_k.pool.weight\", \"module.backbone.blocks.9.attn.pool_k.norm_act.0.weight\", \"module.backbone.blocks.9.attn.pool_k.norm_act.0.bias\", \"module.backbone.blocks.9.attn.pool_v.pool.weight\", \"module.backbone.blocks.9.attn.pool_v.norm_act.0.weight\", \"module.backbone.blocks.9.attn.pool_v.norm_act.0.bias\", \"module.backbone.blocks.9.mlp.0.weight\", \"module.backbone.blocks.9.mlp.0.bias\", \"module.backbone.blocks.9.mlp.3.weight\", \"module.backbone.blocks.9.mlp.3.bias\", \"module.backbone.blocks.10.norm1.weight\", \"module.backbone.blocks.10.norm1.bias\", \"module.backbone.blocks.10.norm2.weight\", \"module.backbone.blocks.10.norm2.bias\", \"module.backbone.blocks.10.attn.rel_pos_h\", \"module.backbone.blocks.10.attn.rel_pos_w\", \"module.backbone.blocks.10.attn.rel_pos_t\", \"module.backbone.blocks.10.attn.qkv.weight\", \"module.backbone.blocks.10.attn.qkv.bias\", \"module.backbone.blocks.10.attn.project.0.weight\", \"module.backbone.blocks.10.attn.project.0.bias\", \"module.backbone.blocks.10.attn.pool_q.pool.weight\", \"module.backbone.blocks.10.attn.pool_q.norm_act.0.weight\", \"module.backbone.blocks.10.attn.pool_q.norm_act.0.bias\", \"module.backbone.blocks.10.attn.pool_k.pool.weight\", \"module.backbone.blocks.10.attn.pool_k.norm_act.0.weight\", \"module.backbone.blocks.10.attn.pool_k.norm_act.0.bias\", \"module.backbone.blocks.10.attn.pool_v.pool.weight\", \"module.backbone.blocks.10.attn.pool_v.norm_act.0.weight\", \"module.backbone.blocks.10.attn.pool_v.norm_act.0.bias\", \"module.backbone.blocks.10.mlp.0.weight\", \"module.backbone.blocks.10.mlp.0.bias\", \"module.backbone.blocks.10.mlp.3.weight\", \"module.backbone.blocks.10.mlp.3.bias\", \"module.backbone.blocks.11.norm1.weight\", \"module.backbone.blocks.11.norm1.bias\", \"module.backbone.blocks.11.norm2.weight\", \"module.backbone.blocks.11.norm2.bias\", \"module.backbone.blocks.11.attn.rel_pos_h\", \"module.backbone.blocks.11.attn.rel_pos_w\", \"module.backbone.blocks.11.attn.rel_pos_t\", \"module.backbone.blocks.11.attn.qkv.weight\", \"module.backbone.blocks.11.attn.qkv.bias\", \"module.backbone.blocks.11.attn.project.0.weight\", \"module.backbone.blocks.11.attn.project.0.bias\", \"module.backbone.blocks.11.attn.pool_q.pool.weight\", \"module.backbone.blocks.11.attn.pool_q.norm_act.0.weight\", \"module.backbone.blocks.11.attn.pool_q.norm_act.0.bias\", \"module.backbone.blocks.11.attn.pool_k.pool.weight\", \"module.backbone.blocks.11.attn.pool_k.norm_act.0.weight\", \"module.backbone.blocks.11.attn.pool_k.norm_act.0.bias\", \"module.backbone.blocks.11.attn.pool_v.pool.weight\", \"module.backbone.blocks.11.attn.pool_v.norm_act.0.weight\", \"module.backbone.blocks.11.attn.pool_v.norm_act.0.bias\", \"module.backbone.blocks.11.mlp.0.weight\", \"module.backbone.blocks.11.mlp.0.bias\", \"module.backbone.blocks.11.mlp.3.weight\", \"module.backbone.blocks.11.mlp.3.bias\", \"module.backbone.blocks.12.norm1.weight\", \"module.backbone.blocks.12.norm1.bias\", \"module.backbone.blocks.12.norm2.weight\", \"module.backbone.blocks.12.norm2.bias\", \"module.backbone.blocks.12.attn.rel_pos_h\", \"module.backbone.blocks.12.attn.rel_pos_w\", \"module.backbone.blocks.12.attn.rel_pos_t\", \"module.backbone.blocks.12.attn.qkv.weight\", \"module.backbone.blocks.12.attn.qkv.bias\", \"module.backbone.blocks.12.attn.project.0.weight\", \"module.backbone.blocks.12.attn.project.0.bias\", \"module.backbone.blocks.12.attn.pool_q.pool.weight\", \"module.backbone.blocks.12.attn.pool_q.norm_act.0.weight\", \"module.backbone.blocks.12.attn.pool_q.norm_act.0.bias\", \"module.backbone.blocks.12.attn.pool_k.pool.weight\", \"module.backbone.blocks.12.attn.pool_k.norm_act.0.weight\", \"module.backbone.blocks.12.attn.pool_k.norm_act.0.bias\", \"module.backbone.blocks.12.attn.pool_v.pool.weight\", \"module.backbone.blocks.12.attn.pool_v.norm_act.0.weight\", \"module.backbone.blocks.12.attn.pool_v.norm_act.0.bias\", \"module.backbone.blocks.12.mlp.0.weight\", \"module.backbone.blocks.12.mlp.0.bias\", \"module.backbone.blocks.12.mlp.3.weight\", \"module.backbone.blocks.12.mlp.3.bias\", \"module.backbone.blocks.13.norm1.weight\", \"module.backbone.blocks.13.norm1.bias\", \"module.backbone.blocks.13.norm2.weight\", \"module.backbone.blocks.13.norm2.bias\", \"module.backbone.blocks.13.attn.rel_pos_h\", \"module.backbone.blocks.13.attn.rel_pos_w\", \"module.backbone.blocks.13.attn.rel_pos_t\", \"module.backbone.blocks.13.attn.qkv.weight\", \"module.backbone.blocks.13.attn.qkv.bias\", \"module.backbone.blocks.13.attn.project.0.weight\", \"module.backbone.blocks.13.attn.project.0.bias\", \"module.backbone.blocks.13.attn.pool_q.pool.weight\", \"module.backbone.blocks.13.attn.pool_q.norm_act.0.weight\", \"module.backbone.blocks.13.attn.pool_q.norm_act.0.bias\", \"module.backbone.blocks.13.attn.pool_k.pool.weight\", \"module.backbone.blocks.13.attn.pool_k.norm_act.0.weight\", \"module.backbone.blocks.13.attn.pool_k.norm_act.0.bias\", \"module.backbone.blocks.13.attn.pool_v.pool.weight\", \"module.backbone.blocks.13.attn.pool_v.norm_act.0.weight\", \"module.backbone.blocks.13.attn.pool_v.norm_act.0.bias\", \"module.backbone.blocks.13.mlp.0.weight\", \"module.backbone.blocks.13.mlp.0.bias\", \"module.backbone.blocks.13.mlp.3.weight\", \"module.backbone.blocks.13.mlp.3.bias\", \"module.backbone.blocks.14.norm1.weight\", \"module.backbone.blocks.14.norm1.bias\", \"module.backbone.blocks.14.norm2.weight\", \"module.backbone.blocks.14.norm2.bias\", \"module.backbone.blocks.14.attn.rel_pos_h\", \"module.backbone.blocks.14.attn.rel_pos_w\", \"module.backbone.blocks.14.attn.rel_pos_t\", \"module.backbone.blocks.14.attn.qkv.weight\", \"module.backbone.blocks.14.attn.qkv.bias\", \"module.backbone.blocks.14.attn.project.0.weight\", \"module.backbone.blocks.14.attn.project.0.bias\", \"module.backbone.blocks.14.attn.pool_q.pool.weight\", \"module.backbone.blocks.14.attn.pool_q.norm_act.0.weight\", \"module.backbone.blocks.14.attn.pool_q.norm_act.0.bias\", \"module.backbone.blocks.14.attn.pool_k.pool.weight\", \"module.backbone.blocks.14.attn.pool_k.norm_act.0.weight\", \"module.backbone.blocks.14.attn.pool_k.norm_act.0.bias\", \"module.backbone.blocks.14.attn.pool_v.pool.weight\", \"module.backbone.blocks.14.attn.pool_v.norm_act.0.weight\", \"module.backbone.blocks.14.attn.pool_v.norm_act.0.bias\", \"module.backbone.blocks.14.mlp.0.weight\", \"module.backbone.blocks.14.mlp.0.bias\", \"module.backbone.blocks.14.mlp.3.weight\", \"module.backbone.blocks.14.mlp.3.bias\", \"module.backbone.blocks.14.project.weight\", \"module.backbone.blocks.14.project.bias\", \"module.backbone.blocks.15.norm1.weight\", \"module.backbone.blocks.15.norm1.bias\", \"module.backbone.blocks.15.norm2.weight\", \"module.backbone.blocks.15.norm2.bias\", \"module.backbone.blocks.15.attn.rel_pos_h\", \"module.backbone.blocks.15.attn.rel_pos_w\", \"module.backbone.blocks.15.attn.rel_pos_t\", \"module.backbone.blocks.15.attn.qkv.weight\", \"module.backbone.blocks.15.attn.qkv.bias\", \"module.backbone.blocks.15.attn.project.0.weight\", \"module.backbone.blocks.15.attn.project.0.bias\", \"module.backbone.blocks.15.attn.pool_q.pool.weight\", \"module.backbone.blocks.15.attn.pool_q.norm_act.0.weight\", \"module.backbone.blocks.15.attn.pool_q.norm_act.0.bias\", \"module.backbone.blocks.15.attn.pool_k.pool.weight\", \"module.backbone.blocks.15.attn.pool_k.norm_act.0.weight\", \"module.backbone.blocks.15.attn.pool_k.norm_act.0.bias\", \"module.backbone.blocks.15.attn.pool_v.pool.weight\", \"module.backbone.blocks.15.attn.pool_v.norm_act.0.weight\", \"module.backbone.blocks.15.attn.pool_v.norm_act.0.bias\", \"module.backbone.blocks.15.mlp.0.weight\", \"module.backbone.blocks.15.mlp.0.bias\", \"module.backbone.blocks.15.mlp.3.weight\", \"module.backbone.blocks.15.mlp.3.bias\", \"module.backbone.norm.weight\", \"module.backbone.norm.bias\", \"module.bottleneck.0.weight\", \"module.bottleneck.0.bias\", \"module.bottleneck.3.weight\", \"module.bottleneck.3.bias\". \n\tUnexpected key(s) in state_dict: \"epoch\", \"model_state_dict\", \"optimizer_state_dict\", \"train_loss\", \"val_loss\". ", "output_type": "error"}]}, {"cell_type": "code", "source": "", "metadata": {"trusted": true}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": "", "metadata": {}, "execution_count": null, "outputs": []}]}