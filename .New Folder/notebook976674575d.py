# -*- coding: utf-8 -*-
"""notebook976674575d

Automatically generated by <PERSON><PERSON>.

Original file is located at
    https://colab.research.google.com/#fileId=https%3A//storage.googleapis.com/kaggle-colab-exported-notebooks/notebook976674575d-42234d02-6778-4c8d-bda7-6e10bcf57e06.ipynb%3FX-Goog-Algorithm%3DGOOG4-RSA-SHA256%26X-Goog-Credential%3Dgcp-kaggle-com%2540kaggle-161607.iam.gserviceaccount.com/********/auto/storage/goog4_request%26X-Goog-Date%3D********T180118Z%26X-Goog-Expires%3D259200%26X-Goog-SignedHeaders%3Dhost%26X-Goog-Signature%3Dad736a9531ba56de0e69eabb27ffdbbcb674b6f960c494f53e84fa5cb1ee1f73170f9513a34d42ff02addfc71ab7f0d4109068bbc78de710ebfb36c3bca7f44dd74175bc18b4b223c8c2ba02984a348675256263a3c33808ce52a0699b768665eed83ffbaa502cd70016839f8e387e6b6f5116ac1fc52b736e0ceb8f8eeb0d9f2395d0941be9d2e6f866ebca522ffae80a823b82b78bef84feca5013136ff646e8414a35e66c7b3bc62f64f9bc3e3f071960bbb83ecb2e51a98bfc22b482693e76e250ea6e88b75f7dd9e0a742e8053fa5c23b798053035382f4b931aaa95975b686523e0edf9454a9f5f8ead2032bbf4d483f36242d4a261a5d5926631bb5f3
"""

# IMPORTANT: RUN THIS CELL IN ORDER TO IMPORT YOUR KAGGLE DATA SOURCES,
# THEN FEEL FREE TO DELETE THIS CELL.
# NOTE: THIS NOTEBOOK ENVIRONMENT DIFFERS FROM KAGGLE'S PYTHON
# ENVIRONMENT SO THERE MAY BE MISSING LIBRARIES USED BY YOUR
# NOTEBOOK.
import kagglehub
niharika41298_yoga_poses_dataset_path = kagglehub.dataset_download('niharika41298/yoga-poses-dataset')

print('Data source import complete.')

import torchvision
from torchvision.transforms import ToTensor
from torchvision import datasets
from torch.optim.lr_scheduler import ReduceLROnPlateau
import torch.nn as nn
import torch.nn.functional as F
import torchvision.transforms as transforms
import torch
from torch.utils.data import DataLoader
from tqdm import tqdm
from torch.utils.data.dataset import random_split
from torch.utils.data import WeightedRandomSampler
import numpy as np
import os
import time
import json
import seaborn as sns
import shutil
import glob
from pathlib import Path
from PIL import Image
import matplotlib.pyplot as plt
import random
import plotly.express as px
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, confusion_matrix
from sklearn.utils.class_weight import compute_class_weight

class CustomCNNClassification(nn.Module):
    def __init__(self, num_classes):
        super(CustomCNNClassification, self).__init__()

        # Convolutional layers
        self.conv1 = nn.Conv2d(3, 64, kernel_size=3, padding=1)
        self.bn1 = nn.BatchNorm2d(64)
        self.conv2 = nn.Conv2d(64, 128, kernel_size=3, padding=1)
        self.bn2 = nn.BatchNorm2d(128)
        self.conv3 = nn.Conv2d(128, 256, kernel_size=3, padding=1)
        self.bn3 = nn.BatchNorm2d(256)
        self.conv4 = nn.Conv2d(256, 512, kernel_size=3, padding=1)
        self.bn4 = nn.BatchNorm2d(512)

        # Pooling layer
        self.pool = nn.MaxPool2d(2, 2)

        # Dropout for regularization
        self.dropout = nn.Dropout(0.5)

        # Fully connected layers
        self.fc1 = nn.Linear(512 * 14 * 14, 4096)
        self.fc2 = nn.Linear(4096, 1024)
        self.fc3 = nn.Linear(1024, num_classes)

        # Loss function
        self.loss_fct = nn.CrossEntropyLoss()

    def forward(self, x, labels=None):
        # Convolutional layers with ReLU and pooling
        x = self.pool(F.relu(self.bn1(self.conv1(x))))
        x = self.pool(F.relu(self.bn2(self.conv2(x))))
        x = self.pool(F.relu(self.bn3(self.conv3(x))))
        x = self.pool(F.relu(self.bn4(self.conv4(x))))

        # Flatten the output for the fully connected layers
        x = x.view(-1, 512 * 14 * 14)

        # Fully connected layers with dropout
        x = self.dropout(F.relu(self.fc1(x)))
        x = self.dropout(F.relu(self.fc2(x)))
        logits = self.fc3(x)

        loss = None
        if labels is not None:
            loss = self.loss_fct(logits, labels)

        return logits, loss

train_dir = Path('/kaggle/input/yoga-poses-dataset/DATASET/TRAIN')
test_dir = Path('//kaggle/input/yoga-poses-dataset/DATASET/TEST')

# Training parameters
EPOCHS = 45
BATCH_SIZE = 16
EVAL_BATCH = 1
LEARNING_RATE = 1e-4
WEIGHT_DECAY = 1e-4
checkpoint_dir = "/kaggle/working/checkpoints"
patience = 10
clip_value = 0.6

def plot_images_in_single_row(directory):
    """
    Plots a random image from each class in a single row.

    Args:
        directory (str): Path to the directory containing class folders.
    """
    # List of class directories
    class_dirs = [d for d in os.listdir(directory) if os.path.isdir(os.path.join(directory, d))]

    # Adjust the figure size based on the number of classes
    plt.figure(figsize=(5 * len(class_dirs), 5))

    for i, class_dir in enumerate(class_dirs):
        class_dir_path = os.path.join(directory, class_dir)

        # Get a random image from the selected class
        image_file = random.choice(os.listdir(class_dir_path))
        image_path = os.path.join(class_dir_path, image_file)

        # Load and display the image in a subplot
        plt.subplot(1, len(class_dirs), i + 1)
        image = Image.open(image_path)
        plt.imshow(image)
        plt.title(f"Class: {class_dir}")
        plt.axis('off')

    plt.show()

plot_images_in_single_row(train_dir)

def get_class_names(folder_path):
    class_names = []
    subfolders = [f.name for f in os.scandir(folder_path) if f.is_dir()]
    class_names = sorted(subfolders)
    return class_names

class_names = get_class_names(train_dir)
print(f"Number of classes: {len(class_names)}")
print(class_names)

class_image_counts = [len(os.listdir(os.path.join(train_dir, class_names))) for class_names in class_names]
print(class_image_counts)

data = {
    'Class': class_names,
    'Number of Images': class_image_counts
}

fig = px.bar(data, x='Class', y='Number of Images', title='Number of Images per Class in Train Data')
fig.update_xaxes(tickangle=45, tickfont=dict(size=10))
fig.show()

# Data transforms
def get_transform(train):
    if train:
        return transforms.Compose([
            transforms.Resize((224, 224)),  # Resize images to fixed size
            transforms.RandomHorizontalFlip(),
            transforms.RandomVerticalFlip(),
            transforms.RandomRotation(10),
            transforms.ColorJitter(brightness=0.1, contrast=0.1, saturation=0.1, hue=0.1),
            transforms.RandomAffine(degrees=0, translate=(0.1, 0.1), scale=(0.9, 1.1)),
            transforms.ToTensor(),
            transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])
        ])
    else:
        return transforms.Compose([
            transforms.Resize((224, 224)),  # Resize images to fixed size
            transforms.ToTensor(),
            transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])
        ])

train_transforms = get_transform(train=True)
valid_transforms = get_transform(train=False)
test_transforms = get_transform(train=False)

# Load datasets
dataset = datasets.ImageFolder(train_dir, transform=train_transforms)

# Split the dataset into training and validation sets
train_size = int(0.8 * len(dataset))
val_size = len(dataset) - train_size
train_dataset, val_dataset = random_split(dataset, [train_size, val_size])

test_dataset = datasets.ImageFolder(test_dir, transform=test_transforms)

device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
model = CustomCNNClassification(len(dataset.classes))  # Changed to dataset.classes
model = model.to(device)

model

optimizer = torch.optim.AdamW(
    model.parameters(),
    lr=LEARNING_RATE,
    weight_decay=WEIGHT_DECAY
)

device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
if torch.cuda.is_available():
    print('cuda is available')
    model.cuda()
else:
    print('cuda not available')

# Compute class weights
class_weights = compute_class_weight(
    class_weight='balanced',
    classes=np.unique(train_dataset.targets),
    y=train_dataset.targets
    )
class_weights = torch.tensor(class_weights, dtype=torch.float).to(device)

loss_func = nn.CrossEntropyLoss(weight=class_weights)

