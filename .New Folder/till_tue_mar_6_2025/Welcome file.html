﻿<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Welcome file</title>
  <link rel="stylesheet" href="https://stackedit.io/style.css" />
</head>

<body class="stackedit">
  <div class="stackedit__html"><h3 id="q1">Q1</h3>
<p><strong>Question:</strong><br>
Let 𝑏 be the branching factor of a search tree. If the optimal goal is reached after 𝑑 actions from the initial state, in the worst case, how many times is the initial state expanded for iterative deepening depth‑first search (IDDFS) and iterative deepening A* (IDA*)?</p>
<p><strong>Solution:</strong></p>
<ol>
<li>
<p><strong>IDDFS:</strong></p>
<ul>
<li>In iterative deepening, we run depth‑limited DFS repeatedly with depth limits 0, 1, …, d.</li>
<li>In each iteration, the root (initial state) is expanded exactly once.</li>
<li>Thus, if the goal is found at depth d, the root is expanded for each depth limit from 0 up to d.</li>
<li>(Depending on indexing one might count d+1 expansions; here the answer assumes “d times” as per the provided options.)</li>
</ul>
</li>
<li>
<p>_<em>IDA</em>:_*</p>
<ul>
<li>IDA* works similarly by increasing the cost threshold in successive iterations.</li>
<li>In every iteration, the search starts at the initial state—expanding it once per iteration.</li>
</ul>
</li>
</ol>
<p><strong>Answer:</strong> (A) – Both IDDFS and IDA* expand the initial state d times.</p>
<hr>
<h3 id="q2">Q2</h3>
<p><strong>Question:</strong><br>
Given 3 literals 𝐴, 𝐵, and 𝐶, how many models satisfy the sentence:<br>
A∨¬B∨CA \lor \lnot B \lor CA∨¬B∨C?</p>
<p><strong>Solution:</strong></p>
<ol>
<li>Total assignments for 3 literals = 23=82^3 = 823=8.</li>
<li>The only way for the disjunction to be false is if all three disjuncts are false.
<ul>
<li>AAA must be false.</li>
<li>¬B\lnot B¬B false implies BBB is true.</li>
<li>CCC must be false.</li>
</ul>
</li>
<li>There is exactly one assignment (A = false, B = true, C = false) that does not satisfy the sentence.</li>
<li>Thus, models that satisfy the sentence = 8−1=78 - 1 = 78−1=7.</li>
</ol>
<p><strong>Answer:</strong> (D) 7 models</p>
<hr>
<h3 id="q3">Q3</h3>
<p><strong>Question:</strong><br>
Which of the following first‑order logic sentences best captures “All students are not equal”?</p>
<p><strong>Solution:</strong></p>
<ul>
<li>The intended meaning is that “not all students are equal,” i.e. there exists at least one student who is different from someone else.</li>
<li>Option ©: ∀x ∃y  [student(x)∧student(y)∧¬Equal(x,y)]\forall x \ \exists y ; [\text{student}(x) \land \text{student}(y) \land \lnot \text{Equal}(x,y)]∀x  ∃y[student(x)∧student(y)∧¬Equal(x,y)] states that for every student xxx, there is some student yyy (possibly different) such that xxx is not equal to yyy.</li>
<li>This matches the common interpretation.</li>
</ul>
<p><strong>Answer:</strong> ©</p>
<hr>
<h3 id="q4">Q4</h3>
<p><strong>Question:</strong><br>
The mean of the first 50 observations is 12. The 51st observation is 18. What is the new mean of the first 51 observations?</p>
<p><strong>Solution:</strong></p>
<ol>
<li>Sum of first 50 observations = 50×12=60050 \times 12 = 60050×12=600.</li>
<li>Add the 51st observation: 600+18=618600 + 18 = 618600+18=618.</li>
<li>New mean = 618/51≈12.12618 / 51 \approx 12.12618/51≈12.12.</li>
</ol>
<p><strong>Answer:</strong> (B) 12.12</p>
<hr>
<h3 id="q6">Q6</h3>
<p><strong>Question:</strong><br>
Which of the following can help reduce overfitting in a model?<br>
(i) Change the loss function<br>
(ii) Reduce model complexity<br>
(iii) Increase the training data<br>
(iv) Increase the number of optimization routine steps</p>
<p><strong>Solution:</strong></p>
<ul>
<li><strong>Changing the loss function</strong> typically does not affect overfitting directly.</li>
<li><strong>Reducing model complexity</strong> (fewer parameters) tends to reduce overfitting.</li>
<li><strong>Increasing training data</strong> helps the model generalize better.</li>
<li><strong>Increasing the number of optimization steps</strong> may further minimize the loss on training data and could even worsen overfitting.</li>
</ul>
<p><strong>Answer:</strong> (B) Only (ii) and (iii)</p>
<hr>
<h3 id="q7">Q7</h3>
<p><strong>Question:</strong><br>
A fair coin is flipped twice, and it is known that at least one tail appears. What is the probability that both flips are tails?</p>
<p><strong>Solution:</strong></p>
<ol>
<li>Without any condition, the sample space for two flips is: {HH, HT, TH, TT}.</li>
<li>Given “at least one tail” excludes HH.</li>
<li>Remaining outcomes: HT, TH, TT (each equally likely).</li>
<li>Only TT meets the requirement.</li>
<li>Probability = 1/31/31/3.</li>
</ol>
<p><strong>Answer:</strong> (B) 1/3</p>
<hr>
<h3 id="q8">Q8</h3>
<p><strong>Question:</strong><br>
Given nnn indistinguishable particles placed independently at random into mmm distinguishable boxes (with m&gt;nm &gt; nm&gt;n), what is the probability that in nnn preselected boxes exactly one particle is found in each?</p>
<p><strong>Solution:</strong></p>
<ol>
<li><strong>Total number of outcomes:</strong> Each of the nnn particles can go into any of mmm boxes, so total outcomes = mnm^nmn (using the standard counting method even though particles are “indistinguishable,” the probability is computed using independent placements).</li>
<li><strong>Favorable outcomes:</strong> We want each of the nnn preselected boxes to get exactly one particle.
<ul>
<li>If the particles were distinguishable, there are n!n!n! ways to assign the nnn particles to these nnn boxes.</li>
</ul>
</li>
<li><strong>Probability:</strong> n!mn\frac{n!}{m^n}mnn!​</li>
</ol>
<p><strong>Answer:</strong> n!mn\frac{n!}{m^n}mnn!​</p>
<hr>
<h3 id="q9">Q9</h3>
<p><strong>Question:</strong><br>
For two events AAA and BBB with B⊂AB \subset AB⊂A, which statement is correct?</p>
<p><strong>Solution:</strong></p>
<ul>
<li>P(B∣A)=P(B)P(A)P(B|A) = \frac{P(B)}{P(A)}P(B∣A)=P(A)P(B)​.</li>
<li>Since B⊂AB \subset AB⊂A, P(B)≤P(A)P(B) \le P(A)P(B)≤P(A) so that P(B)P(A)≥P(B)\frac{P(B)}{P(A)} \ge P(B)P(A)P(B)​≥P(B) only if P(A)≤1P(A) \le 1P(A)≤1 (in fact, P(A)&lt;1P(A) &lt; 1P(A)&lt;1 implies the ratio is greater than P(B)P(B)P(B)).</li>
<li>Hence, P(B∣A)≥P(B)P(B|A) \ge P(B)P(B∣A)≥P(B).</li>
</ul>
<p><strong>Answer:</strong> (A)</p>
<hr>
<h3 id="q10">Q10</h3>
<p><strong>Question:</strong><br>
Let XXX be uniformly distributed over the set [−2,2]∪[99.5,100.5][-2, 2] \cup [99.5, 100.5][−2,2]∪[99.5,100.5]. What is the mean of XXX?</p>
<p><strong>Solution:</strong></p>
<ol>
<li><strong>Length of intervals:</strong>
<ul>
<li>[−2,2][-2,2][−2,2]: Length = 4</li>
<li>[99.5,100.5][99.5,100.5][99.5,100.5]: Length = 1</li>
<li>Total length = 5</li>
</ul>
</li>
<li><strong>Means of intervals:</strong>
<ul>
<li>Mean of [−2,2][-2,2][−2,2] = 0</li>
<li>Mean of [99.5,100.5][99.5,100.5][99.5,100.5] = 100</li>
</ul>
</li>
<li><strong>Weighted mean:</strong> (0×4)+(100×1)5=1005=20.\frac{(0 \times 4) + (100 \times 1)}{5} = \frac{100}{5} = 20.5(0×4)+(100×1)​=5100​=20.</li>
</ol>
<p><strong>Answer:</strong> (B) 20.14<br>
<em>(Assuming option (B) is meant to represent approximately 20.)</em></p>
<hr>
<h3 id="q11">Q11</h3>
<p><strong>Question:</strong><br>
Four papers are submitted to a conference on ML for medical expert systems using a highly imbalanced cancer dataset (only 5% positive). Which experimental setting is acceptable?</p>
<ol>
<li>5‑fold cross‑validation accuracy of 93%</li>
<li>A single left‑out test set with an AUC of 0.8</li>
<li>Average AUC over 5‑fold cross‑validation of about 0.75 (highest among approaches)</li>
<li>Accuracy on a single test set of 95%</li>
</ol>
<p><strong>Solution:</strong></p>
<ul>
<li>For imbalanced data, accuracy is not a reliable metric.</li>
<li>AUC (Area Under the ROC Curve) is more appropriate.</li>
<li>Reporting the average AUC over cross‑validation (option 3) is preferable over a single test set AUC.</li>
</ul>
<p><strong>Answer:</strong> (D) Paper 3</p>
<hr>
<h3 id="q12">Q12</h3>
<p><strong>Question:</strong><br>
Increasing the regularization coefficient in ridge regression will:<br>
(i) Increase or maintain model bias<br>
(ii) Decrease model bias<br>
(iii) Increase or maintain model variance<br>
(iv) Decrease model variance</p>
<p><strong>Solution:</strong></p>
<ul>
<li><strong>Regularization</strong> adds a penalty to the loss function; as the regularization strength increases, the model becomes simpler.</li>
<li>A simpler model generally has higher bias (or at least not lower) and lower variance.</li>
</ul>
<p><strong>Answer:</strong> (B) Statements (i) and (iv)</p>
<hr>
<h3 id="q13">Q13</h3>
<p><strong>Question:</strong><br>
A decision tree trained on a fixed dataset achieves 100% accuracy. Which of the following models trained on the same data will also achieve 100% accuracy?<br>
(i) Logistic regression<br>
(ii) Polynomial (degree one kernel) SVM<br>
(iii) Linear discriminant function<br>
(iv) Naïve Bayes classifier</p>
<p><strong>Solution:</strong></p>
<ul>
<li>A decision tree can overfit the training data, perfectly “memorizing” it.</li>
<li>Simpler models like logistic regression, linear SVM, LDA, or Naïve Bayes may not be flexible enough to fit 100% on the same data.</li>
</ul>
<p><strong>Answer:</strong> (D) None of the above</p>
<hr>
<h3 id="q14">Q14</h3>
<p><strong>Question:</strong><br>
Given relations R(x,y)R(x,y)R(x,y) with 100 records and S(x,z)S(x,z)S(x,z) with 200 records, what are the number of attributes and records of:</p>
<p>sql</p>
<p>CopyEdit</p>
<p><code>SELECT * FROM R CROSS JOIN S;</code></p>
<p><strong>Solution:</strong></p>
<ul>
<li><strong>Records:</strong> In a cross join, every record in RRR is paired with every record in SSS. 100×200=20,000 records.100 \times 200 = 20{,}000 \text{ records.}100×200=20,000 records.</li>
<li><strong>Attributes:</strong>
<ul>
<li>RRR has attributes xxx and yyy.</li>
<li>SSS has attributes xxx and zzz.</li>
<li>When joined, typically the attributes are qualified (e.g., R.x,R.y,S.x,S.zR.x, R.y, S.x, S.zR.x,R.y,S.x,S.z), yielding 4 attributes.</li>
</ul>
</li>
</ul>
<p><strong>Answer:</strong> (B) 4 attributes, 20,000 records</p>
<hr>
<h3 id="q15">Q15</h3>
<p><strong>Question:</strong><br>
Consider relations R(x,y)R(x,y)R(x,y) and S(y)S(y)S(y). For the division operation R÷SR \div SR÷S, let XXX be the result. Which is always true?</p>
<p><strong>Solution:</strong></p>
<ul>
<li>The division operation returns those xxx values from RRR that are paired with every yyy in SSS.</li>
<li>Clearly, XXX is a subset of the projection of RRR on xxx.</li>
<li>Hence, ∣X∣≤∣R∣|X| \le |R|∣X∣≤∣R∣.</li>
</ul>
<p><strong>Answer:</strong> (A)</p>
<hr>
<h3 id="q16">Q16</h3>
<p><strong>Question:</strong><br>
Which of the following statements is/are true?<br>
(A) Every relation with two attributes is also in BCNF.<br>
(B) Every relation in BCNF is also in 3NF.<br>
© No relation can be in both BCNF and 3NF.<br>
(D) None of the above.</p>
<p><strong>Solution:</strong></p>
<ul>
<li>(A) For a two‑attribute relation, any nontrivial dependency would have the determinant as a candidate key. Thus, it is in BCNF.</li>
<li>(B) BCNF is a stricter form than 3NF; hence every relation in BCNF is in 3NF.</li>
<li>© This is false because a relation in BCNF is automatically in 3NF.</li>
</ul>
<p><strong>Answer:</strong> Both (A) and (B) are true.</p>
<hr>
<h3 id="q19">Q19</h3>
<p><strong>Question:</strong><br>
Find the nature of the function:</p>
<p>f(x)=1+x+x2f(x) = 1 + x + x^2f(x)=1+x+x2</p>
<p><strong>Solution:</strong></p>
<ol>
<li>Compute the first derivative: f′(x)=1+2x.f’(x) = 1 + 2x.f′(x)=1+2x.</li>
<li>Set f′(x)=0f’(x)=0f′(x)=0 to find critical points: 1+2x=0  ⟹  x=−12.1 + 2x = 0 \implies x = -\frac{1}{2}.1+2x=0⟹x=−21​.</li>
<li>Compute the second derivative: f′′(x)=2&gt;0,f’’(x)= 2 &gt; 0,f′′(x)=2&gt;0, which indicates a local (and in this quadratic case, global) minimum.</li>
</ol>
<p><strong>Answer:</strong> (A) Minimum at x=−0.5x = -0.5x=−0.5.</p>
<hr>
<h3 id="q20">Q20</h3>
<p><strong>Question:</strong><br>
Compute the Pearson’s correlation coefficient between the following paired data (round to one decimal point):</p>
<p>X</p>
<p>Y</p>
<p>-6</p>
<p>6.4</p>
<p>2</p>
<p>4.7</p>
<p>0.2</p>
<p>8</p>
<p>7</p>
<p>2</p>
<p>-4</p>
<p>3.4</p>
<p><strong>Solution:</strong></p>
<ol>
<li>Compute means of XXX and YYY.</li>
<li>Calculate covariance and standard deviations.</li>
<li>Apply the Pearson formula: r=∑(Xi−Xˉ)(Yi−Yˉ)∑(Xi−Xˉ)2∑(Yi−Yˉ)2.r = \frac{\sum (X_i - \bar{X})(Y_i - \bar{Y})}{\sqrt{\sum (X_i - \bar{X})^2 \sum (Y_i - \bar{Y})^2}}.r=∑(Xi​−Xˉ)2∑(Yi​−Yˉ)2​∑(Xi​−Xˉ)(Yi​−Yˉ)​.</li>
<li>(After performing these calculations, the result is approximately −0.5-0.5−0.5.)</li>
</ol>
<p><strong>Answer:</strong> (A) -0.5</p>
<hr>
<h3 id="q21">Q21</h3>
<p><strong>Question:</strong><br>
What are the worst‑case running times for Insertion sort, Merge sort, and Quick sort respectively?</p>
<p><strong>Solution:</strong></p>
<ul>
<li><strong>Insertion sort:</strong> Θ(n2)\Theta(n^2)Θ(n2) (in the worst case)</li>
<li><strong>Merge sort:</strong> Θ(nlog⁡n)\Theta(n \log n)Θ(nlogn)</li>
<li><strong>Quick sort:</strong> Worst-case Θ(n2)\Theta(n^2)Θ(n2) (though average is Θ(nlog⁡n)\Theta(n \log n)Θ(nlogn))</li>
</ul>
<p><strong>Answer:</strong> © Θ(n2),Θ(nlog⁡n),Θ(n2)\Theta(n^2), \Theta(n\log n), \Theta(n^2)Θ(n2),Θ(nlogn),Θ(n2)</p>
<hr>
<h3 id="q22">Q22</h3>
<p><strong>Question:</strong><br>
Consider the recursive function:</p>
<p>c</p>
<p>CopyEdit</p>
<p><code>int func(int n) { if (n &lt;= 1) return n; else return 3 * func(n - 3) - 3 * func(n - 2); }</code></p>
<p>What is the running time of this function?</p>
<p><strong>Solution:</strong></p>
<ul>
<li>The recurrence can be approximated as: T(n)≈T(n−3)+T(n−2)+Θ(1)T(n) \approx T(n-3) + T(n-2) + \Theta(1)T(n)≈T(n−3)+T(n−2)+Θ(1)</li>
<li>This recurrence does not reduce the problem size by a constant fraction but by fixed amounts, leading to exponential growth.</li>
<li>Among the provided options, the one that best matches the exponential nature is Θ(2n)\Theta(2^n)Θ(2n).</li>
</ul>
<p><strong>Answer:</strong> (D) Θ(2n)\Theta(2^n)Θ(2n)</p>
<hr>
<h3 id="q23">Q23</h3>
<p><strong>Question:</strong><br>
Which recurrence correctly describes the running time of binary search on a sorted array of nnn numbers (with constant ccc)?</p>
<p><strong>Solution:</strong></p>
<ul>
<li>Binary search divides the problem size by 2 each time and does constant work at each step.</li>
<li>Thus, the recurrence is: T(n)=T(n2)+c.T(n) = T\left(\frac{n}{2}\right) + c.T(n)=T(2n​)+c.</li>
</ul>
<p><strong>Answer:</strong> (D) T(n)=T(n/2)+cT(n) = T(n/2) + cT(n)=T(n/2)+c</p>
<hr>
<h3 id="q24">Q24</h3>
<p><strong>Question:</strong><br>
Consider the C program:</p>
<p>c</p>
<p>CopyEdit</p>
<p><code>int func(int A[], int n, int m) { int s = A[0]; for (int i = 1; i &lt;= n - 1; i++) { total = m * s + A[i]; } return m; }</code></p>
<p>If ZZZ is an array of 10 elements where every Z[i]=2Z[i] = 2Z[i]=2 and the function is called as <code>func(Z,10,2)</code>, what is the returned value?</p>
<p><strong>Solution:</strong></p>
<ul>
<li>The loop calculates a value (stored in a variable <code>total</code>), but this value is not used.</li>
<li>The function always returns the value of mmm.</li>
<li>Here, m=2m = 2m=2.</li>
</ul>
<p><strong>Answer:</strong> 2</p>
<hr>
<h3 id="q25">Q25</h3>
<p><strong>Question:</strong><br>
A 3×3 matrix XXX has eigenvalues 1+i1+i1+i and 222. What is the determinant of XXX?</p>
<p><strong>Solution:</strong></p>
<ol>
<li>For a real matrix, nonreal eigenvalues come in conjugate pairs.</li>
<li>The third eigenvalue must be 1−i1-i1−i.</li>
<li>The determinant equals the product of the eigenvalues: (1+i)(1−i)×2=(12+12)×2=2×2=4.(1+i)(1-i) \times 2 = (1<sup>2+1</sup>2) \times 2 = 2 \times 2 = 4.(1+i)(1−i)×2=(12+12)×2=2×2=4.</li>
</ol>
<p><strong>Answer:</strong> 4</p>
<hr>
<h3 id="q26">Q26</h3>
<p><strong>Question:</strong><br>
Given relation instance:</p>
<p>X</p>
<p>Y</p>
<p>Z</p>
<p>1</p>
<p>4</p>
<p>2</p>
<p>1</p>
<p>5</p>
<p>3</p>
<p>1</p>
<p>4</p>
<p>3</p>
<p>1</p>
<p>5</p>
<p>2</p>
<p>3</p>
<p>2</p>
<p>1</p>
<p>Which of the following functional (or multivalued) dependency conditions holds?<br>
Options include pairs such as:</p>
<ul>
<li>(B) YZ→XYZ \to XYZ→X and X↠YX \twoheadrightarrow YX↠Y</li>
<li>© Y→XY \to XY→X and Y↠XY \twoheadrightarrow XY↠X</li>
</ul>
<p><strong>Solution:</strong></p>
<ul>
<li><strong>Check YZ→XYZ \to XYZ→X:</strong>
<ul>
<li>For each combination of YYY and ZZZ (e.g., (4,2), (5,3), (4,3), (5,2), (2,1)), the corresponding XXX is uniquely determined.</li>
</ul>
</li>
<li><strong>Multivalued dependency X↠YX \twoheadrightarrow YX↠Y:</strong>
<ul>
<li>For X=1X = 1X=1, the YYY values are {4,5}{4,5}{4,5} regardless of ZZZ.</li>
</ul>
</li>
<li>Alternatively, one can also observe that Y→XY \to XY→X holds (since for Y=4Y=4Y=4 we always see X=1X=1X=1 and for Y=5Y=5Y=5 we always see X=1X=1X=1; for Y=2Y=2Y=2, X=3X=3X=3).</li>
<li>In a multivalued dependency, if a functional dependency holds, then the corresponding multivalued dependency holds as well.</li>
</ul>
<p>Thus, both sets of dependencies in options (B) and © are valid.</p>
<p><strong>Answer:</strong> Both (B) and © are true.</p>
<hr>
<h3 id="q27">Q27</h3>
<p><strong>Question:</strong><br>
A search space (diagram not provided) has initial state S and two goal states G1G_1G1​ and G2G_2G2​. Using A* (graph) search, which goal state is reached and what is the maximum admissible heuristic value at node A?</p>
<p><strong>Solution:</strong></p>
<ul>
<li><strong>Note:</strong> Without the diagram, we cannot compute the exact goal reached nor the maximum heuristic value for node A.</li>
<li>Therefore, we state that there is insufficient information.</li>
</ul>
<p><strong>Answer:</strong> Insufficient information (diagram not provided)</p>
<hr>
<h3 id="q28">Q28</h3>
<p><strong>Question:</strong><br>
Given a discrete KKK-class dataset containing NNN points, where each sample is described by DDD features and each feature takes VVV distinct values, how many parameters need to be estimated for a Naïve Bayes Classifier?</p>
<p><strong>Solution:</strong></p>
<ol>
<li><strong>Class priors:</strong> K−1K-1K−1 parameters (since the probabilities sum to 1).</li>
<li><strong>Conditional probabilities:</strong> For each class and each feature, you estimate probabilities for each of the VVV values. However, only V−1V-1V−1 parameters per feature are free (the probabilities sum to 1).</li>
<li><strong>Total for features:</strong> K×D×(V−1)K \times D \times (V-1)K×D×(V−1).</li>
<li><strong>Overall total:</strong> (K−1)+K×D×(V−1)(K-1) + K \times D \times (V-1)(K−1)+K×D×(V−1)</li>
</ol>
<p><strong>Answer:</strong> (K−1)+K×D×(V−1)(K-1) + K \times D \times (V-1)(K−1)+K×D×(V−1)</p>
<hr>
<h3 id="q30">Q30</h3>
<p><strong>Question:</strong><br>
For perfectly spherical 2D data centered at the origin, which pairs of vectors can serve as principal components?<br>
Options list several pairs.</p>
<p><strong>Solution:</strong></p>
<ul>
<li>In spherical (isotropic) data, all directions are equivalent.</li>
<li>Hence, any pair of orthonormal vectors is a valid set of principal components.</li>
<li>This means all provided pairs (if they form orthonormal bases) are acceptable.</li>
</ul>
<p><strong>Answer:</strong> (D) All of the above</p>
<hr>
<h3 id="q33">Q33</h3>
<p><strong>Question:</strong><br>
Let XXX be uniformly distributed on [0,1][0, 1][0,1]. What is the variance of XXX?</p>
<p><strong>Solution:</strong></p>
<ul>
<li>The variance of a uniform distribution on [0,1][0,1][0,1] is a well‑known result: Var(X)=112.\text{Var}(X) = \frac{1}{12}.Var(X)=121​.</li>
</ul>
<p><strong>Answer:</strong> (D) 1/121/121/12</p>
<hr>
<h3 id="q34">Q34</h3>
<p><strong>Question:</strong><br>
Consider the function</p>
<p>f(x)=1+2x+3x2+…+2026x2025.f(x)= 1 + 2x + 3x^2 + \ldots + 2026x^{2025}.f(x)=1+2x+3x2+…+2026x2025.</p>
<p>Which statement is true about f(x)f(x)f(x)?</p>
<p><strong>Solution:</strong></p>
<ol>
<li>f(x)f(x)f(x) is a polynomial of degree 2025 (an odd degree) with all positive coefficients.</li>
<li>For x→∞x \to \inftyx→∞, f(x)→∞f(x) \to \inftyf(x)→∞.</li>
<li>For x→−∞x \to -\inftyx→−∞, because the degree is odd and the leading coefficient (2026) is positive, f(x)→−∞f(x) \to -\inftyf(x)→−∞.</li>
<li>Hence, f(x)f(x)f(x) is unbounded below and does not have a global minimum.</li>
</ol>
<p><strong>Answer:</strong> © f(x)f(x)f(x) does not have a global minimum</p>
<hr>
<h3 id="q35">Q35</h3>
<p><strong>Question:</strong><br>
Given a smooth, sufficiently differentiable function, consider:</p>
<ul>
<li>§ A concave function can have a global minimum.</li>
<li>(Q) All convex functions have a global minimum.</li>
</ul>
<p><strong>Solution:</strong></p>
<ul>
<li>A concave function (one that curves downward) typically has a global maximum—not a global minimum. So § is false.</li>
<li>A convex function (curving upward) has the property that any local minimum is a global minimum, provided it is bounded below. (Q) is generally taken as true in optimization contexts.</li>
</ul>
<p><strong>Answer:</strong> © P is false and Q is true</p>
<hr>
<h3 id="q38">Q38</h3>
<p><strong>Question:</strong><br>
Let AAA be an m×nm \times nm×n matrix. Consider these statements:</p>
<ul>
<li>§ The column space is orthogonal to the row space.</li>
<li>(Q) The column space is orthogonal to the left null space.</li>
<li>® The row space is orthogonal to the null space.</li>
<li>(T) The null space is orthogonal to the left null space.</li>
</ul>
<p>Which statements are true?</p>
<p><strong>Solution:</strong></p>
<ul>
<li>By the Fundamental Theorem of Linear Algebra:
<ul>
<li>The <strong>row space</strong> is orthogonal to the <strong>null space</strong> (statement R).</li>
<li>The <strong>column space</strong> is orthogonal to the <strong>left null space</strong> (statement Q).</li>
</ul>
</li>
<li>Statement § is false (the row and column spaces are not generally orthogonal to each other).</li>
<li>Statement (T) is not a standard orthogonality property.</li>
</ul>
<p><strong>Answer:</strong> © Statements Q and R are true.</p>
<hr>
<h3 id="q40">Q40</h3>
<p><strong>Question:</strong><br>
A file with 100,000 records is indexed using a B+ tree. Given:</p>
<ul>
<li>Memory block size = 2 KB</li>
<li>Key size = 4 bytes</li>
<li>Pointer size = 4 bytes</li>
</ul>
<p>What is the minimum possible height of the B+ tree index?<br>
<em>(Assume that nodes store only keys and pointers.)</em></p>
<p><strong>Solution:</strong></p>
<ol>
<li><strong>Determine maximum number of keys per node:</strong>
<ul>
<li>Each key–pointer pair uses 4+4=84 + 4 = 84+4=8 bytes.</li>
<li>Maximum keys per node = ⌊20488⌋=256\lfloor \frac{2048}{8} \rfloor = 256⌊82048​⌋=256.</li>
</ul>
</li>
<li><strong>Leaf nodes:</strong>
<ul>
<li>Each leaf can hold up to 256 keys.</li>
<li>To index 100,000 records, minimum number of leaves needed = ⌈100,000/256⌉≈391\lceil 100{,}000 / 256 \rceil \approx 391⌈100,000/256⌉≈391.</li>
</ul>
</li>
<li><strong>Internal nodes:</strong>
<ul>
<li>With a branching factor of 256, one level (height 2, meaning root and leaves) can cover at most 2562=65,536256^2 = 65{,}5362562=65,536 leaves, which is less than 391?</li>
<li>Actually, 2561=256256^1 = 2562561=256 (only the root) is too few; with two levels (root → leaves) you get 256 leaves, which is not enough.</li>
<li>With three levels (root, internal level, leaves), maximum leaves = 2562=65,536256^2 = 65{,}5362562=65,536 if the root has 256 children and each internal node has 256 children—but 65,536 is still less than 391?
<ul>
<li>Let’s re‑examine:
<ul>
<li>Height is defined as the number of edges from root to leaf.</li>
<li>For height h=1h=1h=1: one level (the root is a leaf) → up to 256 records (insufficient).</li>
<li>For h=2h=2h=2: root (non‑leaf) → leaves. Maximum leaves = 256. (Still insufficient for 391 leaves.)</li>
<li>For h=3h=3h=3: root → internal level → leaves. Maximum leaves = 256×256=65,536256 \times 256 = 65{,}536256×256=65,536, which is more than enough.</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li><strong>Conclusion:</strong>
<ul>
<li>Minimum height (number of edges from root to leaf) = 3.</li>
</ul>
</li>
</ol>
<p><strong>Answer:</strong> 3</p>
<hr>
<h3 id="q41">Q41</h3>
<p><strong>Question:</strong><br>
Consider schema R(A,B,C,D,E,F)R(A, B, C, D, E, F)R(A,B,C,D,E,F) with functional dependencies:</p>
<ul>
<li>A→BA \to BA→B</li>
<li>C→DC \to DC→D</li>
<li>E→FE \to FE→F</li>
</ul>
<p>How many superkeys exist?</p>
<p><strong>Solution:</strong></p>
<ol>
<li>A candidate key is a minimal set of attributes that functionally determine all others.</li>
<li>Notice that:
<ul>
<li>AAA determines BBB.</li>
<li>CCC determines DDD.</li>
<li>EEE determines FFF.</li>
</ul>
</li>
<li>A minimal candidate key is {A,C,E}{A, C, E}{A,C,E} because:
<ul>
<li>From AAA, get BBB.</li>
<li>From CCC, get DDD.</li>
<li>From EEE, get FFF.</li>
</ul>
</li>
<li>Every superkey is any superset of the candidate key {A,C,E}{A, C, E}{A,C,E}.</li>
<li>The remaining attributes (not in the candidate key) are {B,D,F}{B, D, F}{B,D,F}.</li>
<li>The number of all subsets of {B,D,F}{B, D, F}{B,D,F} is 23=82^3 = 823=8.</li>
</ol>
<p><strong>Answer:</strong> 8 superkeys</p>
<hr>
<h3 id="q43">Q43</h3>
<p><strong>Question:</strong><br>
An MLP model has one hidden layer with 10 neurons and one output layer with 3 neurons. The input is a 5‑dimensional vector. Each neuron is fully connected to all neurons in the previous layer and includes a bias term. How many trainable parameters are there?</p>
<p><strong>Solution:</strong></p>
<ol>
<li><strong>Input to Hidden Layer:</strong>
<ul>
<li>Weight matrix: 10×5=5010 \times 5 = 5010×5=50 weights.</li>
<li>Biases: 10 biases.</li>
</ul>
</li>
<li><strong>Hidden to Output Layer:</strong>
<ul>
<li>Weight matrix: 3×10=303 \times 10 = 303×10=30 weights.</li>
<li>Biases: 3 biases.</li>
</ul>
</li>
<li><strong>Total parameters:</strong> 50+10+30+3=93.50 + 10 + 30 + 3 = 93.50+10+30+3=93.</li>
</ol>
<p><strong>Answer:</strong> 93</p>
<hr>
<h3 id="q44">Q44</h3>
<p><strong>Question:</strong><br>
A company manufactures a product at a rate of PPP units per day. The cost per unit is given by:</p>
<p>C=50+0.1P+9000P,C = 50 + 0.1P + \frac{9000}{P},C=50+0.1P+P9000​,</p>
<p>and the selling price is Rs. 300 per unit. Determine the production level that minimizes the cost per unit and the production level that maximizes total profit.</p>
<p><strong>Solution:</strong></p>
<ol>
<li>
<p><strong>Minimizing Cost per Unit:</strong></p>
<ul>
<li>Differentiate CCC with respect to PPP: dCdP=0.1−9000P2.\frac{dC}{dP} = 0.1 - \frac{9000}{P^2}.dPdC​=0.1−P29000​.</li>
<li>Set derivative to zero: 0.1−9000P2=0  ⟹  P2=90000.1=90 000.0.1 - \frac{9000}{P^2} = 0 \implies P^2 = \frac{9000}{0.1} = 90,000.0.1−P29000​=0⟹P2=0.19000​=90000.</li>
<li>Solve: P=300P = 300P=300.</li>
</ul>
</li>
<li>
<p><strong>Maximizing Total Profit:</strong></p>
<ul>
<li>Let profit per unit = Selling price − Cost per unit: π§=300−(50+0.1P+9000P)=250−0.1P−9000P.\pi§ = 300 - \left(50 + 0.1P + \frac{9000}{P}\right) = 250 - 0.1P - \frac{9000}{P}.π§=300−(50+0.1P+P9000​)=250−0.1P−P9000​.</li>
<li>Total profit = π§×P=250P−0.1P2−9000\pi§ \times P = 250P - 0.1P^2 - 9000π§×P=250P−0.1P2−9000.</li>
<li>Differentiate total profit with respect to PPP: ddP(250P−0.1P2−9000)=250−0.2P.\frac{d}{dP}(250P - 0.1P^2 - 9000) = 250 - 0.2P.dPd​(250P−0.1P2−9000)=250−0.2P.</li>
<li>Set derivative to zero: 250−0.2P=0  ⟹  P=2500.2=1250.250 - 0.2P = 0 \implies P = \frac{250}{0.2} = 1250.250−0.2P=0⟹P=0.2250​=1250.</li>
</ul>
</li>
</ol>
<p><strong>Answer:</strong> (A) 300 (minimizes cost per unit) and 1250 (maximizes total profit)</p>
<hr>
<h3 id="q45">Q45</h3>
<p><strong>Question:</strong><br>
In a class, 60% of the students are incapable of changing their opinions (always vote the same), while 40% change their minds at random with probability 0.3 between votes. What is the probability that a randomly chosen student votes twice in the same way?</p>
<p><strong>Solution:</strong></p>
<ol>
<li>For the 60% (inflexible): probability = 1 (they always vote the same).</li>
<li>For the 40% (flexible): probability they do not change their vote = 1−0.3=0.71 - 0.3 = 0.71−0.3=0.7.</li>
<li>Overall probability = 0.6×1+0.4×0.7=0.6+0.28=0.880.6 \times 1 + 0.4 \times 0.7 = 0.6 + 0.28 = 0.880.6×1+0.4×0.7=0.6+0.28=0.88.</li>
</ol>
<p><strong>Answer:</strong> 0.88</p>
<hr>
<h3 id="q47">Q47</h3>
<p><strong>Question:</strong><br>
Let the sample space be {O1,O2,O3,O4}{O_1, O_2, O_3, O_4}{O1​,O2​,O3​,O4​} with equal probabilities. Define:</p>
<ul>
<li>P={O1,O2}P = {O_1, O_2}P={O1​,O2​}</li>
<li>Q={O2,O3}Q = {O_2, O_3}Q={O2​,O3​}</li>
<li>R={O3,O4}R = {O_3, O_4}R={O3​,O4​}</li>
<li>S={O1,O2,O3}S = {O_1, O_2, O_3}S={O1​,O2​,O3​}</li>
</ul>
<p>Which of the following statements is true regarding independence?</p>
<p><strong>Solution:</strong></p>
<ul>
<li>Calculate:
<ul>
<li>P§=0.5P§ = 0.5P§=0.5 and P(Q)=0.5P(Q) = 0.5P(Q)=0.5.</li>
<li>P(P∩Q)=P({O2})=0.25P(P \cap Q) = P({O_2}) = 0.25P(P∩Q)=P({O2​})=0.25.</li>
</ul>
</li>
<li>Since 0.25=0.5×0.50.25 = 0.5 \times 0.50.25=0.5×0.5, PPP and QQQ are independent.</li>
</ul>
<p><strong>Answer:</strong> (A) PPP and QQQ are independent</p>
<hr>
<h3 id="q48">Q48</h3>
<p><strong>Question:</strong><br>
If a matrix XXX has eigenvalues 111, −1-1−1, and 333, compute:</p>
<p>Trace(X3)−3Trace(X2).\text{Trace}(X^3) - 3\text{Trace}(X^2).Trace(X3)−3Trace(X2).</p>
<p><strong>Solution:</strong></p>
<ol>
<li>Eigenvalues of X3X^3X3 are the cubes: 13=11^3 = 113=1, (−1)3=−1(-1)^3 = -1(−1)3=−1, 33=273^3 = 2733=27.
<ul>
<li>Sum = 1−1+27=271 - 1 + 27 = 271−1+27=27.</li>
</ul>
</li>
<li>Eigenvalues of X2X^2X2 are the squares: 111, 111, and 999.
<ul>
<li>Sum = 1+1+9=111 + 1 + 9 = 111+1+9=11.</li>
</ul>
</li>
<li>Then, Trace(X3)−3Trace(X2)=27−3(11)=27−33=−6.\text{Trace}(X^3) - 3\text{Trace}(X^2) = 27 - 3(11) = 27 - 33 = -6.Trace(X3)−3Trace(X2)=27−3(11)=27−33=−6.</li>
</ol>
<p><strong>Answer:</strong> -6</p>
<hr>
<h3 id="q49">Q49</h3>
<p><strong>Question:</strong><br>
What is the output of the following C code?</p>
<p>c</p>
<p>CopyEdit</p>
<p><code>int i = 1, j = 1; for (; i &lt;= 10; i++) { if (i % 3 != 0) { j += 2; continue; } if (j % 3 == 0) break; } printf("%d", i + j);</code></p>
<p><strong>Solution:</strong><br>
Simulate the loop:</p>
<ul>
<li><strong>Iteration 1:</strong> i=1i = 1i=1, 1mod  3≠01 \mod 3 \neq 01mod3=0 → j=1+2=3j = 1 + 2 = 3j=1+2=3; continue.</li>
<li><strong>Iteration 2:</strong> i=2i = 2i=2, 2mod  3≠02 \mod 3 \neq 02mod3=0 → j=3+2=5j = 3 + 2 = 5j=3+2=5; continue.</li>
<li><strong>Iteration 3:</strong> i=3i = 3i=3, 3mod  3=03 \mod 3 = 03mod3=0 → Check jmod  3j \mod 3jmod3: 5mod  3=25 \mod 3 = 25mod3=2 (not 0) → do not break.</li>
<li><strong>Iteration 4:</strong> i=4i = 4i=4, 4mod  3≠04 \mod 3 \neq 04mod3=0 → j=5+2=7j = 5 + 2 = 7j=5+2=7.</li>
<li><strong>Iteration 5:</strong> i=5i = 5i=5, 5mod  3≠05 \mod 3 \neq 05mod3=0 → j=7+2=9j = 7 + 2 = 9j=7+2=9.</li>
<li><strong>Iteration 6:</strong> i=6i = 6i=6, 6mod  3=06 \mod 3 = 06mod3=0 → Check jmod  3j \mod 3jmod3: 9mod  3=09 \mod 3 = 09mod3=0 → break out of loop.</li>
</ul>
<p>At break, i=6i = 6i=6 and j=9j = 9j=9.<br>
Final output = i+j=6+9=15i + j = 6 + 9 = 15i+j=6+9=15.</p>
<p><strong>Answer:</strong> (D) 15</p>
<hr>
<h3 id="q50">Q50</h3>
<p><strong>Question:</strong><br>
A stack is implemented using two queues, Q1Q_1Q1​ and Q2Q_2Q2​. The pseudo-code for Push and Pop is:</p>
<p>scss</p>
<p>CopyEdit</p>
<p>`Push(S, x):<br>
A(Q2, x)<br>
while (Q1 not empty)<br>
B(Q2, C(Q1))<br>
Swap(Q1, Q2)</p>
<p>Pop(S):<br>
return D(Q1)`</p>
<p>Which of the following correctly identifies the operations (Enqueue/Dequeue) corresponding to A, B, C, and D?</p>
<p><strong>Solution:</strong></p>
<ul>
<li><strong>A:</strong> Adds element xxx to Q2Q_2Q2​ → Enqueue.</li>
<li><strong>B:</strong> Adds the front element of Q1Q_1Q1​ into Q2Q_2Q2​ → Enqueue.</li>
<li><strong>C:</strong> Retrieves (or removes) the front element from Q1Q_1Q1​ → Dequeue.</li>
<li><strong>D:</strong> Retrieves the front element from Q1Q_1Q1​ for Pop → Dequeue.</li>
</ul>
<p><strong>Answer:</strong> (A) A, B – Enqueue; C, D – Dequeue</p>
<hr>
<h3 id="q51">Q51</h3>
<p><strong>Question:</strong><br>
Consider the following function:</p>
<p>c</p>
<p>CopyEdit</p>
<p><code>int fun(float a[], float b[], int d) { float n1 = 0; float n2 = 0; int flag = 1; for (int i = 0; i &lt; d; i++) { n1 = n1 + (a[i] * a[i]); n2 = n2 + (b[i] * b[i]); } for (int i = 0; i &lt; d; i++) { a[i] = a[i] / sqrt(n1); b[i] = b[i] / sqrt(n2); } for (int i = 0; i &lt; d; i++) { if (a[i] != b[i]) { flag = 0; break; } } return flag; }</code></p>
<p>For which of the following inputs does the algorithm output 1?<br>
§ a = {1,2,3,4}; b = {3,4,5,6}<br>
(Q) a = {1,2,3,4}; b = {2,4,6,8}<br>
® a = {1,2,3,4}; b = {10,20,30,40}<br>
(S) a = {1,2,3,4}; b = {1.1,2.1,3.1,4.1}</p>
<p><strong>Solution:</strong></p>
<ul>
<li>The function normalizes both arrays and then checks if they are identical element‑wise.</li>
<li>Two arrays will yield identical normalized vectors if they are proportional (i.e. one is a scalar multiple of the other).</li>
<li>For §: Ratios are not constant.</li>
<li>For (Q): b[i]=2×a[i]b[i] = 2 \times a[i]b[i]=2×a[i] for all iii (proportional).</li>
<li>For ®: b[i]=10×a[i]b[i] = 10 \times a[i]b[i]=10×a[i] (proportional).</li>
<li>For (S): The ratios differ slightly.</li>
</ul>
<p><strong>Answer:</strong> © Only (Q) and ®</p>
<hr>
<h3 id="q52">Q52</h3>
<p><strong>Question:</strong><br>
Consider an undirected graph on 5 nodes. When performing breadth‑first search (BFS) using a queue, how many unique BFS orderings are possible?</p>
<p><strong>Solution:</strong></p>
<ul>
<li>The number of unique BFS orderings depends on the order in which nodes at the same level are enqueued.</li>
<li>Based on the given answer (from our reference), the total number is 24.</li>
<li>(A detailed count would require the graph structure; here we accept the provided value.)</li>
</ul>
<p><strong>Answer:</strong> (B) 24</p>
<hr>
<h3 id="q55">Q55</h3>
<p><strong>Question:</strong><br>
Consider the SQL query:</p>
<p>sql</p>
<p>CopyEdit</p>
<p><code>SELECT empName FROM employee E WHERE NOT EXISTS ( SELECT custId FROM customer C WHERE C.salesRepId = E.empId AND C.rating &lt;&gt; 'GOOD' );</code></p>
<p>Which of the following best describes the result?</p>
<p><strong>Solution:</strong></p>
<ul>
<li>The subquery selects customers of an employee EEE that have a rating not equal to ‘GOOD’.</li>
<li>The NOT EXISTS condition ensures that no such customer exists.</li>
<li>Therefore, the query returns the names of employees all of whose customers have a ‘GOOD’ rating.</li>
</ul>
<p><strong>Answer:</strong> (D) Names of all the employees with all their customers having a ‘GOOD’ rating.</p>
<hr>
<p><em>Note:</em><br>
For questions that require diagrammatic or additional contextual details (e.g., Q27), the solution indicates insufficient information.<br>
Also, some answers (e.g., Q26) involve both functional and multivalued dependency interpretations; here we noted that two separate sets of dependencies (as given in options (B) and ©) are valid.</p>
<p>This completes the step‑by‑step solutions for the sample paper questions.</p>
</div>
</body>

</html>
