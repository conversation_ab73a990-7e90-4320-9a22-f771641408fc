import cv2
import mediapipe as mp
import pyautogui
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
from pynput.mouse import <PERSON><PERSON>, Controller
import time
from Xlib import display, X
import subprocess
import os
import json
import pickle

class GestureIntentionNet(nn.Module):
    def __init__(self, input_size=126, hidden_size=64, num_classes=5):
        """Neural network for gesture intention recognition"""
        super(GestureIntentionNet, self).__init__()
        self.lstm = nn.LSTM(input_size, hidden_size, num_layers=2, batch_first=True)
        self.fc1 = nn.Linear(hidden_size, 32)
        self.fc2 = nn.Linear(32, num_classes)
        self.relu = nn.ReLU()
        self.dropout = nn.Dropout(0.2)

    def forward(self, x):
        lstm_out, _ = self.lstm(x)
        lstm_out = lstm_out[:, -1, :]  # Take the last time step
        x = self.relu(self.fc1(lstm_out))
        x = self.dropout(x)
        x = self.fc2(x)
        return x

class GestureDataset(Dataset):
    def __init__(self, sequences, labels):
        self.sequences = sequences
        self.labels = labels

    def __len__(self):
        return len(self.sequences)

    def __getitem__(self, idx):
        return torch.FloatTensor(self.sequences[idx]), torch.LongTensor([self.labels[idx]])


class AIHandController:
    def __init__(self):
        """Initialize the AI-enhanced hand controller"""
        # Initialize MediaPipe
        self.mp_hands = mp.solutions.hands
        self.hands = self.mp_hands.Hands(
            max_num_hands=2,
            min_detection_confidence=0.7,
            min_tracking_confidence=0.7
        )
        self.mp_draw = mp.solutions.drawing_utils

        # Initialize controllers
        self.mouse = Controller()
        self.display = display.Display()
        
        # Screen setup
        self.screen_width, self.screen_height = pyautogui.size()
        
        # Camera setup
        self.cap = cv2.VideoCapture(0)
        self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
        self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)

        # Initialize intentions dictionary
        self.intentions = {
            0: 'idle',
            1: 'file_drag',
            2: 'window_drag',
            3: 'zoom',
            4: 'scroll'
        }

        # AI model setup
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.model = GestureIntentionNet().to(self.device)
        self.sequence_length = 30
        self.gesture_sequence = []
        
        # Action states
        self.current_action = None
        self.is_dragging = False
        self.is_window_dragging = False
        self.is_zooming = False
        self.prev_zoom_distance = None
        
        # Load or create the model
        self.model_path = 'gesture_model.pth'
        if os.path.exists(self.model_path):
            self.model.load_state_dict(torch.load(self.model_path))
        else:
            self.train_model()

    def extract_hand_features(self, hand_landmarks):
        """Extract relevant features from hand landmarks"""
        features = []
        if hand_landmarks:
            for landmark in hand_landmarks.landmark:
                features.extend([landmark.x, landmark.y, landmark.z])
        else:
            features.extend([0] * 63)  # Padding for missing hand
        return features

    def prepare_sequence(self, hand_landmarks_list):
        """Prepare the sequence of hand features for the model"""
        features = []
        if len(hand_landmarks_list) == 2:
            features.extend(self.extract_hand_features(hand_landmarks_list[0]))
            features.extend(self.extract_hand_features(hand_landmarks_list[1]))
        elif len(hand_landmarks_list) == 1:
            features.extend(self.extract_hand_features(hand_landmarks_list[0]))
            features.extend([0] * 63)  # Padding for missing second hand
        else:
            features.extend([0] * 126)  # Padding for no hands

        self.gesture_sequence.append(features)
        if len(self.gesture_sequence) > self.sequence_length:
            self.gesture_sequence.pop(0)
        
        return self.gesture_sequence

    def predict_intention(self, sequence):
        """Predict the user's intention from the gesture sequence"""
        if len(sequence) < self.sequence_length:
            return 'idle'
            
        with torch.no_grad():
            sequence_tensor = torch.FloatTensor([sequence]).to(self.device)
            output = self.model(sequence_tensor)
            _, predicted = torch.max(output, 1)
            return self.intentions[predicted.item()]

    def collect_training_data(self):
        """Collect training data for the model"""
        print("Collecting training data...")
        print("Press 'q' to stop collection for current action")
        
        training_data = []
        training_labels = []
        
        for intention_idx, intention in self.intentions.items():
            print(f"\nPerform {intention} gestures...")
            time.sleep(3)
            
            sequence = []
            while True:
                success, image = self.cap.read()
                if not success:
                    continue
                    
                image = cv2.flip(image, 1)
                results = self.hands.process(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
                
                if results.multi_hand_landmarks:
                    features = self.prepare_sequence(results.multi_hand_landmarks)
                    if len(features) == self.sequence_length:
                        training_data.append(features)
                        training_labels.append(intention_idx)
                
                cv2.imshow('Data Collection', image)
                if cv2.waitKey(1) & 0xFF == ord('q'):
                    break
        
        return training_data, training_labels

    def train_model(self):
        """Train the gesture recognition model"""
        print("Starting model training...")
        
        # Collect training data
        sequences, labels = self.collect_training_data()
        
        # Create dataset and dataloader
        dataset = GestureDataset(sequences, labels)
        dataloader = DataLoader(dataset, batch_size=32, shuffle=True)
        
        # Training setup
        criterion = nn.CrossEntropyLoss()
        optimizer = optim.Adam(self.model.parameters())
        
        # Training loop
        num_epochs = 50
        for epoch in range(num_epochs):
            for batch_sequences, batch_labels in dataloader:
                batch_sequences = batch_sequences.to(self.device)
                batch_labels = batch_labels.squeeze().to(self.device)
                
                optimizer.zero_grad()
                outputs = self.model(batch_sequences)
                loss = criterion(outputs, batch_labels)
                loss.backward()
                optimizer.step()
            
            print(f'Epoch [{epoch+1}/{num_epochs}], Loss: {loss.item():.4f}')
        
        # Save the trained model
        torch.save(self.model.state_dict(), self.model_path)
        print("Model training completed and saved!")

    def handle_window_drag(self, hand_center):
        """Handle window dragging action"""
        if not self.is_window_dragging:
            window_id = self.get_active_window()
            if window_id:
                self.is_window_dragging = True
                self.current_window = window_id
        
        if self.is_window_dragging:
            screen_pos = self.map_to_screen(hand_center)
            self.move_window(self.current_window, screen_pos[0], screen_pos[1])

    def handle_file_drag(self, hand_center):
        """Handle file dragging action"""
        screen_pos = self.map_to_screen(hand_center)
        if not self.is_dragging:
            self.mouse.position = screen_pos
            self.mouse.press(Button.left)
            self.is_dragging = True
        else:
            self.mouse.position = screen_pos

    def handle_zoom(self, hand_landmarks_list):
        """Handle zoom action"""
        if len(hand_landmarks_list) == 2:
            current_distance = self.get_distance_between_hands(hand_landmarks_list)
            if self.prev_zoom_distance is not None:
                zoom_change = current_distance - self.prev_zoom_distance
                if abs(zoom_change) > 20:
                    if zoom_change > 0:
                        pyautogui.hotkey('ctrl', 'plus')
                    else:
                        pyautogui.hotkey('ctrl', 'minus')
            self.prev_zoom_distance = current_distance

    def get_active_window(self):
        """Get the currently active window"""
        try:
            output = subprocess.check_output(['xdotool', 'getactivewindow'])
            return int(output.strip())
        except:
            return None

    def move_window(self, window_id, x, y):
        """Move window to specified coordinates"""
        if window_id:
            subprocess.run(['xdotool', 'windowmove', str(window_id), str(x), str(y)])

    def get_distance_between_hands(self, hand_landmarks_list):
        """Calculate distance between two hands"""
        if len(hand_landmarks_list) < 2:
            return None
            
        center1 = self.get_hand_center(hand_landmarks_list[0])
        center2 = self.get_hand_center(hand_landmarks_list[1])
        
        return np.sqrt((center1[0] - center2[0])**2 + (center1[1] - center2[1])**2)

    def get_hand_center(self, hand_landmarks):
        """Calculate the center point of the hand"""
        x_coords = [landmark.x for landmark in hand_landmarks.landmark]
        y_coords = [landmark.y for landmark in hand_landmarks.landmark]
        center_x = int(np.mean(x_coords) * self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        center_y = int(np.mean(y_coords) * self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        return (center_x, center_y)

    def map_to_screen(self, hand_center):
        """Map hand position to screen coordinates"""
        x = np.interp(hand_center[0], 
                     [0, self.cap.get(cv2.CAP_PROP_FRAME_WIDTH)],
                     [0, self.screen_width])
        y = np.interp(hand_center[1],
                     [0, self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT)],
                     [0, self.screen_height])
        return (int(x), int(y))

    def run(self):
        """Main loop for AI-enhanced hand gesture control"""
        print("\n=== AI-Enhanced Hand Gesture Control System ===")
        print("Features:")
        print("1. Intelligent gesture recognition")
        print("2. Automatic intention detection")
        print("3. Adaptive learning from your movements")
        print("\nPress 'q' to quit, 't' to retrain the model")
        
        while True:
            success, image = self.cap.read()
            if not success:
                continue
                
            image = cv2.flip(image, 1)
            results = self.hands.process(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
            
            if results.multi_hand_landmarks:
                # Draw hand landmarks
                for hand_landmarks in results.multi_hand_landmarks:
                    self.mp_draw.draw_landmarks(
                        image, hand_landmarks, self.mp_hands.HAND_CONNECTIONS)
                
                # Prepare sequence and predict intention
                sequence = self.prepare_sequence(results.multi_hand_landmarks)
                if len(sequence) == self.sequence_length:
                    intention = self.predict_intention(sequence)
                    
                    # Handle different intentions
                    if intention == 'file_drag':
                        self.handle_file_drag(self.get_hand_center(results.multi_hand_landmarks[0]))
                    elif intention == 'window_drag':
                        self.handle_window_drag(self.get_hand_center(results.multi_hand_landmarks[0]))
                    elif intention == 'zoom':
                        self.handle_zoom(results.multi_hand_landmarks)
                    elif intention == 'idle':
                        if self.is_dragging:
                            self.mouse.release(Button.left)
                            self.is_dragging = False
                        if self.is_window_dragging:
                            self.is_window_dragging = False
                    
                    # Display current action
                    cv2.putText(image, f"Action: {intention}", (10, 30),
                              cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
            
            cv2.imshow('AI Hand Control', image)
            
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                break
            elif key == ord('t'):
                self.train_model()
        
        self.cap.release()
        cv2.destroyAllWindows()

if __name__ == "__main__":
    # Initialize and run the AI controller
    controller = AIHandController()
    controller.run()
