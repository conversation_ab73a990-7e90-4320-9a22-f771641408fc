import cv2
import os
import time

def capture_images(output_dir="dataset", total_images=20, images_per_second=4):
    # Ensure the output directory exists
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # Initialize the webcam
    cap = cv2.VideoCapture(0)
    if not cap.isOpened():
        print("Error: Could not open webcam.")
        return

    print("Starting image capture. Look at the camera.")

    # Calculate the delay between frames
    delay = 1 / images_per_second
    count = 0

    try:
        while count < total_images:
            # Capture a frame
            ret, frame = cap.read()
            if not ret:
                print("Error: Failed to capture image.")
                break

            # Save the captured image
            filename = os.path.join(output_dir, f"image_{count+1:03d}.jpg")
            cv2.imwrite(filename, frame)
            print(f"Captured {filename}")

            count += 1
            time.sleep(delay)

    except KeyboardInterrupt:
        print("Image capture interrupted.")

    finally:
        # Release the webcam and close all OpenCV windows
        cap.release()
        cv2.destroyAllWindows()
        print(f"Image capture complete. {count} images saved to '{output_dir}'.")

if __name__ == "__main__":
    capture_images()

