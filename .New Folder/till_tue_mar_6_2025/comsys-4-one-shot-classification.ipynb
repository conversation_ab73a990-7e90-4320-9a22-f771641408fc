{"metadata": {"kernelspec": {"language": "python", "display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python", "version": "3.10.14", "mimetype": "text/x-python", "codemirror_mode": {"name": "ipython", "version": 3}, "pygments_lexer": "ipython3", "nbconvert_exporter": "python", "file_extension": ".py"}, "kaggle": {"accelerator": "none", "dataSources": [{"sourceId": 84417, "databundleVersionId": 9459410, "sourceType": "competition"}], "dockerImageVersionId": 30762, "isInternetEnabled": true, "language": "python", "sourceType": "notebook", "isGpuEnabled": false}}, "nbformat_minor": 4, "nbformat": 4, "cells": [{"cell_type": "code", "source": "!pip install av", "metadata": {"_uuid": "8f2839f25d086af736a60e9eeb907d3b93b6e0e5", "_cell_guid": "b1076dfc-b9ad-4769-8c92-a6c4dae69d19", "execution": {"iopub.status.busy": "2024-09-17T09:18:55.880921Z", "iopub.execute_input": "2024-09-17T09:18:55.881805Z", "iopub.status.idle": "2024-09-17T09:19:12.102666Z", "shell.execute_reply.started": "2024-09-17T09:18:55.881753Z", "shell.execute_reply": "2024-09-17T09:19:12.101080Z"}, "trusted": true}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": "import torch , os , cv2 , random , math, matplotlib.pyplot as plt\nimport torch.nn as nn , torch.optim as optim , numpy as np\nfrom torch.utils.data import Dataset, DataLoader,random_split, Subset\nfrom torchvision import transforms\n\nfrom sklearn.model_selection import train_test_split\nfrom tqdm import tqdm\n# import cv2\nfrom PIL import Image\nfrom torch.nn.parallel import DataParallel\n# import matplotlib.pyplot as plt\nfrom collections import Counter\n\nimport av\nimport torch.nn.functional as F", "metadata": {"execution": {"iopub.status.busy": "2024-09-17T09:19:12.105858Z", "iopub.execute_input": "2024-09-17T09:19:12.106441Z", "iopub.status.idle": "2024-09-17T09:19:12.116060Z", "shell.execute_reply.started": "2024-09-17T09:19:12.106377Z", "shell.execute_reply": "2024-09-17T09:19:12.114808Z"}, "trusted": true}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": "import multiprocessing\nclass VideoDataset(Dataset):\n    def __init__(self, dataset_dir, data=None, labels=None):\n        self.dataset_dir = dataset_dir\n        self.data = data if data else []\n        self.labels = labels if labels else []\n        self.label_to_indices = {}\n        self.pool = multiprocessing.Pool(processes=3)\n\n        # If data and labels aren't passed, load them\n        if not self.data or not self.labels:\n            # Load video files and organize them by label\n            for label in os.listdir(dataset_dir):\n                label_dir = os.path.join(dataset_dir, label)\n                if os.path.isdir(label_dir):\n                    self.label_to_indices[label] = []\n                    for video_file in os.listdir(label_dir):\n                        if video_file.endswith('.mp4'):\n                            self.data.append(os.path.join(label_dir, video_file))\n                            self.labels.append(label)\n                            self.label_to_indices[label].append(len(self.data) - 1)\n\n    def __getitem__(self, index):\n        video_path = self.data[index]\n        label = self.labels[index]\n        \n        \n\n        # Get a positive sample from the same class (but different video)\n        positive_index = random.choice([i for i in self.label_to_indices[label] if i != index])\n        negative_label = self._get_negative_label(label)\n        # Get a negative sample from a different class\n        negative_index = random.choice(self.label_to_indices[negative_label])\n        \n        # Extract frames from the video       \n        \n        \n        video_paths = [video_path, self.data[positive_index], self.data[negative_index]]\n        \n        # Use the existing pool to process the videos\n        video_tensor, positive_tensor, negative_tensor = self.pool.map(get_video, video_paths)\n        \n\n        return video_tensor, positive_tensor, negative_tensor, label\n\n    def __len__(self):\n        return len(self.data)\n    \n    def get_label(self,index):\n        return self.labels[index]\n            \n\n    def _get_negative_label(self, label):\n\n        label_mapping = {\n            'football': 'rugby',    'judo': 'wrestling',\n            'rugby': 'football',    'wrestling': 'judo',\n                        'squash': 'wrestling'\n        }\n\n        if label in label_mapping:\n            return label_mapping[label]\n        \n        raise SystemExit(f\"label {lable} not available in the mapping\")\n\n\ndef stratified_split(dataset, test_size=0.25):\n    \"\"\"\n    Split the dataset into train and validation sets while maintaining the class distribution.\n    \"\"\"\n    # Perform stratified split based on the labels\n    train_indices, val_indices = train_test_split(\n        list(range(len(dataset))), \n        test_size=test_size, \n        stratify=dataset.labels  # Ensure the same distribution of labels in train/val sets\n    )\n    \n    train_labels = [dataset.get_label(i) for i in train_indices]\n    val_labels = [dataset.get_label(i) for i in val_indices]\n    print(f\" train labels {Counter(train_labels)} val labels {Counter(val_labels)}\")\n    # Create train and validation subsets\n    \n    train_dataset = Subset(dataset, train_indices)\n    val_dataset = Subset(dataset, val_indices)\n\n    return train_dataset, val_dataset", "metadata": {"execution": {"iopub.status.busy": "2024-09-17T09:52:34.586442Z", "iopub.execute_input": "2024-09-17T09:52:34.586899Z", "iopub.status.idle": "2024-09-17T09:52:34.606895Z", "shell.execute_reply.started": "2024-09-17T09:52:34.586855Z", "shell.execute_reply": "2024-09-17T09:52:34.605542Z"}, "trusted": true}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": "def get_video(video_path, max_attempts=10):\n    for attempt in range(max_attempts):\n        try:\n            # Open video file\n            container = av.open(video_path)\n\n            # Get video stream\n            stream = next((s for s in container.streams if s.type == 'video'), None)\n            if stream is None:\n                raise ValueError(f\"No video stream found in {video_path}\")\n\n            # Calculate FPS and total frames\n            fps = float(stream.average_rate)\n            total_frames = stream.frames\n            if fps <= 0 or total_frames <= 0:\n                raise ValueError(f\"Invalid FPS ({fps}) or total frames ({total_frames}) in {video_path}\")\n\n            # Calculate frame skipping\n            frame_skip = max(int(math.floor(fps / target_fps)), 1)\n            frames_to_extract = min(sequence_length, max(int(total_frames / frame_skip), 1))\n\n            # Determine starting frame index\n            max_start_frame = max(total_frames - frames_to_extract * frame_skip, 0)\n            start_frame = random.randint(0, max_start_frame)\n\n            frames = []\n            for frame_count, frame in enumerate(container.decode(video=0)):\n                if frame_count < start_frame or (frame_count - start_frame) % frame_skip != 0:\n                    continue\n\n                # Convert frame to PIL Image and apply transformations\n                img = frame.to_image()\n                transformed_frame = transform(img) if transform else img\n                frames.append(transformed_frame)\n\n                if len(frames) == frames_to_extract:\n                    break\n\n            if len(frames) == 0:\n#                 print(f\"    Failed to extract any frames from video: {os.path.join(*video_path.split('/')[-3:])}\")\n#                 print(f\"        Attempt {attempt + 1} \")\n                continue\n\n            if len(frames) < sequence_length:\n                if len(frames) < frames_to_extract:\n\n#                     print(f\"    extracted {len(frames)} frames in required {frames_to_extract} frames\")\n#                     print(f\"    total frames {total_frames} frame skip: {frame_skip} frame count {frame_count} and start frame {start_frame}\")                  \n#                     print(f\"    Attempt {attempt + 1}\")\n                    continue\n                frames.extend([frames[-1]] * (sequence_length - len(frames)))\n\n            return torch.stack(frames)\n\n        except Exception as e:\n#             print(f\"Attempt {attempt + 1}: Error processing {video_path} - {e}\")\n#             print(f\"    Frame count: {len(frames)}, Expected: {frames_to_extract}, Start frame: {start_frame}\")\n            continue\n    \n    print('run time error')\n    raise RuntimeError(f\"Failed to extract frames from video after {max_attempts} attempts: {video_path}\")\n\n\n\nnot_working_url = \"/kaggle/input/few-shot-video-classification/Few Shot video classification/Train/football/18_ftbl.mp4\"\nworking_url = \"/kaggle/input/few-shot-video-classification/Few Shot video classification/Train/football/08_ftbl.mp4\"\n\nresolution = (224,224)\ntarget_fps = 24\nsequence_length = 96\ntransform = transforms.Compose([\n            transforms.Resize(resolution),\n            transforms.ToTensor(),\n            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),\n#             transforms.Normalize(mean=[0.387, 0.39255, 0.332], std=[0.24356,0.23,0.22]),\n\n        ])\nimport time\nstart_time = time.time()\n\nvideo = get_video(not_working_url)\nvideo.shape,\"frame extracted \"\n\n\ntime.time()-start_time,video.shape", "metadata": {"execution": {"iopub.status.busy": "2024-09-17T09:47:16.385840Z", "iopub.execute_input": "2024-09-17T09:47:16.386308Z", "iopub.status.idle": "2024-09-17T09:47:18.519893Z", "shell.execute_reply.started": "2024-09-17T09:47:16.386261Z", "shell.execute_reply": "2024-09-17T09:47:18.518642Z"}, "trusted": true}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": "train_dataset_dir = '/kaggle/input/few-shot-video-classification/Few Shot video classification/Train'\ndataset = VideoDataset(train_dataset_dir)", "metadata": {"execution": {"iopub.status.busy": "2024-09-17T09:52:46.140583Z", "iopub.execute_input": "2024-09-17T09:52:46.141052Z", "iopub.status.idle": "2024-09-17T09:52:46.219321Z", "shell.execute_reply.started": "2024-09-17T09:52:46.141009Z", "shell.execute_reply": "2024-09-17T09:52:46.216904Z"}, "trusted": true}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": "import time\nstart_time = time.time()\nvideo , positive, negative , label = dataset.__getitem__(0)\n\n\ntime.time()-start_time", "metadata": {"execution": {"iopub.status.busy": "2024-09-17T09:51:01.434830Z", "iopub.execute_input": "2024-09-17T09:51:01.435292Z", "iopub.status.idle": "2024-09-17T09:51:14.084355Z", "shell.execute_reply.started": "2024-09-17T09:51:01.435249Z", "shell.execute_reply": "2024-09-17T09:51:14.083192Z"}, "trusted": true}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": "import time\nstart_time = time.time()\nvideo , positive, negative , label = dataset.__getitem__(0)\n\n\ntime.time()-start_time", "metadata": {"execution": {"iopub.status.busy": "2024-09-17T09:52:53.951216Z", "iopub.execute_input": "2024-09-17T09:52:53.951739Z", "iopub.status.idle": "2024-09-17T09:57:33.406612Z", "shell.execute_reply.started": "2024-09-17T09:52:53.951688Z", "shell.execute_reply": "2024-09-17T09:57:33.404765Z"}, "collapsed": true, "jupyter": {"outputs_hidden": true}, "trusted": true}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": "## * Function to plot and see the resulting frame sets per video", "metadata": {}}, {"cell_type": "code", "source": "# train_dataset_dir = '/kaggle/input/comsys-4-one-shot-classification/Few Shot video classification/Train'\n# dataset = VideoDataset(train_dataset_dir)\n\n# import matplotlib.pyplot as plt\n\n# def plot_video_frames(video, num_frames=5, figsize=(10, 5)):\n\n#     # Ensure num_frames does not exceed the actual number of frames in the video\n#     num_frames = min(num_frames, video.shape[0])\n    \n#     # Set up the figure\n#     fig, axes = plt.subplots(1, num_frames, figsize=figsize)\n    \n#     for i in range(num_frames):\n#         frame = video[i].permute(1, 2, 0).cpu().numpy()  # Permute to (height, width, 3) if using PyTorch tensor\n#         axes[i].imshow(frame)\n#         axes[i].axis('off')\n#         axes[i].set_title(f'Frame {i+1}')\n    \n#     plt.show()\n\n# for i in range(50,55):\n#     print(\"data number \",i)\n#     video , positive, negative , label = dataset.__getitem__(i)\n#     video.shape , positive.shape, negative.shape , label\n\n#     print(\"main frames\")\n#     plot_video_frames(video,5)\n\n#     print(\"positivw frames\")\n#     plot_video_frames(positive,5)\n# \n\n#     print(\"negative frames\")\n#     plot_video_frames(negative,5)", "metadata": {"execution": {"iopub.status.busy": "2024-09-12T16:28:49.613514Z", "iopub.execute_input": "2024-09-12T16:28:49.614113Z", "iopub.status.idle": "2024-09-12T16:28:49.619733Z", "shell.execute_reply.started": "2024-09-12T16:28:49.614074Z", "shell.execute_reply": "2024-09-12T16:28:49.618480Z"}, "trusted": true}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": "import torch\nimport torch.nn as nn\nimport torch.nn.functional as F\nfrom torchvision.models.video import mvit_v2_s, MViT_V2_S_Weights\n\nclass Swish(nn.Module):\n    def forward(self, x):\n        return x * torch.sigmoid(x)\n    \nclass SiameseNetworkMViT(nn.Module):\n    def __init__(self, embedding_dim=256):\n        super().__init__()\n        \n        # Load pre-trained MViT v2 small\n        self.mvit = mvit_v2_s(weights=MViT_V2_S_Weights.DEFAULT)\n        \n        # Freeze the first few layers\n        for param in list(self.mvit.parameters())[:100]:  # Adjust this number as needed\n            param.requires_grad = False\n        \n        # Replace the final classification layer\n        self.mvit.head = nn.Sequential(\n            nn.Linear(768, 3072),  # MViT v2 small has 768 features before classification\n            Swish(),\n            nn.Dropout(0.2),\n            nn.Linear(3072, embedding_dim)\n        )\n        \n    def forward(self, x):\n        # x shape: (batch_size, channels, num_frames, height, width)\n        x = x.permute(0, 2, 1, 3, 4)\n        x = self.mvit(x)\n        return F.normalize(x, p=2, dim=1)\n\nclass TripletLoss(nn.Module):\n    def __init__(self, margin=1.0):\n        super().__init__()\n        self.margin = margin\n    \n    def forward(self, anchor, positive, negative):\n        distance_positive = (anchor - positive).pow(2).sum(1)\n        distance_negative = (anchor - negative).pow(2).sum(1)\n        losses = torch.clamp(distance_positive - distance_negative + self.margin, min=0.0)\n        return losses.mean()\n\n# Usage example\nmodel = SiameseNetworkMViT()\ncriterion = TripletLoss()\n\n# MViT v2 small expects input shape (batch_size, channels, num_frames, height, width)\n# where num_frames=16, height=224, width=224\nanchor = torch.randn(1, 16, 3, 224, 224)\npositive = torch.randn(1, 16, 3, 224, 224)\nnegative = torch.randn(1, 16, 3, 224, 224)\n\nanchor_embedding = model(anchor)\npositive_embedding = model(positive)\nnegative_embedding = model(negative)\n\nloss = criterion(anchor_embedding, positive_embedding, negative_embedding)\nprint(f\"Triplet Loss: {loss.item()}\")", "metadata": {"execution": {"iopub.status.busy": "2024-09-25T03:39:16.552932Z", "iopub.execute_input": "2024-09-25T03:39:16.553363Z", "iopub.status.idle": "2024-09-25T03:39:40.064121Z", "shell.execute_reply.started": "2024-09-25T03:39:16.553323Z", "shell.execute_reply": "2024-09-25T03:39:40.062933Z"}, "trusted": true}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": "def train_and_validate(model, train_loader, val_loader, criterion, optimizer, device):\n    model.train()\n    total_train_loss = 0\n    for batch in tqdm(train_loader, desc=\"Training\"):\n        anchors, positives, negatives, _ = batch\n        anchors, positives, negatives = anchors.to(device), positives.to(device), negatives.to(device)\n        \n        anchor_embeddings = model(anchors)\n        positive_embeddings = model(positives)\n        negative_embeddings = model(negatives)\n        loss = criterion(anchor_embeddings, positive_embeddings, negative_embeddings)\n        \n        optimizer.zero_grad()\n        loss.backward()\n        optimizer.step()\n        total_train_loss += loss.item()\n    \n    avg_train_loss = total_train_loss / len(train_loader)\n    \n    # Validation\n    model.eval()\n    total_val_loss = 0\n    with torch.no_grad():\n        for batch in tqdm(val_loader, desc=\"Validating\"):\n            anchors, positives, negatives, _ = batch\n            anchors, positives, negatives = anchors.to(device), positives.to(device), negatives.to(device)\n            anchor_embeddings = model(anchors)\n            positive_embeddings = model(positives)\n            negative_embeddings = model(negatives)\n            loss = criterion(anchor_embeddings, positive_embeddings, negative_embeddings)\n            total_val_loss += loss.item()\n    \n    avg_val_loss = total_val_loss / len(val_loader)\n\n    return avg_train_loss, avg_val_loss", "metadata": {"execution": {"iopub.status.busy": "2024-09-16T14:11:32.171660Z", "iopub.execute_input": "2024-09-16T14:11:32.172884Z", "iopub.status.idle": "2024-09-16T14:11:32.182659Z", "shell.execute_reply.started": "2024-09-16T14:11:32.172805Z", "shell.execute_reply": "2024-09-16T14:11:32.181282Z"}, "trusted": true}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": "# Hyperparameters\nresolution = (224,224)\ntarget_fps = 24\nsequence_length = 16\ntransform = transforms.Compose([\n            transforms.Resize(resolution),\n            transforms.ToTensor(),\n            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),\n#             transforms.Normalize(mean=[0.387, 0.39255, 0.332], std=[0.24356,0.23,0.22]),\n\n        ])\n\n\n\nbatch_size = 2\nlearning_rate = 1e-5\nnum_epochs = 40\nembedding_dim = 2048\npatience = 5  # Number of epochs to wait for improvement before early stopping\ntrain_dataset_dir = '/kaggle/input/few-shot-video-classification/Few Shot video classification/Train'\ntest_dataset_dir = '/kaggle/input/few-shot-video-classification/Few Shot video classification/Test'\ncheckpoint_path = \"/kaggle/working/best_model_checkpoint.pth\"\n", "metadata": {"execution": {"iopub.status.busy": "2024-09-16T14:17:12.399340Z", "iopub.execute_input": "2024-09-16T14:17:12.399721Z", "iopub.status.idle": "2024-09-16T14:17:12.407591Z", "shell.execute_reply.started": "2024-09-16T14:17:12.399682Z", "shell.execute_reply": "2024-09-16T14:17:12.406238Z"}, "trusted": true}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": "dataset = VideoDataset(train_dataset_dir)\n\n# Split the dataset into train and validation sets using stratified split\ntrain_dataset, val_dataset = stratified_split(dataset, test_size=0.2)\n\ntrain_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, pin_memory=True)\nval_loader = DataLoader(val_dataset, batch_size=batch_size, pin_memory=True)", "metadata": {"execution": {"iopub.status.busy": "2024-09-16T14:11:44.426957Z", "iopub.execute_input": "2024-09-16T14:11:44.427357Z", "iopub.status.idle": "2024-09-16T14:11:44.462668Z", "shell.execute_reply.started": "2024-09-16T14:11:44.427322Z", "shell.execute_reply": "2024-09-16T14:11:44.461525Z"}, "trusted": true}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": "!rm -rf ./*", "metadata": {"execution": {"iopub.status.busy": "2024-09-16T14:11:48.926973Z", "iopub.execute_input": "2024-09-16T14:11:48.927473Z", "iopub.status.idle": "2024-09-16T14:11:50.129258Z", "shell.execute_reply.started": "2024-09-16T14:11:48.927425Z", "shell.execute_reply": "2024-09-16T14:11:50.127954Z"}, "trusted": true}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": "# Model, loss, and optimizer initialization\nmodel = SiameseNetworkMViT(embedding_dim=embedding_dim)\nif torch.cuda.device_count() > 1:\n    print(f\"Using {torch.cuda.device_count()} GPUs!\")\n    model = nn.DataParallel(model)\n\ndevice = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\nmodel = model.to(device)\ncriterion = TripletLoss()\noptimizer = optim.AdamW(model.parameters(), lr=learning_rate)\none , two  , three  ,_= dataset.__getitem__(0)\nprint(f'data shape {one.shape}')\n\n\n\n# Variables for tracking training progress\nstart_epoch = 0\ntrain_losses = []\nval_losses = []\nbest_val_loss = float('inf')\nbest_train_loss = float('inf')\nbest_model = None\nepochs_no_improve = 0\n\n\n\n# Load checkpoint if available\nif os.path.exists(checkpoint_path):\n    print(\"Loading checkpoint...\")\n    checkpoint = torch.load(checkpoint_path, weights_only=False)\n    model.load_state_dict(checkpoint['model_state_dict'])\n    optimizer.load_state_dict(checkpoint['optimizer_state_dict'])\n    start_epoch = checkpoint['epoch']\n    best_val_loss = checkpoint['val_loss']\n    best_train_loss = checkpoint['train_loss']\n    train_losses = checkpoint.get('train_losses', [])\n    val_losses = checkpoint.get('val_losses', [])\n    print(f\"Resuming training from epoch {start_epoch}\")\nelse:\n    print(\"No checkpoint found. Starting training from scratch.\")\n\n    \n    \n    \n# Training loop\nfor epoch in range(start_epoch, num_epochs):\n    avg_train_loss, avg_val_loss = train_and_validate(model, train_loader, val_loader, criterion, optimizer, device)\n    \n    # Record losses\n    train_losses.append(avg_train_loss)\n    val_losses.append(avg_val_loss)\n\n    \n    print(f\"Epoch {epoch+1}/{num_epochs}\")\n    print(f\"    Train Loss: {avg_train_loss:.4f}\")\n    print(f\"    Validation Loss: {avg_val_loss:.4f}\")\n    \n    # Check if this is the best model so far\n    if avg_val_loss < best_val_loss :\n        best_val_loss = avg_val_loss\n        best_train_loss = avg_train_loss\n        best_model = model.state_dict()\n        epochs_no_improve = 0\n        \n        # Save the checkpoint\n        checkpoint = {\n            'epoch': epoch + 1,'model_state_dict': best_model,\n            'optimizer_state_dict': optimizer.state_dict(),\n            'train_loss': avg_train_loss,'val_loss': avg_val_loss,\n            'train_losses': train_losses,'val_losses': val_losses,\n        }\n        torch.save(checkpoint, checkpoint_path)\n        print(f\"Checkpoint saved at epoch {epoch+1}\")\n    elif avg_val_loss == 0 and avg_train_loss < best_train_loss:\n        best_train_loss = avg_val_loss\n        best_model = model.state_dict()\n        epochs_no_improve = 0\n        \n        checkpoint = {\n            'epoch': epoch + 1,'model_state_dict': best_model,\n            'optimizer_state_dict': optimizer.state_dict(),\n            'train_loss': avg_train_loss,'val_loss': avg_val_loss,\n            'train_losses': train_losses,'val_losses': val_losses,\n        }\n        torch.save(checkpoint, checkpoint_path)\n        print(f\"Checkpoint saved at epoch {epoch+1}\")\n        \n    else:\n        epochs_no_improve += 1\n    \n    # Early stopping\n    if epochs_no_improve == patience:\n        print(f\"Early stopping triggered at epoch {epoch+1}\")\n        break\n\n# Load the best model\nmodel.load_state_dict(best_model)\n\n# Save the final best model\ntorch.save(model.state_dict(), \"/kaggle/working/best_video_siamese_model.pth\")\nprint(\"Training completed!\")", "metadata": {"execution": {"iopub.status.busy": "2024-09-16T14:17:20.193929Z", "iopub.execute_input": "2024-09-16T14:17:20.194365Z"}, "trusted": true}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": "## function for if you are loading model from an older check point", "metadata": {}}, {"cell_type": "code", "source": "# import torch\n\n# # Load the checkpoint\n# checkpoint = torch.load(checkpoint_path)\n\n# # Access the values\n# epoch = checkpoint['epoch']\n# model_state_dict = checkpoint['model_state_dict']\n\n# optimizer_state_dict = checkpoint['optimizer_state_dict']\n# train_loss = checkpoint['train_loss']\n# val_loss = checkpoint['val_loss']\n# train_losses = checkpoint['train_losses']\n# val_losses = checkpoint['val_losses']\n\n# # Print the loaded values (optional)\n# print(f\"Checkpoint loaded from epoch {epoch}\")\n# print(f\"Train Loss: {train_loss}\")\n# print(f\"Validation Loss: {val_loss}\")\n# print(f\"Train Losses: {train_losses}\")\n# print(f\"Validation Losses: {val_losses}\")", "metadata": {"execution": {"iopub.status.busy": "2024-09-12T18:34:50.238901Z", "iopub.execute_input": "2024-09-12T18:34:50.239268Z", "iopub.status.idle": "2024-09-12T18:34:50.244295Z", "shell.execute_reply.started": "2024-09-12T18:34:50.239229Z", "shell.execute_reply": "2024-09-12T18:34:50.243310Z"}, "trusted": true}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": "# Plotting the losses\nplt.figure(figsize=(10, 5))\nplt.plot( train_losses, label='Train Loss')\nplt.plot( val_losses, label='Validation Loss')\nplt.xlabel('Epochs')\nplt.ylabel('Loss')\nplt.title('Training and Validation Losses')\nplt.legend()\nplt.grid(True)\nplt.savefig('/kaggle/working/loss_plot.png')\nplt.show()\n\nprint(\"Loss plot saved as 'loss_plot.png'\")", "metadata": {"trusted": true}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": "# model = SiameseNetwork(embedding_dim=embedding_dim)\n# if torch.cuda.device_count() > 1:\n#     print(f\"Using {torch.cuda.device_count()} GPUs!\")\n#     model = nn.DataParallel(model)\n\n# device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n# model = model.to(device)\n\n# # checkpoint_path = \"/kaggle/working/best_model_checkpoint.pth\"\n\n# # checkpoint = torch.load(checkpoint_path, weights_only=False)\n# # model.load_state_dict(checkpoint['model_state_dict']),\"model loaded sucessfully\"\n# model.load_state_dict(model_state_dict),\"model loaded sucessfully\"", "metadata": {"execution": {"iopub.status.busy": "2024-09-12T18:34:50.800520Z", "iopub.execute_input": "2024-09-12T18:34:50.800826Z", "iopub.status.idle": "2024-09-12T18:34:50.805053Z", "shell.execute_reply.started": "2024-09-12T18:34:50.800792Z", "shell.execute_reply": "2024-09-12T18:34:50.804116Z"}, "trusted": true}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": "# model = torch.load(\"/kaggle/working/best_video_siamese_model.pth\",weights_only=False)", "metadata": {"execution": {"iopub.status.busy": "2024-09-12T18:34:50.806360Z", "iopub.execute_input": "2024-09-12T18:34:50.806658Z", "iopub.status.idle": "2024-09-12T18:34:50.816959Z", "shell.execute_reply.started": "2024-09-12T18:34:50.806626Z", "shell.execute_reply": "2024-09-12T18:34:50.815904Z"}, "trusted": true}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": "import pandas as pd\nsample_submission = pd.read_csv(\"/kaggle/input/few-shot-video-classification/Few Shot video classification/sample_submission.csv\")\n\n    \nsample_submission.head(sample_submission.shape[0])", "metadata": {"trusted": true}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": "def custom_min(iterable):\n    if not iterable:\n        raise ValueError(\"custom_min() arg is an empty sequence\")\n        \n    key = lambda x: x[1]  # The key function is always fixed to extract the second element\n    \n    min_item = iterable[0]\n    min_value = key(min_item)\n    for item in iterable[1:]:\n        value = key(item)\n        if value < min_value:\n            min_item = item\n            min_value = value\n#     print(\"The returned value:\", min_item)\n    return min_item", "metadata": {"trusted": true}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": "# collecting the support embeddings\nsupport_df = pd.DataFrame(columns=['label','embedding'])\nfor i in range(4):\n    url = f\"/kaggle/input/few-shot-video-classification/Few Shot video classification/Test/Support-set/{i}/clip1.mov\"\n    support_df.loc[i,'label'] = i\n    video_tensor =  get_video(url)\n    \n    model.eval()\n    with torch.no_grad():\n        support_embedding = model(video_tensor.unsqueeze(0).to(device))\n    \n    support_df.loc[i,'embedding'] = support_embedding\n    \n\n\n# compating them to query folders\nquery_embeddings = []\nfor i in range(len(sample_submission)):\n    vid_id = sample_submission.loc[i,'VID_ID']\n    url = f\"/kaggle/input/few-shot-video-classification/Few Shot video classification/Test/Query/clip_{vid_id}.mp4\"\n    video_tensor = get_video(url,30)\n    \n    model.eval()\n    with torch.no_grad():\n        query_embedding = model(video_tensor.unsqueeze(0).to(device))\n        query_embeddings.append(query_embedding)\n    \n    print()\n    print(f\"{i}. for clip {vid_id}.mp4, the distance metrics with the support embeddinds are:\")\n    \n    support_distances = []\n    for j in range(4):\n#         print(\"        label\",support_df.loc[i,'label'])\n        support_embedding = support_df.loc[j,\"embedding\"]\n        \n        \n        \n#         find_diffrence_in_vector_space(query_embedding, support_embedding)\n        distance = (support_embedding - query_embedding).pow(2).sum(1)\n        distance2 = F.cosine_similarity(support_embedding, query_embedding, dim=1)\n        \n        print(f\"    for label {j} this is the distance: {distance.item()} and dis2 {distance2.item()}\")\n        support_distances.append((j,distance))\n        \n\n    # Use custom_min instead of min\n    min_distance_tuple = custom_min(support_distances)\n#     print(f\"the support : {support_distances} min distance tuple {min_distance_tuple}\")\n    \n#     print(\"is this tuple?\",min_distance_tuple)\n    \n    sample_submission.loc[i,\"LABEL\"] =  min_distance_tuple[0]    \n    print(f\"    for item {i} the LABEL is: {min_distance_tuple[0]}\")\n    ", "metadata": {"trusted": true}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": "support_embedding.shape", "metadata": {"trusted": true}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": "sample_submission.head(sample_submission.shape[0])", "metadata": {"trusted": true}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": "sample_submission.to_csv(\"/kaggle/working/submission_MViTv2_s_strat_swish3_2048.csv\",index=False)", "metadata": {"trusted": true}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": "# sample_submission.to_csv(\"/kaggle/working/submission_MViTv2_s_16_3x224_strat_silu2.csv\",index=False)", "metadata": {"execution": {"iopub.status.busy": "2024-09-12T13:58:34.933980Z", "iopub.execute_input": "2024-09-12T13:58:34.934414Z", "iopub.status.idle": "2024-09-12T13:58:34.948519Z", "shell.execute_reply.started": "2024-09-12T13:58:34.934357Z", "shell.execute_reply": "2024-09-12T13:58:34.947674Z"}, "trusted": true}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": "!ls", "metadata": {"execution": {"iopub.status.busy": "2024-09-12T01:04:15.092941Z", "iopub.execute_input": "2024-09-12T01:04:15.093312Z", "iopub.status.idle": "2024-09-12T01:04:16.150003Z", "shell.execute_reply.started": "2024-09-12T01:04:15.093272Z", "shell.execute_reply": "2024-09-12T01:04:16.148716Z"}, "trusted": true}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": "from IPython.display import FileLink\n\n# Assuming 'my_output_file.csv' is saved in /kaggle/working/\nfile_path = 'best_video_siamese_model.pth'\ndisplay(FileLink(file_path))\n", "metadata": {}, "execution_count": null, "outputs": []}]}