# Hotel Search AI Assistant

A Django-based hotel search application that uses OpenAI's GPT models for natural language processing and intelligent hotel recommendations.

## Overview

This application processes natural language queries about hotel searches, extracts relevant information, and returns personalized hotel recommendations with AI-generated summaries.

## Features

- Natural Language Query Processing
- Entity Extraction using OpenAI GPT-4
- Asynchronous Task Processing with Celery
- AI-Powered Hotel Summarization
- RESTful API Interface

## Technical Architecture

### Components

1. **Core Application**
   - Handles main API endpoints
   - Manages asynchronous task processing
   - Coordinates between different services

2. **Hotel Search Module**
   - Entity extraction from natural language queries
   - Hotel search functionality
   - AI-powered hotel summarization

3. **AI Processing**
   - Uses OpenAI's GPT-4 for:
     - Entity extraction from queries
     - Generating concise hotel summaries
     - The summarization process uses direct prompting rather than embeddings

## Setup

1. Create a virtual environment:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

3. Set up environment variables in `.env`:
   ```
   DJANGO_SECRET_KEY=your-secret-key-here
   OPENAI_API_KEY=your-openai-api-key-here
   ```

4. Run migrations:
   ```bash
   python manage.py migrate
   ```

5. Start the development server:
   ```bash
   python manage.py runserver
   ```

6. Start Celery worker:
   ```bash
   celery -A rizem_project worker --loglevel=info
   ```

## API Endpoints

### 1. Hotel Search
- **Endpoint**: `/api/search/`
- **Method**: POST
- **Payload**:
  ```json
  {
    "query": "Find me a luxury hotel in Paris near the Eiffel Tower for next weekend"
  }
  ```
- **Response**:
  ```json
  {
    "task_id": "task-uuid",
    "status": "Processing"
  }
  ```

### 2. Task Status
- **Endpoint**: `/api/tasks/<task_id>/`
- **Method**: GET
- **Response**:
  ```json
  {
    "status": "completed",
    "result": {
      "query": "original query",
      "extracted_entities": {
        "location": "Paris",
        "area": "Eiffel Tower",
        "check_in": "next weekend",
        "preferences": ["luxury"]
      },
      "results": [
        {
          "name": "Hotel Name",
          "summary": "AI-generated summary"
        }
      ]
    }
  }
  ```

## AI Processing Details

### Entity Extraction
- Uses GPT-4 to extract structured information from natural language queries
- Identifies locations, dates, preferences, and other relevant details
- Returns JSON-formatted entity data

### Hotel Summarization
- Generates concise, relevant summaries of hotel information
- Uses direct prompting with GPT-4
- Focuses on key features and relevant details based on user preferences

Note: The system uses direct GPT-4 prompting rather than embeddings for both entity extraction and summarization. This approach was chosen for:
- Better understanding of context and nuance
- More accurate entity extraction
- More natural and relevant summaries
- Flexibility in handling various query formats

## Development

### Project Structure
```
rizem_project/
├── apps/
│   ├── core/
│   │   ├── tasks.py      # Celery tasks
│   │   ├── urls.py       # API endpoints
│   │   └── views.py      # API views
│   └── hotelsearch/
│       ├── api.py        # Hotel search implementation
│       ├── entity_extraction.py  # OpenAI entity extraction
│       └── summarization.py      # Hotel summary generation
├── rizem_project/
│   ├── celery.py         # Celery configuration
│   ├── settings.py       # Project settings
│   └── urls.py           # Main URL configuration
└── manage.py             # Django management script
```