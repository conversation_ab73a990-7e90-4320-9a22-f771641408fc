from celery import shared_task
from apps.hotelsearch.entity_extraction import extract_entities
from apps.hotelsearch.api import search_hotels
from apps.hotelsearch.summarization import generate_hotel_summary

@shared_task
def process_search_query(query):
    # Extract entities from the query using OpenAI
    entities = extract_entities(query)
    
    # Search for hotels using the extracted entities
    hotels = search_hotels(entities)
    
    # Generate summaries for each hotel
    for hotel in hotels:
        hotel['summary'] = generate_hotel_summary(hotel)
    
    # Return the structured response
    return {
        'query': query,
        'extracted_entities': entities,
        'results': hotels
    }