from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from .tasks import process_search_query

class HotelSearchView(APIView):
    def post(self, request):
        query = request.data.get('query')
        if not query:
            return Response(
                {'error': 'Query is required'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Dispatch Celery task
        task = process_search_query.delay(query)
        
        return Response({
            'task_id': task.id,
            'status': 'Processing'
        })

class TaskStatusView(APIView):
    def get(self, request, task_id):
        task = process_search_query.AsyncResult(task_id)
        if task.ready():
            return Response({
                'status': 'completed',
                'result': task.result
            })
        return Response({
            'status': task.status
        })