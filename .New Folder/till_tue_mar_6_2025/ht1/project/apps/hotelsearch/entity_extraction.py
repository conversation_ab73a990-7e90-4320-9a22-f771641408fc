import openai
from django.conf import settings

def extract_entities(query):
    """
    Extract entities from the natural language query using OpenAI's API.
    """
    openai.api_key = settings.OPENAI_API_KEY
    
    system_prompt = """
    Extract travel-related entities from the user's query. Return a JSON object with:
    - location: The city or place
    - area: Specific area within the location
    - check_in: Check-in time/date
    - check_out: Check-out time/date
    - preferences: List of user preferences
    """
    
    response = openai.ChatCompletion.create(
        model="gpt-4",
        messages=[
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": query}
        ]
    )
    
    return response.choices[0].message.content