import openai
from django.conf import settings

def generate_hotel_summary(hotel_data):
    """
    Generate a concise summary of hotel information using OpenAI's API.
    """
    openai.api_key = settings.OPENAI_API_KEY
    
    prompt = f"""
    Summarize the following hotel information in a concise way:
    Name: {hotel_data['name']}
    Rating: {hotel_data['rating']}
    Description: {hotel_data['description']}
    """
    
    response = openai.ChatCompletion.create(
        model="gpt-4",
        messages=[
            {"role": "system", "content": "You are a helpful assistant that creates concise hotel summaries."},
            {"role": "user", "content": prompt}
        ]
    )
    
    return response.choices[0].message.content