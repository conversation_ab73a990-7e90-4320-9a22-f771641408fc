Run:

bash
Copy
Edit
resolvectl status
This will show the DNS servers currently configured for your system. Check if they are valid and reachable.

If they are incorrect or unreachable, you can temporarily change the DNS servers to something like Google's public DNS:

bash
Copy
Edit
sudo nano /etc/resolv.conf
Add or replace the content with:

nginx
Copy
Edit
nameserver 8.8.8.8
nameserver 8.8.4.4
Save and exit, then try the nslookup command again.

