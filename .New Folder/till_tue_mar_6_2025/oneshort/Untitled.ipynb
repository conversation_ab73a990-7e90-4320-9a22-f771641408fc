{"cells": [{"cell_type": "code", "execution_count": 19, "id": "1693d871-2eb8-4ed1-aee2-bd0019460d0a", "metadata": {}, "outputs": [], "source": ["import torch\n", "import torch.nn as nn\n", "import torch.nn.functional as F\n", "import torch.optim as optim\n", "from torchvision import transforms\n", "from torch.utils.data import Dataset, DataLoader\n", "import os\n", "import random\n", "import time\n", "import shutil\n", "import PIL.Image as Image"]}, {"cell_type": "code", "execution_count": 20, "id": "d1f9ba2a-cd0f-4830-8cda-fd60304fb821", "metadata": {}, "outputs": [], "source": ["class CharacterDataset(Dataset):\n", "    def __init__(self, root_dir, transform=None):\n", "        self.root_dir = root_dir\n", "        self.transform = transform\n", "        self.classes = sorted(os.listdir(root_dir))\n", "        self.class_to_idx = {cls_name: i for i, cls_name in enumerate(self.classes)}\n", "        \n", "        self.samples = []\n", "        for class_name in self.classes:\n", "            class_dir = os.path.join(root_dir, class_name)\n", "            for char_dir in os.listdir(class_dir):\n", "                char_path = os.path.join(class_dir, char_dir)\n", "                if os.path.isdir(char_path):\n", "                    for img_name in os.listdir(char_path):\n", "                        if img_name.lower().endswith(('.png', '.jpg', '.jpeg')):\n", "                            img_path = os.path.join(char_path, img_name)\n", "                            self.samples.append((img_path, self.class_to_idx[class_name]))\n", "    \n", "    def __len__(self):\n", "        return len(self.samples)\n", "    \n", "    def __getitem__(self, idx):\n", "        img_path, label = self.samples[idx]\n", "        \n", "        # Load image\n", "        try:\n", "            img = Image.open(img_path).convert('L')  # Convert to grayscale\n", "            if self.transform:\n", "                img = self.transform(img)\n", "            return img, label\n", "        except Exception as e:\n", "            print(f\"Error loading image {img_path}: {e}\")\n", "            # Return a placeholder in case of error\n", "            placeholder = torch.zeros((1, 105, 105))\n", "            return placeholder, label"]}, {"cell_type": "code", "execution_count": 21, "id": "69f9e455-0825-4872-abb7-00ef38ddd2b6", "metadata": {}, "outputs": [], "source": ["class SiameseDataset(Dataset):\n", "    def __init__(self, dataset, num_pairs=30000):\n", "        \"\"\"\n", "        Create pairs from a dataset of individual images\n", "        \"\"\"\n", "        self.dataset = dataset\n", "        self.num_pairs = num_pairs\n", "        self.pairs = []\n", "        self.labels = []\n", "\n", "        # Build a mapping: label -> list of indices\n", "        class_map = {}\n", "        for idx, (_, label) in enumerate(dataset):\n", "            if label not in class_map:\n", "                class_map[label] = []\n", "            class_map[label].append(idx)\n", "\n", "        labels_list = list(class_map.keys())\n", "        \n", "        # Generate pairs: half positive (same class), half negative (different class)\n", "        n_same = num_pairs // 2\n", "        n_diff = num_pairs - n_same\n", "        \n", "        for _ in range(n_same):\n", "            label = random.choice(labels_list)\n", "            # Ensure at least two samples exist for the chosen class\n", "            if len(class_map[label]) < 2:\n", "                continue\n", "            idx1, idx2 = random.sample(class_map[label], 2)\n", "            self.pairs.append((idx1, idx2))\n", "            self.labels.append(1)\n", "            \n", "        for _ in range(n_diff):\n", "            label1, label2 = random.sample(labels_list, 2)\n", "            idx1 = random.choice(class_map[label1])\n", "            idx2 = random.choice(class_map[label2])\n", "            self.pairs.append((idx1, idx2))\n", "            self.labels.append(0)\n", "\n", "    def __len__(self):\n", "        return len(self.pairs)\n", "\n", "    def __getitem__(self, index):\n", "        idx1, idx2 = self.pairs[index]\n", "        img1, _ = self.dataset[idx1]\n", "        img2, _ = self.dataset[idx2]\n", "        label = self.labels[index]\n", "        \n", "        return img1, img2, torch.tensor(label, dtype=torch.float32)"]}, {"cell_type": "code", "execution_count": 22, "id": "8e1154b7-72ce-48c8-b048-998ad519a41e", "metadata": {}, "outputs": [], "source": ["train_dir = 'train'\n", "val_dir = 'val'\n", "test_dir = 'test'"]}, {"cell_type": "code", "execution_count": 23, "id": "75a21c39-52fa-43b0-8a26-3013cef54c91", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Number of training samples: 9225\n", "Number of validation samples: 1978\n", "Number of test samples: 1977\n", "Number of classes: 20\n", "Classes: ['<PERSON><PERSON>', '<PERSON><PERSON><PERSON>_<PERSON>sayer', 'Atlantean', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>_<PERSON><PERSON>', 'Glagolitic', 'Gurmukhi', 'Kannada', 'Keble', 'Malayalam', 'Manipuri', 'Mongolian', 'Old_Church_Slavonic_(Cyrillic)', 'Oriya', '<PERSON>yl<PERSON><PERSON>', 'Syriac_(Serto)', 'Tengwar', 'Tibetan', 'ULOG']\n"]}], "source": ["# -------------------- Data Transformations --------------------\n", "train_transform = transforms.Compose([\n", "    transforms.<PERSON><PERSON><PERSON>((105, 105)),\n", "    transforms.RandomHorizontalFlip(p=0.5),\n", "    transforms.ColorJitter(brightness=0.2, contrast=0.2),\n", "    transforms.RandomAffine(degrees=10, translate=(0.02, 0.02), scale=(0.8, 1.2), shear=17),\n", "    transforms.To<PERSON><PERSON><PERSON>(),\n", "    transforms.Normalize(mean=[0.5], std=[0.5])\n", "])\n", "\n", "val_transform = transforms.Compose([\n", "    transforms.<PERSON><PERSON><PERSON>((105, 105)),\n", "    transforms.To<PERSON><PERSON><PERSON>(),\n", "    transforms.Normalize(mean=[0.5], std=[0.5])\n", "])\n", "\n", "train_dataset_base = CharacterDataset(train_dir, transform=train_transform)\n", "val_dataset_base = CharacterDataset(val_dir, transform=val_transform)\n", "test_dataset_base = CharacterDataset(test_dir, transform=val_transform)\n", "\n", "print(f\"Number of training samples: {len(train_dataset_base)}\")\n", "print(f\"Number of validation samples: {len(val_dataset_base)}\")\n", "print(f\"Number of test samples: {len(test_dataset_base)}\")\n", "print(f\"Number of classes: {len(train_dataset_base.classes)}\")\n", "print(f\"Classes: {train_dataset_base.classes}\")"]}, {"cell_type": "code", "execution_count": 24, "id": "233b9569-1a18-48dc-bfca-161462aed24c", "metadata": {}, "outputs": [], "source": ["train_siamese = SiameseDataset(train_dataset_base, num_pairs=20000)\n", "val_siamese = SiameseDataset(val_dataset_base, num_pairs=3000)\n", "test_siamese = SiameseDataset(test_dataset_base, num_pairs=5000)"]}, {"cell_type": "code", "execution_count": 25, "id": "2b581e9e-7a06-42f6-9e96-a23e97e76098", "metadata": {}, "outputs": [], "source": ["# -------------------- Network Architecture --------------------\n", "class CNNBackbone(nn.Module):\n", "    def __init__(self):\n", "        super(<PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "        self.conv1 = nn.Conv2d(1, 64, kernel_size=10, stride=1)\n", "        self.conv2 = nn.Conv2d(64, 128, kernel_size=7, stride=1)\n", "        self.conv3 = nn.Conv2d(128, 128, kernel_size=4, stride=1)\n", "        self.conv4 = nn.Conv2d(128, 256, kernel_size=4, stride=1)\n", "        self.fc1 = nn.<PERSON>ar(256 * 6 * 6, 4096)\n", "\n", "    def forward(self, x):\n", "        x = <PERSON>.relu(self.conv1(x))\n", "        x = F.max_pool2d(x, 2)\n", "        x = <PERSON>.relu(self.conv2(x))\n", "        x = F.max_pool2d(x, 2)\n", "        x = <PERSON>.relu(self.conv3(x))\n", "        x = F.max_pool2d(x, 2)\n", "        x = <PERSON>.relu(self.conv4(x))\n", "        x = torch.flatten(x, start_dim=1)  # Flatten dynamically\n", "        x = F.relu(self.fc1(x))\n", "        return x\n", "\n", "class SiameseNet(nn.Module):\n", "    def __init__(self):\n", "        super(SiameseNet, self).__init__()\n", "        self.backbone = CNNBackbone()\n", "        # Learnable alpha parameters for L1 distance\n", "        self.alpha = nn.Parameter(torch.ones(4096))  # Paper Eq. in Section 3.1\n", "        # Modified fully connected block with dropout for additional regularization\n", "        self.fc = nn.Sequential(\n", "            nn.<PERSON><PERSON>(4096, 1024),\n", "            nn.ReLU(),\n", "            nn.Dropout(p=0.5),\n", "            nn.Linear(1024, 1, bias=False)\n", "        )\n", "\n", "    def forward(self, x1, x2):\n", "        h1 = self.backbone(x1)\n", "        h2 = self.backbone(x2)\n", "        distance = self.alpha * torch.abs(h1 - h2)  # Weighted L1\n", "        logits = self.fc(distance)\n", "        return logits"]}, {"cell_type": "code", "execution_count": 26, "id": "caf0753d-e1d9-427c-a8be-33b921ea13cb", "metadata": {}, "outputs": [], "source": ["train_loader = DataLoader(train_siamese, batch_size=16, shuffle=True, num_workers=4)\n", "val_loader = DataLoader(val_siamese, batch_size=16, shuffle=False, num_workers=4)\n", "test_loader = DataLoader(test_siamese, batch_size=16, shuffle=False, num_workers=4)"]}, {"cell_type": "code", "execution_count": 27, "id": "39d1aa6d-d956-46f8-b413-fee8861f1e9d", "metadata": {}, "outputs": [{"data": {"text/plain": ["device(type='cuda')"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["# Initialize model and training components\n", "device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "device"]}, {"cell_type": "code", "execution_count": 28, "id": "54f16db1-2306-48e4-a682-3e59379b7b9e", "metadata": {}, "outputs": [], "source": ["torch.manual_seed(42)\n", "random.seed(42)"]}, {"cell_type": "code", "execution_count": 29, "id": "c606476a-584d-4e4f-bc17-fb33d82ddac3", "metadata": {}, "outputs": [{"data": {"text/plain": ["SiameseNet(\n", "  (backbone): CNNBackbone(\n", "    (conv1): Conv2d(1, 64, kernel_size=(10, 10), stride=(1, 1))\n", "    (conv2): Conv2d(64, 128, kernel_size=(7, 7), stride=(1, 1))\n", "    (conv3): Conv2d(128, 128, kernel_size=(4, 4), stride=(1, 1))\n", "    (conv4): Conv2d(128, 256, kernel_size=(4, 4), stride=(1, 1))\n", "    (fc1): Linear(in_features=9216, out_features=4096, bias=True)\n", "  )\n", "  (fc): Sequential(\n", "    (0): Linear(in_features=4096, out_features=1024, bias=True)\n", "    (1): ReLU()\n", "    (2): Dropout(p=0.5, inplace=False)\n", "    (3): Linear(in_features=1024, out_features=1, bias=False)\n", "  )\n", ")"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["model = SiameseNet().to(device)\n", "model"]}, {"cell_type": "code", "execution_count": 30, "id": "1c2f1b6e-581b-4d2a-8c42-4d0e944e3a9e", "metadata": {}, "outputs": [], "source": ["optimizer = optim.SGD(model.parameters(), lr=0.01, momentum=0.9, weight_decay=0.0005)\n", "criterion = nn.BCEWithLogitsLoss()#nn.BCELoss()  # Use BCELoss for probability output"]}, {"cell_type": "code", "execution_count": 31, "id": "13abc9e4-c5f6-45b2-a91a-76904184fa0f", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Epoch 1: Train Loss: 0.6794, Train Acc: 56.09% | Val Loss: 0.6413, Val Acc: 62.53% | Time: 40.34s\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/openvoice/lib/python3.9/site-packages/torch/optim/lr_scheduler.py:240: UserWarning: The epoch parameter in `scheduler.step()` was not necessary and is being deprecated where possible. Please use `scheduler.step()` to step the scheduler. During the deprecation, if epoch is different from None, the closed form is used instead of the new chainable form, where available. Please open an issue if you are unable to replicate your use case: https://github.com/pytorch/pytorch/issues/new/choose.\n", "  warnings.warn(EPOCH_DEPRECATION_WARNING, UserWarning)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 2: Train Loss: 0.6457, Train Acc: 62.42% | Val Loss: 0.5977, Val Acc: 67.03% | Time: 40.24s\n", "Epoch 3: Train Loss: 0.6154, Train Acc: 65.60% | Val Loss: 0.5873, Val Acc: 68.03% | Time: 40.25s\n", "Epoch 4: Train Loss: 0.5840, Train Acc: 68.33% | Val Loss: 0.5818, Val Acc: 69.50% | Time: 40.25s\n", "Epoch 5: Train Loss: 0.5602, Train Acc: 70.34% | Val Loss: 0.5351, Val Acc: 72.20% | Time: 40.27s\n", "Epoch 6: Train Loss: 0.5384, Train Acc: 71.88% | Val Loss: 0.5143, Val Acc: 72.93% | Time: 40.31s\n", "Epoch 7: Train Loss: 0.5127, Train Acc: 73.87% | Val Loss: 0.4991, Val Acc: 74.83% | Time: 40.30s\n", "Epoch 8: Train Loss: 0.4919, Train Acc: 75.49% | Val Loss: 0.4972, Val Acc: 74.77% | Time: 40.29s\n", "Epoch 9: Train Loss: 0.4720, Train Acc: 76.35% | Val Loss: 0.4773, Val Acc: 76.23% | Time: 40.26s\n", "Epoch 10: Train Loss: 0.4552, Train Acc: 77.34% | Val Loss: 0.4779, Val Acc: 75.63% | Time: 40.27s\n", "Epoch 11: Train Loss: 0.4357, Train Acc: 78.69% | Val Loss: 0.4512, Val Acc: 77.90% | Time: 40.29s\n", "Epoch 12: Train Loss: 0.4205, Train Acc: 80.05% | Val Loss: 0.4379, Val Acc: 77.40% | Time: 40.24s\n", "Epoch 13: Train Loss: 0.4106, Train Acc: 80.03% | Val Loss: 0.4466, Val Acc: 77.13% | Time: 40.29s\n", "Epoch 14: Train Loss: 0.3927, Train Acc: 81.29% | Val Loss: 0.4393, Val Acc: 78.50% | Time: 40.28s\n", "Epoch 15: Train Loss: 0.3814, Train Acc: 82.08% | Val Loss: 0.4657, Val Acc: 78.00% | Time: 40.26s\n", "Epoch 16: Train Loss: 0.3739, Train Acc: 82.30% | Val Loss: 0.4447, Val Acc: 78.63% | Time: 40.26s\n", "Epoch 17: Train Loss: 0.3592, Train Acc: 83.25% | Val Loss: 0.4315, Val Acc: 79.57% | Time: 40.24s\n", "Epoch 18: Train Loss: 0.3509, Train Acc: 83.66% | Val Loss: 0.3945, Val Acc: 81.57% | Time: 40.24s\n", "Epoch 19: Train Loss: 0.3426, Train Acc: 84.50% | Val Loss: 0.4069, Val Acc: 81.57% | Time: 40.27s\n", "Epoch 20: Train Loss: 0.3317, Train Acc: 85.08% | Val Loss: 0.3788, Val Acc: 82.17% | Time: 40.27s\n", "Epoch 21: Train Loss: 0.3288, Train Acc: 85.01% | Val Loss: 0.3699, Val Acc: 83.30% | Time: 40.29s\n", "Epoch 22: Train Loss: 0.3179, Train Acc: 85.62% | Val Loss: 0.3897, Val Acc: 82.10% | Time: 40.25s\n", "Epoch 23: Train Loss: 0.3025, Train Acc: 86.37% | Val Loss: 0.3699, Val Acc: 82.87% | Time: 40.30s\n", "Epoch 24: Train Loss: 0.2992, Train Acc: 86.73% | Val Loss: 0.4401, Val Acc: 80.27% | Time: 40.27s\n", "Epoch 25: Train Loss: 0.2928, Train Acc: 86.80% | Val Loss: 0.4040, Val Acc: 81.30% | Time: 40.26s\n", "Epoch 26: Train Loss: 0.2819, Train Acc: 87.77% | Val Loss: 0.4164, Val Acc: 82.40% | Time: 40.27s\n", "Epoch 27: Train Loss: 0.2738, Train Acc: 87.79% | Val Loss: 0.3860, Val Acc: 82.43% | Time: 40.25s\n", "Epoch 28: Train Loss: 0.2732, Train Acc: 88.24% | Val Loss: 0.4282, Val Acc: 80.10% | Time: 40.29s\n", "Epoch 29: Train Loss: 0.2681, Train Acc: 88.65% | Val Loss: 0.3711, Val Acc: 83.27% | Time: 40.24s\n", "Epoch 30: Train Loss: 0.2591, Train Acc: 88.89% | Val Loss: 0.4046, Val Acc: 81.90% | Time: 40.25s\n", "Epoch 31: Train Loss: 0.2500, Train Acc: 89.46% | Val Loss: 0.4144, Val Acc: 82.77% | Time: 40.32s\n", "Epoch 32: Train Loss: 0.2493, Train Acc: 89.25% | Val Loss: 0.4176, Val Acc: 81.17% | Time: 40.26s\n", "Epoch 33: Train Loss: 0.2440, Train Acc: 89.56% | Val Loss: 0.3911, Val Acc: 83.83% | Time: 40.29s\n", "Epoch 34: Train Loss: 0.2386, Train Acc: 89.83% | Val Loss: 0.3379, Val Acc: 84.90% | Time: 40.28s\n", "Epoch 35: Train Loss: 0.2313, Train Acc: 90.25% | Val Loss: 0.3612, Val Acc: 84.47% | Time: 40.30s\n", "Epoch 36: Train Loss: 0.2278, Train Acc: 90.28% | Val Loss: 0.3845, Val Acc: 83.50% | Time: 40.27s\n", "Epoch 37: Train Loss: 0.2241, Train Acc: 90.66% | Val Loss: 0.3702, Val Acc: 84.13% | Time: 40.24s\n", "Epoch 38: Train Loss: 0.2198, Train Acc: 90.79% | Val Loss: 0.3739, Val Acc: 84.07% | Time: 40.26s\n", "Epoch 39: Train Loss: 0.2155, Train Acc: 90.96% | Val Loss: 0.3571, Val Acc: 84.70% | Time: 40.28s\n", "Epoch 40: Train Loss: 0.2162, Train Acc: 90.73% | Val Loss: 0.3929, Val Acc: 83.17% | Time: 40.27s\n", "Epoch 41: Train Loss: 0.2170, Train Acc: 90.93% | Val Loss: 0.3593, Val Acc: 85.03% | Time: 40.26s\n", "Epoch 42: Train Loss: 0.2051, Train Acc: 91.52% | Val Loss: 0.4253, Val Acc: 82.30% | Time: 40.28s\n", "Epoch 43: Train Loss: 0.2034, Train Acc: 91.80% | Val Loss: 0.3945, Val Acc: 83.77% | Time: 40.31s\n", "Epoch 44: Train Loss: 0.2014, Train Acc: 91.56% | Val Loss: 0.3791, Val Acc: 84.93% | Time: 40.32s\n", "Epoch 45: Train Loss: 0.2032, Train Acc: 91.40% | Val Loss: 0.4265, Val Acc: 82.57% | Time: 40.27s\n", "Epoch 46: Train Loss: 0.1914, Train Acc: 92.33% | Val Loss: 0.4173, Val Acc: 83.80% | Time: 40.25s\n", "Epoch 47: Train Loss: 0.1929, Train Acc: 92.03% | Val Loss: 0.3765, Val Acc: 85.40% | Time: 40.28s\n", "Epoch 48: Train Loss: 0.1939, Train Acc: 92.01% | Val Loss: 0.3926, Val Acc: 83.17% | Time: 40.30s\n", "Epoch 49: Train Loss: 0.1920, Train Acc: 91.98% | Val Loss: 0.3772, Val Acc: 83.97% | Time: 40.28s\n", "Epoch 50: Train Loss: 0.1859, Train Acc: 92.36% | Val Loss: 0.3906, Val Acc: 83.63% | Time: 40.27s\n", "Epoch 51: Train Loss: 0.1893, Train Acc: 92.42% | Val Loss: 0.3384, Val Acc: 85.07% | Time: 40.23s\n", "Epoch 52: Train Loss: 0.1812, Train Acc: 92.72% | Val Loss: 0.3717, Val Acc: 85.40% | Time: 40.28s\n", "Epoch 53: Train Loss: 0.1763, Train Acc: 92.95% | Val Loss: 0.3433, Val Acc: 85.90% | Time: 40.34s\n", "Epoch 54: Train Loss: 0.1791, Train Acc: 92.82% | Val Loss: 0.4168, Val Acc: 84.23% | Time: 40.28s\n", "Epoch 55: Train Loss: 0.1793, Train Acc: 92.71% | Val Loss: 0.4062, Val Acc: 83.13% | Time: 40.29s\n", "Epoch 56: Train Loss: 0.1781, Train Acc: 92.71% | Val Loss: 0.3508, Val Acc: 85.80% | Time: 40.24s\n", "Epoch 57: Train Loss: 0.1712, Train Acc: 93.01% | Val Loss: 0.4307, Val Acc: 83.93% | Time: 40.33s\n", "Epoch 58: Train Loss: 0.1716, Train Acc: 93.26% | Val Loss: 0.3906, Val Acc: 83.77% | Time: 40.23s\n", "Epoch 59: Train Loss: 0.1762, Train Acc: 92.86% | Val Loss: 0.3842, Val Acc: 85.13% | Time: 40.30s\n", "Epoch 60: Train Loss: 0.1735, Train Acc: 92.98% | Val Loss: 0.5611, Val Acc: 80.70% | Time: 40.26s\n", "Epoch 61: Train Loss: 0.1707, Train Acc: 93.20% | Val Loss: 0.4272, Val Acc: 84.63% | Time: 40.32s\n", "Epoch 62: Train Loss: 0.1661, Train Acc: 93.27% | Val Loss: 0.4023, Val Acc: 83.20% | Time: 40.31s\n", "Epoch 63: Train Loss: 0.1649, Train Acc: 93.38% | Val Loss: 0.3837, Val Acc: 84.10% | Time: 40.26s\n", "Epoch 64: Train Loss: 0.1634, Train Acc: 93.47% | Val Loss: 0.4133, Val Acc: 84.67% | Time: 40.32s\n", "Epoch 65: Train Loss: 0.1679, Train Acc: 93.36% | Val Loss: 0.3638, Val Acc: 85.63% | Time: 40.34s\n", "Epoch 66: Train Loss: 0.1604, Train Acc: 93.43% | Val Loss: 0.4716, Val Acc: 82.93% | Time: 40.24s\n", "Epoch 67: Train Loss: 0.1649, Train Acc: 93.55% | Val Loss: 0.3785, Val Acc: 85.80% | Time: 40.30s\n", "Epoch 68: Train Loss: 0.1559, Train Acc: 93.76% | Val Loss: 0.4231, Val Acc: 84.63% | Time: 40.26s\n", "Epoch 69: Train Loss: 0.1587, Train Acc: 93.56% | Val Loss: 0.3748, Val Acc: 85.97% | Time: 40.30s\n", "Epoch 70: Train Loss: 0.1570, Train Acc: 93.80% | Val Loss: 0.4277, Val Acc: 84.77% | Time: 40.29s\n", "Final model saved to final_siamese_model.pth\n", "Best model (validation loss: 0.3379) saved to best_siamese_model.pth\n"]}], "source": ["# Define number of epochs\n", "num_epochs = 70\n", "# Initialize a scheduler that reduces LR when validation loss plateaus.\n", "scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', factor=0.1, patience=10, verbose=True)\n", "#scheduler = torch.optim.lr_scheduler.LambdaLR(optimizer, lr_lambda=lambda epoch: 0.99**epoch)\n", "\n", "# Track best validation loss\n", "best_val_loss = float('inf')\n", "\n", "for epoch in range(num_epochs):\n", "    epoch_start_time = time.time()\n", "    \n", "    # Training phase\n", "    model.train()\n", "    train_loss = 0.0\n", "    train_correct = 0\n", "    train_total = 0\n", "    for batch_idx, (img1, img2, labels) in enumerate(train_loader):\n", "        img1, img2, labels = img1.to(device), img2.to(device), labels.to(device)\n", "        optimizer.zero_grad()\n", "        outputs = model(img1, img2)\n", "        loss = criterion(outputs.squeeze(), labels)\n", "        loss.backward()\n", "        optimizer.step()\n", "        \n", "        batch_size = img1.size(0)\n", "        train_loss += loss.item() * batch_size\n", "        predicted = (torch.sigmoid(outputs.squeeze()) > 0.5).float()\n", "        train_total += labels.size(0)\n", "        train_correct += (predicted == labels).sum().item()\n", "    \n", "    avg_train_loss = train_loss / train_total\n", "    train_acc = train_correct / train_total\n", "    \n", "    # Validation phase\n", "    model.eval()\n", "    val_loss = 0.0\n", "    val_correct = 0\n", "    val_total = 0\n", "    with torch.no_grad():\n", "        for img1, img2, labels in val_loader:\n", "            img1, img2, labels = img1.to(device), img2.to(device), labels.to(device)\n", "            outputs = model(img1, img2)\n", "            loss = criterion(outputs.squeeze(), labels)\n", "            batch_size = img1.size(0)\n", "            val_loss += loss.item() * batch_size\n", "            predicted = (torch.sigmoid(outputs.squeeze()) > 0.5).float()\n", "            val_total += labels.size(0)\n", "            val_correct += (predicted == labels).sum().item()\n", "    \n", "    avg_val_loss = val_loss / val_total\n", "    val_acc = val_correct / val_total\n", "    epoch_end_time = time.time()\n", "    \n", "    print(f\"Epoch {epoch+1}: Train Loss: {avg_train_loss:.4f}, Train Acc: {train_acc*100:.2f}% | \"\n", "          f\"Val Loss: {avg_val_loss:.4f}, Val Acc: {val_acc*100:.2f}% | Time: {epoch_end_time - epoch_start_time:.2f}s\")\n", "    \n", "    # Update the learning rate scheduler based on validation loss\n", "    scheduler.step(avg_val_loss)\n", "    \n", "    # Save model if validation loss improves\n", "    if avg_val_loss < best_val_loss:\n", "        best_val_loss = avg_val_loss\n", "        torch.save(model.state_dict(), \"best_siamese_model.pth\")\n", "        #print(f\"Epoch {epoch+1}: Best model saved with validation loss: {best_val_loss:.4f}\")\n", "\n", "# Save the final model\n", "torch.save(model.state_dict(), \"final_siamese_model.pth\")\n", "print(\"Final model saved to final_siamese_model.pth\")\n", "print(f\"Best model (validation loss: {best_val_loss:.4f}) saved to best_siamese_model.pth\")"]}, {"cell_type": "code", "execution_count": 32, "id": "a5c45b56-0286-4e2e-b6e0-27a72cd75aba", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Test Results: Loss: 0.4070, Accuracy: 85.24%\n"]}], "source": ["model.eval()\n", "test_loss = 0.0\n", "test_correct = 0\n", "test_total = 0\n", "\n", "with torch.no_grad():\n", "    for img1, img2, labels in test_loader:\n", "        img1, img2, labels = img1.to(device), img2.to(device), labels.to(device)\n", "        outputs = model(img1, img2)\n", "        loss = criterion(outputs.squeeze(), labels)\n", "        batch_size = img1.size(0)\n", "        test_loss += loss.item() * batch_size\n", "        predicted = (torch.sigmoid(outputs.squeeze()) > 0.5).float()\n", "        test_total += labels.size(0)\n", "        test_correct += (predicted == labels).sum().item()\n", "\n", "avg_test_loss = test_loss / test_total\n", "test_acc = test_correct / test_total\n", "\n", "print(f\"\\nTest Results: Loss: {avg_test_loss:.4f}, Accuracy: {test_acc*100:.2f}%\")"]}, {"cell_type": "code", "execution_count": 33, "id": "bee7155d-953b-4c79-84c3-47ced6f0c0d9", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_5854/**********.py:2: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.\n", "  model.load_state_dict(torch.load(\"best_siamese_model.pth\", map_location=device))\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Test Results: Loss: 0.3635, Accuracy: 84.72%\n"]}], "source": ["# Load the best saved model\n", "model.load_state_dict(torch.load(\"best_siamese_model.pth\", map_location=device))\n", "model.eval()\n", "\n", "test_loss = 0.0\n", "test_correct = 0\n", "test_total = 0\n", "\n", "with torch.no_grad():\n", "    for img1, img2, labels in test_loader:\n", "        img1, img2, labels = img1.to(device), img2.to(device), labels.to(device)\n", "        outputs = model(img1, img2)\n", "        loss = criterion(outputs.squeeze(), labels)\n", "        batch_size = img1.size(0)\n", "        test_loss += loss.item() * batch_size\n", "        predicted = (torch.sigmoid(outputs.squeeze()) > 0.5).float()\n", "        test_total += labels.size(0)\n", "        test_correct += (predicted == labels).sum().item()\n", "\n", "avg_test_loss = test_loss / test_total\n", "test_acc = test_correct / test_total\n", "\n", "print(f\"\\nTest Results: Loss: {avg_test_loss:.4f}, Accuracy: {test_acc*100:.2f}%\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "b50ff8cc-1b81-4d2c-ad83-f1059cf39bfb", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.21"}}, "nbformat": 4, "nbformat_minor": 5}