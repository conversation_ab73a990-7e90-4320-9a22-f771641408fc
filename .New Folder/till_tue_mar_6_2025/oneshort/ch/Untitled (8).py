#!/usr/bin/env python
# coding: utf-8

# In[1]:


import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torchvision import datasets, transforms
from torch.utils.data import Dataset, DataLoader
import torch
import torch.nn as nn
import random
import time 


# In[2]:


class SiameseDataset(Dataset):
    def __init__(self, dataset, transform=None, num_pairs=30000):
        """
        Precompute pairs for efficiency.
        """
        self.dataset = dataset
        self.transform = transform
        self.num_pairs = num_pairs
        self.pairs = []
        self.labels = []

        # Build a mapping: label -> list of indices
        class_map = {}
        for idx, (_, label) in enumerate(dataset):
            if label not in class_map:
                class_map[label] = []
            class_map[label].append(idx)

        labels_list = list(class_map.keys())
        
        # Generate pairs: half positive (same class), half negative (different class)
        n_same = num_pairs // 2
        n_diff = num_pairs - n_same
        
        for _ in range(n_same):
            label = random.choice(labels_list)
            # Ensure at least two samples exist for the chosen class
            if len(class_map[label]) < 2:
                continue
            idx1, idx2 = random.sample(class_map[label], 2)
            self.pairs.append((idx1, idx2))
            self.labels.append(1)
            
        for _ in range(n_diff):
            label1, label2 = random.sample(labels_list, 2)
            idx1 = random.choice(class_map[label1])
            idx2 = random.choice(class_map[label2])
            self.pairs.append((idx1, idx2))
            self.labels.append(0)

    def __len__(self):
        return len(self.pairs)

    def __getitem__(self, index):
        idx1, idx2 = self.pairs[index]
        img1, _ = self.dataset[idx1]
        img2, _ = self.dataset[idx2]
        label = self.labels[index]
        
        if self.transform:
            img1 = self.transform(img1)
            img2 = self.transform(img2)
            
        return img1, img2, torch.tensor(label, dtype=torch.float32)


# In[3]:


# -------------------- Data Transformations --------------------
train_transform = transforms.Compose([
    transforms.Grayscale(num_output_channels=1),
    transforms.Resize((105, 105)),
    transforms.RandomHorizontalFlip(p=0.5),
    transforms.ColorJitter(brightness=0.2, contrast=0.2),
    transforms.RandomAffine(degrees=10, translate=(0.02, 0.02), scale=(0.8, 1.2), shear=17),
    transforms.ToTensor(),
    transforms.Normalize(mean=[0.5], std=[0.5])
])

val_transform = transforms.Compose([
    transforms.Grayscale(num_output_channels=1),
    transforms.Resize((105, 105)),
    transforms.ToTensor(),
    transforms.Normalize(mean=[0.5], std=[0.5])
])

# Load dataset
train_dataset_base = datasets.ImageFolder(root='dataset/train', transform=None)
val_dataset_base = datasets.ImageFolder(root='dataset/val', transform=None)


# In[4]:


train_dataset_base


# In[5]:


val_dataset_base


# In[6]:


# -------------------- Network Architecture --------------------
class CNNBackbone(nn.Module):
    def __init__(self):
        super(CNNBackbone, self).__init__()
        self.conv1 = nn.Conv2d(1, 64, kernel_size=10, stride=1)
        self.conv2 = nn.Conv2d(64, 128, kernel_size=7, stride=1)
        self.conv3 = nn.Conv2d(128, 128, kernel_size=4, stride=1)
        self.conv4 = nn.Conv2d(128, 256, kernel_size=4, stride=1)
        self.fc1 = nn.Linear(256 * 6 * 6, 4096)

    def forward(self, x):
        x = F.relu(self.conv1(x))
        x = F.max_pool2d(x, 2)
        x = F.relu(self.conv2(x))
        x = F.max_pool2d(x, 2)
        x = F.relu(self.conv3(x))
        x = F.max_pool2d(x, 2)
        x = F.relu(self.conv4(x))
        x = torch.flatten(x, start_dim=1)  # Flatten dynamically
        x = F.relu(self.fc1(x))
        return x

class SiameseNet(nn.Module):
    def __init__(self):
        super(SiameseNet, self).__init__()
        self.backbone = CNNBackbone()
        # Modified fully connected block with dropout for additional regularization
        self.fc = nn.Sequential(
            nn.Linear(4096, 1024),
            nn.ReLU(),
            nn.Dropout(p=0.5),
            nn.Linear(1024, 1, bias=False)
        )

    def forward(self, x1, x2):
        h1 = self.backbone(x1)
        h2 = self.backbone(x2)
        distance = torch.abs(h1 - h2)
        logits = self.fc(distance)
        similarity = torch.sigmoid(logits)
        return similarity


# In[7]:


# Create training dataset and loader
train_dataset = SiameseDataset(train_dataset_base, transform=train_transform, num_pairs=20000)
val_dataset = SiameseDataset(val_dataset_base, transform=val_transform, num_pairs=3000)

train_loader = DataLoader(train_dataset, batch_size=16, shuffle=True, num_workers=4)
val_loader = DataLoader(val_dataset, batch_size=16, shuffle=False, num_workers=4)


# In[8]:


# Initialize model and training components
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
device


# In[9]:


model = SiameseNet().to(device)
model


# In[10]:


optimizer = optim.SGD(model.parameters(), lr=0.01, momentum=0.9, weight_decay=0.0005)
criterion = nn.BCELoss()  # Use BCELoss for probability output


# In[12]:


# Define number of epochs
num_epochs = 50
# Initialize a scheduler that reduces LR when validation loss plateaus.
scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', factor=0.1, patience=10, verbose=True)

# Track best validation loss
best_val_loss = float('inf')

for epoch in range(num_epochs):
    epoch_start_time = time.time()
    
    # Training phase
    model.train()
    train_loss = 0.0
    train_correct = 0
    train_total = 0
    for batch_idx, (img1, img2, labels) in enumerate(train_loader):
        img1, img2, labels = img1.to(device), img2.to(device), labels.to(device)
        optimizer.zero_grad()
        outputs = model(img1, img2)
        loss = criterion(outputs.squeeze(), labels)
        loss.backward()
        optimizer.step()
        
        batch_size = img1.size(0)
        train_loss += loss.item() * batch_size
        predicted = (outputs.squeeze() > 0.5).float()
        train_total += labels.size(0)
        train_correct += (predicted == labels).sum().item()
    
    avg_train_loss = train_loss / train_total
    train_acc = train_correct / train_total
    
    # Validation phase
    model.eval()
    val_loss = 0.0
    val_correct = 0
    val_total = 0
    with torch.no_grad():
        for img1, img2, labels in val_loader:
            img1, img2, labels = img1.to(device), img2.to(device), labels.to(device)
            outputs = model(img1, img2)
            loss = criterion(outputs.squeeze(), labels)
            batch_size = img1.size(0)
            val_loss += loss.item() * batch_size
            predicted = (outputs.squeeze() > 0.5).float()
            val_total += labels.size(0)
            val_correct += (predicted == labels).sum().item()
    
    avg_val_loss = val_loss / val_total
    val_acc = val_correct / val_total
    epoch_end_time = time.time()
    
    print(f"Epoch {epoch+1}: Train Loss: {avg_train_loss:.4f}, Train Acc: {train_acc*100:.2f}% | "
          f"Val Loss: {avg_val_loss:.4f}, Val Acc: {val_acc*100:.2f}% | Time: {epoch_end_time - epoch_start_time:.2f}s")
    
    # Update the learning rate scheduler based on validation loss
    scheduler.step(avg_val_loss)
    
    # Save model if validation loss improves
    if avg_val_loss < best_val_loss:
        best_val_loss = avg_val_loss
        torch.save(model.state_dict(), "best_siamese_model.pth")
        print(f"Epoch {epoch+1}: Best model saved with validation loss: {best_val_loss:.4f}")

# Save the final model
torch.save(model.state_dict(), "final_siamese_model.pth")
print("Final model saved to final_siamese_model.pth")
print(f"Best model (validation loss: {best_val_loss:.4f}) saved to best_siamese_model.pth")


# In[ ]:




