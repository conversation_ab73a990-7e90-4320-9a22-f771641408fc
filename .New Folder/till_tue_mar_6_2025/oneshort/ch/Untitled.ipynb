{"cells": [{"cell_type": "code", "execution_count": 1, "id": "1693d871-2eb8-4ed1-aee2-bd0019460d0a", "metadata": {}, "outputs": [], "source": ["import torch\n", "import torch.nn as nn\n", "import torch.nn.functional as F\n", "import torch.optim as optim\n", "from torchvision import datasets, transforms\n", "from torch.utils.data import Dataset, DataLoader\n", "import torch\n", "import torch.nn as nn\n", "import random\n", "import time "]}, {"cell_type": "code", "execution_count": 2, "id": "69f9e455-0825-4872-abb7-00ef38ddd2b6", "metadata": {}, "outputs": [], "source": ["class SiameseDataset(Dataset):\n", "    def __init__(self, dataset, transform=None, num_pairs=30000):\n", "        \"\"\"\n", "        Precompute pairs for efficiency.\n", "        \"\"\"\n", "        self.dataset = dataset\n", "        self.transform = transform\n", "        self.num_pairs = num_pairs\n", "        self.pairs = []\n", "        self.labels = []\n", "\n", "        # Build a mapping: label -> list of indices\n", "        class_map = {}\n", "        for idx, (_, label) in enumerate(dataset):\n", "            if label not in class_map:\n", "                class_map[label] = []\n", "            class_map[label].append(idx)\n", "\n", "        labels_list = list(class_map.keys())\n", "        \n", "        # Generate pairs: half positive (same class), half negative (different class)\n", "        n_same = num_pairs // 2\n", "        n_diff = num_pairs - n_same\n", "        \n", "        for _ in range(n_same):\n", "            label = random.choice(labels_list)\n", "            # Ensure at least two samples exist for the chosen class\n", "            if len(class_map[label]) < 2:\n", "                continue\n", "            idx1, idx2 = random.sample(class_map[label], 2)\n", "            self.pairs.append((idx1, idx2))\n", "            self.labels.append(1)\n", "            \n", "        for _ in range(n_diff):\n", "            label1, label2 = random.sample(labels_list, 2)\n", "            idx1 = random.choice(class_map[label1])\n", "            idx2 = random.choice(class_map[label2])\n", "            self.pairs.append((idx1, idx2))\n", "            self.labels.append(0)\n", "\n", "    def __len__(self):\n", "        return len(self.pairs)\n", "\n", "    def __getitem__(self, index):\n", "        idx1, idx2 = self.pairs[index]\n", "        img1, _ = self.dataset[idx1]\n", "        img2, _ = self.dataset[idx2]\n", "        label = self.labels[index]\n", "        \n", "        if self.transform:\n", "            img1 = self.transform(img1)\n", "            img2 = self.transform(img2)\n", "            \n", "        return img1, img2, torch.tensor(label, dtype=torch.float32)"]}, {"cell_type": "code", "execution_count": 3, "id": "75a21c39-52fa-43b0-8a26-3013cef54c91", "metadata": {}, "outputs": [], "source": ["# -------------------- Data Transformations --------------------\n", "train_transform = transforms.Compose([\n", "    transforms.Grayscale(num_output_channels=1),\n", "    transforms.<PERSON><PERSON><PERSON>((105, 105)),\n", "    transforms.RandomHorizontalFlip(p=0.5),\n", "    transforms.ColorJitter(brightness=0.2, contrast=0.2),\n", "    transforms.RandomAffine(degrees=10, translate=(0.02, 0.02), scale=(0.8, 1.2), shear=17),\n", "    transforms.To<PERSON><PERSON><PERSON>(),\n", "    transforms.Normalize(mean=[0.5], std=[0.5])\n", "])\n", "\n", "val_transform = transforms.Compose([\n", "    transforms.Grayscale(num_output_channels=1),\n", "    transforms.<PERSON><PERSON><PERSON>((105, 105)),\n", "    transforms.To<PERSON><PERSON><PERSON>(),\n", "    transforms.Normalize(mean=[0.5], std=[0.5])\n", "])\n", "\n", "# Load dataset\n", "train_dataset_base = datasets.ImageFolder(root='dataset/train', transform=None)\n", "val_dataset_base = datasets.ImageFolder(root='dataset/val', transform=None)"]}, {"cell_type": "code", "execution_count": 4, "id": "233b9569-1a18-48dc-bfca-161462aed24c", "metadata": {}, "outputs": [{"data": {"text/plain": ["Dataset ImageFolder\n", "    Number of datapoints: 6053\n", "    Root location: dataset/train"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["train_dataset_base"]}, {"cell_type": "code", "execution_count": 5, "id": "12b33b0e-1f46-4718-900d-dfcb994c8207", "metadata": {}, "outputs": [{"data": {"text/plain": ["Dataset ImageFolder\n", "    Number of datapoints: 918\n", "    Root location: dataset/val"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["val_dataset_base"]}, {"cell_type": "code", "execution_count": 6, "id": "2b581e9e-7a06-42f6-9e96-a23e97e76098", "metadata": {}, "outputs": [], "source": ["# -------------------- Network Architecture --------------------\n", "class CNNBackbone(nn.Module):\n", "    def __init__(self):\n", "        super(<PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "        self.conv1 = nn.Conv2d(1, 64, kernel_size=10, stride=1)\n", "        self.conv2 = nn.Conv2d(64, 128, kernel_size=7, stride=1)\n", "        self.conv3 = nn.Conv2d(128, 128, kernel_size=4, stride=1)\n", "        self.conv4 = nn.Conv2d(128, 256, kernel_size=4, stride=1)\n", "        self.fc1 = nn.<PERSON>ar(256 * 6 * 6, 4096)\n", "\n", "    def forward(self, x):\n", "        x = <PERSON>.relu(self.conv1(x))\n", "        x = F.max_pool2d(x, 2)\n", "        x = <PERSON>.relu(self.conv2(x))\n", "        x = F.max_pool2d(x, 2)\n", "        x = <PERSON>.relu(self.conv3(x))\n", "        x = F.max_pool2d(x, 2)\n", "        x = <PERSON>.relu(self.conv4(x))\n", "        x = torch.flatten(x, start_dim=1)  # Flatten dynamically\n", "        x = F.relu(self.fc1(x))\n", "        return x\n", "\n", "class SiameseNet(nn.Module):\n", "    def __init__(self):\n", "        super(SiameseNet, self).__init__()\n", "        self.backbone = CNNBackbone()\n", "        # Modified fully connected block with dropout for additional regularization\n", "        self.fc = nn.Sequential(\n", "            nn.<PERSON><PERSON>(4096, 1024),\n", "            nn.ReLU(),\n", "            nn.Dropout(p=0.5),\n", "            nn.Linear(1024, 1, bias=False)\n", "        )\n", "\n", "    def forward(self, x1, x2):\n", "        h1 = self.backbone(x1)\n", "        h2 = self.backbone(x2)\n", "        distance = torch.abs(h1 - h2)\n", "        logits = self.fc(distance)\n", "        similarity = torch.sigmoid(logits)\n", "        return similarity"]}, {"cell_type": "code", "execution_count": 7, "id": "caf0753d-e1d9-427c-a8be-33b921ea13cb", "metadata": {}, "outputs": [], "source": ["# Create training dataset and loader\n", "train_dataset = SiameseDataset(train_dataset_base, transform=train_transform, num_pairs=20000)\n", "val_dataset = SiameseDataset(val_dataset_base, transform=val_transform, num_pairs=3000)\n", "\n", "train_loader = DataLoader(train_dataset, batch_size=16, shuffle=True, num_workers=4)\n", "val_loader = DataLoader(val_dataset, batch_size=16, shuffle=False, num_workers=4)"]}, {"cell_type": "code", "execution_count": 8, "id": "39d1aa6d-d956-46f8-b413-fee8861f1e9d", "metadata": {}, "outputs": [{"data": {"text/plain": ["device(type='cuda')"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["# Initialize model and training components\n", "device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "device"]}, {"cell_type": "code", "execution_count": 9, "id": "c606476a-584d-4e4f-bc17-fb33d82ddac3", "metadata": {}, "outputs": [{"data": {"text/plain": ["SiameseNet(\n", "  (backbone): CNNBackbone(\n", "    (conv1): Conv2d(1, 64, kernel_size=(10, 10), stride=(1, 1))\n", "    (conv2): Conv2d(64, 128, kernel_size=(7, 7), stride=(1, 1))\n", "    (conv3): Conv2d(128, 128, kernel_size=(4, 4), stride=(1, 1))\n", "    (conv4): Conv2d(128, 256, kernel_size=(4, 4), stride=(1, 1))\n", "    (fc1): Linear(in_features=9216, out_features=4096, bias=True)\n", "  )\n", "  (fc): Sequential(\n", "    (0): Linear(in_features=4096, out_features=1024, bias=True)\n", "    (1): ReLU()\n", "    (2): Dropout(p=0.5, inplace=False)\n", "    (3): Linear(in_features=1024, out_features=1, bias=False)\n", "  )\n", ")"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["model = SiameseNet().to(device)\n", "model"]}, {"cell_type": "code", "execution_count": 10, "id": "1c2f1b6e-581b-4d2a-8c42-4d0e944e3a9e", "metadata": {}, "outputs": [], "source": ["optimizer = optim.SGD(model.parameters(), lr=0.01, momentum=0.9, weight_decay=0.0005)\n", "criterion = nn.BCELoss()  # Use BCELoss for probability output"]}, {"cell_type": "code", "execution_count": 12, "id": "13abc9e4-c5f6-45b2-a91a-76904184fa0f", "metadata": {"scrolled": true}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/openvoice/lib/python3.9/site-packages/torch/optim/lr_scheduler.py:62: UserWarning: The verbose parameter is deprecated. Please use get_last_lr() to access the learning rate.\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 1: Train Loss: 0.2987, Train Acc: 87.37% | Val Loss: 0.5991, Val Acc: 75.20% | Time: 40.93s\n", "Epoch 1: Best model saved with validation loss: 0.5991\n", "Epoch 2: Train Loss: 0.3000, Train Acc: 87.23% | Val Loss: 0.5333, Val Acc: 79.03% | Time: 40.72s\n", "Epoch 2: Best model saved with validation loss: 0.5333\n", "Epoch 3: Train Loss: 0.2891, Train Acc: 87.85% | Val Loss: 0.4883, Val Acc: 77.80% | Time: 40.91s\n", "Epoch 3: Best model saved with validation loss: 0.4883\n"]}, {"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mKeyboardInterrupt\u001b[0m                         <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[12], line 26\u001b[0m\n\u001b[1;32m     23\u001b[0m optimizer\u001b[38;5;241m.\u001b[39mstep()\n\u001b[1;32m     25\u001b[0m batch_size \u001b[38;5;241m=\u001b[39m img1\u001b[38;5;241m.\u001b[39msize(\u001b[38;5;241m0\u001b[39m)\n\u001b[0;32m---> 26\u001b[0m train_loss \u001b[38;5;241m+\u001b[39m\u001b[38;5;241m=\u001b[39m \u001b[43mloss\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mitem\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m \u001b[38;5;241m*\u001b[39m batch_size\n\u001b[1;32m     27\u001b[0m predicted \u001b[38;5;241m=\u001b[39m (outputs\u001b[38;5;241m.\u001b[39msqueeze() \u001b[38;5;241m>\u001b[39m \u001b[38;5;241m0.5\u001b[39m)\u001b[38;5;241m.\u001b[39mfloat()\n\u001b[1;32m     28\u001b[0m train_total \u001b[38;5;241m+\u001b[39m\u001b[38;5;241m=\u001b[39m labels\u001b[38;5;241m.\u001b[39msize(\u001b[38;5;241m0\u001b[39m)\n", "\u001b[0;31mKeyboardInterrupt\u001b[0m: "]}], "source": ["# Define number of epochs\n", "num_epochs = 50\n", "# Initialize a scheduler that reduces LR when validation loss plateaus.\n", "scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', factor=0.1, patience=10, verbose=True)\n", "\n", "# Track best validation loss\n", "best_val_loss = float('inf')\n", "\n", "for epoch in range(num_epochs):\n", "    epoch_start_time = time.time()\n", "    \n", "    # Training phase\n", "    model.train()\n", "    train_loss = 0.0\n", "    train_correct = 0\n", "    train_total = 0\n", "    for batch_idx, (img1, img2, labels) in enumerate(train_loader):\n", "        img1, img2, labels = img1.to(device), img2.to(device), labels.to(device)\n", "        optimizer.zero_grad()\n", "        outputs = model(img1, img2)\n", "        loss = criterion(outputs.squeeze(), labels)\n", "        loss.backward()\n", "        optimizer.step()\n", "        \n", "        batch_size = img1.size(0)\n", "        train_loss += loss.item() * batch_size\n", "        predicted = (outputs.squeeze() > 0.5).float()\n", "        train_total += labels.size(0)\n", "        train_correct += (predicted == labels).sum().item()\n", "    \n", "    avg_train_loss = train_loss / train_total\n", "    train_acc = train_correct / train_total\n", "    \n", "    # Validation phase\n", "    model.eval()\n", "    val_loss = 0.0\n", "    val_correct = 0\n", "    val_total = 0\n", "    with torch.no_grad():\n", "        for img1, img2, labels in val_loader:\n", "            img1, img2, labels = img1.to(device), img2.to(device), labels.to(device)\n", "            outputs = model(img1, img2)\n", "            loss = criterion(outputs.squeeze(), labels)\n", "            batch_size = img1.size(0)\n", "            val_loss += loss.item() * batch_size\n", "            predicted = (outputs.squeeze() > 0.5).float()\n", "            val_total += labels.size(0)\n", "            val_correct += (predicted == labels).sum().item()\n", "    \n", "    avg_val_loss = val_loss / val_total\n", "    val_acc = val_correct / val_total\n", "    epoch_end_time = time.time()\n", "    \n", "    print(f\"Epoch {epoch+1}: Train Loss: {avg_train_loss:.4f}, Train Acc: {train_acc*100:.2f}% | \"\n", "          f\"Val Loss: {avg_val_loss:.4f}, Val Acc: {val_acc*100:.2f}% | Time: {epoch_end_time - epoch_start_time:.2f}s\")\n", "    \n", "    # Update the learning rate scheduler based on validation loss\n", "    scheduler.step(avg_val_loss)\n", "    \n", "    # Save model if validation loss improves\n", "    if avg_val_loss < best_val_loss:\n", "        best_val_loss = avg_val_loss\n", "        torch.save(model.state_dict(), \"best_siamese_model.pth\")\n", "        print(f\"Epoch {epoch+1}: Best model saved with validation loss: {best_val_loss:.4f}\")\n", "\n", "# Save the final model\n", "torch.save(model.state_dict(), \"final_siamese_model.pth\")\n", "print(\"Final model saved to final_siamese_model.pth\")\n", "print(f\"Best model (validation loss: {best_val_loss:.4f}) saved to best_siamese_model.pth\")"]}, {"cell_type": "code", "execution_count": null, "id": "a5c45b56-0286-4e2e-b6e0-27a72cd75aba", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.21"}}, "nbformat": 4, "nbformat_minor": 5}