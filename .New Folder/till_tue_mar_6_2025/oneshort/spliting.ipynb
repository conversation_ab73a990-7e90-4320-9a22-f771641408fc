{"cells": [{"cell_type": "code", "execution_count": 5, "id": "a9dce360-b0a9-441d-8642-973553c439f7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Total Images: 9225\n", "Positive Pairs: 2289400\n", "Negative Pairs: 40256300\n", "Total Pairs: 42545700\n"]}], "source": ["import math\n", "from collections import defaultdict\n", "from torchvision import datasets\n", "\n", "def compute_pairs(dataset):\n", "    \"\"\"\n", "    Computes the number of positive pairs, negative pairs, and total pairs \n", "    from an ImageFolder dataset.\n", "\n", "    Args:\n", "        dataset: A torchvision.datasets.ImageFolder instance.\n", "    \n", "    Returns:\n", "        A tuple (positive_pairs, negative_pairs, total_pairs).\n", "    \"\"\"\n", "    # Count the number of images per class\n", "    class_counts = defaultdict(int)\n", "    for _, label in dataset:\n", "        class_counts[label] += 1\n", "    \n", "    # Compute positive pairs: for each class, compute C(n, 2)\n", "    positive_pairs = 0\n", "    for label, count in class_counts.items():\n", "        if count >= 2:\n", "            positive_pairs += math.comb(count, 2)  # n*(n-1)/2\n", "\n", "    # Total number of images\n", "    total_images = len(dataset)\n", "    # Compute total pairs: C(total_images, 2)\n", "    total_pairs = math.comb(total_images, 2)\n", "    \n", "    # Negative pairs: pairs that are not from the same class\n", "    negative_pairs = total_pairs - positive_pairs\n", "    \n", "    return positive_pairs, negative_pairs, total_pairs\n", "\n", "# Example usage:\n", "if __name__ == \"__main__\":\n", "    # Load your training dataset using ImageFolder\n", "    train_dataset = datasets.ImageFolder(root='train')\n", "    pos_pairs, neg_pairs, tot_pairs = compute_pairs(train_dataset)\n", "    \n", "    print(f\"Total Images: {len(train_dataset)}\")\n", "    print(f\"Positive Pairs: {pos_pairs}\")\n", "    print(f\"Negative Pairs: {neg_pairs}\")\n", "    print(f\"Total Pairs: {tot_pairs}\")\n"]}, {"cell_type": "code", "execution_count": 6, "id": "34d2420f-9221-4de2-acda-3c2546f25a6b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Total Images: 1978\n", "Positive Pairs: 104520\n", "Negative Pairs: 1850733\n", "Total Pairs: 1955253\n"]}], "source": ["# Example usage:\n", "if __name__ == \"__main__\":\n", "    # Load your training dataset using ImageFolder\n", "    train_dataset = datasets.ImageFolder(root='val')\n", "    pos_pairs, neg_pairs, tot_pairs = compute_pairs(train_dataset)\n", "    \n", "    print(f\"Total Images: {len(train_dataset)}\")\n", "    print(f\"Positive Pairs: {pos_pairs}\")\n", "    print(f\"Negative Pairs: {neg_pairs}\")\n", "    print(f\"Total Pairs: {tot_pairs}\")"]}, {"cell_type": "code", "execution_count": 1, "id": "a55541ca-f287-4d76-bf6f-ba3dd6d115a3", "metadata": {}, "outputs": [], "source": ["import torch\n", "import torch.nn as nn\n", "import torch.nn.functional as F\n", "import torch.optim as optim\n", "from torchvision import transforms\n", "from torch.utils.data import Dataset, DataLoader\n", "import os\n", "import random\n", "import time\n", "import shutil\n", "import PIL.Image as Image\n", "from sklearn.model_selection import train_test_split\n", "import glob\n"]}, {"cell_type": "code", "execution_count": null, "id": "097144b6-775b-4dc3-873a-c337da20f007", "metadata": {}, "outputs": [], "source": ["# Function to split data into train/val/test directories\n", "def split_data_into_folders(data_dir, output_dir, val_size=0.15, test_size=0.15):\n", "    \"\"\"\n", "    Split the data into train, validation, and test folders\n", "    \"\"\"\n", "    os.makedirs(output_dir, exist_ok=True)\n", "    train_dir = os.path.join(output_dir, 'train')\n", "    val_dir = os.path.join(output_dir, 'val')\n", "    test_dir = os.path.join(output_dir, 'test')\n", "    \n", "    # Create directories if they don't exist\n", "    for directory in [train_dir, val_dir, test_dir]:\n", "        os.makedirs(directory, exist_ok=True)\n", "    \n", "    # Dictionary to store all data\n", "    all_images = []\n", "    script_types = []\n", "    \n", "    # Collect all images and their script types\n", "    for script_type in os.listdir(data_dir):\n", "        script_path = os.path.join(data_dir, script_type)\n", "        if os.path.isdir(script_path):\n", "            # Create directories for this script in train/val/test\n", "            for split_dir in [train_dir, val_dir, test_dir]:\n", "                os.makedirs(os.path.join(split_dir, script_type), exist_ok=True)\n", "            \n", "            # Collect all character folders for this script\n", "            for character_folder in os.listdir(script_path):\n", "                char_path = os.path.join(script_path, character_folder)\n", "                if os.path.isdir(char_path):\n", "                    # Get all image files in this character folder\n", "                    img_files = glob.glob(os.path.join(char_path, '*.png')) + \\\n", "                               glob.glob(os.path.join(char_path, '*.jpg')) + \\\n", "                               glob.glob(os.path.join(char_path, '*.jpeg'))\n", "                    \n", "                    for img_file in img_files:\n", "                        all_images.append(img_file)\n", "                        script_types.append(script_type)\n", "    \n", "    print(f\"Found {len(all_images)} images across {len(set(script_types))} script types\")\n", "    \n", "    # Split into train, validation, and test sets\n", "    train_imgs, test_imgs, train_scripts, test_scripts = train_test_split(\n", "        all_images, script_types, test_size=test_size, stratify=script_types, random_state=42\n", "    )\n", "    \n", "    # Further split training into actual train and validation\n", "    train_imgs, val_imgs, train_scripts, val_scripts = train_test_split(\n", "        train_imgs, train_scripts, test_size=val_size/(1-test_size), stratify=train_scripts, random_state=42\n", "    )\n", "    \n", "    # Copy images to their respective directories\n", "    def copy_images(images, scripts, target_dir):\n", "        for img, script in zip(images, scripts):\n", "            # Get character folder name from original path\n", "            img_parts = img.split(os.sep)\n", "            char_folder = img_parts[-2]  # Character folder is the second-to-last part\n", "            \n", "            # Create character directory in target\n", "            char_dir = os.path.join(target_dir, script, char_folder)\n", "            os.makedirs(char_dir, exist_ok=True)\n", "            \n", "            # Copy the image\n", "            img_filename = os.path.basename(img)\n", "            shutil.copy2(img, os.path.join(char_dir, img_filename))\n", "    \n", "    # Copy images to respective directories\n", "    copy_images(train_imgs, train_scripts, train_dir)\n", "    copy_images(val_imgs, val_scripts, val_dir)\n", "    copy_images(test_imgs, test_scripts, test_dir)\n", "    \n", "    print(f\"Train set: {len(train_imgs)} images\")\n", "    print(f\"Validation set: {len(val_imgs)} images\")\n", "    print(f\"Test set: {len(test_imgs)} images\")\n", "    \n", "    return train_dir, val_dir, test_dir"]}, {"cell_type": "code", "execution_count": 4, "id": "66369612-d1af-463a-82f7-1c61c97890fe", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Found 13180 images across 20 script types\n", "Train set: 9225 images\n", "Validation set: 1978 images\n", "Test set: 1977 images\n"]}, {"data": {"text/plain": ["('output_dir/train', 'output_dir/val', 'output_dir/test')"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["split_data_into_folders('data', 'output_dir')"]}, {"cell_type": "code", "execution_count": null, "id": "4c94e2bf-1493-4402-883e-2ea5faefacef", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.21"}}, "nbformat": 4, "nbformat_minor": 5}