import cv2
import mediapipe as mp
import pyautogui
import numpy as np
from pynput.mouse import <PERSON><PERSON>, Controller
import time
from Xlib import display, X
from Xlib.protocol import event
import subprocess

class DualHandController:
    def __init__(self):
        """Initialize the dual hand controller with all required components"""
        # Initialize MediaPipe Hand detection - allowing 2 hands
        self.mp_hands = mp.solutions.hands
        self.hands = self.mp_hands.Hands(
            max_num_hands=2,
            min_detection_confidence=0.7,
            min_tracking_confidence=0.7
        )
        self.mp_draw = mp.solutions.drawing_utils
        
        # Initialize controllers
        self.mouse = Controller()
        self.display = display.Display()
        self.root = self.display.screen().root
        
        # Screen dimensions
        self.screen_width, self.screen_height = pyautogui.size()
        
        # Initialize camera
        self.cap = cv2.VideoCapture(0)
        self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
        self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
        
        # State variables
        self.is_dragging = False
        self.is_window_dragging = False
        self.is_zooming = False
        self.prev_zoom_distance = None
        self.prev_hand_centers = {'left': None, 'right': None}
        self.smoothing_factor = 0.5
        
    def get_hand_center(self, hand_landmarks):
        """Calculate the center point of the hand"""
        x_coords = [landmark.x for landmark in hand_landmarks.landmark]
        y_coords = [landmark.y for landmark in hand_landmarks.landmark]
        center_x = int(np.mean(x_coords) * self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        center_y = int(np.mean(y_coords) * self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        return (center_x, center_y)
    
    def get_hand_gesture(self, hand_landmarks):
        """
        Detect hand gesture type
        Returns: 'pinch', 'closed_palm', 'open_palm', or 'none'
        """
        # Get finger landmarks
        thumb_tip = hand_landmarks.landmark[4]
        index_tip = hand_landmarks.landmark[8]
        palm_landmarks = [hand_landmarks.landmark[0], hand_landmarks.landmark[5],
                         hand_landmarks.landmark[9], hand_landmarks.landmark[13],
                         hand_landmarks.landmark[17]]
        
        # Calculate distances
        pinch_distance = np.sqrt((thumb_tip.x - index_tip.x)**2 + 
                               (thumb_tip.y - index_tip.y)**2)
        
        # Calculate palm closure by averaging distances of fingertips to palm center
        palm_center = np.mean([(l.x, l.y) for l in palm_landmarks], axis=0)
        fingertips = [hand_landmarks.landmark[tip] for tip in [8, 12, 16, 20]]
        finger_distances = [np.sqrt((tip.x - palm_center[0])**2 + 
                                  (tip.y - palm_center[1])**2) for tip in fingertips]
        avg_finger_distance = np.mean(finger_distances)
        
        # Determine gesture
        if pinch_distance < 0.05:  # Threshold for pinch
            return 'pinch'
        elif avg_finger_distance < 0.1:  # Threshold for closed palm
            return 'closed_palm'
        elif avg_finger_distance > 0.15:  # Threshold for open palm
            return 'open_palm'
        return 'none'
    
    def get_distance_between_hands(self, hand_landmarks_list):
        """Calculate distance between two hands' centers"""
        if len(hand_landmarks_list) < 2:
            return None
            
        center1 = self.get_hand_center(hand_landmarks_list[0])
        center2 = self.get_hand_center(hand_landmarks_list[1])
        
        return np.sqrt((center1[0] - center2[0])**2 + 
                      (center1[1] - center2[1])**2)
    
    def handle_zoom_gesture(self, distance):
        """Handle zoom in/out based on hand distance"""
        if self.prev_zoom_distance is not None:
            distance_change = distance - self.prev_zoom_distance
            if abs(distance_change) > 20:  # Threshold to prevent small movements
                # Simulate Ctrl + plus/minus for zoom
                if distance_change > 0:
                    pyautogui.hotkey('ctrl', 'plus')
                else:
                    pyautogui.hotkey('ctrl', 'minus')
        self.prev_zoom_distance = distance
    
    def get_active_window(self):
        """Get the currently active window"""
        try:
            output = subprocess.check_output(['xdotool', 'getactivewindow'])
            return int(output.strip())
        except:
            return None
    
    def move_window(self, window_id, x, y):
        """Move window to specified coordinates"""
        if window_id:
            subprocess.run(['xdotool', 'windowmove', str(window_id), str(x), str(y)])
    
    def run(self):
        """Main loop for hand gesture detection and control"""
        print("=== Dual Hand Gesture Control System ===")
        print("Instructions:")
        print("1. File/Folder Movement: Pinch with thumb and index finger to grab and move")
        print("2. Window Movement: Close palm (make a fist) to grab window, move, and release")
        print("3. Zoom Control: Use both hands with palms slightly closed, move apart to zoom in")
        print("                 and together to zoom out")
        print("Press 'q' to quit the program")
        
        while True:
            success, image = self.cap.read()
            if not success:
                continue
                
            image = cv2.flip(image, 1)
            image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            results = self.hands.process(image_rgb)
            
            if results.multi_hand_landmarks:
                # Handle single and dual hand gestures
                gestures = []
                for hand_landmarks in results.multi_hand_landmarks:
                    self.mp_draw.draw_landmarks(image, hand_landmarks,
                                             self.mp_hands.HAND_CONNECTIONS)
                    gestures.append(self.get_hand_gesture(hand_landmarks))
                
                # Handle different gesture combinations
                if len(gestures) == 1:  # Single hand gestures
                    hand_center = self.get_hand_center(results.multi_hand_landmarks[0])
                    screen_pos = self.map_to_screen(hand_center)
                    
                    if gestures[0] == 'pinch':  # File/folder drag
                        if not self.is_dragging:
                            self.mouse.press(Button.left)
                            self.is_dragging = True
                    elif gestures[0] == 'closed_palm':  # Window drag
                        if not self.is_window_dragging:
                            window_id = self.get_active_window()
                            if window_id:
                                self.is_window_dragging = True
                                self.current_window = window_id
                    else:
                        if self.is_dragging:
                            self.mouse.release(Button.left)
                            self.is_dragging = False
                        if self.is_window_dragging:
                            self.is_window_dragging = False
                    
                    # Move mouse or window
                    if self.is_window_dragging:
                        self.move_window(self.current_window, screen_pos[0], screen_pos[1])
                    else:
                        self.mouse.position = screen_pos
                
                elif len(gestures) == 2:  # Dual hand gestures
                    # Check for zoom gesture (both palms slightly closed)
                    if all(g == 'closed_palm' for g in gestures):
                        distance = self.get_distance_between_hands(results.multi_hand_landmarks)
                        if distance:
                            self.handle_zoom_gesture(distance)
                            self.is_zooming = True
                    else:
                        self.is_zooming = False
                        self.prev_zoom_distance = None
            
            cv2.imshow('Dual Hand Gesture Control', image)
            
            if cv2.waitKey(1) & 0xFF == ord('q'):
                break
                
        self.cap.release()
        cv2.destroyAllWindows()
    
    def map_to_screen(self, hand_center):
        """Map hand position to screen coordinates"""
        x = np.interp(hand_center[0], 
                     [0, self.cap.get(cv2.CAP_PROP_FRAME_WIDTH)],
                     [0, self.screen_width])
        y = np.interp(hand_center[1],
                     [0, self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT)],
                     [0, self.screen_height])
        return (int(x), int(y))

if __name__ == "__main__":
    # Initialize and run the controller
    controller = DualHandController()
    controller.run()
