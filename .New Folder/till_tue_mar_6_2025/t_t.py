import cv2
import mediapipe as mp
import pyautogui
import numpy as np
from pynput.mouse import <PERSON><PERSON>, Controller
import time

class HandMouseController:
    def __init__(self):
        # Initialize MediaPipe Hand detection
        self.mp_hands = mp.solutions.hands
        self.hands = self.mp_hands.Hands(
            max_num_hands=1,
            min_detection_confidence=0.7,
            min_tracking_confidence=0.7
        )
        self.mp_draw = mp.solutions.drawing_utils
        
        # Initialize mouse controller
        self.mouse = Controller()
        
        # Screen dimensions
        self.screen_width, self.screen_height = pyautogui.size()
        
        # Initialize camera
        self.cap = cv2.VideoCapture(0)
        self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
        self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
        
        # State variables
        self.is_dragging = False
        self.prev_hand_center = None
        self.smoothing_factor = 0.5
        
    def get_hand_center(self, hand_landmarks):
        """Calculate the center point of the hand"""
        x_coords = [landmark.x for landmark in hand_landmarks.landmark]
        y_coords = [landmark.y for landmark in hand_landmarks.landmark]
        center_x = int(np.mean(x_coords) * self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        center_y = int(np.mean(y_coords) * self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        return (center_x, center_y)
    
    def get_finger_state(self, hand_landmarks):
        """Detect if fingers are extended"""
        thumb_tip = hand_landmarks.landmark[4].y
        thumb_ip = hand_landmarks.landmark[3].y
        index_tip = hand_landmarks.landmark[8].y
        index_pip = hand_landmarks.landmark[6].y
        
        thumb_extended = thumb_tip < thumb_ip
        index_extended = index_tip < index_pip
        
        return thumb_extended, index_extended
    
    def smooth_movement(self, current_pos, prev_pos):
        """Smooth the mouse movement"""
        if prev_pos is None:
            return current_pos
        
        smoothed_x = int(current_pos[0] * self.smoothing_factor + 
                        prev_pos[0] * (1 - self.smoothing_factor))
        smoothed_y = int(current_pos[1] * self.smoothing_factor + 
                        prev_pos[1] * (1 - self.smoothing_factor))
        
        return (smoothed_x, smoothed_y)
    
    def map_to_screen(self, hand_center):
        """Map hand position to screen coordinates"""
        x = np.interp(hand_center[0], 
                     [0, self.cap.get(cv2.CAP_PROP_FRAME_WIDTH)],
                     [0, self.screen_width])
        y = np.interp(hand_center[1],
                     [0, self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT)],
                     [0, self.screen_height])
        return (int(x), int(y))
    
    def run(self):
        while True:
            success, image = self.cap.read()
            if not success:
                continue
                
            # Flip the image horizontally for a later selfie-view display
            image = cv2.flip(image, 1)
            
            # Convert the BGR image to RGB
            image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            
            # Process the image and detect hands
            results = self.hands.process(image_rgb)
            
            if results.multi_hand_landmarks:
                for hand_landmarks in results.multi_hand_landmarks:
                    # Draw hand landmarks
                    self.mp_draw.draw_landmarks(image, hand_landmarks,
                                             self.mp_hands.HAND_CONNECTIONS)
                    
                    # Get hand center and finger states
                    hand_center = self.get_hand_center(hand_landmarks)
                    thumb_extended, index_extended = self.get_finger_state(hand_landmarks)
                    
                    # Map hand position to screen coordinates
                    screen_pos = self.map_to_screen(hand_center)
                    
                    # Smooth the movement
                    smoothed_pos = self.smooth_movement(screen_pos, self.prev_hand_center)
                    self.prev_hand_center = smoothed_pos
                    
                    # Move mouse pointer
                    self.mouse.position = smoothed_pos
                    
                    # Handle clicking and dragging
                    if thumb_extended and index_extended:
                        if not self.is_dragging:
                            self.mouse.press(Button.left)
                            self.is_dragging = True
                    elif self.is_dragging:
                        self.mouse.release(Button.left)
                        self.is_dragging = False
                    
                    # Draw cursor position on image
                    cv2.circle(image, hand_center, 5, (0, 255, 0), -1)
            
            # Display the image
            cv2.imshow('Hand Mouse Control', image)
            
            # Break loop on 'q' press
            if cv2.waitKey(1) & 0xFF == ord('q'):
                break
                
        # Clean up
        self.cap.release()
        cv2.destroyAllWindows()

if __name__ == "__main__":
    # Initialize and run the controller
    controller = HandMouseController()
    controller.run()
