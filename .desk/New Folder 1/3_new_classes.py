#!/usr/bin/env python
# coding: utf-8

# In[2]:


import torchvision
from torchvision.transforms import ToTensor
#from torchvision.models import resnet101, resnet18, resnet50 
from torchvision import datasets
from transformers import ViTModel
from transformers.modeling_outputs import SequenceClassifierOutput
from torch.optim.lr_scheduler import ReduceLROnPlateau
import torch.nn as nn
import torch.nn.functional as F
import torchvision.transforms as transforms
from transformers import ViTFeatureExtractor
import torch.nn as nn
import torch
from torch.utils.data import DataLoader
from tqdm import tqdm
import torch.utils.data as data
from torch.autograd import Variable
from torch.utils.data import WeightedRandomSampler
import numpy as np
import os
import time
import json
import seaborn as sns
import shutil
import glob
from pathlib import Path
from PIL import Image
import matplotlib.pyplot as plt
import random
import plotly.express as px
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, confusion_matrix
from sklearn.utils.class_weight import compute_class_weight


# In[3]:


train_dir = Path('3clslrs/data3/train/')
val_dir = Path('3clslrs/data3/val/')
test_dir = Path('3clslrs/data3/test/')

EPOCHS = 100
BATCH_SIZE = 8
EVAL_BATCH = 1
LEARNING_RATE = 1e-5
WEIGHT_DECAY = 1e-4

checkpoint_dir = "checkpoints"
patience = 10  # Number of epochs to wait for improvement before early stopping
clip_value = 0.6  # Maximum allowed value for gradient clipping


# In[4]:


def plot_images_in_single_row(directory):
    """
    Plots a random image from each class in a single row.

    Args:
        directory (str): Path to the directory containing class folders.
    """
    # List of class directories
    class_dirs = [d for d in os.listdir(directory) if os.path.isdir(os.path.join(directory, d))]
    
    # Adjust the figure size based on the number of classes
    plt.figure(figsize=(5 * len(class_dirs), 5))  

    for i, class_dir in enumerate(class_dirs):
        class_dir_path = os.path.join(directory, class_dir)
        
        # Get a random image from the selected class
        image_file = random.choice(os.listdir(class_dir_path))
        image_path = os.path.join(class_dir_path, image_file)
        
        # Load and display the image in a subplot
        plt.subplot(1, len(class_dirs), i + 1)
        image = Image.open(image_path)
        plt.imshow(image)
        plt.title(f"Class: {class_dir}")
        plt.axis('off')

    plt.show()


# In[5]:


plot_images_in_single_row(train_dir)


# In[6]:


def get_class_names(folder_path):
    class_names = []
    subfolders = [f.name for f in os.scandir(folder_path) if f.is_dir()]
    class_names = sorted(subfolders)
    return class_names


# In[7]:


folder_path = train_dir
class_names = get_class_names(folder_path)
print(f"Number of classes: {len(class_names)}")
print(class_names)


# In[8]:


train_folder_path = train_dir

class_folders = class_names


# In[9]:


class_image_counts = [len(os.listdir(os.path.join(train_folder_path, class_folder))) for class_folder in class_folders]
print(class_image_counts)


# In[10]:


data = {
    'Class': class_folders,
    'Number of Images': class_image_counts
}


# In[11]:


fig = px.bar(data, x='Class', y='Number of Images', title='Number of Images per Class in Train Data')
fig.update_xaxes(tickangle=45, tickfont=dict(size=10))
fig.show()


# In[12]:


def get_transform(train):
    if train:
        return transforms.Compose([
            transforms.RandomHorizontalFlip(),
            transforms.RandomVerticalFlip(),
            transforms.RandomRotation(10),
            transforms.ColorJitter(brightness=0.1, contrast=0.1, saturation=0.1, hue=0.1),
            transforms.RandomAffine(degrees=0, translate=(0.1, 0.1), scale=(0.9, 1.1)),
            transforms.ToTensor(),
            transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])
        ])
    else:
        return transforms.Compose([
            transforms.ToTensor(),
            transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])
        ])


# In[13]:


train_transforms = get_transform(train=True)
valid_transforms = get_transform(train=False)
test_transforms = get_transform(train=False)

# Load datasets
train_ds = datasets.ImageFolder(train_dir, transform=train_transforms)
valid_ds = datasets.ImageFolder(val_dir, transform=valid_transforms)
test_ds = datasets.ImageFolder(test_dir, transform=test_transforms)


# In[14]:


print("New class mapping:", train_ds.class_to_idx)


# In[25]:


class Conv3DBlock(nn.Module):
    def __init__(self, in_channels, out_channels, kernel_size=3, stride=1, padding=1):
        super(Conv3DBlock, self).__init__()
        self.conv = nn.Conv3d(in_channels, out_channels, kernel_size, stride, padding)
        self.bn = nn.BatchNorm3d(out_channels)
        self.relu = nn.ReLU(inplace=True)

    def forward(self, x):
        return self.relu(self.bn(self.conv(x)))

class CombinedAttention(nn.Module):
    def __init__(self, in_channels, out_channels, reduction=16):
        super(CombinedAttention, self).__init__()
        self.spatial_attention = nn.Sequential(
            nn.Conv2d(2, 1, kernel_size=7, padding=3),
            nn.Sigmoid()
        )
        self.channel_attention = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(in_channels, in_channels // reduction, 1),
            nn.ReLU(inplace=True),
            nn.Conv2d(in_channels // reduction, in_channels, 1),
            nn.Sigmoid()
        )
        self.conv = nn.Conv2d(in_channels, out_channels, kernel_size=1)

    def forward(self, x):
        avg_out = torch.mean(x, dim=1, keepdim=True)
        max_out, _ = torch.max(x, dim=1, keepdim=True)
        spatial = self.spatial_attention(torch.cat([avg_out, max_out], dim=1))
        channel = self.channel_attention(x)
        x = x * spatial * channel
        return self.conv(x)

class CNN3DFeatureExtractor(nn.Module):
    def __init__(self, in_channels=3, time_dimension=16):
        super(CNN3DFeatureExtractor, self).__init__()
        self.time_dimension = time_dimension
        self.layer1 = Conv3DBlock(in_channels, 64)
        self.layer2 = Conv3DBlock(64, 128)
        self.layer3 = Conv3DBlock(128, 256)
        self.layer4 = Conv3DBlock(256, 512)
        self.pool = nn.MaxPool3d(kernel_size=2, stride=2)
        
        self.combined_attention = CombinedAttention(512, 512)
        
        self.embedding_reducer = nn.Linear(512, 512)  # Initialize with fixed size
        
    def forward(self, x):
        B, C, H, W = x.shape
        x = x.unsqueeze(2).repeat(1, 1, self.time_dimension, 1, 1)  # Add time dimension
        
        x = self.pool(self.layer1(x))
        x = self.pool(self.layer2(x))
        x = self.pool(self.layer3(x))
        x = self.pool(self.layer4(x))  # Now x has shape (B, 512, T, H, W)
        
        x = x.mean(2)  # Reduce time dimension, now x has shape (B, 512, H, W)
        x = self.combined_attention(x)
        x = x.mean([2, 3])  # Global average pooling, now x has shape (B, 512)
        
        x = self.embedding_reducer(x)
        return x


# In[26]:


class TransformerBlock(nn.Module):
    def __init__(self, num_labels):
        super(TransformerBlock, self).__init__()
        self.vit = ViTModel.from_pretrained('google/vit-base-patch16-224-in21k')
        self.dropout = nn.Dropout(0.2)
        self.classifier = nn.Linear(self.vit.config.hidden_size, num_labels)

    def forward(self, x):
        outputs = self.vit(pixel_values=x)
        output = self.dropout(outputs.last_hidden_state[:, 0])
        logits = self.classifier(output)
        return logits

class CNN3DViTClassification(nn.Module):
    def __init__(self, num_labels):
        super(CNN3DViTClassification, self).__init__()
        self.feature_extractor = CNN3DFeatureExtractor(in_channels=3)
        self.fusion = nn.Sequential(
            nn.Conv2d(4, 8, kernel_size=1),  # 3 (original image) + 1 (feature maps)
            nn.ReLU(),
            nn.BatchNorm2d(8),
            nn.Conv2d(8, 3, kernel_size=1)
        )
        self.transformer_block = TransformerBlock(num_labels)
        self.num_labels = num_labels
        self.loss_fct = nn.CrossEntropyLoss()

    def forward(self, pixel_values, labels=None):
        #print(f"Input shape: {pixel_values.shape}")
        extracted_features = self.feature_extractor(pixel_values)
        #print(f"Extracted features shape: {extracted_features.shape}")
        
        # Reshape extracted_features to match pixel_values dimensions
        extracted_features = extracted_features.unsqueeze(-1).unsqueeze(-1).expand(-1, -1, pixel_values.shape[2], pixel_values.shape[3])
        
        combined_features = torch.cat([pixel_values, extracted_features[:, :1, :, :]], dim=1)
        #print(f"Combined features shape: {combined_features.shape}")
        fused_features = self.fusion(combined_features)
        #print(f"Fused features shape: {fused_features.shape}")
        logits = self.transformer_block(fused_features)
        #print(f"Logits shape: {logits.shape}")
        
        loss = None
        if labels is not None:
            loss = self.loss_fct(logits.view(-1, self.num_labels), labels.view(-1))
        
        return logits, loss


# In[27]:


device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
if torch.cuda.is_available():
    print('cuda is available')
    #model.cuda()
else:
    print('cuda not available')


# In[28]:


model = CNN3DViTClassification(len(train_ds.classes)).to(device)


# In[29]:


model


# In[30]:


from thop import profile
from sklearn.metrics import roc_auc_score, roc_curve


# In[32]:


# ================== 1. Model Statistics (FLOPs/Params) ==================
def print_model_stats(model, input_size=(3, 224, 224)):
    dummy_input = torch.randn(1, *input_size).to(device)
    flops, params = profile(model, inputs=(dummy_input,))
    print(f"FLOPs: {flops/1e9:.2f} G")
    print(f"Parameters: {params/1e6:.2f} M")

# Load best model
best_model_path = '3clslrs/best_model.pth'
model = CNN3DViTClassification(len(test_ds.classes)).to(device)
model.load_state_dict(torch.load(best_model_path))
print_model_stats(model)


# In[42]:


# ================== 2. Grad-CAM Implementation ==================
class GradCAM:
    def __init__(self, model, target_layer):
        self.model = model
        self.target_layer = target_layer
        self.activations = None
        self.gradients = None
        
        # Register hooks
        target_layer.register_forward_hook(self.save_activations)
        target_layer.register_backward_hook(self.save_gradients)
    
    def save_activations(self, module, input, output):
        self.activations = output.detach()
    
    def save_gradients(self, module, grad_input, grad_output):
        self.gradients = grad_output[0].detach()
    
    def generate_cam(self, input_image, target_class=None):
        self.model.zero_grad()
        output, _ = self.model(input_image.unsqueeze(0))
        if target_class is None:
            target_class = torch.argmax(output, dim=1)
        output[0, target_class].backward()
        
        # Compute weights
        pooled_gradients = torch.mean(self.gradients, dim=[2, 3], keepdim=True)
        cam = torch.mul(self.activations, pooled_gradients).sum(dim=1, keepdim=True)
        cam = F.relu(cam)
        cam = F.interpolate(cam, size=input_image.shape[1:], mode='bilinear', align_corners=False)
        cam = cam - cam.min()
        cam = cam / cam.max()
        return cam.squeeze().cpu().numpy()

# Select target layer (using the CombinedAttention's conv layer)
target_layer = model.feature_extractor.combined_attention.conv
grad_cam = GradCAM(model, target_layer)

# Visualize Grad-CAM for sample images
def visualize_gradcam(test_dir, num_samples=3):
    fig, axs = plt.subplots(num_samples, 2, figsize=(10, num_samples*5))
    classes = os.listdir(test_dir)
    
    for row_idx in range(num_samples):
        class_name = random.choice(classes)
        image_path = random.choice(list((test_dir/class_name).glob('*')))
        
        # Load and process image
        img = Image.open(image_path).convert('RGB')
        original_img = np.array(img)
        img_tensor = test_transforms(img).to(device)
        
        # Generate CAM
        cam = grad_cam.generate_cam(img_tensor)
        
        # Plot results
        axs[row_idx, 0].imshow(original_img)
        axs[row_idx, 0].axis('off')
        axs[row_idx, 0].set_title(f"Original ({class_name})")
        
        axs[row_idx, 1].imshow(original_img)
        axs[row_idx, 1].imshow(cam, cmap='jet', alpha=0.5)
        axs[row_idx, 1].axis('off')
        axs[row_idx, 1].set_title("Grad-CAM")
    
    plt.tight_layout()
    plt.show()

visualize_gradcam(test_dir)


# In[36]:


# ================== 3. AUC Curve Calculation ==================
def calculate_auc(model, test_loader):
    all_probs = []
    all_labels = []
    
    model.eval()
    with torch.no_grad():
        for inputs, targets in tqdm(test_loader):
            inputs, targets = inputs.to(device), targets.to(device)
            outputs, _ = model(inputs)
            probs = F.softmax(outputs, dim=1)
            all_probs.append(probs.cpu().numpy())
            all_labels.append(targets.cpu().numpy())
    
    all_probs = np.concatenate(all_probs)
    all_labels = np.concatenate(all_labels)
    
    # Calculate AUC for each class
    n_classes = len(class_names)
    plt.figure(figsize=(10, 8))
    for i in range(n_classes):
        fpr, tpr, _ = roc_curve(all_labels == i, all_probs[:, i])
        auc_score = roc_auc_score(all_labels == i, all_probs[:, i])
        plt.plot(fpr, tpr, label=f'{class_names[i]} (AUC = {auc_score:.2f})')
    
    plt.plot([0, 1], [0, 1], 'k--')
    plt.xlabel('False Positive Rate')
    plt.ylabel('True Positive Rate')
    plt.title('ROC Curves by Class')
    plt.legend()
    plt.show()

    # Calculate overall multiclass AUC
    auc_ovr = roc_auc_score(all_labels, all_probs, multi_class='ovr')
    print(f"Overall AUC (OvR): {auc_ovr:.4f}")

test_loader = DataLoader(test_ds, batch_size=BATCH_SIZE, shuffle=False)
calculate_auc(model, test_loader)


# In[35]:


def count_parameters(model):
    return sum(p.numel() for p in model.parameters() if p.requires_grad)

def print_model_parameters(model):
    total_params = count_parameters(model)
    print(f"Total trainable parameters: {total_params:,}")

    # Optionally, print parameters for each named component
    print("\nParameters by component:")
    for name, parameter in model.named_parameters():
        if parameter.requires_grad:
            print(f"{name}: {parameter.numel():,}")


# In[36]:


print_model_parameters(model)


# In[37]:


optimizer = torch.optim.AdamW(
    model.parameters(),
    lr=LEARNING_RATE,
    weight_decay=WEIGHT_DECAY
)


# In[38]:


# Compute class weights
class_weights = compute_class_weight(
    class_weight='balanced',
    classes=np.unique(train_ds.targets),
    y=train_ds.targets
    )
class_weights = torch.tensor(class_weights, dtype=torch.float).to(device)

loss_func = nn.CrossEntropyLoss(weight=class_weights)


# In[39]:


print("Number of train samples: ", len(train_ds))
print("Number of test samples: ", len(test_ds))


# In[40]:


# Calculate class weights
class_counts = np.bincount(train_ds.targets)
class_weights = 1. / class_counts
sample_weights = class_weights[train_ds.targets]

# Create a sampler
sampler = WeightedRandomSampler(
    weights=sample_weights,
    num_samples=len(sample_weights),
    replacement=True
    )

train_loader = DataLoader(train_ds, batch_size=BATCH_SIZE, num_workers=4, sampler=sampler)
valid_loader = DataLoader(valid_ds, batch_size=BATCH_SIZE, shuffle=True, num_workers=4)
test_loader = DataLoader(test_ds, batch_size=BATCH_SIZE, shuffle=True, num_workers=4)


# In[41]:


def save_checkpoint(model, optimizer, epoch, train_loss, val_loss, val_accuracy, best_val_accuracy, checkpoint_dir):
    checkpoint = {
        'epoch': epoch,
        'model_state_dict': model.state_dict(),
        'optimizer_state_dict': optimizer.state_dict(),
        'train_loss': train_loss,
        'val_loss': val_loss,
        'val_accuracy': val_accuracy,
        'best_val_accuracy': best_val_accuracy
    }
    #checkpoint_path = os.path.join(checkpoint_dir, f'checkpoint_epoch_{epoch}.pth')
    checkpoint_path = os.path.join(checkpoint_dir, f'checkpoint.pth')
    torch.save(checkpoint, checkpoint_path)
    print(f"Checkpoint saved at {checkpoint_path}")


# In[42]:


def load_checkpoint(model, optimizer, checkpoint_path):
    checkpoint = torch.load(checkpoint_path)
    model.load_state_dict(checkpoint['model_state_dict'])
    optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
    epoch = checkpoint['epoch']
    train_loss = checkpoint['train_loss']
    val_loss = checkpoint['val_loss']
    val_accuracy = checkpoint['val_accuracy']
    best_val_accuracy = checkpoint['best_val_accuracy']
    print(f"Loaded checkpoint from epoch {epoch}")
    return epoch, train_loss, val_loss, val_accuracy, best_val_accuracy


# In[43]:


def get_latest_checkpoint(checkpoint_dir):
    #checkpoints = glob.glob(os.path.join(checkpoint_dir, 'checkpoint_epoch_*.pth'))
    checkpoints = glob.glob(os.path.join(checkpoint_dir, 'checkpoint.pth'))
    if not checkpoints:
        return None
    latest_checkpoint = max(checkpoints, key=os.path.getctime)
    return latest_checkpoint


# In[44]:


def evaluate(model, data_loader, device):
    model.eval()
    total_loss = 0
    total_correct = 0
    total_samples = 0
    with torch.no_grad():
        for x, y in data_loader:
            x, y = x.to(device), y.to(device)
            output, loss = model(x, y)
            total_loss += loss.item()
            preds = output.argmax(dim=1)
            total_correct += (preds == y).sum().item()
            total_samples += y.size(0)
    avg_loss = total_loss / len(data_loader)
    accuracy = total_correct / total_samples
    return avg_loss, accuracy


# In[45]:


def train(model, train_loader, valid_loader, optimizer, device, EPOCHS, checkpoint_dir, patience=5, clip_value=1.0):
    if not os.path.exists(checkpoint_dir):
        os.makedirs(checkpoint_dir)

    scheduler = ReduceLROnPlateau(optimizer, mode='max', factor=0.1, patience=3, verbose=True)

    latest_checkpoint = get_latest_checkpoint(checkpoint_dir)
    if latest_checkpoint:
        print(f"Resuming training from checkpoint: {latest_checkpoint}")
        start_epoch, train_loss, val_loss, val_accuracy, best_val_accuracy = load_checkpoint(model, optimizer, latest_checkpoint)
        start_epoch += 1
    else:
        print("Starting training from scratch")
        start_epoch = 0
        best_val_accuracy = 0.0
        train_loss = 0.0
        val_loss = 0.0
        val_accuracy = 0.0

    epochs_without_improvement = 0

    for epoch in range(start_epoch, EPOCHS):
        epoch_start_time = time.time()
        model.train()
        running_loss = 0.0
        running_correct = 0
        total_samples = 0

        for step, (x, y) in enumerate(tqdm(train_loader, desc=f"Epoch {epoch+1}/{EPOCHS}")):
            x, y = x.to(device), y.to(device)
            optimizer.zero_grad()
            output, loss = model(x, y)
            loss.backward()

            torch.nn.utils.clip_grad_norm_(model.parameters(), clip_value)

            optimizer.step()
            running_loss += loss.item()
            preds = output.argmax(dim=1)
            running_correct += (preds == y).sum().item()
            total_samples += y.size(0)

            if step % 500 == 0:
                train_accuracy = running_correct / total_samples
                print(f"Epoch {epoch+1}/{EPOCHS}, Step {step}/{len(train_loader)} | "
                      f"Train Loss: {running_loss / (step + 1):.4f} | "
                      f"Train Accuracy: {train_accuracy * 100:.2f}%")

        val_loss, val_accuracy = evaluate(model, valid_loader, device)
        train_accuracy = running_correct / total_samples
        avg_train_loss = running_loss / len(train_loader)
        epoch_end_time = time.time()

        print(f"Epoch {epoch+1}/{EPOCHS} | Time: {epoch_end_time - epoch_start_time:.2f}s | "
              f"Train Loss: {avg_train_loss:.4f} | Train Accuracy: {train_accuracy * 100:.2f}% ")
        print(f"Validation Loss: {val_loss:.4f} | Validation Accuracy: {val_accuracy * 100:.2f}%")
        print('+-----------+-------------+---------------+----------------+---------------+')

        scheduler.step(val_accuracy)

        save_checkpoint(model, optimizer, epoch, avg_train_loss, val_loss, val_accuracy, best_val_accuracy, checkpoint_dir)

        if val_accuracy > best_val_accuracy:
            best_val_accuracy = val_accuracy
            best_model_path = os.path.join(checkpoint_dir, "best_model.pth")
            torch.save(model.state_dict(), best_model_path)
            print(f"Best model saved at {best_model_path}")
            epochs_without_improvement = 0
        else:
            epochs_without_improvement += 1

        if epochs_without_improvement >= patience:
            print(f"Early stopping triggered after {patience} epochs without improvement.")
            break

    results = {
        'final_train_loss': avg_train_loss,
        'final_val_loss': val_loss,
        'final_val_accuracy': val_accuracy,
        'best_val_accuracy': best_val_accuracy
    }
    with open(os.path.join(checkpoint_dir, 'training_results.json'), 'w') as f:
        json.dump(results, f)


# In[ ]:


get_ipython().run_cell_magic('time', '', 'train(model, train_loader, valid_loader, optimizer, device, EPOCHS, checkpoint_dir, patience, clip_value)\n')


# In[69]:


test_loader = DataLoader(test_ds, batch_size=10, shuffle=True, num_workers=4)


# In[71]:


# Initializeing variables for accuracy calculation
total_correct = 0
total_samples = 0
predicted_labels_all = []
targets_all = []

# Disableing gradient calculation
with torch.no_grad():
    for inputs, targets in tqdm(test_loader):
        # Send data to appropriate computing device
        inputs, targets = inputs.to(device), targets.to(device)

        # Generate predictions
        predictions, _ = model(inputs, targets)

        # Get predicted labels
        predicted_labels = predictions.argmax(dim=1)

        # Update total samples count
        total_samples += targets.size(0)

        # Update correct predictions count
        total_correct += (predicted_labels == targets).sum().item()

        predicted_labels_all.extend(predicted_labels.cpu().numpy())
        targets_all.extend(targets.cpu().numpy())


# In[72]:


accuracy = total_correct / total_samples
precision = precision_score(targets_all, predicted_labels_all, average='weighted')
recall = recall_score(targets_all, predicted_labels_all, average='weighted')
f1 = f1_score(targets_all, predicted_labels_all, average='weighted')
conf_matrix = confusion_matrix(targets_all, predicted_labels_all)

print(f"Test accuracy: {accuracy * 100:.2f}%")
print(f"Precision: {precision:.2f}")
print(f"Recall: {recall:.2f}")
print(f"F1-score: {f1:.2f}")


# In[73]:


plt.figure(figsize=(8, 6))
sns.heatmap(conf_matrix, annot=True, fmt='d', cmap='Blues', cbar=True)
plt.xlabel('Predicted Labels')
plt.ylabel('True Labels')
plt.title('Confusion Matrix')
plt.show()


# In[74]:


# Define the path to the saved best model
best_model_path = 'checkpoints/best_model.pth'

# Load the best saved model
model = CNN3DViTClassification(len(test_ds.classes)).to(device)
model.load_state_dict(torch.load(best_model_path))

# Set the model to evaluation mode
model.eval()

# Initialize variables for accuracy calculation
total_correct = 0
total_samples = 0
predicted_labels_all = []
targets_all = []

# Disable gradient calculation
with torch.no_grad():
    for inputs, targets in tqdm(test_loader):
        # Send data to appropriate computing device
        inputs, targets = inputs.to(device), targets.to(device)

        # Generate predictions
        predictions, _ = model(inputs, targets)

        # Get predicted labels
        predicted_labels = predictions.argmax(dim=1)

        # Update total samples count
        total_samples += targets.size(0)

        # Update correct predictions count
        total_correct += (predicted_labels == targets).sum().item()

        predicted_labels_all.extend(predicted_labels.cpu().numpy())
        targets_all.extend(targets.cpu().numpy())


# In[75]:


accuracy = total_correct / total_samples
precision = precision_score(targets_all, predicted_labels_all, average='weighted')
recall = recall_score(targets_all, predicted_labels_all, average='weighted')
f1 = f1_score(targets_all, predicted_labels_all, average='weighted')
conf_matrix = confusion_matrix(targets_all, predicted_labels_all)

print(f"Test accuracy: {accuracy * 100:.2f}%")
print(f"Precision: {precision:.2f}")
print(f"Recall: {recall:.2f}")
print(f"F1-score: {f1:.2f}")


# In[76]:


plt.figure(figsize=(8, 6))
sns.heatmap(conf_matrix, annot=True, fmt='d', cmap='Blues', cbar=True)
plt.xlabel('Predicted Labels')
plt.ylabel('True Labels')
plt.title('Confusion Matrix')
plt.show()


# In[ ]:




