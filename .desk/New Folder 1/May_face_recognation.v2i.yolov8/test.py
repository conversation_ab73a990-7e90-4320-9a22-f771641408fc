import cv2
from ultralytics import YOLO

# Load the trained model
model = YOLO('yolov8n.pt')

# Capture video from webcam
cap = cv2.VideoCapture(0)

while cap.isOpened():
    ret, frame = cap.read()
    if not ret:
        break

    # Perform inference
    results = model(frame)

    # Loop through results and render the detections on the frame
    for result in results:
        annotated_frame = result.plot()

    # Display the annotated frame
    cv2.imshow('YOLO Detection', annotated_frame)

    # Exit loop if 'q' is pressed
    if cv2.waitKey(1) & 0xFF == ord('q'):
        break

cap.release()
cv2.destroyAllWindows()
'''

results = model('input_image.png')
results[0].show()
results2 = model('img.jpg')
results2[0].show()
'''
