import cv2
import os
from datetime import datetime

# Configuration
OUTPUT_DIR = "dataset/myimage"  # Directory to save images
NUM_IMAGES = 100  # Total images to capture
INTERVAL = 1  # Interval between captures in seconds (adjust if needed)

# Create the output directory if it doesn't exist
os.makedirs(OUTPUT_DIR, exist_ok=True)

# Initialize the webcam
cap = cv2.VideoCapture(0)
if not cap.isOpened():
    raise RuntimeError("Could not open webcam")

print("\nPress 'q' to quit capturing at any time.")

image_count = 0

try:
    while image_count < NUM_IMAGES:
        # Capture frame from the webcam
        ret, frame = cap.read()
        if not ret:
            print("Failed to capture image. Retrying...")
            continue

        # Display the captured frame
        cv2.imshow("Capture - Press 'q' to Quit", frame)

        # Generate a unique filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        image_path = os.path.join(OUTPUT_DIR, f"image_{image_count + 1}_{timestamp}.jpg")

        # Save the frame as an image
        cv2.imwrite(image_path, frame)
        print(f"Saved: {image_path}")
        image_count += 1

        # Wait for the interval or until the user presses 'q'
        if cv2.waitKey(INTERVAL * 1000) & 0xFF == ord('q'):
            print("Capture interrupted by user.")
            break

    print(f"\nFinished capturing {image_count} images.")

finally:
    # Release the webcam and close all OpenCV windows
    cap.release()
    cv2.destroyAllWindows()
