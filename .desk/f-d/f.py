import cv2
import numpy as np

# Load pre-trained face detection model (OpenCV's Haar Cascade)
face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + "haarcascade_frontalface_default.xml")

# Load pre-trained face recognition model (OpenCV's LBPH recognizer)
recognizer = cv2.face.LBPHFaceRecognizer_create()

# Load known faces and labels
known_faces = []
known_labels = []

# Function to add a known face
def add_known_face(image_path, label):
    image = cv2.imread(image_path, cv2.IMREAD_GRAYSCALE)
    faces = face_cascade.detectMultiScale(image, scaleFactor=1.1, minNeighbors=5)
    for (x, y, w, h) in faces:
        face = image[y:y+h, x:x+w]
        known_faces.append(face)
        known_labels.append(label)

# Add your face as "You"
add_known_face("img.jpg", 0)  # Replace with your image file

# Train the recognizer with known faces
recognizer.train(known_faces, np.array(known_labels))

# Open webcam and capture an image
video_capture = cv2.VideoCapture(0)  # Use 0 for the default camera

print("Capturing image from webcam...")
ret, frame = video_capture.read()  # Capture a single frame

if ret:
    # Convert the captured frame to grayscale
    gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

    # Detect faces in the image
    faces = face_cascade.detectMultiScale(gray, scaleFactor=1.1, minNeighbors=5, minSize=(30, 30))

    for (x, y, w, h) in faces:
        # Extract the face region
        face = gray[y:y+h, x:x+w]

        # Recognize the face
        label, confidence = recognizer.predict(face)

        # Determine the name based on the label
        if label == 0 and confidence < 50:  # Adjust confidence threshold as needed
            name = "Boss"
        else:
            name = "Other People"

        # Draw a bounding box around the face
        cv2.rectangle(frame, (x, y), (x+w, y+h), (0, 0, 255), 2)

        # Add label text above the bounding box
        cv2.putText(frame, name, (x, y-10), cv2.FONT_HERSHEY_SIMPLEX, 0.9, (0, 0, 255), 2)

    # Save the processed image
    processed_image_path = "processed_image.jpg"
    cv2.imwrite(processed_image_path, frame)
    print(f"Image saved as {processed_image_path}")

else:
    print("Failed to capture image from webcam.")

# Release the webcam resource
video_capture.release()

