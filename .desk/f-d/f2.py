import cv2
import numpy as np

# Load pre-trained face detection model (OpenCV's Haar Cascade)
face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + "haarcascade_frontalface_default.xml")

# Load pre-trained face recognition model (OpenCV's LBPH recognizer)
recognizer = cv2.face.LBPHFaceRecognizer_create()

# Load known faces and labels
known_faces = []
known_labels = []

# Function to add a known face
def add_known_face(image_path, label):
    image = cv2.imread(image_path, cv2.IMREAD_GRAYSCALE)
    faces = face_cascade.detectMultiScale(image, scaleFactor=1.1, minNeighbors=5)
    for (x, y, w, h) in faces:
        face = image[y:y+h, x:x+w]
        known_faces.append(face)
        known_labels.append(label)

# Add your face as "You"
add_known_face("img.jpg", 0)  # Replace with your image file

# Train the recognizer with known faces
recognizer.train(known_faces, np.array(known_labels))

# Function to process an input image
def process_image(input_image):
    """
    Process the input image to detect and recognize faces.

    Parameters:
        input_image (numpy.ndarray): The input image.

    Returns:
        numpy.ndarray: The processed image with bounding boxes and labels.
    """
    # Convert the input image to grayscale
    gray = cv2.cvtColor(input_image, cv2.COLOR_BGR2GRAY)

    # Detect faces in the image
    faces = face_cascade.detectMultiScale(gray, scaleFactor=1.1, minNeighbors=5, minSize=(30, 30))

    for (x, y, w, h) in faces:
        # Extract the face region
        face = gray[y:y+h, x:x+w]

        # Recognize the face
        label, confidence = recognizer.predict(face)

        # Determine the name based on the label
        if label == 0 and confidence < 50:  # Adjust confidence threshold as needed
            name = "Boss"
        else:
            name = "Other People"

        # Draw a bounding box around the face
        cv2.rectangle(input_image, (x, y), (x+w, y+h), (0, 0, 255), 2)

        # Add label text above the bounding box
        cv2.putText(input_image, name, (x, y-10), cv2.FONT_HERSHEY_SIMPLEX, 0.9, (0, 0, 255), 2)

    return input_image

# Example usage
if __name__ == "__main__":
    # Load an example image
    image_path = "test.png"  # Replace with your image file path
    input_image = cv2.imread(image_path)

    # Process the image
    processed_image = process_image(input_image)

    # Save and display the processed image
    processed_image_path = "processed_image.jpg"
    cv2.imwrite(processed_image_path, processed_image)
    print(f"Processed image saved as {processed_image_path}")

    # Optionally display the image
    cv2.imshow("Processed Image", processed_image)
    cv2.waitKey(0)
    cv2.destroyAllWindows()

