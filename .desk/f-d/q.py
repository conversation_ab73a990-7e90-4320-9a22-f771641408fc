from torch.quantization import quantize_dynamic
import torch.nn as nn
import torch

import torch.optim as optim
from torchvision import datasets, transforms, models
from torch.utils.data import DataLoader
import os

DATA_DIR = "dataset"
BATCH_SIZE = 32
NUM_CLASSES = 2  # "myimage" and "other"
LEARNING_RATE = 1e-3
EPOCHS = 10
DEVICE = torch.device("cuda" if torch.cuda.is_available() else "cpu")

class LightweightClassifier(nn.Module):
    def __init__(self, num_classes):
        super(LightweightClassifier, self).__init__()
        # Use MobileNetV2 backbone
        self.backbone = models.mobilenet_v2(pretrained=True)
        self.backbone.classifier[1] = nn.Linear(self.backbone.last_channel, num_classes)

    def forward(self, x):
        return self.backbone(x)

model = LightweightClassifier(num_classes=NUM_CLASSES).to(DEVICE)


model = LightweightClassifier(num_classes=NUM_CLASSES)
model.load_state_dict(torch.load("lightweight_model.pth"))
quantized_model = quantize_dynamic(model, {nn.Linear}, dtype=torch.qint8)
torch.save(quantized_model.state_dict(), "quantized_lightweight_model.pth")
print("Quantized model saved.")


