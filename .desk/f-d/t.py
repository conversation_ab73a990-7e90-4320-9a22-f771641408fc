import torch
import torch.nn as nn
from torchvision import transforms, models
from PIL import Image
import os
from torch.quantization import quantize_dynamic

# Constants
NUM_CLASSES = 2
DEVICE = torch.device("cuda" if torch.cuda.is_available() else "cpu")

class LightweightClassifier(nn.Module):
    def __init__(self, num_classes):
        super(LightweightClassifier, self).__init__()
        # Use weights=None to avoid deprecation warning
        self.backbone = models.mobilenet_v2(weights=None)
        self.backbone.classifier[1] = nn.Linear(self.backbone.last_channel, num_classes)

    def forward(self, x):
        return self.backbone(x)

def load_and_prepare_image(image_path):
    """Load and preprocess an image for inference"""
    transform = transforms.Compose([
        transforms.Resize(256),
        transforms.CenterCrop(224),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], 
                           std=[0.229, 0.224, 0.225])
    ])
    
    image = Image.open(image_path).convert('RGB')
    image_tensor = transform(image).unsqueeze(0)
    return image_tensor

def test_image(model_path, image_path):
    """Test the model on a single image"""
    try:
        # Initialize model
        model = LightweightClassifier(num_classes=NUM_CLASSES)
        
        # Quantize the model first
        model = quantize_dynamic(model, {nn.Linear}, dtype=torch.qint8)
        
        # Load the quantized state dict with safety flag
        state_dict = torch.load(model_path, weights_only=True)
        model.load_state_dict(state_dict)
        model.eval()
        
        # Prepare image
        image_tensor = load_and_prepare_image(image_path)
        
        # Make prediction
        with torch.no_grad():
            outputs = model(image_tensor)
            probabilities = torch.nn.functional.softmax(outputs, dim=1)
            predicted_class = torch.argmax(probabilities, dim=1).item()
            confidence = probabilities[0][predicted_class].item() * 100
            
        # Map prediction to class name
        class_names = ['0', '1']
        predicted_class_name = class_names[predicted_class]
        
        return {
            'class': predicted_class_name,
            'confidence': confidence,
            'raw_probabilities': probabilities[0].tolist()
        }
        
    except Exception as e:
        import traceback
        traceback.print_exc()  # This will print the full error trace
        return {'error': str(e)}

def main():
    # Paths
    model_path = "quantized_lightweight_model.pth"
    
    # Test a single image
    test_image_path = "dataset/train/0/image_1_20241229_215805.jpg"  # Replace with your test image path
    
    # Check if files exist
    if not os.path.exists(model_path):
        print(f"Error: Model file not found at {model_path}")
        return
    if not os.path.exists(test_image_path):
        print(f"Error: Test image not found at {test_image_path}")
        return
    
    # Run prediction
    result = test_image(model_path, test_image_path)
    
    # Print results
    if 'error' in result:
        print(f"Error during prediction: {result['error']}")
    else:
        print(f"\nPrediction Results for {test_image_path}:")
        print(f"Predicted Class: {result['class']}")
        print(f"Confidence: {result['confidence']:.2f}%")
        print("\nClass Probabilities:")
        for idx, prob in enumerate(['other', 'myimage']):
            print(f"{prob}: {result['raw_probabilities'][idx]*100:.2f}%")

if __name__ == "__main__":
    main()
