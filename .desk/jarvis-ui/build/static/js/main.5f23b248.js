/*! For license information please see main.5f23b248.js.LICENSE.txt */
(()=>{"use strict";var e={730:(e,t,n)=>{var r=n(43),o=n(853);function a(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var i=new Set,l={};function s(e,t){u(e,t),u(e+"Capture",t)}function u(e,t){for(l[e]=t,e=0;e<t.length;e++)i.add(t[e])}var c=!("undefined"===typeof window||"undefined"===typeof window.document||"undefined"===typeof window.document.createElement),d=Object.prototype.hasOwnProperty,f=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,p={},h={};function m(e,t,n,r,o,a,i){this.acceptsBooleans=2===t||3===t||4===t,this.attributeName=r,this.attributeNamespace=o,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=a,this.removeEmptyString=i}var v={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach((function(e){v[e]=new m(e,0,!1,e,null,!1,!1)})),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach((function(e){var t=e[0];v[t]=new m(t,1,!1,e[1],null,!1,!1)})),["contentEditable","draggable","spellCheck","value"].forEach((function(e){v[e]=new m(e,2,!1,e.toLowerCase(),null,!1,!1)})),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach((function(e){v[e]=new m(e,2,!1,e,null,!1,!1)})),"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach((function(e){v[e]=new m(e,3,!1,e.toLowerCase(),null,!1,!1)})),["checked","multiple","muted","selected"].forEach((function(e){v[e]=new m(e,3,!0,e,null,!1,!1)})),["capture","download"].forEach((function(e){v[e]=new m(e,4,!1,e,null,!1,!1)})),["cols","rows","size","span"].forEach((function(e){v[e]=new m(e,6,!1,e,null,!1,!1)})),["rowSpan","start"].forEach((function(e){v[e]=new m(e,5,!1,e.toLowerCase(),null,!1,!1)}));var g=/[\-:]([a-z])/g;function y(e){return e[1].toUpperCase()}function b(e,t,n,r){var o=v.hasOwnProperty(t)?v[t]:null;(null!==o?0!==o.type:r||!(2<t.length)||"o"!==t[0]&&"O"!==t[0]||"n"!==t[1]&&"N"!==t[1])&&(function(e,t,n,r){if(null===t||"undefined"===typeof t||function(e,t,n,r){if(null!==n&&0===n.type)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return!r&&(null!==n?!n.acceptsBooleans:"data-"!==(e=e.toLowerCase().slice(0,5))&&"aria-"!==e);default:return!1}}(e,t,n,r))return!0;if(r)return!1;if(null!==n)switch(n.type){case 3:return!t;case 4:return!1===t;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}(t,n,o,r)&&(n=null),r||null===o?function(e){return!!d.call(h,e)||!d.call(p,e)&&(f.test(e)?h[e]=!0:(p[e]=!0,!1))}(t)&&(null===n?e.removeAttribute(t):e.setAttribute(t,""+n)):o.mustUseProperty?e[o.propertyName]=null===n?3!==o.type&&"":n:(t=o.attributeName,r=o.attributeNamespace,null===n?e.removeAttribute(t):(n=3===(o=o.type)||4===o&&!0===n?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach((function(e){var t=e.replace(g,y);v[t]=new m(t,1,!1,e,null,!1,!1)})),"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach((function(e){var t=e.replace(g,y);v[t]=new m(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)})),["xml:base","xml:lang","xml:space"].forEach((function(e){var t=e.replace(g,y);v[t]=new m(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)})),["tabIndex","crossOrigin"].forEach((function(e){v[e]=new m(e,1,!1,e.toLowerCase(),null,!1,!1)})),v.xlinkHref=new m("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1),["src","href","action","formAction"].forEach((function(e){v[e]=new m(e,1,!1,e.toLowerCase(),null,!0,!0)}));var w=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,x=Symbol.for("react.element"),k=Symbol.for("react.portal"),S=Symbol.for("react.fragment"),E=Symbol.for("react.strict_mode"),C=Symbol.for("react.profiler"),P=Symbol.for("react.provider"),T=Symbol.for("react.context"),L=Symbol.for("react.forward_ref"),M=Symbol.for("react.suspense"),_=Symbol.for("react.suspense_list"),N=Symbol.for("react.memo"),V=Symbol.for("react.lazy");Symbol.for("react.scope"),Symbol.for("react.debug_trace_mode");var R=Symbol.for("react.offscreen");Symbol.for("react.legacy_hidden"),Symbol.for("react.cache"),Symbol.for("react.tracing_marker");var D=Symbol.iterator;function A(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=D&&e[D]||e["@@iterator"])?e:null}var z,O=Object.assign;function F(e){if(void 0===z)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);z=t&&t[1]||""}return"\n"+z+e}var I=!1;function j(e,t){if(!e||I)return"";I=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),"object"===typeof Reflect&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&"string"===typeof u.stack){for(var o=u.stack.split("\n"),a=r.stack.split("\n"),i=o.length-1,l=a.length-1;1<=i&&0<=l&&o[i]!==a[l];)l--;for(;1<=i&&0<=l;i--,l--)if(o[i]!==a[l]){if(1!==i||1!==l)do{if(i--,0>--l||o[i]!==a[l]){var s="\n"+o[i].replace(" at new "," at ");return e.displayName&&s.includes("<anonymous>")&&(s=s.replace("<anonymous>",e.displayName)),s}}while(1<=i&&0<=l);break}}}finally{I=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?F(e):""}function B(e){switch(e.tag){case 5:return F(e.type);case 16:return F("Lazy");case 13:return F("Suspense");case 19:return F("SuspenseList");case 0:case 2:case 15:return e=j(e.type,!1);case 11:return e=j(e.type.render,!1);case 1:return e=j(e.type,!0);default:return""}}function U(e){if(null==e)return null;if("function"===typeof e)return e.displayName||e.name||null;if("string"===typeof e)return e;switch(e){case S:return"Fragment";case k:return"Portal";case C:return"Profiler";case E:return"StrictMode";case M:return"Suspense";case _:return"SuspenseList"}if("object"===typeof e)switch(e.$$typeof){case T:return(e.displayName||"Context")+".Consumer";case P:return(e._context.displayName||"Context")+".Provider";case L:var t=e.render;return(e=e.displayName)||(e=""!==(e=t.displayName||t.name||"")?"ForwardRef("+e+")":"ForwardRef"),e;case N:return null!==(t=e.displayName||null)?t:U(e.type)||"Memo";case V:t=e._payload,e=e._init;try{return U(e(t))}catch(n){}}return null}function H(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=(e=t.render).displayName||e.name||"",t.displayName||(""!==e?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return U(t);case 8:return t===E?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if("function"===typeof t)return t.displayName||t.name||null;if("string"===typeof t)return t}return null}function W(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":case"object":return e;default:return""}}function $(e){var t=e.type;return(e=e.nodeName)&&"input"===e.toLowerCase()&&("checkbox"===t||"radio"===t)}function Q(e){e._valueTracker||(e._valueTracker=function(e){var t=$(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&"undefined"!==typeof n&&"function"===typeof n.get&&"function"===typeof n.set){var o=n.get,a=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return o.call(this)},set:function(e){r=""+e,a.call(this,e)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(e){r=""+e},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}(e))}function Y(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=$(e)?e.checked?"true":"false":e.value),(e=r)!==n&&(t.setValue(e),!0)}function X(e){if("undefined"===typeof(e=e||("undefined"!==typeof document?document:void 0)))return null;try{return e.activeElement||e.body}catch(t){return e.body}}function q(e,t){var n=t.checked;return O({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:null!=n?n:e._wrapperState.initialChecked})}function K(e,t){var n=null==t.defaultValue?"":t.defaultValue,r=null!=t.checked?t.checked:t.defaultChecked;n=W(null!=t.value?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:"checkbox"===t.type||"radio"===t.type?null!=t.checked:null!=t.value}}function G(e,t){null!=(t=t.checked)&&b(e,"checked",t,!1)}function Z(e,t){G(e,t);var n=W(t.value),r=t.type;if(null!=n)"number"===r?(0===n&&""===e.value||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if("submit"===r||"reset"===r)return void e.removeAttribute("value");t.hasOwnProperty("value")?ee(e,t.type,n):t.hasOwnProperty("defaultValue")&&ee(e,t.type,W(t.defaultValue)),null==t.checked&&null!=t.defaultChecked&&(e.defaultChecked=!!t.defaultChecked)}function J(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!("submit"!==r&&"reset"!==r||void 0!==t.value&&null!==t.value))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}""!==(n=e.name)&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,""!==n&&(e.name=n)}function ee(e,t,n){"number"===t&&X(e.ownerDocument)===e||(null==n?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var te=Array.isArray;function ne(e,t,n,r){if(e=e.options,t){t={};for(var o=0;o<n.length;o++)t["$"+n[o]]=!0;for(n=0;n<e.length;n++)o=t.hasOwnProperty("$"+e[n].value),e[n].selected!==o&&(e[n].selected=o),o&&r&&(e[n].defaultSelected=!0)}else{for(n=""+W(n),t=null,o=0;o<e.length;o++){if(e[o].value===n)return e[o].selected=!0,void(r&&(e[o].defaultSelected=!0));null!==t||e[o].disabled||(t=e[o])}null!==t&&(t.selected=!0)}}function re(e,t){if(null!=t.dangerouslySetInnerHTML)throw Error(a(91));return O({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function oe(e,t){var n=t.value;if(null==n){if(n=t.children,t=t.defaultValue,null!=n){if(null!=t)throw Error(a(92));if(te(n)){if(1<n.length)throw Error(a(93));n=n[0]}t=n}null==t&&(t=""),n=t}e._wrapperState={initialValue:W(n)}}function ae(e,t){var n=W(t.value),r=W(t.defaultValue);null!=n&&((n=""+n)!==e.value&&(e.value=n),null==t.defaultValue&&e.defaultValue!==n&&(e.defaultValue=n)),null!=r&&(e.defaultValue=""+r)}function ie(e){var t=e.textContent;t===e._wrapperState.initialValue&&""!==t&&null!==t&&(e.value=t)}function le(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function se(e,t){return null==e||"http://www.w3.org/1999/xhtml"===e?le(t):"http://www.w3.org/2000/svg"===e&&"foreignObject"===t?"http://www.w3.org/1999/xhtml":e}var ue,ce,de=(ce=function(e,t){if("http://www.w3.org/2000/svg"!==e.namespaceURI||"innerHTML"in e)e.innerHTML=t;else{for((ue=ue||document.createElement("div")).innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=ue.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}},"undefined"!==typeof MSApp&&MSApp.execUnsafeLocalFunction?function(e,t,n,r){MSApp.execUnsafeLocalFunction((function(){return ce(e,t)}))}:ce);function fe(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&3===n.nodeType)return void(n.nodeValue=t)}e.textContent=t}var pe={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},he=["Webkit","ms","Moz","O"];function me(e,t,n){return null==t||"boolean"===typeof t||""===t?"":n||"number"!==typeof t||0===t||pe.hasOwnProperty(e)&&pe[e]?(""+t).trim():t+"px"}function ve(e,t){for(var n in e=e.style,t)if(t.hasOwnProperty(n)){var r=0===n.indexOf("--"),o=me(n,t[n],r);"float"===n&&(n="cssFloat"),r?e.setProperty(n,o):e[n]=o}}Object.keys(pe).forEach((function(e){he.forEach((function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),pe[t]=pe[e]}))}));var ge=O({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function ye(e,t){if(t){if(ge[e]&&(null!=t.children||null!=t.dangerouslySetInnerHTML))throw Error(a(137,e));if(null!=t.dangerouslySetInnerHTML){if(null!=t.children)throw Error(a(60));if("object"!==typeof t.dangerouslySetInnerHTML||!("__html"in t.dangerouslySetInnerHTML))throw Error(a(61))}if(null!=t.style&&"object"!==typeof t.style)throw Error(a(62))}}function be(e,t){if(-1===e.indexOf("-"))return"string"===typeof t.is;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var we=null;function xe(e){return(e=e.target||e.srcElement||window).correspondingUseElement&&(e=e.correspondingUseElement),3===e.nodeType?e.parentNode:e}var ke=null,Se=null,Ee=null;function Ce(e){if(e=wo(e)){if("function"!==typeof ke)throw Error(a(280));var t=e.stateNode;t&&(t=ko(t),ke(e.stateNode,e.type,t))}}function Pe(e){Se?Ee?Ee.push(e):Ee=[e]:Se=e}function Te(){if(Se){var e=Se,t=Ee;if(Ee=Se=null,Ce(e),t)for(e=0;e<t.length;e++)Ce(t[e])}}function Le(e,t){return e(t)}function Me(){}var _e=!1;function Ne(e,t,n){if(_e)return e(t,n);_e=!0;try{return Le(e,t,n)}finally{_e=!1,(null!==Se||null!==Ee)&&(Me(),Te())}}function Ve(e,t){var n=e.stateNode;if(null===n)return null;var r=ko(n);if(null===r)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(r=!("button"===(e=e.type)||"input"===e||"select"===e||"textarea"===e)),e=!r;break e;default:e=!1}if(e)return null;if(n&&"function"!==typeof n)throw Error(a(231,t,typeof n));return n}var Re=!1;if(c)try{var De={};Object.defineProperty(De,"passive",{get:function(){Re=!0}}),window.addEventListener("test",De,De),window.removeEventListener("test",De,De)}catch(ce){Re=!1}function Ae(e,t,n,r,o,a,i,l,s){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(c){this.onError(c)}}var ze=!1,Oe=null,Fe=!1,Ie=null,je={onError:function(e){ze=!0,Oe=e}};function Be(e,t,n,r,o,a,i,l,s){ze=!1,Oe=null,Ae.apply(je,arguments)}function Ue(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do{0!==(4098&(t=e).flags)&&(n=t.return),e=t.return}while(e)}return 3===t.tag?n:null}function He(e){if(13===e.tag){var t=e.memoizedState;if(null===t&&(null!==(e=e.alternate)&&(t=e.memoizedState)),null!==t)return t.dehydrated}return null}function We(e){if(Ue(e)!==e)throw Error(a(188))}function $e(e){return null!==(e=function(e){var t=e.alternate;if(!t){if(null===(t=Ue(e)))throw Error(a(188));return t!==e?null:e}for(var n=e,r=t;;){var o=n.return;if(null===o)break;var i=o.alternate;if(null===i){if(null!==(r=o.return)){n=r;continue}break}if(o.child===i.child){for(i=o.child;i;){if(i===n)return We(o),e;if(i===r)return We(o),t;i=i.sibling}throw Error(a(188))}if(n.return!==r.return)n=o,r=i;else{for(var l=!1,s=o.child;s;){if(s===n){l=!0,n=o,r=i;break}if(s===r){l=!0,r=o,n=i;break}s=s.sibling}if(!l){for(s=i.child;s;){if(s===n){l=!0,n=i,r=o;break}if(s===r){l=!0,r=i,n=o;break}s=s.sibling}if(!l)throw Error(a(189))}}if(n.alternate!==r)throw Error(a(190))}if(3!==n.tag)throw Error(a(188));return n.stateNode.current===n?e:t}(e))?Qe(e):null}function Qe(e){if(5===e.tag||6===e.tag)return e;for(e=e.child;null!==e;){var t=Qe(e);if(null!==t)return t;e=e.sibling}return null}var Ye=o.unstable_scheduleCallback,Xe=o.unstable_cancelCallback,qe=o.unstable_shouldYield,Ke=o.unstable_requestPaint,Ge=o.unstable_now,Ze=o.unstable_getCurrentPriorityLevel,Je=o.unstable_ImmediatePriority,et=o.unstable_UserBlockingPriority,tt=o.unstable_NormalPriority,nt=o.unstable_LowPriority,rt=o.unstable_IdlePriority,ot=null,at=null;var it=Math.clz32?Math.clz32:function(e){return e>>>=0,0===e?32:31-(lt(e)/st|0)|0},lt=Math.log,st=Math.LN2;var ut=64,ct=4194304;function dt(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return 4194240&e;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return 130023424&e;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function ft(e,t){var n=e.pendingLanes;if(0===n)return 0;var r=0,o=e.suspendedLanes,a=e.pingedLanes,i=268435455&n;if(0!==i){var l=i&~o;0!==l?r=dt(l):0!==(a&=i)&&(r=dt(a))}else 0!==(i=n&~o)?r=dt(i):0!==a&&(r=dt(a));if(0===r)return 0;if(0!==t&&t!==r&&0===(t&o)&&((o=r&-r)>=(a=t&-t)||16===o&&0!==(4194240&a)))return t;if(0!==(4&r)&&(r|=16&n),0!==(t=e.entangledLanes))for(e=e.entanglements,t&=r;0<t;)o=1<<(n=31-it(t)),r|=e[n],t&=~o;return r}function pt(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;default:return-1}}function ht(e){return 0!==(e=-1073741825&e.pendingLanes)?e:1073741824&e?1073741824:0}function mt(){var e=ut;return 0===(4194240&(ut<<=1))&&(ut=64),e}function vt(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function gt(e,t,n){e.pendingLanes|=t,536870912!==t&&(e.suspendedLanes=0,e.pingedLanes=0),(e=e.eventTimes)[t=31-it(t)]=n}function yt(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-it(n),o=1<<r;o&t|e[r]&t&&(e[r]|=t),n&=~o}}var bt=0;function wt(e){return 1<(e&=-e)?4<e?0!==(268435455&e)?16:536870912:4:1}var xt,kt,St,Et,Ct,Pt=!1,Tt=[],Lt=null,Mt=null,_t=null,Nt=new Map,Vt=new Map,Rt=[],Dt="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function At(e,t){switch(e){case"focusin":case"focusout":Lt=null;break;case"dragenter":case"dragleave":Mt=null;break;case"mouseover":case"mouseout":_t=null;break;case"pointerover":case"pointerout":Nt.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Vt.delete(t.pointerId)}}function zt(e,t,n,r,o,a){return null===e||e.nativeEvent!==a?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:a,targetContainers:[o]},null!==t&&(null!==(t=wo(t))&&kt(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,null!==o&&-1===t.indexOf(o)&&t.push(o),e)}function Ot(e){var t=bo(e.target);if(null!==t){var n=Ue(t);if(null!==n)if(13===(t=n.tag)){if(null!==(t=He(n)))return e.blockedOn=t,void Ct(e.priority,(function(){St(n)}))}else if(3===t&&n.stateNode.current.memoizedState.isDehydrated)return void(e.blockedOn=3===n.tag?n.stateNode.containerInfo:null)}e.blockedOn=null}function Ft(e){if(null!==e.blockedOn)return!1;for(var t=e.targetContainers;0<t.length;){var n=qt(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(null!==n)return null!==(t=wo(n))&&kt(t),e.blockedOn=n,!1;var r=new(n=e.nativeEvent).constructor(n.type,n);we=r,n.target.dispatchEvent(r),we=null,t.shift()}return!0}function It(e,t,n){Ft(e)&&n.delete(t)}function jt(){Pt=!1,null!==Lt&&Ft(Lt)&&(Lt=null),null!==Mt&&Ft(Mt)&&(Mt=null),null!==_t&&Ft(_t)&&(_t=null),Nt.forEach(It),Vt.forEach(It)}function Bt(e,t){e.blockedOn===t&&(e.blockedOn=null,Pt||(Pt=!0,o.unstable_scheduleCallback(o.unstable_NormalPriority,jt)))}function Ut(e){function t(t){return Bt(t,e)}if(0<Tt.length){Bt(Tt[0],e);for(var n=1;n<Tt.length;n++){var r=Tt[n];r.blockedOn===e&&(r.blockedOn=null)}}for(null!==Lt&&Bt(Lt,e),null!==Mt&&Bt(Mt,e),null!==_t&&Bt(_t,e),Nt.forEach(t),Vt.forEach(t),n=0;n<Rt.length;n++)(r=Rt[n]).blockedOn===e&&(r.blockedOn=null);for(;0<Rt.length&&null===(n=Rt[0]).blockedOn;)Ot(n),null===n.blockedOn&&Rt.shift()}var Ht=w.ReactCurrentBatchConfig,Wt=!0;function $t(e,t,n,r){var o=bt,a=Ht.transition;Ht.transition=null;try{bt=1,Yt(e,t,n,r)}finally{bt=o,Ht.transition=a}}function Qt(e,t,n,r){var o=bt,a=Ht.transition;Ht.transition=null;try{bt=4,Yt(e,t,n,r)}finally{bt=o,Ht.transition=a}}function Yt(e,t,n,r){if(Wt){var o=qt(e,t,n,r);if(null===o)Wr(e,t,r,Xt,n),At(e,r);else if(function(e,t,n,r,o){switch(t){case"focusin":return Lt=zt(Lt,e,t,n,r,o),!0;case"dragenter":return Mt=zt(Mt,e,t,n,r,o),!0;case"mouseover":return _t=zt(_t,e,t,n,r,o),!0;case"pointerover":var a=o.pointerId;return Nt.set(a,zt(Nt.get(a)||null,e,t,n,r,o)),!0;case"gotpointercapture":return a=o.pointerId,Vt.set(a,zt(Vt.get(a)||null,e,t,n,r,o)),!0}return!1}(o,e,t,n,r))r.stopPropagation();else if(At(e,r),4&t&&-1<Dt.indexOf(e)){for(;null!==o;){var a=wo(o);if(null!==a&&xt(a),null===(a=qt(e,t,n,r))&&Wr(e,t,r,Xt,n),a===o)break;o=a}null!==o&&r.stopPropagation()}else Wr(e,t,r,null,n)}}var Xt=null;function qt(e,t,n,r){if(Xt=null,null!==(e=bo(e=xe(r))))if(null===(t=Ue(e)))e=null;else if(13===(n=t.tag)){if(null!==(e=He(t)))return e;e=null}else if(3===n){if(t.stateNode.current.memoizedState.isDehydrated)return 3===t.tag?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Xt=e,null}function Kt(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Ze()){case Je:return 1;case et:return 4;case tt:case nt:return 16;case rt:return 536870912;default:return 16}default:return 16}}var Gt=null,Zt=null,Jt=null;function en(){if(Jt)return Jt;var e,t,n=Zt,r=n.length,o="value"in Gt?Gt.value:Gt.textContent,a=o.length;for(e=0;e<r&&n[e]===o[e];e++);var i=r-e;for(t=1;t<=i&&n[r-t]===o[a-t];t++);return Jt=o.slice(e,1<t?1-t:void 0)}function tn(e){var t=e.keyCode;return"charCode"in e?0===(e=e.charCode)&&13===t&&(e=13):e=t,10===e&&(e=13),32<=e||13===e?e:0}function nn(){return!0}function rn(){return!1}function on(e){function t(t,n,r,o,a){for(var i in this._reactName=t,this._targetInst=r,this.type=n,this.nativeEvent=o,this.target=a,this.currentTarget=null,e)e.hasOwnProperty(i)&&(t=e[i],this[i]=t?t(o):o[i]);return this.isDefaultPrevented=(null!=o.defaultPrevented?o.defaultPrevented:!1===o.returnValue)?nn:rn,this.isPropagationStopped=rn,this}return O(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var e=this.nativeEvent;e&&(e.preventDefault?e.preventDefault():"unknown"!==typeof e.returnValue&&(e.returnValue=!1),this.isDefaultPrevented=nn)},stopPropagation:function(){var e=this.nativeEvent;e&&(e.stopPropagation?e.stopPropagation():"unknown"!==typeof e.cancelBubble&&(e.cancelBubble=!0),this.isPropagationStopped=nn)},persist:function(){},isPersistent:nn}),t}var an,ln,sn,un={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},cn=on(un),dn=O({},un,{view:0,detail:0}),fn=on(dn),pn=O({},dn,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Cn,button:0,buttons:0,relatedTarget:function(e){return void 0===e.relatedTarget?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==sn&&(sn&&"mousemove"===e.type?(an=e.screenX-sn.screenX,ln=e.screenY-sn.screenY):ln=an=0,sn=e),an)},movementY:function(e){return"movementY"in e?e.movementY:ln}}),hn=on(pn),mn=on(O({},pn,{dataTransfer:0})),vn=on(O({},dn,{relatedTarget:0})),gn=on(O({},un,{animationName:0,elapsedTime:0,pseudoElement:0})),yn=O({},un,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),bn=on(yn),wn=on(O({},un,{data:0})),xn={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},kn={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Sn={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function En(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):!!(e=Sn[e])&&!!t[e]}function Cn(){return En}var Pn=O({},dn,{key:function(e){if(e.key){var t=xn[e.key]||e.key;if("Unidentified"!==t)return t}return"keypress"===e.type?13===(e=tn(e))?"Enter":String.fromCharCode(e):"keydown"===e.type||"keyup"===e.type?kn[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Cn,charCode:function(e){return"keypress"===e.type?tn(e):0},keyCode:function(e){return"keydown"===e.type||"keyup"===e.type?e.keyCode:0},which:function(e){return"keypress"===e.type?tn(e):"keydown"===e.type||"keyup"===e.type?e.keyCode:0}}),Tn=on(Pn),Ln=on(O({},pn,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0})),Mn=on(O({},dn,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Cn})),_n=on(O({},un,{propertyName:0,elapsedTime:0,pseudoElement:0})),Nn=O({},pn,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Vn=on(Nn),Rn=[9,13,27,32],Dn=c&&"CompositionEvent"in window,An=null;c&&"documentMode"in document&&(An=document.documentMode);var zn=c&&"TextEvent"in window&&!An,On=c&&(!Dn||An&&8<An&&11>=An),Fn=String.fromCharCode(32),In=!1;function jn(e,t){switch(e){case"keyup":return-1!==Rn.indexOf(t.keyCode);case"keydown":return 229!==t.keyCode;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Bn(e){return"object"===typeof(e=e.detail)&&"data"in e?e.data:null}var Un=!1;var Hn={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Wn(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return"input"===t?!!Hn[e.type]:"textarea"===t}function $n(e,t,n,r){Pe(r),0<(t=Qr(t,"onChange")).length&&(n=new cn("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Qn=null,Yn=null;function Xn(e){Fr(e,0)}function qn(e){if(Y(xo(e)))return e}function Kn(e,t){if("change"===e)return t}var Gn=!1;if(c){var Zn;if(c){var Jn="oninput"in document;if(!Jn){var er=document.createElement("div");er.setAttribute("oninput","return;"),Jn="function"===typeof er.oninput}Zn=Jn}else Zn=!1;Gn=Zn&&(!document.documentMode||9<document.documentMode)}function tr(){Qn&&(Qn.detachEvent("onpropertychange",nr),Yn=Qn=null)}function nr(e){if("value"===e.propertyName&&qn(Yn)){var t=[];$n(t,Yn,e,xe(e)),Ne(Xn,t)}}function rr(e,t,n){"focusin"===e?(tr(),Yn=n,(Qn=t).attachEvent("onpropertychange",nr)):"focusout"===e&&tr()}function or(e){if("selectionchange"===e||"keyup"===e||"keydown"===e)return qn(Yn)}function ar(e,t){if("click"===e)return qn(t)}function ir(e,t){if("input"===e||"change"===e)return qn(t)}var lr="function"===typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e===1/t)||e!==e&&t!==t};function sr(e,t){if(lr(e,t))return!0;if("object"!==typeof e||null===e||"object"!==typeof t||null===t)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var o=n[r];if(!d.call(t,o)||!lr(e[o],t[o]))return!1}return!0}function ur(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function cr(e,t){var n,r=ur(e);for(e=0;r;){if(3===r.nodeType){if(n=e+r.textContent.length,e<=t&&n>=t)return{node:r,offset:t-e};e=n}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=ur(r)}}function dr(e,t){return!(!e||!t)&&(e===t||(!e||3!==e.nodeType)&&(t&&3===t.nodeType?dr(e,t.parentNode):"contains"in e?e.contains(t):!!e.compareDocumentPosition&&!!(16&e.compareDocumentPosition(t))))}function fr(){for(var e=window,t=X();t instanceof e.HTMLIFrameElement;){try{var n="string"===typeof t.contentWindow.location.href}catch(r){n=!1}if(!n)break;t=X((e=t.contentWindow).document)}return t}function pr(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&("input"===t&&("text"===e.type||"search"===e.type||"tel"===e.type||"url"===e.type||"password"===e.type)||"textarea"===t||"true"===e.contentEditable)}function hr(e){var t=fr(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&dr(n.ownerDocument.documentElement,n)){if(null!==r&&pr(n))if(t=r.start,void 0===(e=r.end)&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if((e=(t=n.ownerDocument||document)&&t.defaultView||window).getSelection){e=e.getSelection();var o=n.textContent.length,a=Math.min(r.start,o);r=void 0===r.end?a:Math.min(r.end,o),!e.extend&&a>r&&(o=r,r=a,a=o),o=cr(n,a);var i=cr(n,r);o&&i&&(1!==e.rangeCount||e.anchorNode!==o.node||e.anchorOffset!==o.offset||e.focusNode!==i.node||e.focusOffset!==i.offset)&&((t=t.createRange()).setStart(o.node,o.offset),e.removeAllRanges(),a>r?(e.addRange(t),e.extend(i.node,i.offset)):(t.setEnd(i.node,i.offset),e.addRange(t)))}for(t=[],e=n;e=e.parentNode;)1===e.nodeType&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for("function"===typeof n.focus&&n.focus(),n=0;n<t.length;n++)(e=t[n]).element.scrollLeft=e.left,e.element.scrollTop=e.top}}var mr=c&&"documentMode"in document&&11>=document.documentMode,vr=null,gr=null,yr=null,br=!1;function wr(e,t,n){var r=n.window===n?n.document:9===n.nodeType?n:n.ownerDocument;br||null==vr||vr!==X(r)||("selectionStart"in(r=vr)&&pr(r)?r={start:r.selectionStart,end:r.selectionEnd}:r={anchorNode:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection()).anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset},yr&&sr(yr,r)||(yr=r,0<(r=Qr(gr,"onSelect")).length&&(t=new cn("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=vr)))}function xr(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var kr={animationend:xr("Animation","AnimationEnd"),animationiteration:xr("Animation","AnimationIteration"),animationstart:xr("Animation","AnimationStart"),transitionend:xr("Transition","TransitionEnd")},Sr={},Er={};function Cr(e){if(Sr[e])return Sr[e];if(!kr[e])return e;var t,n=kr[e];for(t in n)if(n.hasOwnProperty(t)&&t in Er)return Sr[e]=n[t];return e}c&&(Er=document.createElement("div").style,"AnimationEvent"in window||(delete kr.animationend.animation,delete kr.animationiteration.animation,delete kr.animationstart.animation),"TransitionEvent"in window||delete kr.transitionend.transition);var Pr=Cr("animationend"),Tr=Cr("animationiteration"),Lr=Cr("animationstart"),Mr=Cr("transitionend"),_r=new Map,Nr="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Vr(e,t){_r.set(e,t),s(t,[e])}for(var Rr=0;Rr<Nr.length;Rr++){var Dr=Nr[Rr];Vr(Dr.toLowerCase(),"on"+(Dr[0].toUpperCase()+Dr.slice(1)))}Vr(Pr,"onAnimationEnd"),Vr(Tr,"onAnimationIteration"),Vr(Lr,"onAnimationStart"),Vr("dblclick","onDoubleClick"),Vr("focusin","onFocus"),Vr("focusout","onBlur"),Vr(Mr,"onTransitionEnd"),u("onMouseEnter",["mouseout","mouseover"]),u("onMouseLeave",["mouseout","mouseover"]),u("onPointerEnter",["pointerout","pointerover"]),u("onPointerLeave",["pointerout","pointerover"]),s("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),s("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),s("onBeforeInput",["compositionend","keypress","textInput","paste"]),s("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),s("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),s("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Ar="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),zr=new Set("cancel close invalid load scroll toggle".split(" ").concat(Ar));function Or(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,function(e,t,n,r,o,i,l,s,u){if(Be.apply(this,arguments),ze){if(!ze)throw Error(a(198));var c=Oe;ze=!1,Oe=null,Fe||(Fe=!0,Ie=c)}}(r,t,void 0,e),e.currentTarget=null}function Fr(e,t){t=0!==(4&t);for(var n=0;n<e.length;n++){var r=e[n],o=r.event;r=r.listeners;e:{var a=void 0;if(t)for(var i=r.length-1;0<=i;i--){var l=r[i],s=l.instance,u=l.currentTarget;if(l=l.listener,s!==a&&o.isPropagationStopped())break e;Or(o,l,u),a=s}else for(i=0;i<r.length;i++){if(s=(l=r[i]).instance,u=l.currentTarget,l=l.listener,s!==a&&o.isPropagationStopped())break e;Or(o,l,u),a=s}}}if(Fe)throw e=Ie,Fe=!1,Ie=null,e}function Ir(e,t){var n=t[vo];void 0===n&&(n=t[vo]=new Set);var r=e+"__bubble";n.has(r)||(Hr(t,e,2,!1),n.add(r))}function jr(e,t,n){var r=0;t&&(r|=4),Hr(n,e,r,t)}var Br="_reactListening"+Math.random().toString(36).slice(2);function Ur(e){if(!e[Br]){e[Br]=!0,i.forEach((function(t){"selectionchange"!==t&&(zr.has(t)||jr(t,!1,e),jr(t,!0,e))}));var t=9===e.nodeType?e:e.ownerDocument;null===t||t[Br]||(t[Br]=!0,jr("selectionchange",!1,t))}}function Hr(e,t,n,r){switch(Kt(t)){case 1:var o=$t;break;case 4:o=Qt;break;default:o=Yt}n=o.bind(null,t,n,e),o=void 0,!Re||"touchstart"!==t&&"touchmove"!==t&&"wheel"!==t||(o=!0),r?void 0!==o?e.addEventListener(t,n,{capture:!0,passive:o}):e.addEventListener(t,n,!0):void 0!==o?e.addEventListener(t,n,{passive:o}):e.addEventListener(t,n,!1)}function Wr(e,t,n,r,o){var a=r;if(0===(1&t)&&0===(2&t)&&null!==r)e:for(;;){if(null===r)return;var i=r.tag;if(3===i||4===i){var l=r.stateNode.containerInfo;if(l===o||8===l.nodeType&&l.parentNode===o)break;if(4===i)for(i=r.return;null!==i;){var s=i.tag;if((3===s||4===s)&&((s=i.stateNode.containerInfo)===o||8===s.nodeType&&s.parentNode===o))return;i=i.return}for(;null!==l;){if(null===(i=bo(l)))return;if(5===(s=i.tag)||6===s){r=a=i;continue e}l=l.parentNode}}r=r.return}Ne((function(){var r=a,o=xe(n),i=[];e:{var l=_r.get(e);if(void 0!==l){var s=cn,u=e;switch(e){case"keypress":if(0===tn(n))break e;case"keydown":case"keyup":s=Tn;break;case"focusin":u="focus",s=vn;break;case"focusout":u="blur",s=vn;break;case"beforeblur":case"afterblur":s=vn;break;case"click":if(2===n.button)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":s=hn;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":s=mn;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":s=Mn;break;case Pr:case Tr:case Lr:s=gn;break;case Mr:s=_n;break;case"scroll":s=fn;break;case"wheel":s=Vn;break;case"copy":case"cut":case"paste":s=bn;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":s=Ln}var c=0!==(4&t),d=!c&&"scroll"===e,f=c?null!==l?l+"Capture":null:l;c=[];for(var p,h=r;null!==h;){var m=(p=h).stateNode;if(5===p.tag&&null!==m&&(p=m,null!==f&&(null!=(m=Ve(h,f))&&c.push($r(h,m,p)))),d)break;h=h.return}0<c.length&&(l=new s(l,u,null,n,o),i.push({event:l,listeners:c}))}}if(0===(7&t)){if(s="mouseout"===e||"pointerout"===e,(!(l="mouseover"===e||"pointerover"===e)||n===we||!(u=n.relatedTarget||n.fromElement)||!bo(u)&&!u[mo])&&(s||l)&&(l=o.window===o?o:(l=o.ownerDocument)?l.defaultView||l.parentWindow:window,s?(s=r,null!==(u=(u=n.relatedTarget||n.toElement)?bo(u):null)&&(u!==(d=Ue(u))||5!==u.tag&&6!==u.tag)&&(u=null)):(s=null,u=r),s!==u)){if(c=hn,m="onMouseLeave",f="onMouseEnter",h="mouse","pointerout"!==e&&"pointerover"!==e||(c=Ln,m="onPointerLeave",f="onPointerEnter",h="pointer"),d=null==s?l:xo(s),p=null==u?l:xo(u),(l=new c(m,h+"leave",s,n,o)).target=d,l.relatedTarget=p,m=null,bo(o)===r&&((c=new c(f,h+"enter",u,n,o)).target=p,c.relatedTarget=d,m=c),d=m,s&&u)e:{for(f=u,h=0,p=c=s;p;p=Yr(p))h++;for(p=0,m=f;m;m=Yr(m))p++;for(;0<h-p;)c=Yr(c),h--;for(;0<p-h;)f=Yr(f),p--;for(;h--;){if(c===f||null!==f&&c===f.alternate)break e;c=Yr(c),f=Yr(f)}c=null}else c=null;null!==s&&Xr(i,l,s,c,!1),null!==u&&null!==d&&Xr(i,d,u,c,!0)}if("select"===(s=(l=r?xo(r):window).nodeName&&l.nodeName.toLowerCase())||"input"===s&&"file"===l.type)var v=Kn;else if(Wn(l))if(Gn)v=ir;else{v=or;var g=rr}else(s=l.nodeName)&&"input"===s.toLowerCase()&&("checkbox"===l.type||"radio"===l.type)&&(v=ar);switch(v&&(v=v(e,r))?$n(i,v,n,o):(g&&g(e,l,r),"focusout"===e&&(g=l._wrapperState)&&g.controlled&&"number"===l.type&&ee(l,"number",l.value)),g=r?xo(r):window,e){case"focusin":(Wn(g)||"true"===g.contentEditable)&&(vr=g,gr=r,yr=null);break;case"focusout":yr=gr=vr=null;break;case"mousedown":br=!0;break;case"contextmenu":case"mouseup":case"dragend":br=!1,wr(i,n,o);break;case"selectionchange":if(mr)break;case"keydown":case"keyup":wr(i,n,o)}var y;if(Dn)e:{switch(e){case"compositionstart":var b="onCompositionStart";break e;case"compositionend":b="onCompositionEnd";break e;case"compositionupdate":b="onCompositionUpdate";break e}b=void 0}else Un?jn(e,n)&&(b="onCompositionEnd"):"keydown"===e&&229===n.keyCode&&(b="onCompositionStart");b&&(On&&"ko"!==n.locale&&(Un||"onCompositionStart"!==b?"onCompositionEnd"===b&&Un&&(y=en()):(Zt="value"in(Gt=o)?Gt.value:Gt.textContent,Un=!0)),0<(g=Qr(r,b)).length&&(b=new wn(b,e,null,n,o),i.push({event:b,listeners:g}),y?b.data=y:null!==(y=Bn(n))&&(b.data=y))),(y=zn?function(e,t){switch(e){case"compositionend":return Bn(t);case"keypress":return 32!==t.which?null:(In=!0,Fn);case"textInput":return(e=t.data)===Fn&&In?null:e;default:return null}}(e,n):function(e,t){if(Un)return"compositionend"===e||!Dn&&jn(e,t)?(e=en(),Jt=Zt=Gt=null,Un=!1,e):null;switch(e){case"paste":default:return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return On&&"ko"!==t.locale?null:t.data}}(e,n))&&(0<(r=Qr(r,"onBeforeInput")).length&&(o=new wn("onBeforeInput","beforeinput",null,n,o),i.push({event:o,listeners:r}),o.data=y))}Fr(i,t)}))}function $r(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Qr(e,t){for(var n=t+"Capture",r=[];null!==e;){var o=e,a=o.stateNode;5===o.tag&&null!==a&&(o=a,null!=(a=Ve(e,n))&&r.unshift($r(e,a,o)),null!=(a=Ve(e,t))&&r.push($r(e,a,o))),e=e.return}return r}function Yr(e){if(null===e)return null;do{e=e.return}while(e&&5!==e.tag);return e||null}function Xr(e,t,n,r,o){for(var a=t._reactName,i=[];null!==n&&n!==r;){var l=n,s=l.alternate,u=l.stateNode;if(null!==s&&s===r)break;5===l.tag&&null!==u&&(l=u,o?null!=(s=Ve(n,a))&&i.unshift($r(n,s,l)):o||null!=(s=Ve(n,a))&&i.push($r(n,s,l))),n=n.return}0!==i.length&&e.push({event:t,listeners:i})}var qr=/\r\n?/g,Kr=/\u0000|\uFFFD/g;function Gr(e){return("string"===typeof e?e:""+e).replace(qr,"\n").replace(Kr,"")}function Zr(e,t,n){if(t=Gr(t),Gr(e)!==t&&n)throw Error(a(425))}function Jr(){}var eo=null,to=null;function no(e,t){return"textarea"===e||"noscript"===e||"string"===typeof t.children||"number"===typeof t.children||"object"===typeof t.dangerouslySetInnerHTML&&null!==t.dangerouslySetInnerHTML&&null!=t.dangerouslySetInnerHTML.__html}var ro="function"===typeof setTimeout?setTimeout:void 0,oo="function"===typeof clearTimeout?clearTimeout:void 0,ao="function"===typeof Promise?Promise:void 0,io="function"===typeof queueMicrotask?queueMicrotask:"undefined"!==typeof ao?function(e){return ao.resolve(null).then(e).catch(lo)}:ro;function lo(e){setTimeout((function(){throw e}))}function so(e,t){var n=t,r=0;do{var o=n.nextSibling;if(e.removeChild(n),o&&8===o.nodeType)if("/$"===(n=o.data)){if(0===r)return e.removeChild(o),void Ut(t);r--}else"$"!==n&&"$?"!==n&&"$!"!==n||r++;n=o}while(n);Ut(t)}function uo(e){for(;null!=e;e=e.nextSibling){var t=e.nodeType;if(1===t||3===t)break;if(8===t){if("$"===(t=e.data)||"$!"===t||"$?"===t)break;if("/$"===t)return null}}return e}function co(e){e=e.previousSibling;for(var t=0;e;){if(8===e.nodeType){var n=e.data;if("$"===n||"$!"===n||"$?"===n){if(0===t)return e;t--}else"/$"===n&&t++}e=e.previousSibling}return null}var fo=Math.random().toString(36).slice(2),po="__reactFiber$"+fo,ho="__reactProps$"+fo,mo="__reactContainer$"+fo,vo="__reactEvents$"+fo,go="__reactListeners$"+fo,yo="__reactHandles$"+fo;function bo(e){var t=e[po];if(t)return t;for(var n=e.parentNode;n;){if(t=n[mo]||n[po]){if(n=t.alternate,null!==t.child||null!==n&&null!==n.child)for(e=co(e);null!==e;){if(n=e[po])return n;e=co(e)}return t}n=(e=n).parentNode}return null}function wo(e){return!(e=e[po]||e[mo])||5!==e.tag&&6!==e.tag&&13!==e.tag&&3!==e.tag?null:e}function xo(e){if(5===e.tag||6===e.tag)return e.stateNode;throw Error(a(33))}function ko(e){return e[ho]||null}var So=[],Eo=-1;function Co(e){return{current:e}}function Po(e){0>Eo||(e.current=So[Eo],So[Eo]=null,Eo--)}function To(e,t){Eo++,So[Eo]=e.current,e.current=t}var Lo={},Mo=Co(Lo),_o=Co(!1),No=Lo;function Vo(e,t){var n=e.type.contextTypes;if(!n)return Lo;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var o,a={};for(o in n)a[o]=t[o];return r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=a),a}function Ro(e){return null!==(e=e.childContextTypes)&&void 0!==e}function Do(){Po(_o),Po(Mo)}function Ao(e,t,n){if(Mo.current!==Lo)throw Error(a(168));To(Mo,t),To(_o,n)}function zo(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,"function"!==typeof r.getChildContext)return n;for(var o in r=r.getChildContext())if(!(o in t))throw Error(a(108,H(e)||"Unknown",o));return O({},n,r)}function Oo(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Lo,No=Mo.current,To(Mo,e),To(_o,_o.current),!0}function Fo(e,t,n){var r=e.stateNode;if(!r)throw Error(a(169));n?(e=zo(e,t,No),r.__reactInternalMemoizedMergedChildContext=e,Po(_o),Po(Mo),To(Mo,e)):Po(_o),To(_o,n)}var Io=null,jo=!1,Bo=!1;function Uo(e){null===Io?Io=[e]:Io.push(e)}function Ho(){if(!Bo&&null!==Io){Bo=!0;var e=0,t=bt;try{var n=Io;for(bt=1;e<n.length;e++){var r=n[e];do{r=r(!0)}while(null!==r)}Io=null,jo=!1}catch(o){throw null!==Io&&(Io=Io.slice(e+1)),Ye(Je,Ho),o}finally{bt=t,Bo=!1}}return null}var Wo=[],$o=0,Qo=null,Yo=0,Xo=[],qo=0,Ko=null,Go=1,Zo="";function Jo(e,t){Wo[$o++]=Yo,Wo[$o++]=Qo,Qo=e,Yo=t}function ea(e,t,n){Xo[qo++]=Go,Xo[qo++]=Zo,Xo[qo++]=Ko,Ko=e;var r=Go;e=Zo;var o=32-it(r)-1;r&=~(1<<o),n+=1;var a=32-it(t)+o;if(30<a){var i=o-o%5;a=(r&(1<<i)-1).toString(32),r>>=i,o-=i,Go=1<<32-it(t)+o|n<<o|r,Zo=a+e}else Go=1<<a|n<<o|r,Zo=e}function ta(e){null!==e.return&&(Jo(e,1),ea(e,1,0))}function na(e){for(;e===Qo;)Qo=Wo[--$o],Wo[$o]=null,Yo=Wo[--$o],Wo[$o]=null;for(;e===Ko;)Ko=Xo[--qo],Xo[qo]=null,Zo=Xo[--qo],Xo[qo]=null,Go=Xo[--qo],Xo[qo]=null}var ra=null,oa=null,aa=!1,ia=null;function la(e,t){var n=Vu(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,null===(t=e.deletions)?(e.deletions=[n],e.flags|=16):t.push(n)}function sa(e,t){switch(e.tag){case 5:var n=e.type;return null!==(t=1!==t.nodeType||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t)&&(e.stateNode=t,ra=e,oa=uo(t.firstChild),!0);case 6:return null!==(t=""===e.pendingProps||3!==t.nodeType?null:t)&&(e.stateNode=t,ra=e,oa=null,!0);case 13:return null!==(t=8!==t.nodeType?null:t)&&(n=null!==Ko?{id:Go,overflow:Zo}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},(n=Vu(18,null,null,0)).stateNode=t,n.return=e,e.child=n,ra=e,oa=null,!0);default:return!1}}function ua(e){return 0!==(1&e.mode)&&0===(128&e.flags)}function ca(e){if(aa){var t=oa;if(t){var n=t;if(!sa(e,t)){if(ua(e))throw Error(a(418));t=uo(n.nextSibling);var r=ra;t&&sa(e,t)?la(r,n):(e.flags=-4097&e.flags|2,aa=!1,ra=e)}}else{if(ua(e))throw Error(a(418));e.flags=-4097&e.flags|2,aa=!1,ra=e}}}function da(e){for(e=e.return;null!==e&&5!==e.tag&&3!==e.tag&&13!==e.tag;)e=e.return;ra=e}function fa(e){if(e!==ra)return!1;if(!aa)return da(e),aa=!0,!1;var t;if((t=3!==e.tag)&&!(t=5!==e.tag)&&(t="head"!==(t=e.type)&&"body"!==t&&!no(e.type,e.memoizedProps)),t&&(t=oa)){if(ua(e))throw pa(),Error(a(418));for(;t;)la(e,t),t=uo(t.nextSibling)}if(da(e),13===e.tag){if(!(e=null!==(e=e.memoizedState)?e.dehydrated:null))throw Error(a(317));e:{for(e=e.nextSibling,t=0;e;){if(8===e.nodeType){var n=e.data;if("/$"===n){if(0===t){oa=uo(e.nextSibling);break e}t--}else"$"!==n&&"$!"!==n&&"$?"!==n||t++}e=e.nextSibling}oa=null}}else oa=ra?uo(e.stateNode.nextSibling):null;return!0}function pa(){for(var e=oa;e;)e=uo(e.nextSibling)}function ha(){oa=ra=null,aa=!1}function ma(e){null===ia?ia=[e]:ia.push(e)}var va=w.ReactCurrentBatchConfig;function ga(e,t){if(e&&e.defaultProps){for(var n in t=O({},t),e=e.defaultProps)void 0===t[n]&&(t[n]=e[n]);return t}return t}var ya=Co(null),ba=null,wa=null,xa=null;function ka(){xa=wa=ba=null}function Sa(e){var t=ya.current;Po(ya),e._currentValue=t}function Ea(e,t,n){for(;null!==e;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,null!==r&&(r.childLanes|=t)):null!==r&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Ca(e,t){ba=e,xa=wa=null,null!==(e=e.dependencies)&&null!==e.firstContext&&(0!==(e.lanes&t)&&(wl=!0),e.firstContext=null)}function Pa(e){var t=e._currentValue;if(xa!==e)if(e={context:e,memoizedValue:t,next:null},null===wa){if(null===ba)throw Error(a(308));wa=e,ba.dependencies={lanes:0,firstContext:e}}else wa=wa.next=e;return t}var Ta=null;function La(e){null===Ta?Ta=[e]:Ta.push(e)}function Ma(e,t,n,r){var o=t.interleaved;return null===o?(n.next=n,La(t)):(n.next=o.next,o.next=n),t.interleaved=n,_a(e,r)}function _a(e,t){e.lanes|=t;var n=e.alternate;for(null!==n&&(n.lanes|=t),n=e,e=e.return;null!==e;)e.childLanes|=t,null!==(n=e.alternate)&&(n.childLanes|=t),n=e,e=e.return;return 3===n.tag?n.stateNode:null}var Na=!1;function Va(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Ra(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Da(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Aa(e,t,n){var r=e.updateQueue;if(null===r)return null;if(r=r.shared,0!==(2&Ms)){var o=r.pending;return null===o?t.next=t:(t.next=o.next,o.next=t),r.pending=t,_a(e,n)}return null===(o=r.interleaved)?(t.next=t,La(r)):(t.next=o.next,o.next=t),r.interleaved=t,_a(e,n)}function za(e,t,n){if(null!==(t=t.updateQueue)&&(t=t.shared,0!==(4194240&n))){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,yt(e,n)}}function Oa(e,t){var n=e.updateQueue,r=e.alternate;if(null!==r&&n===(r=r.updateQueue)){var o=null,a=null;if(null!==(n=n.firstBaseUpdate)){do{var i={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};null===a?o=a=i:a=a.next=i,n=n.next}while(null!==n);null===a?o=a=t:a=a.next=t}else o=a=t;return n={baseState:r.baseState,firstBaseUpdate:o,lastBaseUpdate:a,shared:r.shared,effects:r.effects},void(e.updateQueue=n)}null===(e=n.lastBaseUpdate)?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Fa(e,t,n,r){var o=e.updateQueue;Na=!1;var a=o.firstBaseUpdate,i=o.lastBaseUpdate,l=o.shared.pending;if(null!==l){o.shared.pending=null;var s=l,u=s.next;s.next=null,null===i?a=u:i.next=u,i=s;var c=e.alternate;null!==c&&((l=(c=c.updateQueue).lastBaseUpdate)!==i&&(null===l?c.firstBaseUpdate=u:l.next=u,c.lastBaseUpdate=s))}if(null!==a){var d=o.baseState;for(i=0,c=u=s=null,l=a;;){var f=l.lane,p=l.eventTime;if((r&f)===f){null!==c&&(c=c.next={eventTime:p,lane:0,tag:l.tag,payload:l.payload,callback:l.callback,next:null});e:{var h=e,m=l;switch(f=t,p=n,m.tag){case 1:if("function"===typeof(h=m.payload)){d=h.call(p,d,f);break e}d=h;break e;case 3:h.flags=-65537&h.flags|128;case 0:if(null===(f="function"===typeof(h=m.payload)?h.call(p,d,f):h)||void 0===f)break e;d=O({},d,f);break e;case 2:Na=!0}}null!==l.callback&&0!==l.lane&&(e.flags|=64,null===(f=o.effects)?o.effects=[l]:f.push(l))}else p={eventTime:p,lane:f,tag:l.tag,payload:l.payload,callback:l.callback,next:null},null===c?(u=c=p,s=d):c=c.next=p,i|=f;if(null===(l=l.next)){if(null===(l=o.shared.pending))break;l=(f=l).next,f.next=null,o.lastBaseUpdate=f,o.shared.pending=null}}if(null===c&&(s=d),o.baseState=s,o.firstBaseUpdate=u,o.lastBaseUpdate=c,null!==(t=o.shared.interleaved)){o=t;do{i|=o.lane,o=o.next}while(o!==t)}else null===a&&(o.shared.lanes=0);Os|=i,e.lanes=i,e.memoizedState=d}}function Ia(e,t,n){if(e=t.effects,t.effects=null,null!==e)for(t=0;t<e.length;t++){var r=e[t],o=r.callback;if(null!==o){if(r.callback=null,r=n,"function"!==typeof o)throw Error(a(191,o));o.call(r)}}}var ja=(new r.Component).refs;function Ba(e,t,n,r){n=null===(n=n(r,t=e.memoizedState))||void 0===n?t:O({},t,n),e.memoizedState=n,0===e.lanes&&(e.updateQueue.baseState=n)}var Ua={isMounted:function(e){return!!(e=e._reactInternals)&&Ue(e)===e},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=tu(),o=nu(e),a=Da(r,o);a.payload=t,void 0!==n&&null!==n&&(a.callback=n),null!==(t=Aa(e,a,o))&&(ru(t,e,o,r),za(t,e,o))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=tu(),o=nu(e),a=Da(r,o);a.tag=1,a.payload=t,void 0!==n&&null!==n&&(a.callback=n),null!==(t=Aa(e,a,o))&&(ru(t,e,o,r),za(t,e,o))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=tu(),r=nu(e),o=Da(n,r);o.tag=2,void 0!==t&&null!==t&&(o.callback=t),null!==(t=Aa(e,o,r))&&(ru(t,e,r,n),za(t,e,r))}};function Ha(e,t,n,r,o,a,i){return"function"===typeof(e=e.stateNode).shouldComponentUpdate?e.shouldComponentUpdate(r,a,i):!t.prototype||!t.prototype.isPureReactComponent||(!sr(n,r)||!sr(o,a))}function Wa(e,t,n){var r=!1,o=Lo,a=t.contextType;return"object"===typeof a&&null!==a?a=Pa(a):(o=Ro(t)?No:Mo.current,a=(r=null!==(r=t.contextTypes)&&void 0!==r)?Vo(e,o):Lo),t=new t(n,a),e.memoizedState=null!==t.state&&void 0!==t.state?t.state:null,t.updater=Ua,e.stateNode=t,t._reactInternals=e,r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=o,e.__reactInternalMemoizedMaskedChildContext=a),t}function $a(e,t,n,r){e=t.state,"function"===typeof t.componentWillReceiveProps&&t.componentWillReceiveProps(n,r),"function"===typeof t.UNSAFE_componentWillReceiveProps&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&Ua.enqueueReplaceState(t,t.state,null)}function Qa(e,t,n,r){var o=e.stateNode;o.props=n,o.state=e.memoizedState,o.refs=ja,Va(e);var a=t.contextType;"object"===typeof a&&null!==a?o.context=Pa(a):(a=Ro(t)?No:Mo.current,o.context=Vo(e,a)),o.state=e.memoizedState,"function"===typeof(a=t.getDerivedStateFromProps)&&(Ba(e,t,a,n),o.state=e.memoizedState),"function"===typeof t.getDerivedStateFromProps||"function"===typeof o.getSnapshotBeforeUpdate||"function"!==typeof o.UNSAFE_componentWillMount&&"function"!==typeof o.componentWillMount||(t=o.state,"function"===typeof o.componentWillMount&&o.componentWillMount(),"function"===typeof o.UNSAFE_componentWillMount&&o.UNSAFE_componentWillMount(),t!==o.state&&Ua.enqueueReplaceState(o,o.state,null),Fa(e,n,o,r),o.state=e.memoizedState),"function"===typeof o.componentDidMount&&(e.flags|=4194308)}function Ya(e,t,n){if(null!==(e=n.ref)&&"function"!==typeof e&&"object"!==typeof e){if(n._owner){if(n=n._owner){if(1!==n.tag)throw Error(a(309));var r=n.stateNode}if(!r)throw Error(a(147,e));var o=r,i=""+e;return null!==t&&null!==t.ref&&"function"===typeof t.ref&&t.ref._stringRef===i?t.ref:(t=function(e){var t=o.refs;t===ja&&(t=o.refs={}),null===e?delete t[i]:t[i]=e},t._stringRef=i,t)}if("string"!==typeof e)throw Error(a(284));if(!n._owner)throw Error(a(290,e))}return e}function Xa(e,t){throw e=Object.prototype.toString.call(t),Error(a(31,"[object Object]"===e?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function qa(e){return(0,e._init)(e._payload)}function Ka(e){function t(t,n){if(e){var r=t.deletions;null===r?(t.deletions=[n],t.flags|=16):r.push(n)}}function n(n,r){if(!e)return null;for(;null!==r;)t(n,r),r=r.sibling;return null}function r(e,t){for(e=new Map;null!==t;)null!==t.key?e.set(t.key,t):e.set(t.index,t),t=t.sibling;return e}function o(e,t){return(e=Du(e,t)).index=0,e.sibling=null,e}function i(t,n,r){return t.index=r,e?null!==(r=t.alternate)?(r=r.index)<n?(t.flags|=2,n):r:(t.flags|=2,n):(t.flags|=1048576,n)}function l(t){return e&&null===t.alternate&&(t.flags|=2),t}function s(e,t,n,r){return null===t||6!==t.tag?((t=Fu(n,e.mode,r)).return=e,t):((t=o(t,n)).return=e,t)}function u(e,t,n,r){var a=n.type;return a===S?d(e,t,n.props.children,r,n.key):null!==t&&(t.elementType===a||"object"===typeof a&&null!==a&&a.$$typeof===V&&qa(a)===t.type)?((r=o(t,n.props)).ref=Ya(e,t,n),r.return=e,r):((r=Au(n.type,n.key,n.props,null,e.mode,r)).ref=Ya(e,t,n),r.return=e,r)}function c(e,t,n,r){return null===t||4!==t.tag||t.stateNode.containerInfo!==n.containerInfo||t.stateNode.implementation!==n.implementation?((t=Iu(n,e.mode,r)).return=e,t):((t=o(t,n.children||[])).return=e,t)}function d(e,t,n,r,a){return null===t||7!==t.tag?((t=zu(n,e.mode,r,a)).return=e,t):((t=o(t,n)).return=e,t)}function f(e,t,n){if("string"===typeof t&&""!==t||"number"===typeof t)return(t=Fu(""+t,e.mode,n)).return=e,t;if("object"===typeof t&&null!==t){switch(t.$$typeof){case x:return(n=Au(t.type,t.key,t.props,null,e.mode,n)).ref=Ya(e,null,t),n.return=e,n;case k:return(t=Iu(t,e.mode,n)).return=e,t;case V:return f(e,(0,t._init)(t._payload),n)}if(te(t)||A(t))return(t=zu(t,e.mode,n,null)).return=e,t;Xa(e,t)}return null}function p(e,t,n,r){var o=null!==t?t.key:null;if("string"===typeof n&&""!==n||"number"===typeof n)return null!==o?null:s(e,t,""+n,r);if("object"===typeof n&&null!==n){switch(n.$$typeof){case x:return n.key===o?u(e,t,n,r):null;case k:return n.key===o?c(e,t,n,r):null;case V:return p(e,t,(o=n._init)(n._payload),r)}if(te(n)||A(n))return null!==o?null:d(e,t,n,r,null);Xa(e,n)}return null}function h(e,t,n,r,o){if("string"===typeof r&&""!==r||"number"===typeof r)return s(t,e=e.get(n)||null,""+r,o);if("object"===typeof r&&null!==r){switch(r.$$typeof){case x:return u(t,e=e.get(null===r.key?n:r.key)||null,r,o);case k:return c(t,e=e.get(null===r.key?n:r.key)||null,r,o);case V:return h(e,t,n,(0,r._init)(r._payload),o)}if(te(r)||A(r))return d(t,e=e.get(n)||null,r,o,null);Xa(t,r)}return null}function m(o,a,l,s){for(var u=null,c=null,d=a,m=a=0,v=null;null!==d&&m<l.length;m++){d.index>m?(v=d,d=null):v=d.sibling;var g=p(o,d,l[m],s);if(null===g){null===d&&(d=v);break}e&&d&&null===g.alternate&&t(o,d),a=i(g,a,m),null===c?u=g:c.sibling=g,c=g,d=v}if(m===l.length)return n(o,d),aa&&Jo(o,m),u;if(null===d){for(;m<l.length;m++)null!==(d=f(o,l[m],s))&&(a=i(d,a,m),null===c?u=d:c.sibling=d,c=d);return aa&&Jo(o,m),u}for(d=r(o,d);m<l.length;m++)null!==(v=h(d,o,m,l[m],s))&&(e&&null!==v.alternate&&d.delete(null===v.key?m:v.key),a=i(v,a,m),null===c?u=v:c.sibling=v,c=v);return e&&d.forEach((function(e){return t(o,e)})),aa&&Jo(o,m),u}function v(o,l,s,u){var c=A(s);if("function"!==typeof c)throw Error(a(150));if(null==(s=c.call(s)))throw Error(a(151));for(var d=c=null,m=l,v=l=0,g=null,y=s.next();null!==m&&!y.done;v++,y=s.next()){m.index>v?(g=m,m=null):g=m.sibling;var b=p(o,m,y.value,u);if(null===b){null===m&&(m=g);break}e&&m&&null===b.alternate&&t(o,m),l=i(b,l,v),null===d?c=b:d.sibling=b,d=b,m=g}if(y.done)return n(o,m),aa&&Jo(o,v),c;if(null===m){for(;!y.done;v++,y=s.next())null!==(y=f(o,y.value,u))&&(l=i(y,l,v),null===d?c=y:d.sibling=y,d=y);return aa&&Jo(o,v),c}for(m=r(o,m);!y.done;v++,y=s.next())null!==(y=h(m,o,v,y.value,u))&&(e&&null!==y.alternate&&m.delete(null===y.key?v:y.key),l=i(y,l,v),null===d?c=y:d.sibling=y,d=y);return e&&m.forEach((function(e){return t(o,e)})),aa&&Jo(o,v),c}return function e(r,a,i,s){if("object"===typeof i&&null!==i&&i.type===S&&null===i.key&&(i=i.props.children),"object"===typeof i&&null!==i){switch(i.$$typeof){case x:e:{for(var u=i.key,c=a;null!==c;){if(c.key===u){if((u=i.type)===S){if(7===c.tag){n(r,c.sibling),(a=o(c,i.props.children)).return=r,r=a;break e}}else if(c.elementType===u||"object"===typeof u&&null!==u&&u.$$typeof===V&&qa(u)===c.type){n(r,c.sibling),(a=o(c,i.props)).ref=Ya(r,c,i),a.return=r,r=a;break e}n(r,c);break}t(r,c),c=c.sibling}i.type===S?((a=zu(i.props.children,r.mode,s,i.key)).return=r,r=a):((s=Au(i.type,i.key,i.props,null,r.mode,s)).ref=Ya(r,a,i),s.return=r,r=s)}return l(r);case k:e:{for(c=i.key;null!==a;){if(a.key===c){if(4===a.tag&&a.stateNode.containerInfo===i.containerInfo&&a.stateNode.implementation===i.implementation){n(r,a.sibling),(a=o(a,i.children||[])).return=r,r=a;break e}n(r,a);break}t(r,a),a=a.sibling}(a=Iu(i,r.mode,s)).return=r,r=a}return l(r);case V:return e(r,a,(c=i._init)(i._payload),s)}if(te(i))return m(r,a,i,s);if(A(i))return v(r,a,i,s);Xa(r,i)}return"string"===typeof i&&""!==i||"number"===typeof i?(i=""+i,null!==a&&6===a.tag?(n(r,a.sibling),(a=o(a,i)).return=r,r=a):(n(r,a),(a=Fu(i,r.mode,s)).return=r,r=a),l(r)):n(r,a)}}var Ga=Ka(!0),Za=Ka(!1),Ja={},ei=Co(Ja),ti=Co(Ja),ni=Co(Ja);function ri(e){if(e===Ja)throw Error(a(174));return e}function oi(e,t){switch(To(ni,t),To(ti,e),To(ei,Ja),e=t.nodeType){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:se(null,"");break;default:t=se(t=(e=8===e?t.parentNode:t).namespaceURI||null,e=e.tagName)}Po(ei),To(ei,t)}function ai(){Po(ei),Po(ti),Po(ni)}function ii(e){ri(ni.current);var t=ri(ei.current),n=se(t,e.type);t!==n&&(To(ti,e),To(ei,n))}function li(e){ti.current===e&&(Po(ei),Po(ti))}var si=Co(0);function ui(e){for(var t=e;null!==t;){if(13===t.tag){var n=t.memoizedState;if(null!==n&&(null===(n=n.dehydrated)||"$?"===n.data||"$!"===n.data))return t}else if(19===t.tag&&void 0!==t.memoizedProps.revealOrder){if(0!==(128&t.flags))return t}else if(null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var ci=[];function di(){for(var e=0;e<ci.length;e++)ci[e]._workInProgressVersionPrimary=null;ci.length=0}var fi=w.ReactCurrentDispatcher,pi=w.ReactCurrentBatchConfig,hi=0,mi=null,vi=null,gi=null,yi=!1,bi=!1,wi=0,xi=0;function ki(){throw Error(a(321))}function Si(e,t){if(null===t)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!lr(e[n],t[n]))return!1;return!0}function Ei(e,t,n,r,o,i){if(hi=i,mi=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,fi.current=null===e||null===e.memoizedState?ll:sl,e=n(r,o),bi){i=0;do{if(bi=!1,wi=0,25<=i)throw Error(a(301));i+=1,gi=vi=null,t.updateQueue=null,fi.current=ul,e=n(r,o)}while(bi)}if(fi.current=il,t=null!==vi&&null!==vi.next,hi=0,gi=vi=mi=null,yi=!1,t)throw Error(a(300));return e}function Ci(){var e=0!==wi;return wi=0,e}function Pi(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===gi?mi.memoizedState=gi=e:gi=gi.next=e,gi}function Ti(){if(null===vi){var e=mi.alternate;e=null!==e?e.memoizedState:null}else e=vi.next;var t=null===gi?mi.memoizedState:gi.next;if(null!==t)gi=t,vi=e;else{if(null===e)throw Error(a(310));e={memoizedState:(vi=e).memoizedState,baseState:vi.baseState,baseQueue:vi.baseQueue,queue:vi.queue,next:null},null===gi?mi.memoizedState=gi=e:gi=gi.next=e}return gi}function Li(e,t){return"function"===typeof t?t(e):t}function Mi(e){var t=Ti(),n=t.queue;if(null===n)throw Error(a(311));n.lastRenderedReducer=e;var r=vi,o=r.baseQueue,i=n.pending;if(null!==i){if(null!==o){var l=o.next;o.next=i.next,i.next=l}r.baseQueue=o=i,n.pending=null}if(null!==o){i=o.next,r=r.baseState;var s=l=null,u=null,c=i;do{var d=c.lane;if((hi&d)===d)null!==u&&(u=u.next={lane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),r=c.hasEagerState?c.eagerState:e(r,c.action);else{var f={lane:d,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null};null===u?(s=u=f,l=r):u=u.next=f,mi.lanes|=d,Os|=d}c=c.next}while(null!==c&&c!==i);null===u?l=r:u.next=s,lr(r,t.memoizedState)||(wl=!0),t.memoizedState=r,t.baseState=l,t.baseQueue=u,n.lastRenderedState=r}if(null!==(e=n.interleaved)){o=e;do{i=o.lane,mi.lanes|=i,Os|=i,o=o.next}while(o!==e)}else null===o&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function _i(e){var t=Ti(),n=t.queue;if(null===n)throw Error(a(311));n.lastRenderedReducer=e;var r=n.dispatch,o=n.pending,i=t.memoizedState;if(null!==o){n.pending=null;var l=o=o.next;do{i=e(i,l.action),l=l.next}while(l!==o);lr(i,t.memoizedState)||(wl=!0),t.memoizedState=i,null===t.baseQueue&&(t.baseState=i),n.lastRenderedState=i}return[i,r]}function Ni(){}function Vi(e,t){var n=mi,r=Ti(),o=t(),i=!lr(r.memoizedState,o);if(i&&(r.memoizedState=o,wl=!0),r=r.queue,Wi(Ai.bind(null,n,r,e),[e]),r.getSnapshot!==t||i||null!==gi&&1&gi.memoizedState.tag){if(n.flags|=2048,Ii(9,Di.bind(null,n,r,o,t),void 0,null),null===_s)throw Error(a(349));0!==(30&hi)||Ri(n,t,o)}return o}function Ri(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},null===(t=mi.updateQueue)?(t={lastEffect:null,stores:null},mi.updateQueue=t,t.stores=[e]):null===(n=t.stores)?t.stores=[e]:n.push(e)}function Di(e,t,n,r){t.value=n,t.getSnapshot=r,zi(t)&&Oi(e)}function Ai(e,t,n){return n((function(){zi(t)&&Oi(e)}))}function zi(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!lr(e,n)}catch(r){return!0}}function Oi(e){var t=_a(e,1);null!==t&&ru(t,e,1,-1)}function Fi(e){var t=Pi();return"function"===typeof e&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Li,lastRenderedState:e},t.queue=e,e=e.dispatch=nl.bind(null,mi,e),[t.memoizedState,e]}function Ii(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},null===(t=mi.updateQueue)?(t={lastEffect:null,stores:null},mi.updateQueue=t,t.lastEffect=e.next=e):null===(n=t.lastEffect)?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e),e}function ji(){return Ti().memoizedState}function Bi(e,t,n,r){var o=Pi();mi.flags|=e,o.memoizedState=Ii(1|t,n,void 0,void 0===r?null:r)}function Ui(e,t,n,r){var o=Ti();r=void 0===r?null:r;var a=void 0;if(null!==vi){var i=vi.memoizedState;if(a=i.destroy,null!==r&&Si(r,i.deps))return void(o.memoizedState=Ii(t,n,a,r))}mi.flags|=e,o.memoizedState=Ii(1|t,n,a,r)}function Hi(e,t){return Bi(8390656,8,e,t)}function Wi(e,t){return Ui(2048,8,e,t)}function $i(e,t){return Ui(4,2,e,t)}function Qi(e,t){return Ui(4,4,e,t)}function Yi(e,t){return"function"===typeof t?(e=e(),t(e),function(){t(null)}):null!==t&&void 0!==t?(e=e(),t.current=e,function(){t.current=null}):void 0}function Xi(e,t,n){return n=null!==n&&void 0!==n?n.concat([e]):null,Ui(4,4,Yi.bind(null,t,e),n)}function qi(){}function Ki(e,t){var n=Ti();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&Si(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Gi(e,t){var n=Ti();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&Si(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Zi(e,t,n){return 0===(21&hi)?(e.baseState&&(e.baseState=!1,wl=!0),e.memoizedState=n):(lr(n,t)||(n=mt(),mi.lanes|=n,Os|=n,e.baseState=!0),t)}function Ji(e,t){var n=bt;bt=0!==n&&4>n?n:4,e(!0);var r=pi.transition;pi.transition={};try{e(!1),t()}finally{bt=n,pi.transition=r}}function el(){return Ti().memoizedState}function tl(e,t,n){var r=nu(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},rl(e))ol(t,n);else if(null!==(n=Ma(e,t,n,r))){ru(n,e,r,tu()),al(n,t,r)}}function nl(e,t,n){var r=nu(e),o={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(rl(e))ol(t,o);else{var a=e.alternate;if(0===e.lanes&&(null===a||0===a.lanes)&&null!==(a=t.lastRenderedReducer))try{var i=t.lastRenderedState,l=a(i,n);if(o.hasEagerState=!0,o.eagerState=l,lr(l,i)){var s=t.interleaved;return null===s?(o.next=o,La(t)):(o.next=s.next,s.next=o),void(t.interleaved=o)}}catch(u){}null!==(n=Ma(e,t,o,r))&&(ru(n,e,r,o=tu()),al(n,t,r))}}function rl(e){var t=e.alternate;return e===mi||null!==t&&t===mi}function ol(e,t){bi=yi=!0;var n=e.pending;null===n?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function al(e,t,n){if(0!==(4194240&n)){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,yt(e,n)}}var il={readContext:Pa,useCallback:ki,useContext:ki,useEffect:ki,useImperativeHandle:ki,useInsertionEffect:ki,useLayoutEffect:ki,useMemo:ki,useReducer:ki,useRef:ki,useState:ki,useDebugValue:ki,useDeferredValue:ki,useTransition:ki,useMutableSource:ki,useSyncExternalStore:ki,useId:ki,unstable_isNewReconciler:!1},ll={readContext:Pa,useCallback:function(e,t){return Pi().memoizedState=[e,void 0===t?null:t],e},useContext:Pa,useEffect:Hi,useImperativeHandle:function(e,t,n){return n=null!==n&&void 0!==n?n.concat([e]):null,Bi(4194308,4,Yi.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Bi(4194308,4,e,t)},useInsertionEffect:function(e,t){return Bi(4,2,e,t)},useMemo:function(e,t){var n=Pi();return t=void 0===t?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=Pi();return t=void 0!==n?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=tl.bind(null,mi,e),[r.memoizedState,e]},useRef:function(e){return e={current:e},Pi().memoizedState=e},useState:Fi,useDebugValue:qi,useDeferredValue:function(e){return Pi().memoizedState=e},useTransition:function(){var e=Fi(!1),t=e[0];return e=Ji.bind(null,e[1]),Pi().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=mi,o=Pi();if(aa){if(void 0===n)throw Error(a(407));n=n()}else{if(n=t(),null===_s)throw Error(a(349));0!==(30&hi)||Ri(r,t,n)}o.memoizedState=n;var i={value:n,getSnapshot:t};return o.queue=i,Hi(Ai.bind(null,r,i,e),[e]),r.flags|=2048,Ii(9,Di.bind(null,r,i,n,t),void 0,null),n},useId:function(){var e=Pi(),t=_s.identifierPrefix;if(aa){var n=Zo;t=":"+t+"R"+(n=(Go&~(1<<32-it(Go)-1)).toString(32)+n),0<(n=wi++)&&(t+="H"+n.toString(32)),t+=":"}else t=":"+t+"r"+(n=xi++).toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},sl={readContext:Pa,useCallback:Ki,useContext:Pa,useEffect:Wi,useImperativeHandle:Xi,useInsertionEffect:$i,useLayoutEffect:Qi,useMemo:Gi,useReducer:Mi,useRef:ji,useState:function(){return Mi(Li)},useDebugValue:qi,useDeferredValue:function(e){return Zi(Ti(),vi.memoizedState,e)},useTransition:function(){return[Mi(Li)[0],Ti().memoizedState]},useMutableSource:Ni,useSyncExternalStore:Vi,useId:el,unstable_isNewReconciler:!1},ul={readContext:Pa,useCallback:Ki,useContext:Pa,useEffect:Wi,useImperativeHandle:Xi,useInsertionEffect:$i,useLayoutEffect:Qi,useMemo:Gi,useReducer:_i,useRef:ji,useState:function(){return _i(Li)},useDebugValue:qi,useDeferredValue:function(e){var t=Ti();return null===vi?t.memoizedState=e:Zi(t,vi.memoizedState,e)},useTransition:function(){return[_i(Li)[0],Ti().memoizedState]},useMutableSource:Ni,useSyncExternalStore:Vi,useId:el,unstable_isNewReconciler:!1};function cl(e,t){try{var n="",r=t;do{n+=B(r),r=r.return}while(r);var o=n}catch(a){o="\nError generating stack: "+a.message+"\n"+a.stack}return{value:e,source:t,stack:o,digest:null}}function dl(e,t,n){return{value:e,source:null,stack:null!=n?n:null,digest:null!=t?t:null}}function fl(e,t){try{console.error(t.value)}catch(n){setTimeout((function(){throw n}))}}var pl="function"===typeof WeakMap?WeakMap:Map;function hl(e,t,n){(n=Da(-1,n)).tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){$s||($s=!0,Qs=r),fl(0,t)},n}function ml(e,t,n){(n=Da(-1,n)).tag=3;var r=e.type.getDerivedStateFromError;if("function"===typeof r){var o=t.value;n.payload=function(){return r(o)},n.callback=function(){fl(0,t)}}var a=e.stateNode;return null!==a&&"function"===typeof a.componentDidCatch&&(n.callback=function(){fl(0,t),"function"!==typeof r&&(null===Ys?Ys=new Set([this]):Ys.add(this));var e=t.stack;this.componentDidCatch(t.value,{componentStack:null!==e?e:""})}),n}function vl(e,t,n){var r=e.pingCache;if(null===r){r=e.pingCache=new pl;var o=new Set;r.set(t,o)}else void 0===(o=r.get(t))&&(o=new Set,r.set(t,o));o.has(n)||(o.add(n),e=Pu.bind(null,e,t,n),t.then(e,e))}function gl(e){do{var t;if((t=13===e.tag)&&(t=null===(t=e.memoizedState)||null!==t.dehydrated),t)return e;e=e.return}while(null!==e);return null}function yl(e,t,n,r,o){return 0===(1&e.mode)?(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,1===n.tag&&(null===n.alternate?n.tag=17:((t=Da(-1,1)).tag=2,Aa(n,t,1))),n.lanes|=1),e):(e.flags|=65536,e.lanes=o,e)}var bl=w.ReactCurrentOwner,wl=!1;function xl(e,t,n,r){t.child=null===e?Za(t,null,n,r):Ga(t,e.child,n,r)}function kl(e,t,n,r,o){n=n.render;var a=t.ref;return Ca(t,o),r=Ei(e,t,n,r,a,o),n=Ci(),null===e||wl?(aa&&n&&ta(t),t.flags|=1,xl(e,t,r,o),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,$l(e,t,o))}function Sl(e,t,n,r,o){if(null===e){var a=n.type;return"function"!==typeof a||Ru(a)||void 0!==a.defaultProps||null!==n.compare||void 0!==n.defaultProps?((e=Au(n.type,null,r,t,t.mode,o)).ref=t.ref,e.return=t,t.child=e):(t.tag=15,t.type=a,El(e,t,a,r,o))}if(a=e.child,0===(e.lanes&o)){var i=a.memoizedProps;if((n=null!==(n=n.compare)?n:sr)(i,r)&&e.ref===t.ref)return $l(e,t,o)}return t.flags|=1,(e=Du(a,r)).ref=t.ref,e.return=t,t.child=e}function El(e,t,n,r,o){if(null!==e){var a=e.memoizedProps;if(sr(a,r)&&e.ref===t.ref){if(wl=!1,t.pendingProps=r=a,0===(e.lanes&o))return t.lanes=e.lanes,$l(e,t,o);0!==(131072&e.flags)&&(wl=!0)}}return Tl(e,t,n,r,o)}function Cl(e,t,n){var r=t.pendingProps,o=r.children,a=null!==e?e.memoizedState:null;if("hidden"===r.mode)if(0===(1&t.mode))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},To(Ds,Rs),Rs|=n;else{if(0===(1073741824&n))return e=null!==a?a.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,To(Ds,Rs),Rs|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=null!==a?a.baseLanes:n,To(Ds,Rs),Rs|=r}else null!==a?(r=a.baseLanes|n,t.memoizedState=null):r=n,To(Ds,Rs),Rs|=r;return xl(e,t,o,n),t.child}function Pl(e,t){var n=t.ref;(null===e&&null!==n||null!==e&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Tl(e,t,n,r,o){var a=Ro(n)?No:Mo.current;return a=Vo(t,a),Ca(t,o),n=Ei(e,t,n,r,a,o),r=Ci(),null===e||wl?(aa&&r&&ta(t),t.flags|=1,xl(e,t,n,o),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,$l(e,t,o))}function Ll(e,t,n,r,o){if(Ro(n)){var a=!0;Oo(t)}else a=!1;if(Ca(t,o),null===t.stateNode)Wl(e,t),Wa(t,n,r),Qa(t,n,r,o),r=!0;else if(null===e){var i=t.stateNode,l=t.memoizedProps;i.props=l;var s=i.context,u=n.contextType;"object"===typeof u&&null!==u?u=Pa(u):u=Vo(t,u=Ro(n)?No:Mo.current);var c=n.getDerivedStateFromProps,d="function"===typeof c||"function"===typeof i.getSnapshotBeforeUpdate;d||"function"!==typeof i.UNSAFE_componentWillReceiveProps&&"function"!==typeof i.componentWillReceiveProps||(l!==r||s!==u)&&$a(t,i,r,u),Na=!1;var f=t.memoizedState;i.state=f,Fa(t,r,i,o),s=t.memoizedState,l!==r||f!==s||_o.current||Na?("function"===typeof c&&(Ba(t,n,c,r),s=t.memoizedState),(l=Na||Ha(t,n,l,r,f,s,u))?(d||"function"!==typeof i.UNSAFE_componentWillMount&&"function"!==typeof i.componentWillMount||("function"===typeof i.componentWillMount&&i.componentWillMount(),"function"===typeof i.UNSAFE_componentWillMount&&i.UNSAFE_componentWillMount()),"function"===typeof i.componentDidMount&&(t.flags|=4194308)):("function"===typeof i.componentDidMount&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=s),i.props=r,i.state=s,i.context=u,r=l):("function"===typeof i.componentDidMount&&(t.flags|=4194308),r=!1)}else{i=t.stateNode,Ra(e,t),l=t.memoizedProps,u=t.type===t.elementType?l:ga(t.type,l),i.props=u,d=t.pendingProps,f=i.context,"object"===typeof(s=n.contextType)&&null!==s?s=Pa(s):s=Vo(t,s=Ro(n)?No:Mo.current);var p=n.getDerivedStateFromProps;(c="function"===typeof p||"function"===typeof i.getSnapshotBeforeUpdate)||"function"!==typeof i.UNSAFE_componentWillReceiveProps&&"function"!==typeof i.componentWillReceiveProps||(l!==d||f!==s)&&$a(t,i,r,s),Na=!1,f=t.memoizedState,i.state=f,Fa(t,r,i,o);var h=t.memoizedState;l!==d||f!==h||_o.current||Na?("function"===typeof p&&(Ba(t,n,p,r),h=t.memoizedState),(u=Na||Ha(t,n,u,r,f,h,s)||!1)?(c||"function"!==typeof i.UNSAFE_componentWillUpdate&&"function"!==typeof i.componentWillUpdate||("function"===typeof i.componentWillUpdate&&i.componentWillUpdate(r,h,s),"function"===typeof i.UNSAFE_componentWillUpdate&&i.UNSAFE_componentWillUpdate(r,h,s)),"function"===typeof i.componentDidUpdate&&(t.flags|=4),"function"===typeof i.getSnapshotBeforeUpdate&&(t.flags|=1024)):("function"!==typeof i.componentDidUpdate||l===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!==typeof i.getSnapshotBeforeUpdate||l===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=h),i.props=r,i.state=h,i.context=s,r=u):("function"!==typeof i.componentDidUpdate||l===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!==typeof i.getSnapshotBeforeUpdate||l===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),r=!1)}return Ml(e,t,n,r,a,o)}function Ml(e,t,n,r,o,a){Pl(e,t);var i=0!==(128&t.flags);if(!r&&!i)return o&&Fo(t,n,!1),$l(e,t,a);r=t.stateNode,bl.current=t;var l=i&&"function"!==typeof n.getDerivedStateFromError?null:r.render();return t.flags|=1,null!==e&&i?(t.child=Ga(t,e.child,null,a),t.child=Ga(t,null,l,a)):xl(e,t,l,a),t.memoizedState=r.state,o&&Fo(t,n,!0),t.child}function _l(e){var t=e.stateNode;t.pendingContext?Ao(0,t.pendingContext,t.pendingContext!==t.context):t.context&&Ao(0,t.context,!1),oi(e,t.containerInfo)}function Nl(e,t,n,r,o){return ha(),ma(o),t.flags|=256,xl(e,t,n,r),t.child}var Vl,Rl,Dl,Al,zl={dehydrated:null,treeContext:null,retryLane:0};function Ol(e){return{baseLanes:e,cachePool:null,transitions:null}}function Fl(e,t,n){var r,o=t.pendingProps,i=si.current,l=!1,s=0!==(128&t.flags);if((r=s)||(r=(null===e||null!==e.memoizedState)&&0!==(2&i)),r?(l=!0,t.flags&=-129):null!==e&&null===e.memoizedState||(i|=1),To(si,1&i),null===e)return ca(t),null!==(e=t.memoizedState)&&null!==(e=e.dehydrated)?(0===(1&t.mode)?t.lanes=1:"$!"===e.data?t.lanes=8:t.lanes=1073741824,null):(s=o.children,e=o.fallback,l?(o=t.mode,l=t.child,s={mode:"hidden",children:s},0===(1&o)&&null!==l?(l.childLanes=0,l.pendingProps=s):l=Ou(s,o,0,null),e=zu(e,o,n,null),l.return=t,e.return=t,l.sibling=e,t.child=l,t.child.memoizedState=Ol(n),t.memoizedState=zl,e):Il(t,s));if(null!==(i=e.memoizedState)&&null!==(r=i.dehydrated))return function(e,t,n,r,o,i,l){if(n)return 256&t.flags?(t.flags&=-257,jl(e,t,l,r=dl(Error(a(422))))):null!==t.memoizedState?(t.child=e.child,t.flags|=128,null):(i=r.fallback,o=t.mode,r=Ou({mode:"visible",children:r.children},o,0,null),(i=zu(i,o,l,null)).flags|=2,r.return=t,i.return=t,r.sibling=i,t.child=r,0!==(1&t.mode)&&Ga(t,e.child,null,l),t.child.memoizedState=Ol(l),t.memoizedState=zl,i);if(0===(1&t.mode))return jl(e,t,l,null);if("$!"===o.data){if(r=o.nextSibling&&o.nextSibling.dataset)var s=r.dgst;return r=s,jl(e,t,l,r=dl(i=Error(a(419)),r,void 0))}if(s=0!==(l&e.childLanes),wl||s){if(null!==(r=_s)){switch(l&-l){case 4:o=2;break;case 16:o=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:o=32;break;case 536870912:o=268435456;break;default:o=0}0!==(o=0!==(o&(r.suspendedLanes|l))?0:o)&&o!==i.retryLane&&(i.retryLane=o,_a(e,o),ru(r,e,o,-1))}return vu(),jl(e,t,l,r=dl(Error(a(421))))}return"$?"===o.data?(t.flags|=128,t.child=e.child,t=Lu.bind(null,e),o._reactRetry=t,null):(e=i.treeContext,oa=uo(o.nextSibling),ra=t,aa=!0,ia=null,null!==e&&(Xo[qo++]=Go,Xo[qo++]=Zo,Xo[qo++]=Ko,Go=e.id,Zo=e.overflow,Ko=t),t=Il(t,r.children),t.flags|=4096,t)}(e,t,s,o,r,i,n);if(l){l=o.fallback,s=t.mode,r=(i=e.child).sibling;var u={mode:"hidden",children:o.children};return 0===(1&s)&&t.child!==i?((o=t.child).childLanes=0,o.pendingProps=u,t.deletions=null):(o=Du(i,u)).subtreeFlags=14680064&i.subtreeFlags,null!==r?l=Du(r,l):(l=zu(l,s,n,null)).flags|=2,l.return=t,o.return=t,o.sibling=l,t.child=o,o=l,l=t.child,s=null===(s=e.child.memoizedState)?Ol(n):{baseLanes:s.baseLanes|n,cachePool:null,transitions:s.transitions},l.memoizedState=s,l.childLanes=e.childLanes&~n,t.memoizedState=zl,o}return e=(l=e.child).sibling,o=Du(l,{mode:"visible",children:o.children}),0===(1&t.mode)&&(o.lanes=n),o.return=t,o.sibling=null,null!==e&&(null===(n=t.deletions)?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=o,t.memoizedState=null,o}function Il(e,t){return(t=Ou({mode:"visible",children:t},e.mode,0,null)).return=e,e.child=t}function jl(e,t,n,r){return null!==r&&ma(r),Ga(t,e.child,null,n),(e=Il(t,t.pendingProps.children)).flags|=2,t.memoizedState=null,e}function Bl(e,t,n){e.lanes|=t;var r=e.alternate;null!==r&&(r.lanes|=t),Ea(e.return,t,n)}function Ul(e,t,n,r,o){var a=e.memoizedState;null===a?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:o}:(a.isBackwards=t,a.rendering=null,a.renderingStartTime=0,a.last=r,a.tail=n,a.tailMode=o)}function Hl(e,t,n){var r=t.pendingProps,o=r.revealOrder,a=r.tail;if(xl(e,t,r.children,n),0!==(2&(r=si.current)))r=1&r|2,t.flags|=128;else{if(null!==e&&0!==(128&e.flags))e:for(e=t.child;null!==e;){if(13===e.tag)null!==e.memoizedState&&Bl(e,n,t);else if(19===e.tag)Bl(e,n,t);else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;null===e.sibling;){if(null===e.return||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(To(si,r),0===(1&t.mode))t.memoizedState=null;else switch(o){case"forwards":for(n=t.child,o=null;null!==n;)null!==(e=n.alternate)&&null===ui(e)&&(o=n),n=n.sibling;null===(n=o)?(o=t.child,t.child=null):(o=n.sibling,n.sibling=null),Ul(t,!1,o,n,a);break;case"backwards":for(n=null,o=t.child,t.child=null;null!==o;){if(null!==(e=o.alternate)&&null===ui(e)){t.child=o;break}e=o.sibling,o.sibling=n,n=o,o=e}Ul(t,!0,n,null,a);break;case"together":Ul(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Wl(e,t){0===(1&t.mode)&&null!==e&&(e.alternate=null,t.alternate=null,t.flags|=2)}function $l(e,t,n){if(null!==e&&(t.dependencies=e.dependencies),Os|=t.lanes,0===(n&t.childLanes))return null;if(null!==e&&t.child!==e.child)throw Error(a(153));if(null!==t.child){for(n=Du(e=t.child,e.pendingProps),t.child=n,n.return=t;null!==e.sibling;)e=e.sibling,(n=n.sibling=Du(e,e.pendingProps)).return=t;n.sibling=null}return t.child}function Ql(e,t){if(!aa)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;null!==t;)null!==t.alternate&&(n=t),t=t.sibling;null===n?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;null!==n;)null!==n.alternate&&(r=n),n=n.sibling;null===r?t||null===e.tail?e.tail=null:e.tail.sibling=null:r.sibling=null}}function Yl(e){var t=null!==e.alternate&&e.alternate.child===e.child,n=0,r=0;if(t)for(var o=e.child;null!==o;)n|=o.lanes|o.childLanes,r|=14680064&o.subtreeFlags,r|=14680064&o.flags,o.return=e,o=o.sibling;else for(o=e.child;null!==o;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags,r|=o.flags,o.return=e,o=o.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function Xl(e,t,n){var r=t.pendingProps;switch(na(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Yl(t),null;case 1:case 17:return Ro(t.type)&&Do(),Yl(t),null;case 3:return r=t.stateNode,ai(),Po(_o),Po(Mo),di(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),null!==e&&null!==e.child||(fa(t)?t.flags|=4:null===e||e.memoizedState.isDehydrated&&0===(256&t.flags)||(t.flags|=1024,null!==ia&&(lu(ia),ia=null))),Rl(e,t),Yl(t),null;case 5:li(t);var o=ri(ni.current);if(n=t.type,null!==e&&null!=t.stateNode)Dl(e,t,n,r,o),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(null===t.stateNode)throw Error(a(166));return Yl(t),null}if(e=ri(ei.current),fa(t)){r=t.stateNode,n=t.type;var i=t.memoizedProps;switch(r[po]=t,r[ho]=i,e=0!==(1&t.mode),n){case"dialog":Ir("cancel",r),Ir("close",r);break;case"iframe":case"object":case"embed":Ir("load",r);break;case"video":case"audio":for(o=0;o<Ar.length;o++)Ir(Ar[o],r);break;case"source":Ir("error",r);break;case"img":case"image":case"link":Ir("error",r),Ir("load",r);break;case"details":Ir("toggle",r);break;case"input":K(r,i),Ir("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!i.multiple},Ir("invalid",r);break;case"textarea":oe(r,i),Ir("invalid",r)}for(var s in ye(n,i),o=null,i)if(i.hasOwnProperty(s)){var u=i[s];"children"===s?"string"===typeof u?r.textContent!==u&&(!0!==i.suppressHydrationWarning&&Zr(r.textContent,u,e),o=["children",u]):"number"===typeof u&&r.textContent!==""+u&&(!0!==i.suppressHydrationWarning&&Zr(r.textContent,u,e),o=["children",""+u]):l.hasOwnProperty(s)&&null!=u&&"onScroll"===s&&Ir("scroll",r)}switch(n){case"input":Q(r),J(r,i,!0);break;case"textarea":Q(r),ie(r);break;case"select":case"option":break;default:"function"===typeof i.onClick&&(r.onclick=Jr)}r=o,t.updateQueue=r,null!==r&&(t.flags|=4)}else{s=9===o.nodeType?o:o.ownerDocument,"http://www.w3.org/1999/xhtml"===e&&(e=le(n)),"http://www.w3.org/1999/xhtml"===e?"script"===n?((e=s.createElement("div")).innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):"string"===typeof r.is?e=s.createElement(n,{is:r.is}):(e=s.createElement(n),"select"===n&&(s=e,r.multiple?s.multiple=!0:r.size&&(s.size=r.size))):e=s.createElementNS(e,n),e[po]=t,e[ho]=r,Vl(e,t,!1,!1),t.stateNode=e;e:{switch(s=be(n,r),n){case"dialog":Ir("cancel",e),Ir("close",e),o=r;break;case"iframe":case"object":case"embed":Ir("load",e),o=r;break;case"video":case"audio":for(o=0;o<Ar.length;o++)Ir(Ar[o],e);o=r;break;case"source":Ir("error",e),o=r;break;case"img":case"image":case"link":Ir("error",e),Ir("load",e),o=r;break;case"details":Ir("toggle",e),o=r;break;case"input":K(e,r),o=q(e,r),Ir("invalid",e);break;case"option":default:o=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},o=O({},r,{value:void 0}),Ir("invalid",e);break;case"textarea":oe(e,r),o=re(e,r),Ir("invalid",e)}for(i in ye(n,o),u=o)if(u.hasOwnProperty(i)){var c=u[i];"style"===i?ve(e,c):"dangerouslySetInnerHTML"===i?null!=(c=c?c.__html:void 0)&&de(e,c):"children"===i?"string"===typeof c?("textarea"!==n||""!==c)&&fe(e,c):"number"===typeof c&&fe(e,""+c):"suppressContentEditableWarning"!==i&&"suppressHydrationWarning"!==i&&"autoFocus"!==i&&(l.hasOwnProperty(i)?null!=c&&"onScroll"===i&&Ir("scroll",e):null!=c&&b(e,i,c,s))}switch(n){case"input":Q(e),J(e,r,!1);break;case"textarea":Q(e),ie(e);break;case"option":null!=r.value&&e.setAttribute("value",""+W(r.value));break;case"select":e.multiple=!!r.multiple,null!=(i=r.value)?ne(e,!!r.multiple,i,!1):null!=r.defaultValue&&ne(e,!!r.multiple,r.defaultValue,!0);break;default:"function"===typeof o.onClick&&(e.onclick=Jr)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}null!==t.ref&&(t.flags|=512,t.flags|=2097152)}return Yl(t),null;case 6:if(e&&null!=t.stateNode)Al(e,t,e.memoizedProps,r);else{if("string"!==typeof r&&null===t.stateNode)throw Error(a(166));if(n=ri(ni.current),ri(ei.current),fa(t)){if(r=t.stateNode,n=t.memoizedProps,r[po]=t,(i=r.nodeValue!==n)&&null!==(e=ra))switch(e.tag){case 3:Zr(r.nodeValue,n,0!==(1&e.mode));break;case 5:!0!==e.memoizedProps.suppressHydrationWarning&&Zr(r.nodeValue,n,0!==(1&e.mode))}i&&(t.flags|=4)}else(r=(9===n.nodeType?n:n.ownerDocument).createTextNode(r))[po]=t,t.stateNode=r}return Yl(t),null;case 13:if(Po(si),r=t.memoizedState,null===e||null!==e.memoizedState&&null!==e.memoizedState.dehydrated){if(aa&&null!==oa&&0!==(1&t.mode)&&0===(128&t.flags))pa(),ha(),t.flags|=98560,i=!1;else if(i=fa(t),null!==r&&null!==r.dehydrated){if(null===e){if(!i)throw Error(a(318));if(!(i=null!==(i=t.memoizedState)?i.dehydrated:null))throw Error(a(317));i[po]=t}else ha(),0===(128&t.flags)&&(t.memoizedState=null),t.flags|=4;Yl(t),i=!1}else null!==ia&&(lu(ia),ia=null),i=!0;if(!i)return 65536&t.flags?t:null}return 0!==(128&t.flags)?(t.lanes=n,t):((r=null!==r)!==(null!==e&&null!==e.memoizedState)&&r&&(t.child.flags|=8192,0!==(1&t.mode)&&(null===e||0!==(1&si.current)?0===As&&(As=3):vu())),null!==t.updateQueue&&(t.flags|=4),Yl(t),null);case 4:return ai(),Rl(e,t),null===e&&Ur(t.stateNode.containerInfo),Yl(t),null;case 10:return Sa(t.type._context),Yl(t),null;case 19:if(Po(si),null===(i=t.memoizedState))return Yl(t),null;if(r=0!==(128&t.flags),null===(s=i.rendering))if(r)Ql(i,!1);else{if(0!==As||null!==e&&0!==(128&e.flags))for(e=t.child;null!==e;){if(null!==(s=ui(e))){for(t.flags|=128,Ql(i,!1),null!==(r=s.updateQueue)&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;null!==n;)e=r,(i=n).flags&=14680066,null===(s=i.alternate)?(i.childLanes=0,i.lanes=e,i.child=null,i.subtreeFlags=0,i.memoizedProps=null,i.memoizedState=null,i.updateQueue=null,i.dependencies=null,i.stateNode=null):(i.childLanes=s.childLanes,i.lanes=s.lanes,i.child=s.child,i.subtreeFlags=0,i.deletions=null,i.memoizedProps=s.memoizedProps,i.memoizedState=s.memoizedState,i.updateQueue=s.updateQueue,i.type=s.type,e=s.dependencies,i.dependencies=null===e?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return To(si,1&si.current|2),t.child}e=e.sibling}null!==i.tail&&Ge()>Hs&&(t.flags|=128,r=!0,Ql(i,!1),t.lanes=4194304)}else{if(!r)if(null!==(e=ui(s))){if(t.flags|=128,r=!0,null!==(n=e.updateQueue)&&(t.updateQueue=n,t.flags|=4),Ql(i,!0),null===i.tail&&"hidden"===i.tailMode&&!s.alternate&&!aa)return Yl(t),null}else 2*Ge()-i.renderingStartTime>Hs&&1073741824!==n&&(t.flags|=128,r=!0,Ql(i,!1),t.lanes=4194304);i.isBackwards?(s.sibling=t.child,t.child=s):(null!==(n=i.last)?n.sibling=s:t.child=s,i.last=s)}return null!==i.tail?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=Ge(),t.sibling=null,n=si.current,To(si,r?1&n|2:1&n),t):(Yl(t),null);case 22:case 23:return fu(),r=null!==t.memoizedState,null!==e&&null!==e.memoizedState!==r&&(t.flags|=8192),r&&0!==(1&t.mode)?0!==(1073741824&Rs)&&(Yl(t),6&t.subtreeFlags&&(t.flags|=8192)):Yl(t),null;case 24:case 25:return null}throw Error(a(156,t.tag))}function ql(e,t){switch(na(t),t.tag){case 1:return Ro(t.type)&&Do(),65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 3:return ai(),Po(_o),Po(Mo),di(),0!==(65536&(e=t.flags))&&0===(128&e)?(t.flags=-65537&e|128,t):null;case 5:return li(t),null;case 13:if(Po(si),null!==(e=t.memoizedState)&&null!==e.dehydrated){if(null===t.alternate)throw Error(a(340));ha()}return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 19:return Po(si),null;case 4:return ai(),null;case 10:return Sa(t.type._context),null;case 22:case 23:return fu(),null;default:return null}}Vl=function(e,t){for(var n=t.child;null!==n;){if(5===n.tag||6===n.tag)e.appendChild(n.stateNode);else if(4!==n.tag&&null!==n.child){n.child.return=n,n=n.child;continue}if(n===t)break;for(;null===n.sibling;){if(null===n.return||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}},Rl=function(){},Dl=function(e,t,n,r){var o=e.memoizedProps;if(o!==r){e=t.stateNode,ri(ei.current);var a,i=null;switch(n){case"input":o=q(e,o),r=q(e,r),i=[];break;case"select":o=O({},o,{value:void 0}),r=O({},r,{value:void 0}),i=[];break;case"textarea":o=re(e,o),r=re(e,r),i=[];break;default:"function"!==typeof o.onClick&&"function"===typeof r.onClick&&(e.onclick=Jr)}for(c in ye(n,r),n=null,o)if(!r.hasOwnProperty(c)&&o.hasOwnProperty(c)&&null!=o[c])if("style"===c){var s=o[c];for(a in s)s.hasOwnProperty(a)&&(n||(n={}),n[a]="")}else"dangerouslySetInnerHTML"!==c&&"children"!==c&&"suppressContentEditableWarning"!==c&&"suppressHydrationWarning"!==c&&"autoFocus"!==c&&(l.hasOwnProperty(c)?i||(i=[]):(i=i||[]).push(c,null));for(c in r){var u=r[c];if(s=null!=o?o[c]:void 0,r.hasOwnProperty(c)&&u!==s&&(null!=u||null!=s))if("style"===c)if(s){for(a in s)!s.hasOwnProperty(a)||u&&u.hasOwnProperty(a)||(n||(n={}),n[a]="");for(a in u)u.hasOwnProperty(a)&&s[a]!==u[a]&&(n||(n={}),n[a]=u[a])}else n||(i||(i=[]),i.push(c,n)),n=u;else"dangerouslySetInnerHTML"===c?(u=u?u.__html:void 0,s=s?s.__html:void 0,null!=u&&s!==u&&(i=i||[]).push(c,u)):"children"===c?"string"!==typeof u&&"number"!==typeof u||(i=i||[]).push(c,""+u):"suppressContentEditableWarning"!==c&&"suppressHydrationWarning"!==c&&(l.hasOwnProperty(c)?(null!=u&&"onScroll"===c&&Ir("scroll",e),i||s===u||(i=[])):(i=i||[]).push(c,u))}n&&(i=i||[]).push("style",n);var c=i;(t.updateQueue=c)&&(t.flags|=4)}},Al=function(e,t,n,r){n!==r&&(t.flags|=4)};var Kl=!1,Gl=!1,Zl="function"===typeof WeakSet?WeakSet:Set,Jl=null;function es(e,t){var n=e.ref;if(null!==n)if("function"===typeof n)try{n(null)}catch(r){Cu(e,t,r)}else n.current=null}function ts(e,t,n){try{n()}catch(r){Cu(e,t,r)}}var ns=!1;function rs(e,t,n){var r=t.updateQueue;if(null!==(r=null!==r?r.lastEffect:null)){var o=r=r.next;do{if((o.tag&e)===e){var a=o.destroy;o.destroy=void 0,void 0!==a&&ts(t,n,a)}o=o.next}while(o!==r)}}function os(e,t){if(null!==(t=null!==(t=t.updateQueue)?t.lastEffect:null)){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function as(e){var t=e.ref;if(null!==t){var n=e.stateNode;e.tag,e=n,"function"===typeof t?t(e):t.current=e}}function is(e){var t=e.alternate;null!==t&&(e.alternate=null,is(t)),e.child=null,e.deletions=null,e.sibling=null,5===e.tag&&(null!==(t=e.stateNode)&&(delete t[po],delete t[ho],delete t[vo],delete t[go],delete t[yo])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function ls(e){return 5===e.tag||3===e.tag||4===e.tag}function ss(e){e:for(;;){for(;null===e.sibling;){if(null===e.return||ls(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;5!==e.tag&&6!==e.tag&&18!==e.tag;){if(2&e.flags)continue e;if(null===e.child||4===e.tag)continue e;e.child.return=e,e=e.child}if(!(2&e.flags))return e.stateNode}}function us(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?8===n.nodeType?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(8===n.nodeType?(t=n.parentNode).insertBefore(e,n):(t=n).appendChild(e),null!==(n=n._reactRootContainer)&&void 0!==n||null!==t.onclick||(t.onclick=Jr));else if(4!==r&&null!==(e=e.child))for(us(e,t,n),e=e.sibling;null!==e;)us(e,t,n),e=e.sibling}function cs(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(4!==r&&null!==(e=e.child))for(cs(e,t,n),e=e.sibling;null!==e;)cs(e,t,n),e=e.sibling}var ds=null,fs=!1;function ps(e,t,n){for(n=n.child;null!==n;)hs(e,t,n),n=n.sibling}function hs(e,t,n){if(at&&"function"===typeof at.onCommitFiberUnmount)try{at.onCommitFiberUnmount(ot,n)}catch(l){}switch(n.tag){case 5:Gl||es(n,t);case 6:var r=ds,o=fs;ds=null,ps(e,t,n),fs=o,null!==(ds=r)&&(fs?(e=ds,n=n.stateNode,8===e.nodeType?e.parentNode.removeChild(n):e.removeChild(n)):ds.removeChild(n.stateNode));break;case 18:null!==ds&&(fs?(e=ds,n=n.stateNode,8===e.nodeType?so(e.parentNode,n):1===e.nodeType&&so(e,n),Ut(e)):so(ds,n.stateNode));break;case 4:r=ds,o=fs,ds=n.stateNode.containerInfo,fs=!0,ps(e,t,n),ds=r,fs=o;break;case 0:case 11:case 14:case 15:if(!Gl&&(null!==(r=n.updateQueue)&&null!==(r=r.lastEffect))){o=r=r.next;do{var a=o,i=a.destroy;a=a.tag,void 0!==i&&(0!==(2&a)||0!==(4&a))&&ts(n,t,i),o=o.next}while(o!==r)}ps(e,t,n);break;case 1:if(!Gl&&(es(n,t),"function"===typeof(r=n.stateNode).componentWillUnmount))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(l){Cu(n,t,l)}ps(e,t,n);break;case 21:ps(e,t,n);break;case 22:1&n.mode?(Gl=(r=Gl)||null!==n.memoizedState,ps(e,t,n),Gl=r):ps(e,t,n);break;default:ps(e,t,n)}}function ms(e){var t=e.updateQueue;if(null!==t){e.updateQueue=null;var n=e.stateNode;null===n&&(n=e.stateNode=new Zl),t.forEach((function(t){var r=Mu.bind(null,e,t);n.has(t)||(n.add(t),t.then(r,r))}))}}function vs(e,t){var n=t.deletions;if(null!==n)for(var r=0;r<n.length;r++){var o=n[r];try{var i=e,l=t,s=l;e:for(;null!==s;){switch(s.tag){case 5:ds=s.stateNode,fs=!1;break e;case 3:case 4:ds=s.stateNode.containerInfo,fs=!0;break e}s=s.return}if(null===ds)throw Error(a(160));hs(i,l,o),ds=null,fs=!1;var u=o.alternate;null!==u&&(u.return=null),o.return=null}catch(c){Cu(o,t,c)}}if(12854&t.subtreeFlags)for(t=t.child;null!==t;)gs(t,e),t=t.sibling}function gs(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(vs(t,e),ys(e),4&r){try{rs(3,e,e.return),os(3,e)}catch(v){Cu(e,e.return,v)}try{rs(5,e,e.return)}catch(v){Cu(e,e.return,v)}}break;case 1:vs(t,e),ys(e),512&r&&null!==n&&es(n,n.return);break;case 5:if(vs(t,e),ys(e),512&r&&null!==n&&es(n,n.return),32&e.flags){var o=e.stateNode;try{fe(o,"")}catch(v){Cu(e,e.return,v)}}if(4&r&&null!=(o=e.stateNode)){var i=e.memoizedProps,l=null!==n?n.memoizedProps:i,s=e.type,u=e.updateQueue;if(e.updateQueue=null,null!==u)try{"input"===s&&"radio"===i.type&&null!=i.name&&G(o,i),be(s,l);var c=be(s,i);for(l=0;l<u.length;l+=2){var d=u[l],f=u[l+1];"style"===d?ve(o,f):"dangerouslySetInnerHTML"===d?de(o,f):"children"===d?fe(o,f):b(o,d,f,c)}switch(s){case"input":Z(o,i);break;case"textarea":ae(o,i);break;case"select":var p=o._wrapperState.wasMultiple;o._wrapperState.wasMultiple=!!i.multiple;var h=i.value;null!=h?ne(o,!!i.multiple,h,!1):p!==!!i.multiple&&(null!=i.defaultValue?ne(o,!!i.multiple,i.defaultValue,!0):ne(o,!!i.multiple,i.multiple?[]:"",!1))}o[ho]=i}catch(v){Cu(e,e.return,v)}}break;case 6:if(vs(t,e),ys(e),4&r){if(null===e.stateNode)throw Error(a(162));o=e.stateNode,i=e.memoizedProps;try{o.nodeValue=i}catch(v){Cu(e,e.return,v)}}break;case 3:if(vs(t,e),ys(e),4&r&&null!==n&&n.memoizedState.isDehydrated)try{Ut(t.containerInfo)}catch(v){Cu(e,e.return,v)}break;case 4:default:vs(t,e),ys(e);break;case 13:vs(t,e),ys(e),8192&(o=e.child).flags&&(i=null!==o.memoizedState,o.stateNode.isHidden=i,!i||null!==o.alternate&&null!==o.alternate.memoizedState||(Us=Ge())),4&r&&ms(e);break;case 22:if(d=null!==n&&null!==n.memoizedState,1&e.mode?(Gl=(c=Gl)||d,vs(t,e),Gl=c):vs(t,e),ys(e),8192&r){if(c=null!==e.memoizedState,(e.stateNode.isHidden=c)&&!d&&0!==(1&e.mode))for(Jl=e,d=e.child;null!==d;){for(f=Jl=d;null!==Jl;){switch(h=(p=Jl).child,p.tag){case 0:case 11:case 14:case 15:rs(4,p,p.return);break;case 1:es(p,p.return);var m=p.stateNode;if("function"===typeof m.componentWillUnmount){r=p,n=p.return;try{t=r,m.props=t.memoizedProps,m.state=t.memoizedState,m.componentWillUnmount()}catch(v){Cu(r,n,v)}}break;case 5:es(p,p.return);break;case 22:if(null!==p.memoizedState){ks(f);continue}}null!==h?(h.return=p,Jl=h):ks(f)}d=d.sibling}e:for(d=null,f=e;;){if(5===f.tag){if(null===d){d=f;try{o=f.stateNode,c?"function"===typeof(i=o.style).setProperty?i.setProperty("display","none","important"):i.display="none":(s=f.stateNode,l=void 0!==(u=f.memoizedProps.style)&&null!==u&&u.hasOwnProperty("display")?u.display:null,s.style.display=me("display",l))}catch(v){Cu(e,e.return,v)}}}else if(6===f.tag){if(null===d)try{f.stateNode.nodeValue=c?"":f.memoizedProps}catch(v){Cu(e,e.return,v)}}else if((22!==f.tag&&23!==f.tag||null===f.memoizedState||f===e)&&null!==f.child){f.child.return=f,f=f.child;continue}if(f===e)break e;for(;null===f.sibling;){if(null===f.return||f.return===e)break e;d===f&&(d=null),f=f.return}d===f&&(d=null),f.sibling.return=f.return,f=f.sibling}}break;case 19:vs(t,e),ys(e),4&r&&ms(e);case 21:}}function ys(e){var t=e.flags;if(2&t){try{e:{for(var n=e.return;null!==n;){if(ls(n)){var r=n;break e}n=n.return}throw Error(a(160))}switch(r.tag){case 5:var o=r.stateNode;32&r.flags&&(fe(o,""),r.flags&=-33),cs(e,ss(e),o);break;case 3:case 4:var i=r.stateNode.containerInfo;us(e,ss(e),i);break;default:throw Error(a(161))}}catch(l){Cu(e,e.return,l)}e.flags&=-3}4096&t&&(e.flags&=-4097)}function bs(e,t,n){Jl=e,ws(e,t,n)}function ws(e,t,n){for(var r=0!==(1&e.mode);null!==Jl;){var o=Jl,a=o.child;if(22===o.tag&&r){var i=null!==o.memoizedState||Kl;if(!i){var l=o.alternate,s=null!==l&&null!==l.memoizedState||Gl;l=Kl;var u=Gl;if(Kl=i,(Gl=s)&&!u)for(Jl=o;null!==Jl;)s=(i=Jl).child,22===i.tag&&null!==i.memoizedState?Ss(o):null!==s?(s.return=i,Jl=s):Ss(o);for(;null!==a;)Jl=a,ws(a,t,n),a=a.sibling;Jl=o,Kl=l,Gl=u}xs(e)}else 0!==(8772&o.subtreeFlags)&&null!==a?(a.return=o,Jl=a):xs(e)}}function xs(e){for(;null!==Jl;){var t=Jl;if(0!==(8772&t.flags)){var n=t.alternate;try{if(0!==(8772&t.flags))switch(t.tag){case 0:case 11:case 15:Gl||os(5,t);break;case 1:var r=t.stateNode;if(4&t.flags&&!Gl)if(null===n)r.componentDidMount();else{var o=t.elementType===t.type?n.memoizedProps:ga(t.type,n.memoizedProps);r.componentDidUpdate(o,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var i=t.updateQueue;null!==i&&Ia(t,i,r);break;case 3:var l=t.updateQueue;if(null!==l){if(n=null,null!==t.child)switch(t.child.tag){case 5:case 1:n=t.child.stateNode}Ia(t,l,n)}break;case 5:var s=t.stateNode;if(null===n&&4&t.flags){n=s;var u=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":u.autoFocus&&n.focus();break;case"img":u.src&&(n.src=u.src)}}break;case 6:case 4:case 12:case 19:case 17:case 21:case 22:case 23:case 25:break;case 13:if(null===t.memoizedState){var c=t.alternate;if(null!==c){var d=c.memoizedState;if(null!==d){var f=d.dehydrated;null!==f&&Ut(f)}}}break;default:throw Error(a(163))}Gl||512&t.flags&&as(t)}catch(p){Cu(t,t.return,p)}}if(t===e){Jl=null;break}if(null!==(n=t.sibling)){n.return=t.return,Jl=n;break}Jl=t.return}}function ks(e){for(;null!==Jl;){var t=Jl;if(t===e){Jl=null;break}var n=t.sibling;if(null!==n){n.return=t.return,Jl=n;break}Jl=t.return}}function Ss(e){for(;null!==Jl;){var t=Jl;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{os(4,t)}catch(s){Cu(t,n,s)}break;case 1:var r=t.stateNode;if("function"===typeof r.componentDidMount){var o=t.return;try{r.componentDidMount()}catch(s){Cu(t,o,s)}}var a=t.return;try{as(t)}catch(s){Cu(t,a,s)}break;case 5:var i=t.return;try{as(t)}catch(s){Cu(t,i,s)}}}catch(s){Cu(t,t.return,s)}if(t===e){Jl=null;break}var l=t.sibling;if(null!==l){l.return=t.return,Jl=l;break}Jl=t.return}}var Es,Cs=Math.ceil,Ps=w.ReactCurrentDispatcher,Ts=w.ReactCurrentOwner,Ls=w.ReactCurrentBatchConfig,Ms=0,_s=null,Ns=null,Vs=0,Rs=0,Ds=Co(0),As=0,zs=null,Os=0,Fs=0,Is=0,js=null,Bs=null,Us=0,Hs=1/0,Ws=null,$s=!1,Qs=null,Ys=null,Xs=!1,qs=null,Ks=0,Gs=0,Zs=null,Js=-1,eu=0;function tu(){return 0!==(6&Ms)?Ge():-1!==Js?Js:Js=Ge()}function nu(e){return 0===(1&e.mode)?1:0!==(2&Ms)&&0!==Vs?Vs&-Vs:null!==va.transition?(0===eu&&(eu=mt()),eu):0!==(e=bt)?e:e=void 0===(e=window.event)?16:Kt(e.type)}function ru(e,t,n,r){if(50<Gs)throw Gs=0,Zs=null,Error(a(185));gt(e,n,r),0!==(2&Ms)&&e===_s||(e===_s&&(0===(2&Ms)&&(Fs|=n),4===As&&su(e,Vs)),ou(e,r),1===n&&0===Ms&&0===(1&t.mode)&&(Hs=Ge()+500,jo&&Ho()))}function ou(e,t){var n=e.callbackNode;!function(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,o=e.expirationTimes,a=e.pendingLanes;0<a;){var i=31-it(a),l=1<<i,s=o[i];-1===s?0!==(l&n)&&0===(l&r)||(o[i]=pt(l,t)):s<=t&&(e.expiredLanes|=l),a&=~l}}(e,t);var r=ft(e,e===_s?Vs:0);if(0===r)null!==n&&Xe(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(null!=n&&Xe(n),1===t)0===e.tag?function(e){jo=!0,Uo(e)}(uu.bind(null,e)):Uo(uu.bind(null,e)),io((function(){0===(6&Ms)&&Ho()})),n=null;else{switch(wt(r)){case 1:n=Je;break;case 4:n=et;break;case 16:default:n=tt;break;case 536870912:n=rt}n=_u(n,au.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function au(e,t){if(Js=-1,eu=0,0!==(6&Ms))throw Error(a(327));var n=e.callbackNode;if(Su()&&e.callbackNode!==n)return null;var r=ft(e,e===_s?Vs:0);if(0===r)return null;if(0!==(30&r)||0!==(r&e.expiredLanes)||t)t=gu(e,r);else{t=r;var o=Ms;Ms|=2;var i=mu();for(_s===e&&Vs===t||(Ws=null,Hs=Ge()+500,pu(e,t));;)try{bu();break}catch(s){hu(e,s)}ka(),Ps.current=i,Ms=o,null!==Ns?t=0:(_s=null,Vs=0,t=As)}if(0!==t){if(2===t&&(0!==(o=ht(e))&&(r=o,t=iu(e,o))),1===t)throw n=zs,pu(e,0),su(e,r),ou(e,Ge()),n;if(6===t)su(e,r);else{if(o=e.current.alternate,0===(30&r)&&!function(e){for(var t=e;;){if(16384&t.flags){var n=t.updateQueue;if(null!==n&&null!==(n=n.stores))for(var r=0;r<n.length;r++){var o=n[r],a=o.getSnapshot;o=o.value;try{if(!lr(a(),o))return!1}catch(l){return!1}}}if(n=t.child,16384&t.subtreeFlags&&null!==n)n.return=t,t=n;else{if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}(o)&&(2===(t=gu(e,r))&&(0!==(i=ht(e))&&(r=i,t=iu(e,i))),1===t))throw n=zs,pu(e,0),su(e,r),ou(e,Ge()),n;switch(e.finishedWork=o,e.finishedLanes=r,t){case 0:case 1:throw Error(a(345));case 2:case 5:ku(e,Bs,Ws);break;case 3:if(su(e,r),(130023424&r)===r&&10<(t=Us+500-Ge())){if(0!==ft(e,0))break;if(((o=e.suspendedLanes)&r)!==r){tu(),e.pingedLanes|=e.suspendedLanes&o;break}e.timeoutHandle=ro(ku.bind(null,e,Bs,Ws),t);break}ku(e,Bs,Ws);break;case 4:if(su(e,r),(4194240&r)===r)break;for(t=e.eventTimes,o=-1;0<r;){var l=31-it(r);i=1<<l,(l=t[l])>o&&(o=l),r&=~i}if(r=o,10<(r=(120>(r=Ge()-r)?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*Cs(r/1960))-r)){e.timeoutHandle=ro(ku.bind(null,e,Bs,Ws),r);break}ku(e,Bs,Ws);break;default:throw Error(a(329))}}}return ou(e,Ge()),e.callbackNode===n?au.bind(null,e):null}function iu(e,t){var n=js;return e.current.memoizedState.isDehydrated&&(pu(e,t).flags|=256),2!==(e=gu(e,t))&&(t=Bs,Bs=n,null!==t&&lu(t)),e}function lu(e){null===Bs?Bs=e:Bs.push.apply(Bs,e)}function su(e,t){for(t&=~Is,t&=~Fs,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-it(t),r=1<<n;e[n]=-1,t&=~r}}function uu(e){if(0!==(6&Ms))throw Error(a(327));Su();var t=ft(e,0);if(0===(1&t))return ou(e,Ge()),null;var n=gu(e,t);if(0!==e.tag&&2===n){var r=ht(e);0!==r&&(t=r,n=iu(e,r))}if(1===n)throw n=zs,pu(e,0),su(e,t),ou(e,Ge()),n;if(6===n)throw Error(a(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,ku(e,Bs,Ws),ou(e,Ge()),null}function cu(e,t){var n=Ms;Ms|=1;try{return e(t)}finally{0===(Ms=n)&&(Hs=Ge()+500,jo&&Ho())}}function du(e){null!==qs&&0===qs.tag&&0===(6&Ms)&&Su();var t=Ms;Ms|=1;var n=Ls.transition,r=bt;try{if(Ls.transition=null,bt=1,e)return e()}finally{bt=r,Ls.transition=n,0===(6&(Ms=t))&&Ho()}}function fu(){Rs=Ds.current,Po(Ds)}function pu(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(-1!==n&&(e.timeoutHandle=-1,oo(n)),null!==Ns)for(n=Ns.return;null!==n;){var r=n;switch(na(r),r.tag){case 1:null!==(r=r.type.childContextTypes)&&void 0!==r&&Do();break;case 3:ai(),Po(_o),Po(Mo),di();break;case 5:li(r);break;case 4:ai();break;case 13:case 19:Po(si);break;case 10:Sa(r.type._context);break;case 22:case 23:fu()}n=n.return}if(_s=e,Ns=e=Du(e.current,null),Vs=Rs=t,As=0,zs=null,Is=Fs=Os=0,Bs=js=null,null!==Ta){for(t=0;t<Ta.length;t++)if(null!==(r=(n=Ta[t]).interleaved)){n.interleaved=null;var o=r.next,a=n.pending;if(null!==a){var i=a.next;a.next=o,r.next=i}n.pending=r}Ta=null}return e}function hu(e,t){for(;;){var n=Ns;try{if(ka(),fi.current=il,yi){for(var r=mi.memoizedState;null!==r;){var o=r.queue;null!==o&&(o.pending=null),r=r.next}yi=!1}if(hi=0,gi=vi=mi=null,bi=!1,wi=0,Ts.current=null,null===n||null===n.return){As=1,zs=t,Ns=null;break}e:{var i=e,l=n.return,s=n,u=t;if(t=Vs,s.flags|=32768,null!==u&&"object"===typeof u&&"function"===typeof u.then){var c=u,d=s,f=d.tag;if(0===(1&d.mode)&&(0===f||11===f||15===f)){var p=d.alternate;p?(d.updateQueue=p.updateQueue,d.memoizedState=p.memoizedState,d.lanes=p.lanes):(d.updateQueue=null,d.memoizedState=null)}var h=gl(l);if(null!==h){h.flags&=-257,yl(h,l,s,0,t),1&h.mode&&vl(i,c,t),u=c;var m=(t=h).updateQueue;if(null===m){var v=new Set;v.add(u),t.updateQueue=v}else m.add(u);break e}if(0===(1&t)){vl(i,c,t),vu();break e}u=Error(a(426))}else if(aa&&1&s.mode){var g=gl(l);if(null!==g){0===(65536&g.flags)&&(g.flags|=256),yl(g,l,s,0,t),ma(cl(u,s));break e}}i=u=cl(u,s),4!==As&&(As=2),null===js?js=[i]:js.push(i),i=l;do{switch(i.tag){case 3:i.flags|=65536,t&=-t,i.lanes|=t,Oa(i,hl(0,u,t));break e;case 1:s=u;var y=i.type,b=i.stateNode;if(0===(128&i.flags)&&("function"===typeof y.getDerivedStateFromError||null!==b&&"function"===typeof b.componentDidCatch&&(null===Ys||!Ys.has(b)))){i.flags|=65536,t&=-t,i.lanes|=t,Oa(i,ml(i,s,t));break e}}i=i.return}while(null!==i)}xu(n)}catch(w){t=w,Ns===n&&null!==n&&(Ns=n=n.return);continue}break}}function mu(){var e=Ps.current;return Ps.current=il,null===e?il:e}function vu(){0!==As&&3!==As&&2!==As||(As=4),null===_s||0===(268435455&Os)&&0===(268435455&Fs)||su(_s,Vs)}function gu(e,t){var n=Ms;Ms|=2;var r=mu();for(_s===e&&Vs===t||(Ws=null,pu(e,t));;)try{yu();break}catch(o){hu(e,o)}if(ka(),Ms=n,Ps.current=r,null!==Ns)throw Error(a(261));return _s=null,Vs=0,As}function yu(){for(;null!==Ns;)wu(Ns)}function bu(){for(;null!==Ns&&!qe();)wu(Ns)}function wu(e){var t=Es(e.alternate,e,Rs);e.memoizedProps=e.pendingProps,null===t?xu(e):Ns=t,Ts.current=null}function xu(e){var t=e;do{var n=t.alternate;if(e=t.return,0===(32768&t.flags)){if(null!==(n=Xl(n,t,Rs)))return void(Ns=n)}else{if(null!==(n=ql(n,t)))return n.flags&=32767,void(Ns=n);if(null===e)return As=6,void(Ns=null);e.flags|=32768,e.subtreeFlags=0,e.deletions=null}if(null!==(t=t.sibling))return void(Ns=t);Ns=t=e}while(null!==t);0===As&&(As=5)}function ku(e,t,n){var r=bt,o=Ls.transition;try{Ls.transition=null,bt=1,function(e,t,n,r){do{Su()}while(null!==qs);if(0!==(6&Ms))throw Error(a(327));n=e.finishedWork;var o=e.finishedLanes;if(null===n)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(a(177));e.callbackNode=null,e.callbackPriority=0;var i=n.lanes|n.childLanes;if(function(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var o=31-it(n),a=1<<o;t[o]=0,r[o]=-1,e[o]=-1,n&=~a}}(e,i),e===_s&&(Ns=_s=null,Vs=0),0===(2064&n.subtreeFlags)&&0===(2064&n.flags)||Xs||(Xs=!0,_u(tt,(function(){return Su(),null}))),i=0!==(15990&n.flags),0!==(15990&n.subtreeFlags)||i){i=Ls.transition,Ls.transition=null;var l=bt;bt=1;var s=Ms;Ms|=4,Ts.current=null,function(e,t){if(eo=Wt,pr(e=fr())){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{var r=(n=(n=e.ownerDocument)&&n.defaultView||window).getSelection&&n.getSelection();if(r&&0!==r.rangeCount){n=r.anchorNode;var o=r.anchorOffset,i=r.focusNode;r=r.focusOffset;try{n.nodeType,i.nodeType}catch(x){n=null;break e}var l=0,s=-1,u=-1,c=0,d=0,f=e,p=null;t:for(;;){for(var h;f!==n||0!==o&&3!==f.nodeType||(s=l+o),f!==i||0!==r&&3!==f.nodeType||(u=l+r),3===f.nodeType&&(l+=f.nodeValue.length),null!==(h=f.firstChild);)p=f,f=h;for(;;){if(f===e)break t;if(p===n&&++c===o&&(s=l),p===i&&++d===r&&(u=l),null!==(h=f.nextSibling))break;p=(f=p).parentNode}f=h}n=-1===s||-1===u?null:{start:s,end:u}}else n=null}n=n||{start:0,end:0}}else n=null;for(to={focusedElem:e,selectionRange:n},Wt=!1,Jl=t;null!==Jl;)if(e=(t=Jl).child,0!==(1028&t.subtreeFlags)&&null!==e)e.return=t,Jl=e;else for(;null!==Jl;){t=Jl;try{var m=t.alternate;if(0!==(1024&t.flags))switch(t.tag){case 0:case 11:case 15:case 5:case 6:case 4:case 17:break;case 1:if(null!==m){var v=m.memoizedProps,g=m.memoizedState,y=t.stateNode,b=y.getSnapshotBeforeUpdate(t.elementType===t.type?v:ga(t.type,v),g);y.__reactInternalSnapshotBeforeUpdate=b}break;case 3:var w=t.stateNode.containerInfo;1===w.nodeType?w.textContent="":9===w.nodeType&&w.documentElement&&w.removeChild(w.documentElement);break;default:throw Error(a(163))}}catch(x){Cu(t,t.return,x)}if(null!==(e=t.sibling)){e.return=t.return,Jl=e;break}Jl=t.return}m=ns,ns=!1}(e,n),gs(n,e),hr(to),Wt=!!eo,to=eo=null,e.current=n,bs(n,e,o),Ke(),Ms=s,bt=l,Ls.transition=i}else e.current=n;if(Xs&&(Xs=!1,qs=e,Ks=o),i=e.pendingLanes,0===i&&(Ys=null),function(e){if(at&&"function"===typeof at.onCommitFiberRoot)try{at.onCommitFiberRoot(ot,e,void 0,128===(128&e.current.flags))}catch(t){}}(n.stateNode),ou(e,Ge()),null!==t)for(r=e.onRecoverableError,n=0;n<t.length;n++)o=t[n],r(o.value,{componentStack:o.stack,digest:o.digest});if($s)throw $s=!1,e=Qs,Qs=null,e;0!==(1&Ks)&&0!==e.tag&&Su(),i=e.pendingLanes,0!==(1&i)?e===Zs?Gs++:(Gs=0,Zs=e):Gs=0,Ho()}(e,t,n,r)}finally{Ls.transition=o,bt=r}return null}function Su(){if(null!==qs){var e=wt(Ks),t=Ls.transition,n=bt;try{if(Ls.transition=null,bt=16>e?16:e,null===qs)var r=!1;else{if(e=qs,qs=null,Ks=0,0!==(6&Ms))throw Error(a(331));var o=Ms;for(Ms|=4,Jl=e.current;null!==Jl;){var i=Jl,l=i.child;if(0!==(16&Jl.flags)){var s=i.deletions;if(null!==s){for(var u=0;u<s.length;u++){var c=s[u];for(Jl=c;null!==Jl;){var d=Jl;switch(d.tag){case 0:case 11:case 15:rs(8,d,i)}var f=d.child;if(null!==f)f.return=d,Jl=f;else for(;null!==Jl;){var p=(d=Jl).sibling,h=d.return;if(is(d),d===c){Jl=null;break}if(null!==p){p.return=h,Jl=p;break}Jl=h}}}var m=i.alternate;if(null!==m){var v=m.child;if(null!==v){m.child=null;do{var g=v.sibling;v.sibling=null,v=g}while(null!==v)}}Jl=i}}if(0!==(2064&i.subtreeFlags)&&null!==l)l.return=i,Jl=l;else e:for(;null!==Jl;){if(0!==(2048&(i=Jl).flags))switch(i.tag){case 0:case 11:case 15:rs(9,i,i.return)}var y=i.sibling;if(null!==y){y.return=i.return,Jl=y;break e}Jl=i.return}}var b=e.current;for(Jl=b;null!==Jl;){var w=(l=Jl).child;if(0!==(2064&l.subtreeFlags)&&null!==w)w.return=l,Jl=w;else e:for(l=b;null!==Jl;){if(0!==(2048&(s=Jl).flags))try{switch(s.tag){case 0:case 11:case 15:os(9,s)}}catch(k){Cu(s,s.return,k)}if(s===l){Jl=null;break e}var x=s.sibling;if(null!==x){x.return=s.return,Jl=x;break e}Jl=s.return}}if(Ms=o,Ho(),at&&"function"===typeof at.onPostCommitFiberRoot)try{at.onPostCommitFiberRoot(ot,e)}catch(k){}r=!0}return r}finally{bt=n,Ls.transition=t}}return!1}function Eu(e,t,n){e=Aa(e,t=hl(0,t=cl(n,t),1),1),t=tu(),null!==e&&(gt(e,1,t),ou(e,t))}function Cu(e,t,n){if(3===e.tag)Eu(e,e,n);else for(;null!==t;){if(3===t.tag){Eu(t,e,n);break}if(1===t.tag){var r=t.stateNode;if("function"===typeof t.type.getDerivedStateFromError||"function"===typeof r.componentDidCatch&&(null===Ys||!Ys.has(r))){t=Aa(t,e=ml(t,e=cl(n,e),1),1),e=tu(),null!==t&&(gt(t,1,e),ou(t,e));break}}t=t.return}}function Pu(e,t,n){var r=e.pingCache;null!==r&&r.delete(t),t=tu(),e.pingedLanes|=e.suspendedLanes&n,_s===e&&(Vs&n)===n&&(4===As||3===As&&(130023424&Vs)===Vs&&500>Ge()-Us?pu(e,0):Is|=n),ou(e,t)}function Tu(e,t){0===t&&(0===(1&e.mode)?t=1:(t=ct,0===(130023424&(ct<<=1))&&(ct=4194304)));var n=tu();null!==(e=_a(e,t))&&(gt(e,t,n),ou(e,n))}function Lu(e){var t=e.memoizedState,n=0;null!==t&&(n=t.retryLane),Tu(e,n)}function Mu(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,o=e.memoizedState;null!==o&&(n=o.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(a(314))}null!==r&&r.delete(t),Tu(e,n)}function _u(e,t){return Ye(e,t)}function Nu(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Vu(e,t,n,r){return new Nu(e,t,n,r)}function Ru(e){return!(!(e=e.prototype)||!e.isReactComponent)}function Du(e,t){var n=e.alternate;return null===n?((n=Vu(e.tag,t,e.key,e.mode)).elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=14680064&e.flags,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Au(e,t,n,r,o,i){var l=2;if(r=e,"function"===typeof e)Ru(e)&&(l=1);else if("string"===typeof e)l=5;else e:switch(e){case S:return zu(n.children,o,i,t);case E:l=8,o|=8;break;case C:return(e=Vu(12,n,t,2|o)).elementType=C,e.lanes=i,e;case M:return(e=Vu(13,n,t,o)).elementType=M,e.lanes=i,e;case _:return(e=Vu(19,n,t,o)).elementType=_,e.lanes=i,e;case R:return Ou(n,o,i,t);default:if("object"===typeof e&&null!==e)switch(e.$$typeof){case P:l=10;break e;case T:l=9;break e;case L:l=11;break e;case N:l=14;break e;case V:l=16,r=null;break e}throw Error(a(130,null==e?e:typeof e,""))}return(t=Vu(l,n,t,o)).elementType=e,t.type=r,t.lanes=i,t}function zu(e,t,n,r){return(e=Vu(7,e,r,t)).lanes=n,e}function Ou(e,t,n,r){return(e=Vu(22,e,r,t)).elementType=R,e.lanes=n,e.stateNode={isHidden:!1},e}function Fu(e,t,n){return(e=Vu(6,e,null,t)).lanes=n,e}function Iu(e,t,n){return(t=Vu(4,null!==e.children?e.children:[],e.key,t)).lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function ju(e,t,n,r,o){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=vt(0),this.expirationTimes=vt(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=vt(0),this.identifierPrefix=r,this.onRecoverableError=o,this.mutableSourceEagerHydrationData=null}function Bu(e,t,n,r,o,a,i,l,s){return e=new ju(e,t,n,l,s),1===t?(t=1,!0===a&&(t|=8)):t=0,a=Vu(3,null,null,t),e.current=a,a.stateNode=e,a.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Va(a),e}function Uu(e){if(!e)return Lo;e:{if(Ue(e=e._reactInternals)!==e||1!==e.tag)throw Error(a(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Ro(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(null!==t);throw Error(a(171))}if(1===e.tag){var n=e.type;if(Ro(n))return zo(e,n,t)}return t}function Hu(e,t,n,r,o,a,i,l,s){return(e=Bu(n,r,!0,e,0,a,0,l,s)).context=Uu(null),n=e.current,(a=Da(r=tu(),o=nu(n))).callback=void 0!==t&&null!==t?t:null,Aa(n,a,o),e.current.lanes=o,gt(e,o,r),ou(e,r),e}function Wu(e,t,n,r){var o=t.current,a=tu(),i=nu(o);return n=Uu(n),null===t.context?t.context=n:t.pendingContext=n,(t=Da(a,i)).payload={element:e},null!==(r=void 0===r?null:r)&&(t.callback=r),null!==(e=Aa(o,t,i))&&(ru(e,o,i,a),za(e,o,i)),i}function $u(e){return(e=e.current).child?(e.child.tag,e.child.stateNode):null}function Qu(e,t){if(null!==(e=e.memoizedState)&&null!==e.dehydrated){var n=e.retryLane;e.retryLane=0!==n&&n<t?n:t}}function Yu(e,t){Qu(e,t),(e=e.alternate)&&Qu(e,t)}Es=function(e,t,n){if(null!==e)if(e.memoizedProps!==t.pendingProps||_o.current)wl=!0;else{if(0===(e.lanes&n)&&0===(128&t.flags))return wl=!1,function(e,t,n){switch(t.tag){case 3:_l(t),ha();break;case 5:ii(t);break;case 1:Ro(t.type)&&Oo(t);break;case 4:oi(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,o=t.memoizedProps.value;To(ya,r._currentValue),r._currentValue=o;break;case 13:if(null!==(r=t.memoizedState))return null!==r.dehydrated?(To(si,1&si.current),t.flags|=128,null):0!==(n&t.child.childLanes)?Fl(e,t,n):(To(si,1&si.current),null!==(e=$l(e,t,n))?e.sibling:null);To(si,1&si.current);break;case 19:if(r=0!==(n&t.childLanes),0!==(128&e.flags)){if(r)return Hl(e,t,n);t.flags|=128}if(null!==(o=t.memoizedState)&&(o.rendering=null,o.tail=null,o.lastEffect=null),To(si,si.current),r)break;return null;case 22:case 23:return t.lanes=0,Cl(e,t,n)}return $l(e,t,n)}(e,t,n);wl=0!==(131072&e.flags)}else wl=!1,aa&&0!==(1048576&t.flags)&&ea(t,Yo,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Wl(e,t),e=t.pendingProps;var o=Vo(t,Mo.current);Ca(t,n),o=Ei(null,t,r,e,o,n);var i=Ci();return t.flags|=1,"object"===typeof o&&null!==o&&"function"===typeof o.render&&void 0===o.$$typeof?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Ro(r)?(i=!0,Oo(t)):i=!1,t.memoizedState=null!==o.state&&void 0!==o.state?o.state:null,Va(t),o.updater=Ua,t.stateNode=o,o._reactInternals=t,Qa(t,r,e,n),t=Ml(null,t,r,!0,i,n)):(t.tag=0,aa&&i&&ta(t),xl(null,t,o,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Wl(e,t),e=t.pendingProps,r=(o=r._init)(r._payload),t.type=r,o=t.tag=function(e){if("function"===typeof e)return Ru(e)?1:0;if(void 0!==e&&null!==e){if((e=e.$$typeof)===L)return 11;if(e===N)return 14}return 2}(r),e=ga(r,e),o){case 0:t=Tl(null,t,r,e,n);break e;case 1:t=Ll(null,t,r,e,n);break e;case 11:t=kl(null,t,r,e,n);break e;case 14:t=Sl(null,t,r,ga(r.type,e),n);break e}throw Error(a(306,r,""))}return t;case 0:return r=t.type,o=t.pendingProps,Tl(e,t,r,o=t.elementType===r?o:ga(r,o),n);case 1:return r=t.type,o=t.pendingProps,Ll(e,t,r,o=t.elementType===r?o:ga(r,o),n);case 3:e:{if(_l(t),null===e)throw Error(a(387));r=t.pendingProps,o=(i=t.memoizedState).element,Ra(e,t),Fa(t,r,null,n);var l=t.memoizedState;if(r=l.element,i.isDehydrated){if(i={element:r,isDehydrated:!1,cache:l.cache,pendingSuspenseBoundaries:l.pendingSuspenseBoundaries,transitions:l.transitions},t.updateQueue.baseState=i,t.memoizedState=i,256&t.flags){t=Nl(e,t,r,n,o=cl(Error(a(423)),t));break e}if(r!==o){t=Nl(e,t,r,n,o=cl(Error(a(424)),t));break e}for(oa=uo(t.stateNode.containerInfo.firstChild),ra=t,aa=!0,ia=null,n=Za(t,null,r,n),t.child=n;n;)n.flags=-3&n.flags|4096,n=n.sibling}else{if(ha(),r===o){t=$l(e,t,n);break e}xl(e,t,r,n)}t=t.child}return t;case 5:return ii(t),null===e&&ca(t),r=t.type,o=t.pendingProps,i=null!==e?e.memoizedProps:null,l=o.children,no(r,o)?l=null:null!==i&&no(r,i)&&(t.flags|=32),Pl(e,t),xl(e,t,l,n),t.child;case 6:return null===e&&ca(t),null;case 13:return Fl(e,t,n);case 4:return oi(t,t.stateNode.containerInfo),r=t.pendingProps,null===e?t.child=Ga(t,null,r,n):xl(e,t,r,n),t.child;case 11:return r=t.type,o=t.pendingProps,kl(e,t,r,o=t.elementType===r?o:ga(r,o),n);case 7:return xl(e,t,t.pendingProps,n),t.child;case 8:case 12:return xl(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,o=t.pendingProps,i=t.memoizedProps,l=o.value,To(ya,r._currentValue),r._currentValue=l,null!==i)if(lr(i.value,l)){if(i.children===o.children&&!_o.current){t=$l(e,t,n);break e}}else for(null!==(i=t.child)&&(i.return=t);null!==i;){var s=i.dependencies;if(null!==s){l=i.child;for(var u=s.firstContext;null!==u;){if(u.context===r){if(1===i.tag){(u=Da(-1,n&-n)).tag=2;var c=i.updateQueue;if(null!==c){var d=(c=c.shared).pending;null===d?u.next=u:(u.next=d.next,d.next=u),c.pending=u}}i.lanes|=n,null!==(u=i.alternate)&&(u.lanes|=n),Ea(i.return,n,t),s.lanes|=n;break}u=u.next}}else if(10===i.tag)l=i.type===t.type?null:i.child;else if(18===i.tag){if(null===(l=i.return))throw Error(a(341));l.lanes|=n,null!==(s=l.alternate)&&(s.lanes|=n),Ea(l,n,t),l=i.sibling}else l=i.child;if(null!==l)l.return=i;else for(l=i;null!==l;){if(l===t){l=null;break}if(null!==(i=l.sibling)){i.return=l.return,l=i;break}l=l.return}i=l}xl(e,t,o.children,n),t=t.child}return t;case 9:return o=t.type,r=t.pendingProps.children,Ca(t,n),r=r(o=Pa(o)),t.flags|=1,xl(e,t,r,n),t.child;case 14:return o=ga(r=t.type,t.pendingProps),Sl(e,t,r,o=ga(r.type,o),n);case 15:return El(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:ga(r,o),Wl(e,t),t.tag=1,Ro(r)?(e=!0,Oo(t)):e=!1,Ca(t,n),Wa(t,r,o),Qa(t,r,o,n),Ml(null,t,r,!0,e,n);case 19:return Hl(e,t,n);case 22:return Cl(e,t,n)}throw Error(a(156,t.tag))};var Xu="function"===typeof reportError?reportError:function(e){console.error(e)};function qu(e){this._internalRoot=e}function Ku(e){this._internalRoot=e}function Gu(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType)}function Zu(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType&&(8!==e.nodeType||" react-mount-point-unstable "!==e.nodeValue))}function Ju(){}function ec(e,t,n,r,o){var a=n._reactRootContainer;if(a){var i=a;if("function"===typeof o){var l=o;o=function(){var e=$u(i);l.call(e)}}Wu(t,i,e,o)}else i=function(e,t,n,r,o){if(o){if("function"===typeof r){var a=r;r=function(){var e=$u(i);a.call(e)}}var i=Hu(t,r,e,0,null,!1,0,"",Ju);return e._reactRootContainer=i,e[mo]=i.current,Ur(8===e.nodeType?e.parentNode:e),du(),i}for(;o=e.lastChild;)e.removeChild(o);if("function"===typeof r){var l=r;r=function(){var e=$u(s);l.call(e)}}var s=Bu(e,0,!1,null,0,!1,0,"",Ju);return e._reactRootContainer=s,e[mo]=s.current,Ur(8===e.nodeType?e.parentNode:e),du((function(){Wu(t,s,n,r)})),s}(n,t,e,o,r);return $u(i)}Ku.prototype.render=qu.prototype.render=function(e){var t=this._internalRoot;if(null===t)throw Error(a(409));Wu(e,t,null,null)},Ku.prototype.unmount=qu.prototype.unmount=function(){var e=this._internalRoot;if(null!==e){this._internalRoot=null;var t=e.containerInfo;du((function(){Wu(null,e,null,null)})),t[mo]=null}},Ku.prototype.unstable_scheduleHydration=function(e){if(e){var t=Et();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Rt.length&&0!==t&&t<Rt[n].priority;n++);Rt.splice(n,0,e),0===n&&Ot(e)}},xt=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=dt(t.pendingLanes);0!==n&&(yt(t,1|n),ou(t,Ge()),0===(6&Ms)&&(Hs=Ge()+500,Ho()))}break;case 13:du((function(){var t=_a(e,1);if(null!==t){var n=tu();ru(t,e,1,n)}})),Yu(e,1)}},kt=function(e){if(13===e.tag){var t=_a(e,134217728);if(null!==t)ru(t,e,134217728,tu());Yu(e,134217728)}},St=function(e){if(13===e.tag){var t=nu(e),n=_a(e,t);if(null!==n)ru(n,e,t,tu());Yu(e,t)}},Et=function(){return bt},Ct=function(e,t){var n=bt;try{return bt=e,t()}finally{bt=n}},ke=function(e,t,n){switch(t){case"input":if(Z(e,n),t=n.name,"radio"===n.type&&null!=t){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var o=ko(r);if(!o)throw Error(a(90));Y(r),Z(r,o)}}}break;case"textarea":ae(e,n);break;case"select":null!=(t=n.value)&&ne(e,!!n.multiple,t,!1)}},Le=cu,Me=du;var tc={usingClientEntryPoint:!1,Events:[wo,xo,ko,Pe,Te,cu]},nc={findFiberByHostInstance:bo,bundleType:0,version:"18.2.0",rendererPackageName:"react-dom"},rc={bundleType:nc.bundleType,version:nc.version,rendererPackageName:nc.rendererPackageName,rendererConfig:nc.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:w.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return null===(e=$e(e))?null:e.stateNode},findFiberByHostInstance:nc.findFiberByHostInstance||function(){return null},findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.2.0-next-9e3b772b8-20220608"};if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__){var oc=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!oc.isDisabled&&oc.supportsFiber)try{ot=oc.inject(rc),at=oc}catch(ce){}}t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=tc,t.createPortal=function(e,t){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!Gu(t))throw Error(a(200));return function(e,t,n){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:k,key:null==r?null:""+r,children:e,containerInfo:t,implementation:n}}(e,t,null,n)},t.createRoot=function(e,t){if(!Gu(e))throw Error(a(299));var n=!1,r="",o=Xu;return null!==t&&void 0!==t&&(!0===t.unstable_strictMode&&(n=!0),void 0!==t.identifierPrefix&&(r=t.identifierPrefix),void 0!==t.onRecoverableError&&(o=t.onRecoverableError)),t=Bu(e,1,!1,null,0,n,0,r,o),e[mo]=t.current,Ur(8===e.nodeType?e.parentNode:e),new qu(t)},t.findDOMNode=function(e){if(null==e)return null;if(1===e.nodeType)return e;var t=e._reactInternals;if(void 0===t){if("function"===typeof e.render)throw Error(a(188));throw e=Object.keys(e).join(","),Error(a(268,e))}return e=null===(e=$e(t))?null:e.stateNode},t.flushSync=function(e){return du(e)},t.hydrate=function(e,t,n){if(!Zu(t))throw Error(a(200));return ec(null,e,t,!0,n)},t.hydrateRoot=function(e,t,n){if(!Gu(e))throw Error(a(405));var r=null!=n&&n.hydratedSources||null,o=!1,i="",l=Xu;if(null!==n&&void 0!==n&&(!0===n.unstable_strictMode&&(o=!0),void 0!==n.identifierPrefix&&(i=n.identifierPrefix),void 0!==n.onRecoverableError&&(l=n.onRecoverableError)),t=Hu(t,null,e,1,null!=n?n:null,o,0,i,l),e[mo]=t.current,Ur(e),r)for(e=0;e<r.length;e++)o=(o=(n=r[e])._getVersion)(n._source),null==t.mutableSourceEagerHydrationData?t.mutableSourceEagerHydrationData=[n,o]:t.mutableSourceEagerHydrationData.push(n,o);return new Ku(t)},t.render=function(e,t,n){if(!Zu(t))throw Error(a(200));return ec(null,e,t,!1,n)},t.unmountComponentAtNode=function(e){if(!Zu(e))throw Error(a(40));return!!e._reactRootContainer&&(du((function(){ec(null,null,e,!1,(function(){e._reactRootContainer=null,e[mo]=null}))})),!0)},t.unstable_batchedUpdates=cu,t.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Zu(n))throw Error(a(200));if(null==e||void 0===e._reactInternals)throw Error(a(38));return ec(e,t,n,!1,r)},t.version="18.2.0-next-9e3b772b8-20220608"},391:(e,t,n)=>{var r=n(950);t.createRoot=r.createRoot,t.hydrateRoot=r.hydrateRoot},950:(e,t,n)=>{!function e(){if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"===typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){console.error(t)}}(),e.exports=n(730)},153:(e,t,n)=>{var r=n(43),o=Symbol.for("react.element"),a=Symbol.for("react.fragment"),i=Object.prototype.hasOwnProperty,l=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,s={key:!0,ref:!0,__self:!0,__source:!0};function u(e,t,n){var r,a={},u=null,c=null;for(r in void 0!==n&&(u=""+n),void 0!==t.key&&(u=""+t.key),void 0!==t.ref&&(c=t.ref),t)i.call(t,r)&&!s.hasOwnProperty(r)&&(a[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps)void 0===a[r]&&(a[r]=t[r]);return{$$typeof:o,type:e,key:u,ref:c,props:a,_owner:l.current}}t.jsx=u,t.jsxs=u},202:(e,t)=>{var n=Symbol.for("react.element"),r=Symbol.for("react.portal"),o=Symbol.for("react.fragment"),a=Symbol.for("react.strict_mode"),i=Symbol.for("react.profiler"),l=Symbol.for("react.provider"),s=Symbol.for("react.context"),u=Symbol.for("react.forward_ref"),c=Symbol.for("react.suspense"),d=Symbol.for("react.memo"),f=Symbol.for("react.lazy"),p=Symbol.iterator;var h={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},m=Object.assign,v={};function g(e,t,n){this.props=e,this.context=t,this.refs=v,this.updater=n||h}function y(){}function b(e,t,n){this.props=e,this.context=t,this.refs=v,this.updater=n||h}g.prototype.isReactComponent={},g.prototype.setState=function(e,t){if("object"!==typeof e&&"function"!==typeof e&&null!=e)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},g.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},y.prototype=g.prototype;var w=b.prototype=new y;w.constructor=b,m(w,g.prototype),w.isPureReactComponent=!0;var x=Array.isArray,k=Object.prototype.hasOwnProperty,S={current:null},E={key:!0,ref:!0,__self:!0,__source:!0};function C(e,t,r){var o,a={},i=null,l=null;if(null!=t)for(o in void 0!==t.ref&&(l=t.ref),void 0!==t.key&&(i=""+t.key),t)k.call(t,o)&&!E.hasOwnProperty(o)&&(a[o]=t[o]);var s=arguments.length-2;if(1===s)a.children=r;else if(1<s){for(var u=Array(s),c=0;c<s;c++)u[c]=arguments[c+2];a.children=u}if(e&&e.defaultProps)for(o in s=e.defaultProps)void 0===a[o]&&(a[o]=s[o]);return{$$typeof:n,type:e,key:i,ref:l,props:a,_owner:S.current}}function P(e){return"object"===typeof e&&null!==e&&e.$$typeof===n}var T=/\/+/g;function L(e,t){return"object"===typeof e&&null!==e&&null!=e.key?function(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,(function(e){return t[e]}))}(""+e.key):t.toString(36)}function M(e,t,o,a,i){var l=typeof e;"undefined"!==l&&"boolean"!==l||(e=null);var s=!1;if(null===e)s=!0;else switch(l){case"string":case"number":s=!0;break;case"object":switch(e.$$typeof){case n:case r:s=!0}}if(s)return i=i(s=e),e=""===a?"."+L(s,0):a,x(i)?(o="",null!=e&&(o=e.replace(T,"$&/")+"/"),M(i,t,o,"",(function(e){return e}))):null!=i&&(P(i)&&(i=function(e,t){return{$$typeof:n,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}(i,o+(!i.key||s&&s.key===i.key?"":(""+i.key).replace(T,"$&/")+"/")+e)),t.push(i)),1;if(s=0,a=""===a?".":a+":",x(e))for(var u=0;u<e.length;u++){var c=a+L(l=e[u],u);s+=M(l,t,o,c,i)}else if(c=function(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=p&&e[p]||e["@@iterator"])?e:null}(e),"function"===typeof c)for(e=c.call(e),u=0;!(l=e.next()).done;)s+=M(l=l.value,t,o,c=a+L(l,u++),i);else if("object"===l)throw t=String(e),Error("Objects are not valid as a React child (found: "+("[object Object]"===t?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return s}function _(e,t,n){if(null==e)return e;var r=[],o=0;return M(e,r,"","",(function(e){return t.call(n,e,o++)})),r}function N(e){if(-1===e._status){var t=e._result;(t=t()).then((function(t){0!==e._status&&-1!==e._status||(e._status=1,e._result=t)}),(function(t){0!==e._status&&-1!==e._status||(e._status=2,e._result=t)})),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}var V={current:null},R={transition:null},D={ReactCurrentDispatcher:V,ReactCurrentBatchConfig:R,ReactCurrentOwner:S};t.Children={map:_,forEach:function(e,t,n){_(e,(function(){t.apply(this,arguments)}),n)},count:function(e){var t=0;return _(e,(function(){t++})),t},toArray:function(e){return _(e,(function(e){return e}))||[]},only:function(e){if(!P(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},t.Component=g,t.Fragment=o,t.Profiler=i,t.PureComponent=b,t.StrictMode=a,t.Suspense=c,t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=D,t.cloneElement=function(e,t,r){if(null===e||void 0===e)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var o=m({},e.props),a=e.key,i=e.ref,l=e._owner;if(null!=t){if(void 0!==t.ref&&(i=t.ref,l=S.current),void 0!==t.key&&(a=""+t.key),e.type&&e.type.defaultProps)var s=e.type.defaultProps;for(u in t)k.call(t,u)&&!E.hasOwnProperty(u)&&(o[u]=void 0===t[u]&&void 0!==s?s[u]:t[u])}var u=arguments.length-2;if(1===u)o.children=r;else if(1<u){s=Array(u);for(var c=0;c<u;c++)s[c]=arguments[c+2];o.children=s}return{$$typeof:n,type:e.type,key:a,ref:i,props:o,_owner:l}},t.createContext=function(e){return(e={$$typeof:s,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null}).Provider={$$typeof:l,_context:e},e.Consumer=e},t.createElement=C,t.createFactory=function(e){var t=C.bind(null,e);return t.type=e,t},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:u,render:e}},t.isValidElement=P,t.lazy=function(e){return{$$typeof:f,_payload:{_status:-1,_result:e},_init:N}},t.memo=function(e,t){return{$$typeof:d,type:e,compare:void 0===t?null:t}},t.startTransition=function(e){var t=R.transition;R.transition={};try{e()}finally{R.transition=t}},t.unstable_act=function(){throw Error("act(...) is not supported in production builds of React.")},t.useCallback=function(e,t){return V.current.useCallback(e,t)},t.useContext=function(e){return V.current.useContext(e)},t.useDebugValue=function(){},t.useDeferredValue=function(e){return V.current.useDeferredValue(e)},t.useEffect=function(e,t){return V.current.useEffect(e,t)},t.useId=function(){return V.current.useId()},t.useImperativeHandle=function(e,t,n){return V.current.useImperativeHandle(e,t,n)},t.useInsertionEffect=function(e,t){return V.current.useInsertionEffect(e,t)},t.useLayoutEffect=function(e,t){return V.current.useLayoutEffect(e,t)},t.useMemo=function(e,t){return V.current.useMemo(e,t)},t.useReducer=function(e,t,n){return V.current.useReducer(e,t,n)},t.useRef=function(e){return V.current.useRef(e)},t.useState=function(e){return V.current.useState(e)},t.useSyncExternalStore=function(e,t,n){return V.current.useSyncExternalStore(e,t,n)},t.useTransition=function(){return V.current.useTransition()},t.version="18.2.0"},43:(e,t,n)=>{e.exports=n(202)},579:(e,t,n)=>{e.exports=n(153)},234:(e,t)=>{function n(e,t){var n=e.length;e.push(t);e:for(;0<n;){var r=n-1>>>1,o=e[r];if(!(0<a(o,t)))break e;e[r]=t,e[n]=o,n=r}}function r(e){return 0===e.length?null:e[0]}function o(e){if(0===e.length)return null;var t=e[0],n=e.pop();if(n!==t){e[0]=n;e:for(var r=0,o=e.length,i=o>>>1;r<i;){var l=2*(r+1)-1,s=e[l],u=l+1,c=e[u];if(0>a(s,n))u<o&&0>a(c,s)?(e[r]=c,e[u]=n,r=u):(e[r]=s,e[l]=n,r=l);else{if(!(u<o&&0>a(c,n)))break e;e[r]=c,e[u]=n,r=u}}}return t}function a(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}if("object"===typeof performance&&"function"===typeof performance.now){var i=performance;t.unstable_now=function(){return i.now()}}else{var l=Date,s=l.now();t.unstable_now=function(){return l.now()-s}}var u=[],c=[],d=1,f=null,p=3,h=!1,m=!1,v=!1,g="function"===typeof setTimeout?setTimeout:null,y="function"===typeof clearTimeout?clearTimeout:null,b="undefined"!==typeof setImmediate?setImmediate:null;function w(e){for(var t=r(c);null!==t;){if(null===t.callback)o(c);else{if(!(t.startTime<=e))break;o(c),t.sortIndex=t.expirationTime,n(u,t)}t=r(c)}}function x(e){if(v=!1,w(e),!m)if(null!==r(u))m=!0,R(k);else{var t=r(c);null!==t&&D(x,t.startTime-e)}}function k(e,n){m=!1,v&&(v=!1,y(P),P=-1),h=!0;var a=p;try{for(w(n),f=r(u);null!==f&&(!(f.expirationTime>n)||e&&!M());){var i=f.callback;if("function"===typeof i){f.callback=null,p=f.priorityLevel;var l=i(f.expirationTime<=n);n=t.unstable_now(),"function"===typeof l?f.callback=l:f===r(u)&&o(u),w(n)}else o(u);f=r(u)}if(null!==f)var s=!0;else{var d=r(c);null!==d&&D(x,d.startTime-n),s=!1}return s}finally{f=null,p=a,h=!1}}"undefined"!==typeof navigator&&void 0!==navigator.scheduling&&void 0!==navigator.scheduling.isInputPending&&navigator.scheduling.isInputPending.bind(navigator.scheduling);var S,E=!1,C=null,P=-1,T=5,L=-1;function M(){return!(t.unstable_now()-L<T)}function _(){if(null!==C){var e=t.unstable_now();L=e;var n=!0;try{n=C(!0,e)}finally{n?S():(E=!1,C=null)}}else E=!1}if("function"===typeof b)S=function(){b(_)};else if("undefined"!==typeof MessageChannel){var N=new MessageChannel,V=N.port2;N.port1.onmessage=_,S=function(){V.postMessage(null)}}else S=function(){g(_,0)};function R(e){C=e,E||(E=!0,S())}function D(e,n){P=g((function(){e(t.unstable_now())}),n)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(e){e.callback=null},t.unstable_continueExecution=function(){m||h||(m=!0,R(k))},t.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):T=0<e?Math.floor(1e3/e):5},t.unstable_getCurrentPriorityLevel=function(){return p},t.unstable_getFirstCallbackNode=function(){return r(u)},t.unstable_next=function(e){switch(p){case 1:case 2:case 3:var t=3;break;default:t=p}var n=p;p=t;try{return e()}finally{p=n}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=function(){},t.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=p;p=e;try{return t()}finally{p=n}},t.unstable_scheduleCallback=function(e,o,a){var i=t.unstable_now();switch("object"===typeof a&&null!==a?a="number"===typeof(a=a.delay)&&0<a?i+a:i:a=i,e){case 1:var l=-1;break;case 2:l=250;break;case 5:l=1073741823;break;case 4:l=1e4;break;default:l=5e3}return e={id:d++,callback:o,priorityLevel:e,startTime:a,expirationTime:l=a+l,sortIndex:-1},a>i?(e.sortIndex=a,n(c,e),null===r(u)&&e===r(c)&&(v?(y(P),P=-1):v=!0,D(x,a-i))):(e.sortIndex=l,n(u,e),m||h||(m=!0,R(k))),e},t.unstable_shouldYield=M,t.unstable_wrapCallback=function(e){var t=p;return function(){var n=p;p=t;try{return e.apply(this,arguments)}finally{p=n}}}},853:(e,t,n)=>{e.exports=n(234)}},t={};function n(r){var o=t[r];if(void 0!==o)return o.exports;var a=t[r]={exports:{}};return e[r](a,a.exports,n),a.exports}var r=n(43),o=n(391);function a(e){return a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},a(e)}function i(e){var t=function(e,t){if("object"!=a(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=a(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==a(t)?t:t+""}function l(e,t,n){return(t=i(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function s(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function u(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?s(Object(n),!0).forEach((function(t){l(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):s(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}const c=(0,r.createContext)({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"}),d=(0,r.createContext)({});const f=(0,r.createContext)(null),p="undefined"!==typeof document,h=p?r.useLayoutEffect:r.useEffect,m=(0,r.createContext)({strict:!1});function v(e,t,n,o){const a=(0,r.useContext)(d).visualElement,i=(0,r.useContext)(m),l=(0,r.useContext)(f),s=(0,r.useContext)(c).reducedMotion,u=(0,r.useRef)();o=o||i.renderer,!u.current&&o&&(u.current=o(e,{visualState:t,parent:a,props:n,presenceId:l?l.id:void 0,blockInitialAnimation:!!l&&!1===l.initial,reducedMotionConfig:s}));const p=u.current;return h((()=>{p&&p.render()})),h((()=>{p&&p.animationState&&p.animationState.animateChanges()})),h((()=>()=>p&&p.notify("Unmount")),[]),p}function g(e){return"object"===typeof e&&Object.prototype.hasOwnProperty.call(e,"current")}function y(e){return"string"===typeof e||Array.isArray(e)}function b(e){return"object"===typeof e&&"function"===typeof e.start}const w=["initial","animate","exit","whileHover","whileDrag","whileTap","whileFocus","whileInView"];function x(e){return b(e.animate)||w.some((t=>y(e[t])))}function k(e){return Boolean(x(e)||e.variants)}function S(e){const{initial:t,animate:n}=function(e,t){if(x(e)){const{initial:t,animate:n}=e;return{initial:!1===t||y(t)?t:void 0,animate:y(n)?n:void 0}}return!1!==e.inherit?t:{}}(e,(0,r.useContext)(d));return(0,r.useMemo)((()=>({initial:t,animate:n})),[E(t),E(n)])}function E(e){return Array.isArray(e)?e.join(" "):e}const C=e=>({isEnabled:t=>e.some((e=>!!t[e]))}),P={measureLayout:C(["layout","layoutId","drag"]),animation:C(["animate","exit","variants","whileHover","whileTap","whileFocus","whileDrag","whileInView"]),exit:C(["exit"]),drag:C(["drag","dragControls"]),focus:C(["whileFocus"]),hover:C(["whileHover","onHoverStart","onHoverEnd"]),tap:C(["whileTap","onTap","onTapStart","onTapCancel"]),pan:C(["onPan","onPanStart","onPanSessionStart","onPanEnd"]),inView:C(["whileInView","onViewportEnter","onViewportLeave"])};function T(e){const t=(0,r.useRef)(null);return null===t.current&&(t.current=e()),t.current}const L={hasAnimatedSinceResize:!0,hasEverUpdated:!1};let M=1;const _=(0,r.createContext)({});class N extends r.Component{getSnapshotBeforeUpdate(){const{visualElement:e,props:t}=this.props;return e&&e.setProps(t),null}componentDidUpdate(){}render(){return this.props.children}}const V=(0,r.createContext)({}),R=Symbol.for("motionComponentSymbol");function D(e){let{preloadedFeatures:t,createVisualElement:n,projectionNodeConstructor:o,useRender:a,useVisualState:i,Component:l}=e;t&&function(e){for(const t in e)"projectionNodeConstructor"===t?P.projectionNodeConstructor=e[t]:P[t].Component=e[t]}(t);const s=(0,r.forwardRef)((function(e,s){const f=u(u(u({},(0,r.useContext)(c)),e),{},{layoutId:A(e)}),{isStatic:h}=f;let y=null;const b=S(e),w=h?void 0:T((()=>{if(L.hasEverUpdated)return M++})),x=i(e,h);if(!h&&p){b.visualElement=v(l,x,f,n);const e=(0,r.useContext)(m).strict,a=(0,r.useContext)(V);b.visualElement&&(y=b.visualElement.loadFeatures(f,e,t,w,o||P.projectionNodeConstructor,a))}return r.createElement(N,{visualElement:b.visualElement,props:f},y,r.createElement(d.Provider,{value:b},a(l,e,w,function(e,t,n){return(0,r.useCallback)((r=>{r&&e.mount&&e.mount(r),t&&(r?t.mount(r):t.unmount()),n&&("function"===typeof n?n(r):g(n)&&(n.current=r))}),[t])}(x,b.visualElement,s),x,h,b.visualElement)))}));return s[R]=l,s}function A(e){let{layoutId:t}=e;const n=(0,r.useContext)(_).id;return n&&void 0!==t?n+"-"+t:t}function z(e){function t(t){return D(e(t,arguments.length>1&&void 0!==arguments[1]?arguments[1]:{}))}if("undefined"===typeof Proxy)return t;const n=new Map;return new Proxy(t,{get:(e,r)=>(n.has(r)||n.set(r,t(r)),n.get(r))})}const O=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function F(e){return"string"===typeof e&&!e.includes("-")&&!!(O.indexOf(e)>-1||/[A-Z]/.test(e))}const I={};const j=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],B=new Set(j);function U(e,t){let{layout:n,layoutId:r}=t;return B.has(e)||e.startsWith("origin")||(n||void 0!==r)&&(!!I[e]||"opacity"===e)}const H=e=>!!(null===e||void 0===e?void 0:e.getVelocity),W={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},$=(e,t)=>j.indexOf(e)-j.indexOf(t);function Q(e){return e.startsWith("--")}const Y=(e,t)=>t&&"number"===typeof e?t.transform(e):e,X=(e,t,n)=>Math.min(Math.max(n,e),t),q={test:e=>"number"===typeof e,parse:parseFloat,transform:e=>e},K=u(u({},q),{},{transform:e=>X(0,1,e)}),G=u(u({},q),{},{default:1}),Z=e=>Math.round(1e5*e)/1e5,J=/(-)?([\d]*\.?[\d])+/g,ee=/(#[0-9a-f]{6}|#[0-9a-f]{3}|#(?:[0-9a-f]{2}){2,4}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))/gi,te=/^(#[0-9a-f]{3}|#(?:[0-9a-f]{2}){2,4}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))$/i;function ne(e){return"string"===typeof e}const re=e=>({test:t=>ne(t)&&t.endsWith(e)&&1===t.split(" ").length,parse:parseFloat,transform:t=>"".concat(t).concat(e)}),oe=re("deg"),ae=re("%"),ie=re("px"),le=re("vh"),se=re("vw"),ue=u(u({},ae),{},{parse:e=>ae.parse(e)/100,transform:e=>ae.transform(100*e)}),ce=u(u({},q),{},{transform:Math.round}),de={borderWidth:ie,borderTopWidth:ie,borderRightWidth:ie,borderBottomWidth:ie,borderLeftWidth:ie,borderRadius:ie,radius:ie,borderTopLeftRadius:ie,borderTopRightRadius:ie,borderBottomRightRadius:ie,borderBottomLeftRadius:ie,width:ie,maxWidth:ie,height:ie,maxHeight:ie,size:ie,top:ie,right:ie,bottom:ie,left:ie,padding:ie,paddingTop:ie,paddingRight:ie,paddingBottom:ie,paddingLeft:ie,margin:ie,marginTop:ie,marginRight:ie,marginBottom:ie,marginLeft:ie,rotate:oe,rotateX:oe,rotateY:oe,rotateZ:oe,scale:G,scaleX:G,scaleY:G,scaleZ:G,skew:oe,skewX:oe,skewY:oe,distance:ie,translateX:ie,translateY:ie,translateZ:ie,x:ie,y:ie,z:ie,perspective:ie,transformPerspective:ie,opacity:K,originX:ue,originY:ue,originZ:ie,zIndex:ce,fillOpacity:K,strokeOpacity:K,numOctaves:ce};function fe(e,t,n,r){const{style:o,vars:a,transform:i,transformKeys:l,transformOrigin:s}=e;l.length=0;let u=!1,c=!1,d=!0;for(const f in t){const e=t[f];if(Q(f)){a[f]=e;continue}const n=de[f],r=Y(e,n);if(B.has(f)){if(u=!0,i[f]=r,l.push(f),!d)continue;e!==(n.default||0)&&(d=!1)}else f.startsWith("origin")?(c=!0,s[f]=r):o[f]=r}if(t.transform||(u||r?o.transform=function(e,t,n,r){let{transform:o,transformKeys:a}=e,{enableHardwareAcceleration:i=!0,allowTransformNone:l=!0}=t,s="";a.sort($);for(const u of a)s+="".concat(W[u]||u,"(").concat(o[u],") ");return i&&!o.z&&(s+="translateZ(0)"),s=s.trim(),r?s=r(o,n?"":s):l&&n&&(s="none"),s}(e,n,d,r):o.transform&&(o.transform="none")),c){const{originX:e="50%",originY:t="50%",originZ:n=0}=s;o.transformOrigin="".concat(e," ").concat(t," ").concat(n)}}const pe=()=>({style:{},transform:{},transformKeys:[],transformOrigin:{},vars:{}});function he(e,t,n){for(const r in t)H(t[r])||U(r,n)||(e[r]=t[r])}function me(e,t,n){const o={};return he(o,e.style||{},e),Object.assign(o,function(e,t,n){let{transformTemplate:o}=e;return(0,r.useMemo)((()=>{const e={style:{},transform:{},transformKeys:[],transformOrigin:{},vars:{}};return fe(e,t,{enableHardwareAcceleration:!n},o),Object.assign({},e.vars,e.style)}),[t])}(e,t,n)),e.transformValues?e.transformValues(o):o}function ve(e,t,n){const r={},o=me(e,t,n);return e.drag&&!1!==e.dragListener&&(r.draggable=!1,o.userSelect=o.WebkitUserSelect=o.WebkitTouchCallout="none",o.touchAction=!0===e.drag?"none":"pan-".concat("x"===e.drag?"y":"x")),r.style=o,r}const ge=new Set(["initial","style","values","variants","transition","transformTemplate","transformValues","custom","inherit","layout","layoutId","layoutDependency","onLayoutAnimationStart","onLayoutAnimationComplete","onLayoutMeasure","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","drag","dragControls","dragListener","dragConstraints","dragDirectionLock","dragSnapToOrigin","_dragX","_dragY","dragElastic","dragMomentum","dragPropagation","dragTransition","onHoverStart","onHoverEnd","layoutScroll","whileInView","onViewportEnter","onViewportLeave","viewport","whileTap","onTap","onTapStart","onTapCancel","animate","exit","variants","whileHover","whileTap","whileFocus","whileDrag","whileInView","onPan","onPanStart","onPanSessionStart","onPanEnd"]);function ye(e){return ge.has(e)}let be=e=>!ye(e);try{(we=require("@emotion/is-prop-valid").default)&&(be=e=>e.startsWith("on")?!ye(e):we(e))}catch(Ni){}var we;function xe(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(t.includes(r))continue;n[r]=e[r]}return n}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(r=0;r<a.length;r++)n=a[r],t.includes(n)||{}.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}function ke(e,t,n){return"string"===typeof e?e:ie.transform(t+n*e)}const Se={offset:"stroke-dashoffset",array:"stroke-dasharray"},Ee={offset:"strokeDashoffset",array:"strokeDasharray"};const Ce=["attrX","attrY","originX","originY","pathLength","pathSpacing","pathOffset"];function Pe(e,t,n,r,o){let{attrX:a,attrY:i,originX:l,originY:s,pathLength:u,pathSpacing:c=1,pathOffset:d=0}=t;if(fe(e,xe(t,Ce),n,o),r)return void(e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox));e.attrs=e.style,e.style={};const{attrs:f,style:p,dimensions:h}=e;f.transform&&(h&&(p.transform=f.transform),delete f.transform),h&&(void 0!==l||void 0!==s||p.transform)&&(p.transformOrigin=function(e,t,n){const r=ke(t,e.x,e.width),o=ke(n,e.y,e.height);return"".concat(r," ").concat(o)}(h,void 0!==l?l:.5,void 0!==s?s:.5)),void 0!==a&&(f.x=a),void 0!==i&&(f.y=i),void 0!==u&&function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,o=!(arguments.length>4&&void 0!==arguments[4])||arguments[4];e.pathLength=1;const a=o?Se:Ee;e[a.offset]=ie.transform(-r);const i=ie.transform(t),l=ie.transform(n);e[a.array]="".concat(i," ").concat(l)}(f,u,c,d,!1)}const Te=()=>u(u({},{style:{},transform:{},transformKeys:[],transformOrigin:{},vars:{}}),{},{attrs:{}}),Le=e=>"string"===typeof e&&"svg"===e.toLowerCase();function Me(e,t,n,o){const a=(0,r.useMemo)((()=>{const n=Te();return Pe(n,t,{enableHardwareAcceleration:!1},Le(o),e.transformTemplate),u(u({},n.attrs),{},{style:u({},n.style)})}),[t]);if(e.style){const t={};he(t,e.style,e),a.style=u(u({},t),a.style)}return a}function _e(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return(t,n,o,a,i,l)=>{let{latestValues:s}=i;const c=(F(t)?Me:ve)(n,s,l,t),d=function(e,t,n){const r={};for(const o in e)(be(o)||!0===n&&ye(o)||!t&&!ye(o)||e.draggable&&o.startsWith("onDrag"))&&(r[o]=e[o]);return r}(n,"string"===typeof t,e),f=u(u(u({},d),c),{},{ref:a});return o&&(f["data-projection-id"]=o),(0,r.createElement)(t,f)}}const Ne=e=>e.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase();function Ve(e,t,n,r){let{style:o,vars:a}=t;Object.assign(e.style,o,r&&r.getProjectionStyles(n));for(const i in a)e.style.setProperty(i,a[i])}const Re=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function De(e,t,n,r){Ve(e,t,void 0,r);for(const o in t.attrs)e.setAttribute(Re.has(o)?o:Ne(o),t.attrs[o])}function Ae(e){const{style:t}=e,n={};for(const r in t)(H(t[r])||U(r,e))&&(n[r]=t[r]);return n}function ze(e){const t=Ae(e);for(const n in e)if(H(e[n])){t["x"===n||"y"===n?"attr"+n.toUpperCase():n]=e[n]}return t}function Oe(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{};return"function"===typeof t&&(t=t(void 0!==n?n:e.custom,r,o)),"string"===typeof t&&(t=e.variants&&e.variants[t]),"function"===typeof t&&(t=t(void 0!==n?n:e.custom,r,o)),t}const Fe=e=>Array.isArray(e);function Ie(e){const t=H(e)?e.get():e;return n=t,Boolean(n&&"object"===typeof n&&n.mix&&n.toValue)?t.toValue():t;var n}const je=["transitionEnd","transition"];const Be=e=>(t,n)=>{const o=(0,r.useContext)(d),a=(0,r.useContext)(f),i=()=>function(e,t,n,r){let{scrapeMotionValuesFromProps:o,createRenderState:a,onMount:i}=e;const l={latestValues:Ue(t,n,r,o),renderState:a()};return i&&(l.mount=e=>i(t,e,l)),l}(e,t,o,a);return n?i():T(i)};function Ue(e,t,n,r){const o={},a=r(e);for(const f in a)o[f]=Ie(a[f]);let{initial:i,animate:l}=e;const s=x(e),u=k(e);t&&u&&!s&&!1!==e.inherit&&(void 0===i&&(i=t.initial),void 0===l&&(l=t.animate));let c=!!n&&!1===n.initial;c=c||!1===i;const d=c?l:i;if(d&&"boolean"!==typeof d&&!b(d)){(Array.isArray(d)?d:[d]).forEach((t=>{const n=Oe(e,t);if(!n)return;const{transitionEnd:r,transition:a}=n,i=xe(n,je);for(const e in i){let t=i[e];if(Array.isArray(t)){t=t[c?t.length-1:0]}null!==t&&(o[e]=t)}for(const e in r)o[e]=r[e]}))}return o}const He={useVisualState:Be({scrapeMotionValuesFromProps:ze,createRenderState:Te,onMount:(e,t,n)=>{let{renderState:r,latestValues:o}=n;try{r.dimensions="function"===typeof t.getBBox?t.getBBox():t.getBoundingClientRect()}catch(a){r.dimensions={x:0,y:0,width:0,height:0}}Pe(r,o,{enableHardwareAcceleration:!1},Le(t.tagName),e.transformTemplate),De(t,r)}})},We={useVisualState:Be({scrapeMotionValuesFromProps:Ae,createRenderState:pe})};var $e;function Qe(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{passive:!0};return e.addEventListener(t,n,r),()=>e.removeEventListener(t,n)}function Ye(e,t,n,o){(0,r.useEffect)((()=>{const r=e.current;if(n&&r)return Qe(r,t,n,o)}),[e,t,n,o])}function Xe(e){return"undefined"!==typeof PointerEvent&&e instanceof PointerEvent?!("mouse"!==e.pointerType):e instanceof MouseEvent}function qe(e){return!!e.touches}!function(e){e.Animate="animate",e.Hover="whileHover",e.Tap="whileTap",e.Drag="whileDrag",e.Focus="whileFocus",e.InView="whileInView",e.Exit="exit"}($e||($e={}));const Ke={pageX:0,pageY:0};function Ge(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"page";const n=e.touches[0]||e.changedTouches[0]||Ke;return{x:n[t+"X"],y:n[t+"Y"]}}function Ze(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"page";return{x:e[t+"X"],y:e[t+"Y"]}}function Je(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"page";return{point:qe(e)?Ge(e,t):Ze(e,t)}}const et=function(e){const t=t=>e(t,Je(t));return arguments.length>1&&void 0!==arguments[1]&&arguments[1]?(n=t,e=>{const t=e instanceof MouseEvent;(!t||t&&0===e.button)&&n(e)}):t;var n},tt={pointerdown:"mousedown",pointermove:"mousemove",pointerup:"mouseup",pointercancel:"mousecancel",pointerover:"mouseover",pointerout:"mouseout",pointerenter:"mouseenter",pointerleave:"mouseleave"},nt={pointerdown:"touchstart",pointermove:"touchmove",pointerup:"touchend",pointercancel:"touchcancel"};function rt(e){return p&&null===window.onpointerdown?e:p&&null===window.ontouchstart?nt[e]:p&&null===window.onmousedown?tt[e]:e}function ot(e,t,n,r){return Qe(e,rt(t),et(n,"pointerdown"===t),r)}function at(e,t,n,r){return Ye(e,rt(t),n&&et(n,"pointerdown"===t),r)}function it(e){let t=null;return()=>{const n=()=>{t=null};return null===t&&(t=e,n)}}const lt=it("dragHorizontal"),st=it("dragVertical");function ut(e){let t=!1;if("y"===e)t=st();else if("x"===e)t=lt();else{const e=lt(),n=st();e&&n?t=()=>{e(),n()}:(e&&e(),n&&n())}return t}function ct(){const e=ut(!0);return!e||(e(),!1)}function dt(e,t,n){return(r,o)=>{Xe(r)&&!ct()&&(e.animationState&&e.animationState.setActive($e.Hover,t),n&&n(r,o))}}const ft=(e,t)=>!!t&&(e===t||ft(e,t.parentElement));function pt(e){return(0,r.useEffect)((()=>()=>e()),[])}const ht=(e,t)=>n=>t(e(n)),mt=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.reduce(ht)};new Set;const vt=["root"],gt=new WeakMap,yt=new WeakMap,bt=e=>{const t=gt.get(e.target);t&&t(e)},wt=e=>{e.forEach(bt)};function xt(e,t,n){const r=function(e){let{root:t}=e,n=xe(e,vt);const r=t||document;yt.has(r)||yt.set(r,{});const o=yt.get(r),a=JSON.stringify(n);return o[a]||(o[a]=new IntersectionObserver(wt,u({root:t},n))),o[a]}(t);return gt.set(e,n),r.observe(e),()=>{gt.delete(e),r.unobserve(e)}}const kt={some:0,all:1};function St(e,t,n,o){let{root:a,margin:i,amount:l="some",once:s}=o;(0,r.useEffect)((()=>{if(!e||!n.current)return;const r={root:null===a||void 0===a?void 0:a.current,rootMargin:i,threshold:"number"===typeof l?l:kt[l]};return xt(n.current,r,(e=>{const{isIntersecting:r}=e;if(t.isInView===r)return;if(t.isInView=r,s&&!r&&t.hasEnteredView)return;r&&(t.hasEnteredView=!0),n.animationState&&n.animationState.setActive($e.InView,r);const o=n.getProps(),a=r?o.onViewportEnter:o.onViewportLeave;a&&a(e)}))}),[e,a,i,l])}function Et(e,t,n,o){let{fallback:a=!0}=o;(0,r.useEffect)((()=>{e&&a&&requestAnimationFrame((()=>{t.hasEnteredView=!0;const{onViewportEnter:e}=n.getProps();e&&e(null),n.animationState&&n.animationState.setActive($e.InView,!0)}))}),[e])}const Ct=e=>t=>(e(t),null),Pt={inView:Ct((function(e){let{visualElement:t,whileInView:n,onViewportEnter:o,onViewportLeave:a,viewport:i={}}=e;const l=(0,r.useRef)({hasEnteredView:!1,isInView:!1});let s=Boolean(n||o||a);i.once&&l.current.hasEnteredView&&(s=!1),("undefined"===typeof IntersectionObserver?Et:St)(s,l.current,t,i)})),tap:Ct((function(e){let{onTap:t,onTapStart:n,onTapCancel:o,whileTap:a,visualElement:i}=e;const l=t||n||o||a,s=(0,r.useRef)(!1),u=(0,r.useRef)(null),c={passive:!(n||t||o||m)};function d(){u.current&&u.current(),u.current=null}function f(){return d(),s.current=!1,i.animationState&&i.animationState.setActive($e.Tap,!1),!ct()}function p(e,n){f()&&(ft(i.current,e.target)?t&&t(e,n):o&&o(e,n))}function h(e,t){f()&&o&&o(e,t)}function m(e,t){d(),s.current||(s.current=!0,u.current=mt(ot(window,"pointerup",p,c),ot(window,"pointercancel",h,c)),i.animationState&&i.animationState.setActive($e.Tap,!0),n&&n(e,t))}at(i,"pointerdown",l?m:void 0,c),pt(d)})),focus:Ct((function(e){let{whileFocus:t,visualElement:n}=e;const{animationState:r}=n;Ye(n,"focus",t?()=>{r&&r.setActive($e.Focus,!0)}:void 0),Ye(n,"blur",t?()=>{r&&r.setActive($e.Focus,!1)}:void 0)})),hover:Ct((function(e){let{onHoverStart:t,onHoverEnd:n,whileHover:r,visualElement:o}=e;at(o,"pointerenter",t||r?dt(o,!0,t):void 0,{passive:!t}),at(o,"pointerleave",n||r?dt(o,!1,n):void 0,{passive:!n})}))};function Tt(){const e=(0,r.useContext)(f);if(null===e)return[!0,null];const{isPresent:t,onExitComplete:n,register:o}=e,a=(0,r.useId)();(0,r.useEffect)((()=>o(a)),[]);return!t&&n?[!1,()=>n&&n(a)]:[!0]}function Lt(e,t){if(!Array.isArray(t))return!1;const n=t.length;if(n!==e.length)return!1;for(let r=0;r<n;r++)if(t[r]!==e[r])return!1;return!0}const Mt=e=>/^0[^.\s]+$/.test(e),_t={delta:0,timestamp:0},Nt=1/60*1e3,Vt="undefined"!==typeof performance?()=>performance.now():()=>Date.now(),Rt="undefined"!==typeof window?e=>window.requestAnimationFrame(e):e=>setTimeout((()=>e(Vt())),Nt);let Dt=!0,At=!1,zt=!1;const Ot=["read","update","preRender","render","postRender"],Ft=Ot.reduce(((e,t)=>(e[t]=function(e){let t=[],n=[],r=0,o=!1,a=!1;const i=new WeakSet,l={schedule:function(e){const a=arguments.length>2&&void 0!==arguments[2]&&arguments[2]&&o,l=a?t:n;return arguments.length>1&&void 0!==arguments[1]&&arguments[1]&&i.add(e),-1===l.indexOf(e)&&(l.push(e),a&&o&&(r=t.length)),e},cancel:e=>{const t=n.indexOf(e);-1!==t&&n.splice(t,1),i.delete(e)},process:s=>{if(o)a=!0;else{if(o=!0,[t,n]=[n,t],n.length=0,r=t.length,r)for(let n=0;n<r;n++){const r=t[n];r(s),i.has(r)&&(l.schedule(r),e())}o=!1,a&&(a=!1,l.process(s))}}};return l}((()=>At=!0)),e)),{}),It=Ot.reduce(((e,t)=>{const n=Ft[t];return e[t]=function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return At||Wt(),n.schedule(e,t,r)},e}),{}),jt=Ot.reduce(((e,t)=>(e[t]=Ft[t].cancel,e)),{}),Bt=Ot.reduce(((e,t)=>(e[t]=()=>Ft[t].process(_t),e)),{}),Ut=e=>Ft[e].process(_t),Ht=e=>{At=!1,_t.delta=Dt?Nt:Math.max(Math.min(e-_t.timestamp,40),1),_t.timestamp=e,zt=!0,Ot.forEach(Ut),zt=!1,At&&(Dt=!1,Rt(Ht))},Wt=()=>{At=!0,Dt=!0,zt||Rt(Ht)};function $t(e,t){-1===e.indexOf(t)&&e.push(t)}function Qt(e,t){const n=e.indexOf(t);n>-1&&e.splice(n,1)}class Yt{constructor(){this.subscriptions=[]}add(e){return $t(this.subscriptions,e),()=>Qt(this.subscriptions,e)}notify(e,t,n){const r=this.subscriptions.length;if(r)if(1===r)this.subscriptions[0](e,t,n);else for(let o=0;o<r;o++){const r=this.subscriptions[o];r&&r(e,t,n)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}function Xt(e,t){return t?e*(1e3/t):0}class qt{constructor(e){var t=this;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};var r;this.version="7.10.3",this.timeDelta=0,this.lastUpdated=0,this.canTrackVelocity=!1,this.events={},this.updateAndNotify=function(e){let n=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];t.prev=t.current,t.current=e;const{delta:r,timestamp:o}=_t;t.lastUpdated!==o&&(t.timeDelta=r,t.lastUpdated=o,It.postRender(t.scheduleVelocityCheck)),t.prev!==t.current&&t.events.change&&t.events.change.notify(t.current),t.events.velocityChange&&t.events.velocityChange.notify(t.getVelocity()),n&&t.events.renderRequest&&t.events.renderRequest.notify(t.current)},this.scheduleVelocityCheck=()=>It.postRender(this.velocityCheck),this.velocityCheck=e=>{let{timestamp:t}=e;t!==this.lastUpdated&&(this.prev=this.current,this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()))},this.hasAnimated=!1,this.prev=this.current=e,this.canTrackVelocity=(r=this.current,!isNaN(parseFloat(r))),this.owner=n.owner}onChange(e){return this.on("change",e)}on(e,t){return this.events[e]||(this.events[e]=new Yt),this.events[e].add(t)}clearListeners(){for(const e in this.events)this.events[e].clear()}attach(e){this.passiveEffect=e}set(e){let t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];t&&this.passiveEffect?this.passiveEffect(e,this.updateAndNotify):this.updateAndNotify(e,t)}setWithVelocity(e,t,n){this.set(t),this.prev=e,this.timeDelta=n}get(){return this.current}getPrevious(){return this.prev}getVelocity(){return this.canTrackVelocity?Xt(parseFloat(this.current)-parseFloat(this.prev),this.timeDelta):0}start(e){return this.stop(),new Promise((t=>{this.hasAnimated=!0,this.stopAnimation=e(t),this.events.animationStart&&this.events.animationStart.notify()})).then((()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()}))}stop(){this.stopAnimation&&(this.stopAnimation(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.stopAnimation}clearAnimation(){this.stopAnimation=null}destroy(){this.clearListeners(),this.stop()}}function Kt(e,t){return new qt(e,t)}const Gt=(e,t)=>n=>Boolean(ne(n)&&te.test(n)&&n.startsWith(e)||t&&Object.prototype.hasOwnProperty.call(n,t)),Zt=(e,t,n)=>r=>{if(!ne(r))return r;const[o,a,i,l]=r.match(J);return{[e]:parseFloat(o),[t]:parseFloat(a),[n]:parseFloat(i),alpha:void 0!==l?parseFloat(l):1}},Jt=u(u({},q),{},{transform:e=>Math.round((e=>X(0,255,e))(e))}),en={test:Gt("rgb","red"),parse:Zt("red","green","blue"),transform:e=>{let{red:t,green:n,blue:r,alpha:o=1}=e;return"rgba("+Jt.transform(t)+", "+Jt.transform(n)+", "+Jt.transform(r)+", "+Z(K.transform(o))+")"}};const tn={test:Gt("#"),parse:function(e){let t="",n="",r="",o="";return e.length>5?(t=e.substring(1,3),n=e.substring(3,5),r=e.substring(5,7),o=e.substring(7,9)):(t=e.substring(1,2),n=e.substring(2,3),r=e.substring(3,4),o=e.substring(4,5),t+=t,n+=n,r+=r,o+=o),{red:parseInt(t,16),green:parseInt(n,16),blue:parseInt(r,16),alpha:o?parseInt(o,16)/255:1}},transform:en.transform},nn={test:Gt("hsl","hue"),parse:Zt("hue","saturation","lightness"),transform:e=>{let{hue:t,saturation:n,lightness:r,alpha:o=1}=e;return"hsla("+Math.round(t)+", "+ae.transform(Z(n))+", "+ae.transform(Z(r))+", "+Z(K.transform(o))+")"}},rn={test:e=>en.test(e)||tn.test(e)||nn.test(e),parse:e=>en.test(e)?en.parse(e):nn.test(e)?nn.parse(e):tn.parse(e),transform:e=>ne(e)?e:e.hasOwnProperty("red")?en.transform(e):nn.transform(e)},on="${c}",an="${n}";function ln(e){"number"===typeof e&&(e="".concat(e));const t=[];let n=0,r=0;const o=e.match(ee);o&&(n=o.length,e=e.replace(ee,on),t.push(...o.map(rn.parse)));const a=e.match(J);return a&&(r=a.length,e=e.replace(J,an),t.push(...a.map(q.parse))),{values:t,numColors:n,numNumbers:r,tokenised:e}}function sn(e){return ln(e).values}function un(e){const{values:t,numColors:n,tokenised:r}=ln(e),o=t.length;return e=>{let t=r;for(let r=0;r<o;r++)t=t.replace(r<n?on:an,r<n?rn.transform(e[r]):Z(e[r]));return t}}const cn=e=>"number"===typeof e?0:e;const dn={test:function(e){var t,n;return isNaN(e)&&ne(e)&&((null===(t=e.match(J))||void 0===t?void 0:t.length)||0)+((null===(n=e.match(ee))||void 0===n?void 0:n.length)||0)>0},parse:sn,createTransformer:un,getAnimatableNone:function(e){const t=sn(e);return un(e)(t.map(cn))}},fn=new Set(["brightness","contrast","saturate","opacity"]);function pn(e){const[t,n]=e.slice(0,-1).split("(");if("drop-shadow"===t)return e;const[r]=n.match(J)||[];if(!r)return e;const o=n.replace(r,"");let a=fn.has(t)?1:0;return r!==n&&(a*=100),t+"("+a+o+")"}const hn=/([a-z-]*)\(.*?\)/g,mn=u(u({},dn),{},{getAnimatableNone:e=>{const t=e.match(hn);return t?t.map(pn).join(" "):e}}),vn=u(u({},de),{},{color:rn,backgroundColor:rn,outlineColor:rn,fill:rn,stroke:rn,borderColor:rn,borderTopColor:rn,borderRightColor:rn,borderBottomColor:rn,borderLeftColor:rn,filter:mn,WebkitFilter:mn}),gn=e=>vn[e];function yn(e,t){var n;let r=gn(e);return r!==mn&&(r=dn),null===(n=r.getAnimatableNone)||void 0===n?void 0:n.call(r,t)}const bn=e=>t=>t.test(e),wn=[q,ie,ae,oe,se,le,{test:e=>"auto"===e,parse:e=>e}],xn=e=>wn.find(bn(e)),kn=[...wn,rn,dn],Sn=e=>kn.find(bn(e));function En(e,t,n){const r=e.getProps();return Oe(r,t,void 0!==n?n:r.custom,function(e){const t={};return e.values.forEach(((e,n)=>t[n]=e.get())),t}(e),function(e){const t={};return e.values.forEach(((e,n)=>t[n]=e.getVelocity())),t}(e))}const Cn=["transitionEnd","transition"];function Pn(e,t,n){e.hasValue(t)?e.getValue(t).set(n):e.addValue(t,Kt(n))}function Tn(e,t){const n=En(e,t);let r=n?e.makeTargetAnimatable(n,!1):{},{transitionEnd:o={},transition:a={}}=r,i=xe(r,Cn);i=u(u({},i),o);for(const s in i){Pn(e,s,(l=i[s],Fe(l)?l[l.length-1]||0:l))}var l}function Ln(e,t){if(!t)return;return(t[e]||t.default||t).from}function Mn(e){return Boolean(H(e)&&e.add)}function _n(e,t){const{MotionAppearAnimations:n}=window,r=((e,t)=>"".concat(e,": ").concat(t))(e,B.has(t)?"transform":t),o=n&&n.get(r);return o?(It.render((()=>{try{o.cancel(),n.delete(r)}catch(e){}})),o.currentTime||0):0}const Nn="data-"+Ne("framerAppearId");const Vn=e=>1e3*e,Rn=!1,Dn=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,An=e=>t=>1-e(1-t),zn=e=>e*e,On=An(zn),Fn=Dn(zn),In=(e,t,n)=>-n*e+n*t+e;function jn(e,t,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?e+6*(t-e)*n:n<.5?t:n<2/3?e+(t-e)*(2/3-n)*6:e}const Bn=(e,t,n)=>{const r=e*e;return Math.sqrt(Math.max(0,n*(t*t-r)+r))},Un=[tn,en,nn];function Hn(e){const t=(n=e,Un.find((e=>e.test(n))));var n;Boolean(t),"'".concat(e,"' is not an animatable color. Use the equivalent color code instead.");let r=t.parse(e);return t===nn&&(r=function(e){let{hue:t,saturation:n,lightness:r,alpha:o}=e;t/=360,n/=100,r/=100;let a=0,i=0,l=0;if(n){const e=r<.5?r*(1+n):r+n-r*n,o=2*r-e;a=jn(o,e,t+1/3),i=jn(o,e,t),l=jn(o,e,t-1/3)}else a=i=l=r;return{red:Math.round(255*a),green:Math.round(255*i),blue:Math.round(255*l),alpha:o}}(r)),r}const Wn=(e,t)=>{const n=Hn(e),r=Hn(t),o=u({},n);return e=>(o.red=Bn(n.red,r.red,e),o.green=Bn(n.green,r.green,e),o.blue=Bn(n.blue,r.blue,e),o.alpha=In(n.alpha,r.alpha,e),en.transform(o))};function $n(e,t){return"number"===typeof e?n=>In(e,t,n):rn.test(e)?Wn(e,t):Xn(e,t)}const Qn=(e,t)=>{const n=[...e],r=n.length,o=e.map(((e,n)=>$n(e,t[n])));return e=>{for(let t=0;t<r;t++)n[t]=o[t](e);return n}},Yn=(e,t)=>{const n=u(u({},e),t),r={};for(const o in n)void 0!==e[o]&&void 0!==t[o]&&(r[o]=$n(e[o],t[o]));return e=>{for(const t in r)n[t]=r[t](e);return n}},Xn=(e,t)=>{const n=dn.createTransformer(t),r=ln(e),o=ln(t);return r.numColors===o.numColors&&r.numNumbers>=o.numNumbers?mt(Qn(r.values,o.values),n):("Complex values '".concat(e,"' and '").concat(t,"' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition."),n=>"".concat(n>0?t:e))},qn=(e,t,n)=>{const r=t-e;return 0===r?1:(n-e)/r},Kn=(e,t)=>n=>In(e,t,n);function Gn(e,t,n){const r=[],o=n||("number"===typeof(a=e[0])?Kn:"string"===typeof a?rn.test(a)?Wn:Xn:Array.isArray(a)?Qn:"object"===typeof a?Yn:Kn);var a;const i=e.length-1;for(let l=0;l<i;l++){let n=o(e[l],e[l+1]);if(t){const e=Array.isArray(t)?t[l]:t;n=mt(e,n)}r.push(n)}return r}function Zn(e,t){let{clamp:n=!0,ease:r,mixer:o}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const a=e.length;t.length,!r||!Array.isArray(r)||r.length,e[0]>e[a-1]&&(e=[...e].reverse(),t=[...t].reverse());const i=Gn(t,r,o),l=i.length,s=t=>{let n=0;if(l>1)for(;n<e.length-2&&!(t<e[n+1]);n++);const r=qn(e[n],e[n+1],t);return i[n](r)};return n?t=>s(X(e[0],e[a-1],t)):s}const Jn=e=>e,er=(e,t,n)=>(((1-3*n+3*t)*e+(3*n-6*t))*e+3*t)*e;function tr(e,t,n,r){if(e===t&&n===r)return Jn;const o=t=>function(e,t,n,r,o){let a,i,l=0;do{i=t+(n-t)/2,a=er(i,r,o)-e,a>0?n=i:t=i}while(Math.abs(a)>1e-7&&++l<12);return i}(t,0,1,e,n);return e=>0===e||1===e?e:er(o(e),t,r)}const nr=e=>1-Math.sin(Math.acos(e)),rr=An(nr),or=Dn(rr),ar=tr(.33,1.53,.69,.99),ir=An(ar),lr=Dn(ir),sr={linear:Jn,easeIn:zn,easeInOut:Fn,easeOut:On,circIn:nr,circInOut:or,circOut:rr,backIn:ir,backInOut:lr,backOut:ar,anticipate:e=>(e*=2)<1?.5*ir(e):.5*(2-Math.pow(2,-10*(e-1)))},ur=e=>{if(Array.isArray(e)){e.length;const[t,n,r,o]=e;return tr(t,n,r,o)}return"string"===typeof e?("Invalid easing type '".concat(e,"'"),sr[e]):e};function cr(e){let{keyframes:t,ease:n=Fn,times:r,duration:o=300}=e;t=[...t];const a=cr[0],i=(e=>Array.isArray(e)&&"number"!==typeof e[0])(n)?n.map(ur):ur(n),l={done:!1,value:a},s=function(e,t){return e.map((e=>e*t))}(r&&r.length===cr.length?r:function(e){const t=e.length;return e.map(((e,n)=>0!==n?n/(t-1):0))}(t),o);function u(){return Zn(s,t,{ease:Array.isArray(i)?i:(e=t,n=i,e.map((()=>n||Fn)).splice(0,e.length-1))});var e,n}let c=u();return{next:e=>(l.value=c(e),l.done=e>=o,l),flipTarget:()=>{t.reverse(),c=u()}}}const dr=.001;function fr(e){let t,n,{duration:r=800,bounce:o=.25,velocity:a=0,mass:i=1}=e,l=1-o;l=X(.05,1,l),r=X(.01,10,r/1e3),l<1?(t=e=>{const t=e*l,n=t*r,o=t-a,i=hr(e,l),s=Math.exp(-n);return dr-o/i*s},n=e=>{const n=e*l*r,o=n*a+a,i=Math.pow(l,2)*Math.pow(e,2)*r,s=Math.exp(-n),u=hr(Math.pow(e,2),l);return(-t(e)+dr>0?-1:1)*((o-i)*s)/u}):(t=e=>Math.exp(-e*r)*((e-a)*r+1)-.001,n=e=>Math.exp(-e*r)*(r*r*(a-e)));const s=function(e,t,n){let r=n;for(let o=1;o<pr;o++)r-=e(r)/t(r);return r}(t,n,5/r);if(r*=1e3,isNaN(s))return{stiffness:100,damping:10,duration:r};{const e=Math.pow(s,2)*i;return{stiffness:e,damping:2*l*Math.sqrt(i*e),duration:r}}}const pr=12;function hr(e,t){return e*Math.sqrt(1-t*t)}const mr=["keyframes","restSpeed","restDelta"],vr=["duration","bounce"],gr=["stiffness","damping","mass"];function yr(e,t){return t.some((t=>void 0!==e[t]))}function br(e){let{keyframes:t,restSpeed:n=2,restDelta:r=.01}=e,o=xe(e,mr),a=t[0],i=t[t.length-1];const l={done:!1,value:a},{stiffness:s,damping:c,mass:d,velocity:f,duration:p,isResolvedFromDuration:h}=function(e){let t=u({velocity:0,stiffness:100,damping:10,mass:1,isResolvedFromDuration:!1},e);if(!yr(e,gr)&&yr(e,vr)){const n=fr(e);t=u(u(u({},t),n),{},{velocity:0,mass:1}),t.isResolvedFromDuration=!0}return t}(o);let m=wr,v=f?-f/1e3:0;const g=c/(2*Math.sqrt(s*d));function y(){const e=i-a,t=Math.sqrt(s/d)/1e3;if(void 0===r&&(r=Math.min(Math.abs(i-a)/100,.4)),g<1){const n=hr(t,g);m=r=>{const o=Math.exp(-g*t*r);return i-o*((v+g*t*e)/n*Math.sin(n*r)+e*Math.cos(n*r))}}else if(1===g)m=n=>i-Math.exp(-t*n)*(e+(v+t*e)*n);else{const n=t*Math.sqrt(g*g-1);m=r=>{const o=Math.exp(-g*t*r),a=Math.min(n*r,300);return i-o*((v+g*t*e)*Math.sinh(a)+n*e*Math.cosh(a))/n}}}return y(),{next:e=>{const t=m(e);if(h)l.done=e>=p;else{let o=v;if(0!==e)if(g<1){const n=Math.max(0,e-5);o=Xt(t-m(n),e-n)}else o=0;const a=Math.abs(o)<=n,s=Math.abs(i-t)<=r;l.done=a&&s}return l.value=l.done?i:t,l},flipTarget:()=>{v=-v,[a,i]=[i,a],y()}}}br.needsInterpolation=(e,t)=>"string"===typeof e||"string"===typeof t;const wr=e=>0;const xr=["duration","driver","elapsed","repeat","repeatType","repeatDelay","keyframes","autoplay","onPlay","onStop","onComplete","onRepeat","onUpdate","type"],kr={decay:function(e){let{keyframes:t=[0],velocity:n=0,power:r=.8,timeConstant:o=350,restDelta:a=.5,modifyTarget:i}=e;const l=t[0],s={done:!1,value:l};let u=r*n;const c=l+u,d=void 0===i?c:i(c);return d!==c&&(u=d-l),{next:e=>{const t=-u*Math.exp(-e/o);return s.done=!(t>a||t<-a),s.value=s.done?d:d+t,s},flipTarget:()=>{}}},keyframes:cr,tween:cr,spring:br};function Sr(e,t){return e-t-(arguments.length>2&&void 0!==arguments[2]?arguments[2]:0)}const Er=e=>{const t=t=>{let{delta:n}=t;return e(n)};return{start:()=>It.update(t,!0),stop:()=>jt.update(t)}};function Cr(e){let{duration:t,driver:n=Er,elapsed:r=0,repeat:o=0,repeatType:a="loop",repeatDelay:i=0,keyframes:l,autoplay:s=!0,onPlay:c,onStop:d,onComplete:f,onRepeat:p,onUpdate:h,type:m="keyframes"}=e,v=xe(e,xr);var g,y;let b,w,x,k=0,S=t,E=!1,C=!0;const P=kr[l.length>2?"keyframes":m],T=l[0],L=l[l.length-1];(null===(y=(g=P).needsInterpolation)||void 0===y?void 0:y.call(g,T,L))&&(x=Zn([0,100],[T,L],{clamp:!1}),l=[0,100]);const M=P(u(u({},v),{},{duration:t,keyframes:l}));function _(){k++,"reverse"===a?(C=k%2===0,r=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;return arguments.length>3&&void 0!==arguments[3]&&!arguments[3]?t-(e-t)+n:Sr(t+-e,t,n)}(r,S,i,C)):(r=Sr(r,S,i),"mirror"===a&&M.flipTarget()),E=!1,p&&p()}function N(e){if(C||(e=-e),r+=e,!E){const e=M.next(Math.max(0,r));w=e.value,x&&(w=x(w)),E=C?e.done:r<=0}h&&h(w),E&&(0===k&&(S=void 0!==S?S:r),k<o?function(e,t,n,r){return r?e>=t+n:e<=-n}(r,S,i,C)&&_():(b.stop(),f&&f()))}return s&&(c&&c(),b=n(N),b.start()),{stop:()=>{d&&d(),b.stop()},sample:e=>M.next(Math.max(0,e))}}const Pr=e=>{let[t,n,r,o]=e;return"cubic-bezier(".concat(t,", ").concat(n,", ").concat(r,", ").concat(o,")")},Tr={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:Pr([0,.65,.55,1]),circOut:Pr([.55,0,1,.45]),backIn:Pr([.31,.01,.66,-.59]),backOut:Pr([.33,1.53,.69,.99])};function Lr(e){if(e)return Array.isArray(e)?Pr(e):Tr[e]}const Mr=["onUpdate","onComplete"];function _r(e,t,n){let{onUpdate:r,onComplete:o}=n,a=xe(n,Mr),{keyframes:i,duration:l=.3,elapsed:s=0,ease:c}=a;if("spring"===a.type||!(!(d=a.ease)||Array.isArray(d)||"string"===typeof d&&Tr[d])){const e=Cr(a);let t={done:!1,value:i[0]};const n=[];let r=0;for(;!t.done;)t=e.sample(r),n.push(t.value),r+=10;i=n,l=r-10,c="linear"}var d;const f=function(e,t,n){let{delay:r=0,duration:o,repeat:a=0,repeatType:i="loop",ease:l,times:s}=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};return e.animate({[t]:n,offset:s},{delay:r,duration:o,easing:Lr(l),fill:"both",iterations:a+1,direction:"reverse"===i?"alternate":"normal"})}(e.owner.current,t,i,u(u({},a),{},{delay:-s,duration:l,ease:c}));return f.onfinish=()=>{e.set(i[i.length-1]),o&&o()},()=>{const{currentTime:t}=f;if(t){const n=Cr(a);e.setWithVelocity(n.sample(t-10).value,n.sample(t).value,10)}It.update((()=>f.cancel()))}}function Nr(e,t){const n=performance.now(),r=o=>{let{timestamp:a}=o;const i=a-n;i>=t&&(jt.read(r),e(i-t))};return It.read(r,!0),()=>jt.read(r)}function Vr(e){let{keyframes:t,elapsed:n,onUpdate:r,onComplete:o}=e;const a=()=>(r&&r(t[t.length-1]),o&&o(),()=>{});return n?Nr(a,-n):a()}const Rr=()=>({type:"spring",stiffness:500,damping:25,restSpeed:10}),Dr=e=>({type:"spring",stiffness:550,damping:0===e?2*Math.sqrt(550):30,restSpeed:10}),Ar=()=>({type:"keyframes",ease:"linear",duration:.3}),zr={type:"keyframes",duration:.8},Or={x:Rr,y:Rr,z:Rr,rotate:Rr,rotateX:Rr,rotateY:Rr,rotateZ:Rr,scaleX:Dr,scaleY:Dr,scale:Dr,opacity:Ar,backgroundColor:Ar,color:Ar,default:Dr},Fr=(e,t)=>"zIndex"!==e&&(!("number"!==typeof t&&!Array.isArray(t))||!("string"!==typeof t||!dn.test(t)||t.startsWith("url("))),Ir=["when","delay","delayChildren","staggerChildren","staggerDirection","repeat","repeatType","repeatDelay","from"];function jr(e){return 0===e||"string"===typeof e&&0===parseFloat(e)&&-1===e.indexOf(" ")}function Br(e){return"number"===typeof e?0:yn("",e)}function Ur(e,t){return e[t]||e.default||e}const Hr={waapi:()=>Object.hasOwnProperty.call(Element.prototype,"animate")},Wr={},$r={};for(const Vi in Hr)$r[Vi]=()=>(void 0===Wr[Vi]&&(Wr[Vi]=Hr[Vi]()),Wr[Vi]);const Qr=new Set(["opacity"]),Yr=function(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};return o=>{const a=Ur(r,e)||{},i=a.delay||r.delay||0;let{elapsed:l=0}=r;l-=Vn(i);const s=function(e,t,n,r){const o=Fr(t,n);let a=void 0!==r.from?r.from:e.get();return"none"===a&&o&&"string"===typeof n?a=yn(t,n):jr(a)&&"string"===typeof n?a=Br(n):!Array.isArray(n)&&jr(n)&&"string"===typeof a&&(n=Br(a)),Array.isArray(n)?(null===n[0]&&(n[0]=a),n):[a,n]}(t,e,n,a),c=s[0],d=s[s.length-1],f=Fr(e,c),p=Fr(e,d);"You are trying to animate ".concat(e,' from "').concat(c,'" to "').concat(d,'". ').concat(c," is not an animatable value - to enable this animation set ").concat(c," to a value animatable to ").concat(d," via the `style` property.");let h=u(u({keyframes:s,velocity:t.getVelocity()},a),{},{elapsed:l,onUpdate:e=>{t.set(e),a.onUpdate&&a.onUpdate(e)},onComplete:()=>{o(),a.onComplete&&a.onComplete()}});if(!f||!p||Rn||!1===a.type)return Vr(h);if("inertia"===a.type){const e=function(e){let{keyframes:t,velocity:n=0,min:r,max:o,power:a=.8,timeConstant:i=750,bounceStiffness:l=500,bounceDamping:s=10,restDelta:c=1,modifyTarget:d,driver:f,onUpdate:p,onComplete:h,onStop:m}=e;const v=t[0];let g;function y(e){return void 0!==r&&e<r||void 0!==o&&e>o}function b(e){return void 0===r?o:void 0===o||Math.abs(r-e)<Math.abs(o-e)?r:o}function w(e){null===g||void 0===g||g.stop(),g=Cr(u(u({keyframes:[0,1],velocity:0},e),{},{driver:f,onUpdate:t=>{var n;null===p||void 0===p||p(t),null===(n=e.onUpdate)||void 0===n||n.call(e,t)},onComplete:h,onStop:m}))}function x(e){w(u({type:"spring",stiffness:l,damping:s,restDelta:c},e))}if(y(v))x({velocity:n,keyframes:[v,b(v)]});else{let e=a*n+v;"undefined"!==typeof d&&(e=d(e));const t=b(e),o=t===r?-1:1;let l,s;const u=e=>{l=s,s=e,n=Xt(e-l,_t.delta),(1===o&&e>t||-1===o&&e<t)&&x({keyframes:[e,t],velocity:n})};w({type:"decay",keyframes:[v,0],velocity:n,timeConstant:i,power:a,restDelta:c,modifyTarget:d,onUpdate:y(e)?u:void 0})}return{stop:()=>null===g||void 0===g?void 0:g.stop()}}(h);return()=>e.stop()}(function(e){let{when:t,delay:n,delayChildren:r,staggerChildren:o,staggerDirection:a,repeat:i,repeatType:l,repeatDelay:s,from:u}=e,c=xe(e,Ir);return!!Object.keys(c).length})(a)||(h=u(u({},h),((e,t)=>{let{keyframes:n}=t;return n.length>2?zr:(Or[e]||Or.default)(n[1])})(e,h))),h.duration&&(h.duration=Vn(h.duration)),h.repeatDelay&&(h.repeatDelay=Vn(h.repeatDelay));const m=t.owner,v=m&&m.current;if($r.waapi()&&Qr.has(e)&&!h.repeatDelay&&"mirror"!==h.repeatType&&0!==h.damping&&m&&v instanceof HTMLElement&&!m.getProps().onUpdate)return _r(t,e,h);{const e=Cr(h);return()=>e.stop()}}},Xr=["transition","transitionEnd"];function qr(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};var r;const o=En(e,t,n.custom);let{transition:a=e.getDefaultTransition()||{}}=o||{};n.transitionOverride&&(a=n.transitionOverride);const i=o?()=>Kr(e,o,n):()=>Promise.resolve(),l=(null===(r=e.variantChildren)||void 0===r?void 0:r.size)?function(){let r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;const{delayChildren:o=0,staggerChildren:i,staggerDirection:l}=a;return function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,a=arguments.length>5?arguments[5]:void 0;const i=[],l=(e.variantChildren.size-1)*r,s=1===o?function(){return(arguments.length>0&&void 0!==arguments[0]?arguments[0]:0)*r}:function(){return l-(arguments.length>0&&void 0!==arguments[0]?arguments[0]:0)*r};return Array.from(e.variantChildren).sort(Gr).forEach(((e,r)=>{i.push(qr(e,t,u(u({},a),{},{delay:n+s(r)})).then((()=>e.notify("AnimationComplete",t))))})),Promise.all(i)}(e,t,o+r,i,l,n)}:()=>Promise.resolve(),{when:s}=a;if(s){const[e,t]="beforeChildren"===s?[i,l]:[l,i];return e().then(t)}return Promise.all([i(),l(n.delay)])}function Kr(e,t){let{delay:n=0,transitionOverride:r,type:o}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};var a;let i=e.makeTargetAnimatable(t),{transition:l=e.getDefaultTransition(),transitionEnd:s}=i,c=xe(i,Xr);const d=e.getValue("willChange");r&&(l=r);const f=[],p=o&&(null===(a=e.animationState)||void 0===a?void 0:a.getState()[o]);for(const h in c){const t=e.getValue(h),r=c[h];if(!t||void 0===r||p&&Zr(p,h))continue;let o=u({delay:n,elapsed:0},l);if(e.shouldReduceMotion&&B.has(h)&&(o=u(u({},o),{},{type:!1,delay:0})),!t.hasAnimated){const t=e.getProps()[Nn];t&&(o.elapsed=_n(t,h))}let a=t.start(Yr(h,t,r,o));Mn(d)&&(d.add(h),a=a.then((()=>d.remove(h)))),f.push(a)}return Promise.all(f).then((()=>{s&&Tn(e,s)}))}function Gr(e,t){return e.sortNodePosition(t)}function Zr(e,t){let{protectedKeys:n,needsAnimating:r}=e;const o=n.hasOwnProperty(t)&&!0!==r[t];return r[t]=!1,o}const Jr=["transition","transitionEnd"],eo=[$e.Animate,$e.InView,$e.Focus,$e.Hover,$e.Tap,$e.Drag,$e.Exit],to=[...eo].reverse(),no=eo.length;function ro(e){return t=>Promise.all(t.map((t=>{let{animation:n,options:r}=t;return function(e,t){let n,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(e.notify("AnimationStart",t),Array.isArray(t)){const o=t.map((t=>qr(e,t,r)));n=Promise.all(o)}else if("string"===typeof t)n=qr(e,t,r);else{const o="function"===typeof t?En(e,t,r.custom):t;n=Kr(e,o,r)}return n.then((()=>e.notify("AnimationComplete",t)))}(e,n,r)})))}function oo(e){let t=ro(e);const n={[$e.Animate]:io(!0),[$e.InView]:io(),[$e.Hover]:io(),[$e.Tap]:io(),[$e.Drag]:io(),[$e.Focus]:io(),[$e.Exit]:io()};let r=!0;const o=(t,n)=>{const r=En(e,n);if(r){const{transition:e,transitionEnd:n}=r,o=xe(r,Jr);t=u(u(u({},t),o),n)}return t};function a(a,i){const l=e.getProps(),s=e.getVariantContext(!0)||{},c=[],d=new Set;let f={},p=1/0;for(let t=0;t<no;t++){const h=to[t],m=n[h],v=void 0!==l[h]?l[h]:s[h],g=y(v),w=h===i?m.isActive:null;!1===w&&(p=t);let x=v===s[h]&&v!==l[h]&&g;if(x&&r&&e.manuallyAnimateOnMount&&(x=!1),m.protectedKeys=u({},f),!m.isActive&&null===w||!v&&!m.prevProp||b(v)||"boolean"===typeof v)continue;const k=ao(m.prevProp,v);let S=k||h===i&&m.isActive&&!x&&g||t>p&&g;const E=Array.isArray(v)?v:[v];let C=E.reduce(o,{});!1===w&&(C={});const{prevResolvedValues:P={}}=m,T=u(u({},P),C),L=e=>{S=!0,d.delete(e),m.needsAnimating[e]=!0};for(const e in T){const t=C[e],n=P[e];f.hasOwnProperty(e)||(t!==n?Fe(t)&&Fe(n)?!Lt(t,n)||k?L(e):m.protectedKeys[e]=!0:void 0!==t?L(e):d.add(e):void 0!==t&&d.has(e)?L(e):m.protectedKeys[e]=!0)}m.prevProp=v,m.prevResolvedValues=C,m.isActive&&(f=u(u({},f),C)),r&&e.blockInitialAnimation&&(S=!1),S&&!x&&c.push(...E.map((e=>({animation:e,options:u({type:h},a)}))))}if(d.size){const t={};d.forEach((n=>{const r=e.getBaseTarget(n);void 0!==r&&(t[n]=r)})),c.push({animation:t})}let h=Boolean(c.length);return r&&!1===l.initial&&!e.manuallyAnimateOnMount&&(h=!1),r=!1,h?t(c):Promise.resolve()}return{animateChanges:a,setActive:function(t,r,o){var i;if(n[t].isActive===r)return Promise.resolve();null===(i=e.variantChildren)||void 0===i||i.forEach((e=>{var n;return null===(n=e.animationState)||void 0===n?void 0:n.setActive(t,r)})),n[t].isActive=r;const l=a(o,t);for(const e in n)n[e].protectedKeys={};return l},setAnimateFunction:function(n){t=n(e)},getState:()=>n}}function ao(e,t){return"string"===typeof t?t!==e:!!Array.isArray(t)&&!Lt(t,e)}function io(){return{isActive:arguments.length>0&&void 0!==arguments[0]&&arguments[0],protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}const lo={animation:Ct((e=>{let{visualElement:t,animate:n}=e;t.animationState||(t.animationState=oo(t)),b(n)&&(0,r.useEffect)((()=>n.subscribe(t)),[n])})),exit:Ct((e=>{const{custom:t,visualElement:n}=e,[o,a]=Tt(),i=(0,r.useContext)(f);(0,r.useEffect)((()=>{n.isPresent=o;const e=n.animationState&&n.animationState.setActive($e.Exit,!o,{custom:i&&i.custom||t});e&&!o&&e.then(a)}),[o])}))},so=(e,t)=>Math.abs(e-t);class uo{constructor(e,t){let{transformPagePoint:n}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.updatePoint=()=>{if(!this.lastMoveEvent||!this.lastMoveEventInfo)return;const e=po(this.lastMoveEventInfo,this.history),t=null!==this.startEvent,n=function(e,t){const n=so(e.x,t.x),r=so(e.y,t.y);return Math.sqrt(n**2+r**2)}(e.offset,{x:0,y:0})>=3;if(!t&&!n)return;const{point:r}=e,{timestamp:o}=_t;this.history.push(u(u({},r),{},{timestamp:o}));const{onStart:a,onMove:i}=this.handlers;t||(a&&a(this.lastMoveEvent,e),this.startEvent=this.lastMoveEvent),i&&i(this.lastMoveEvent,e)},this.handlePointerMove=(e,t)=>{this.lastMoveEvent=e,this.lastMoveEventInfo=co(t,this.transformPagePoint),Xe(e)&&0===e.buttons?this.handlePointerUp(e,t):It.update(this.updatePoint,!0)},this.handlePointerUp=(e,t)=>{this.end();const{onEnd:n,onSessionEnd:r}=this.handlers,o=po(co(t,this.transformPagePoint),this.history);this.startEvent&&n&&n(e,o),r&&r(e,o)},qe(e)&&e.touches.length>1)return;this.handlers=t,this.transformPagePoint=n;const r=co(Je(e),this.transformPagePoint),{point:o}=r,{timestamp:a}=_t;this.history=[u(u({},o),{},{timestamp:a})];const{onSessionStart:i}=t;i&&i(e,po(r,this.history)),this.removeListeners=mt(ot(window,"pointermove",this.handlePointerMove),ot(window,"pointerup",this.handlePointerUp),ot(window,"pointercancel",this.handlePointerUp))}updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),jt.update(this.updatePoint)}}function co(e,t){return t?{point:t(e.point)}:e}function fo(e,t){return{x:e.x-t.x,y:e.y-t.y}}function po(e,t){let{point:n}=e;return{point:n,delta:fo(n,mo(t)),offset:fo(n,ho(t)),velocity:vo(t,.1)}}function ho(e){return e[0]}function mo(e){return e[e.length-1]}function vo(e,t){if(e.length<2)return{x:0,y:0};let n=e.length-1,r=null;const o=mo(e);for(;n>=0&&(r=e[n],!(o.timestamp-r.timestamp>Vn(t)));)n--;if(!r)return{x:0,y:0};const a=(o.timestamp-r.timestamp)/1e3;if(0===a)return{x:0,y:0};const i={x:(o.x-r.x)/a,y:(o.y-r.y)/a};return i.x===1/0&&(i.x=0),i.y===1/0&&(i.y=0),i}function go(e){return e.max-e.min}function yo(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:.01;return Math.abs(e-t)<=n}function bo(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:.5;e.origin=r,e.originPoint=In(t.min,t.max,e.origin),e.scale=go(n)/go(t),(yo(e.scale,1,1e-4)||isNaN(e.scale))&&(e.scale=1),e.translate=In(n.min,n.max,e.origin)-e.originPoint,(yo(e.translate)||isNaN(e.translate))&&(e.translate=0)}function wo(e,t,n,r){bo(e.x,t.x,n.x,null===r||void 0===r?void 0:r.originX),bo(e.y,t.y,n.y,null===r||void 0===r?void 0:r.originY)}function xo(e,t,n){e.min=n.min+t.min,e.max=e.min+go(t)}function ko(e,t,n){e.min=t.min-n.min,e.max=e.min+go(t)}function So(e,t,n){ko(e.x,t.x,n.x),ko(e.y,t.y,n.y)}function Eo(e,t,n){return{min:void 0!==t?e.min+t:void 0,max:void 0!==n?e.max+n-(e.max-e.min):void 0}}function Co(e,t){let n=t.min-e.min,r=t.max-e.max;return t.max-t.min<e.max-e.min&&([n,r]=[r,n]),{min:n,max:r}}const Po=.35;function To(e,t,n){return{min:Lo(e,t),max:Lo(e,n)}}function Lo(e,t){return"number"===typeof e?e:e[t]||0}function Mo(e){return[e("x"),e("y")]}function _o(e){let{top:t,left:n,right:r,bottom:o}=e;return{x:{min:n,max:r},y:{min:t,max:o}}}function No(e){return void 0===e||1===e}function Vo(e){let{scale:t,scaleX:n,scaleY:r}=e;return!No(t)||!No(n)||!No(r)}function Ro(e){return Vo(e)||Do(e)||e.z||e.rotate||e.rotateX||e.rotateY}function Do(e){return Ao(e.x)||Ao(e.y)}function Ao(e){return e&&"0%"!==e}function zo(e,t,n){return n+t*(e-n)}function Oo(e,t,n,r,o){return void 0!==o&&(e=zo(e,o,r)),zo(e,n,r)+t}function Fo(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,r=arguments.length>3?arguments[3]:void 0,o=arguments.length>4?arguments[4]:void 0;e.min=Oo(e.min,t,n,r,o),e.max=Oo(e.max,t,n,r,o)}function Io(e,t){let{x:n,y:r}=t;Fo(e.x,n.translate,n.scale,n.originPoint),Fo(e.y,r.translate,r.scale,r.originPoint)}function jo(e){return Number.isInteger(e)||e>1.0000000000001||e<.999999999999?e:1}function Bo(e,t){e.min=e.min+t,e.max=e.max+t}function Uo(e,t,n){let[r,o,a]=n;const i=void 0!==t[a]?t[a]:.5,l=In(e.min,e.max,i);Fo(e,t[r],t[o],l,t.scale)}const Ho=["x","scaleX","originX"],Wo=["y","scaleY","originY"];function $o(e,t){Uo(e.x,t,Ho),Uo(e.y,t,Wo)}function Qo(e,t){return _o(function(e,t){if(!t)return e;const n=t({x:e.left,y:e.top}),r=t({x:e.right,y:e.bottom});return{top:n.y,left:n.x,bottom:r.y,right:r.x}}(e.getBoundingClientRect(),t))}const Yo=new WeakMap;class Xo{constructor(e){this.openGlobalLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic={x:{min:0,max:0},y:{min:0,max:0}},this.visualElement=e}start(e){let{snapToCursor:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!1===this.visualElement.isPresent)return;this.panSession=new uo(e,{onSessionStart:e=>{this.stopAnimation(),t&&this.snapToCursor(Je(e,"page").point)},onStart:(e,t)=>{var n;const{drag:r,dragPropagation:o,onDragStart:a}=this.getProps();(!r||o||(this.openGlobalLock&&this.openGlobalLock(),this.openGlobalLock=ut(r),this.openGlobalLock))&&(this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),Mo((e=>{var t,n;let r=this.getAxisMotionValue(e).get()||0;if(ae.test(r)){const o=null===(n=null===(t=this.visualElement.projection)||void 0===t?void 0:t.layout)||void 0===n?void 0:n.layoutBox[e];if(o){r=go(o)*(parseFloat(r)/100)}}this.originPoint[e]=r})),null===a||void 0===a||a(e,t),null===(n=this.visualElement.animationState)||void 0===n||n.setActive($e.Drag,!0))},onMove:(e,t)=>{const{dragPropagation:n,dragDirectionLock:r,onDirectionLock:o,onDrag:a}=this.getProps();if(!n&&!this.openGlobalLock)return;const{offset:i}=t;if(r&&null===this.currentDirection)return this.currentDirection=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10,n=null;Math.abs(e.y)>t?n="y":Math.abs(e.x)>t&&(n="x");return n}(i),void(null!==this.currentDirection&&(null===o||void 0===o||o(this.currentDirection)));this.updateAxis("x",t.point,i),this.updateAxis("y",t.point,i),this.visualElement.render(),null===a||void 0===a||a(e,t)},onSessionEnd:(e,t)=>this.stop(e,t)},{transformPagePoint:this.visualElement.getTransformPagePoint()})}stop(e,t){const n=this.isDragging;if(this.cancel(),!n)return;const{velocity:r}=t;this.startAnimation(r);const{onDragEnd:o}=this.getProps();null===o||void 0===o||o(e,t)}cancel(){var e,t;this.isDragging=!1,this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!1),null===(e=this.panSession)||void 0===e||e.end(),this.panSession=void 0;const{dragPropagation:n}=this.getProps();!n&&this.openGlobalLock&&(this.openGlobalLock(),this.openGlobalLock=null),null===(t=this.visualElement.animationState)||void 0===t||t.setActive($e.Drag,!1)}updateAxis(e,t,n){const{drag:r}=this.getProps();if(!n||!qo(e,r,this.currentDirection))return;const o=this.getAxisMotionValue(e);let a=this.originPoint[e]+n[e];this.constraints&&this.constraints[e]&&(a=function(e,t,n){let{min:r,max:o}=t;return void 0!==r&&e<r?e=n?In(r,e,n.min):Math.max(e,r):void 0!==o&&e>o&&(e=n?In(o,e,n.max):Math.min(e,o)),e}(a,this.constraints[e],this.elastic[e])),o.set(a)}resolveConstraints(){const{dragConstraints:e,dragElastic:t}=this.getProps(),{layout:n}=this.visualElement.projection||{},r=this.constraints;e&&g(e)?this.constraints||(this.constraints=this.resolveRefConstraints()):this.constraints=!(!e||!n)&&function(e,t){let{top:n,left:r,bottom:o,right:a}=t;return{x:Eo(e.x,r,a),y:Eo(e.y,n,o)}}(n.layoutBox,e),this.elastic=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Po;return!1===e?e=0:!0===e&&(e=Po),{x:To(e,"left","right"),y:To(e,"top","bottom")}}(t),r!==this.constraints&&n&&this.constraints&&!this.hasMutatedConstraints&&Mo((e=>{this.getAxisMotionValue(e)&&(this.constraints[e]=function(e,t){const n={};return void 0!==t.min&&(n.min=t.min-e.min),void 0!==t.max&&(n.max=t.max-e.min),n}(n.layoutBox[e],this.constraints[e]))}))}resolveRefConstraints(){const{dragConstraints:e,onMeasureDragConstraints:t}=this.getProps();if(!e||!g(e))return!1;const n=e.current,{projection:r}=this.visualElement;if(!r||!r.layout)return!1;const o=function(e,t,n){const r=Qo(e,n),{scroll:o}=t;return o&&(Bo(r.x,o.offset.x),Bo(r.y,o.offset.y)),r}(n,r.root,this.visualElement.getTransformPagePoint());let a=function(e,t){return{x:Co(e.x,t.x),y:Co(e.y,t.y)}}(r.layout.layoutBox,o);if(t){const e=t(function(e){let{x:t,y:n}=e;return{top:n.min,right:t.max,bottom:n.max,left:t.min}}(a));this.hasMutatedConstraints=!!e,e&&(a=_o(e))}return a}startAnimation(e){const{drag:t,dragMomentum:n,dragElastic:r,dragTransition:o,dragSnapToOrigin:a,onDragTransitionEnd:i}=this.getProps(),l=this.constraints||{},s=Mo((i=>{if(!qo(i,t,this.currentDirection))return;let s=(null===l||void 0===l?void 0:l[i])||{};a&&(s={min:0,max:0});const c=r?200:1e6,d=r?40:1e7,f=u(u({type:"inertia",velocity:n?e[i]:0,bounceStiffness:c,bounceDamping:d,timeConstant:750,restDelta:1,restSpeed:10},o),s);return this.startAxisValueAnimation(i,f)}));return Promise.all(s).then(i)}startAxisValueAnimation(e,t){const n=this.getAxisMotionValue(e);return n.start(Yr(e,n,0,t))}stopAnimation(){Mo((e=>this.getAxisMotionValue(e).stop()))}getAxisMotionValue(e){var t;const n="_drag"+e.toUpperCase(),r=this.visualElement.getProps()[n];return r||this.visualElement.getValue(e,(null===(t=this.visualElement.getProps().initial)||void 0===t?void 0:t[e])||0)}snapToCursor(e){Mo((t=>{const{drag:n}=this.getProps();if(!qo(t,n,this.currentDirection))return;const{projection:r}=this.visualElement,o=this.getAxisMotionValue(t);if(r&&r.layout){const{min:n,max:a}=r.layout.layoutBox[t];o.set(e[t]-In(n,a,.5))}}))}scalePositionWithinConstraints(){var e;if(!this.visualElement.current)return;const{drag:t,dragConstraints:n}=this.getProps(),{projection:r}=this.visualElement;if(!g(n)||!r||!this.constraints)return;this.stopAnimation();const o={x:0,y:0};Mo((e=>{const t=this.getAxisMotionValue(e);if(t){const n=t.get();o[e]=function(e,t){let n=.5;const r=go(e),o=go(t);return o>r?n=qn(t.min,t.max-r,e.min):r>o&&(n=qn(e.min,e.max-o,t.min)),X(0,1,n)}({min:n,max:n},this.constraints[e])}}));const{transformTemplate:a}=this.visualElement.getProps();this.visualElement.current.style.transform=a?a({},""):"none",null===(e=r.root)||void 0===e||e.updateScroll(),r.updateLayout(),this.resolveConstraints(),Mo((e=>{if(!qo(e,t,null))return;const n=this.getAxisMotionValue(e),{min:r,max:a}=this.constraints[e];n.set(In(r,a,o[e]))}))}addListeners(){var e;if(!this.visualElement.current)return;Yo.set(this.visualElement,this);const t=ot(this.visualElement.current,"pointerdown",(e=>{const{drag:t,dragListener:n=!0}=this.getProps();t&&n&&this.start(e)})),n=()=>{const{dragConstraints:e}=this.getProps();g(e)&&(this.constraints=this.resolveRefConstraints())},{projection:r}=this.visualElement,o=r.addEventListener("measure",n);r&&!r.layout&&(null===(e=r.root)||void 0===e||e.updateScroll(),r.updateLayout()),n();const a=Qe(window,"resize",(()=>this.scalePositionWithinConstraints())),i=r.addEventListener("didUpdate",(e=>{let{delta:t,hasLayoutChanged:n}=e;this.isDragging&&n&&(Mo((e=>{const n=this.getAxisMotionValue(e);n&&(this.originPoint[e]+=t[e].translate,n.set(n.get()+t[e].translate))})),this.visualElement.render())}));return()=>{a(),t(),o(),null===i||void 0===i||i()}}getProps(){const e=this.visualElement.getProps(),{drag:t=!1,dragDirectionLock:n=!1,dragPropagation:r=!1,dragConstraints:o=!1,dragElastic:a=Po,dragMomentum:i=!0}=e;return u(u({},e),{},{drag:t,dragDirectionLock:n,dragPropagation:r,dragConstraints:o,dragElastic:a,dragMomentum:i})}}function qo(e,t,n){return(!0===t||t===e)&&(null===n||n===e)}const Ko={pan:Ct((function(e){let{onPan:t,onPanStart:n,onPanEnd:o,onPanSessionStart:a,visualElement:i}=e;const l=t||n||o||a,s=(0,r.useRef)(null),{transformPagePoint:u}=(0,r.useContext)(c),d={onSessionStart:a,onStart:n,onMove:t,onEnd:(e,t)=>{s.current=null,o&&o(e,t)}};(0,r.useEffect)((()=>{null!==s.current&&s.current.updateHandlers(d)})),at(i,"pointerdown",l&&function(e){s.current=new uo(e,d,{transformPagePoint:u})}),pt((()=>s.current&&s.current.end()))})),drag:Ct((function(e){const{dragControls:t,visualElement:n}=e,o=T((()=>new Xo(n)));(0,r.useEffect)((()=>t&&t.subscribe(o)),[o,t]),(0,r.useEffect)((()=>o.addListeners()),[o])}))};function Go(e){return"string"===typeof e&&e.startsWith("var(--")}const Zo=/var\((--[a-zA-Z0-9-_]+),? ?([a-zA-Z0-9 ()%#.,-]+)?\)/;function Jo(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1;'Max CSS variable fallback depth detected in property "'.concat(e,'". This may indicate a circular fallback dependency.');const[r,o]=function(e){const t=Zo.exec(e);if(!t)return[,];const[,n,r]=t;return[n,r]}(e);if(!r)return;const a=window.getComputedStyle(t).getPropertyValue(r);return a?a.trim():Go(o)?Jo(o,t,n+1):o}function ea(e,t,n){let r=Object.assign({},(function(e){if(null==e)throw new TypeError("Cannot destructure "+e)}(t),t));const o=e.current;if(!(o instanceof Element))return{target:r,transitionEnd:n};n&&(n=u({},n)),e.values.forEach((e=>{const t=e.get();if(!Go(t))return;const n=Jo(t,o);n&&e.set(n)}));for(const a in r){const e=r[a];if(!Go(e))continue;const t=Jo(e,o);t&&(r[a]=t,n&&void 0===n[a]&&(n[a]=e))}return{target:r,transitionEnd:n}}const ta=new Set(["width","height","top","left","right","bottom","x","y"]),na=e=>ta.has(e),ra=(e,t)=>{e.set(t,!1),e.set(t)},oa=e=>e===q||e===ie;var aa;!function(e){e.width="width",e.height="height",e.left="left",e.right="right",e.top="top",e.bottom="bottom"}(aa||(aa={}));const ia=(e,t)=>parseFloat(e.split(", ")[t]),la=(e,t)=>(n,r)=>{let{transform:o}=r;if("none"===o||!o)return 0;const a=o.match(/^matrix3d\((.+)\)$/);if(a)return ia(a[1],t);{const t=o.match(/^matrix\((.+)\)$/);return t?ia(t[1],e):0}},sa=new Set(["x","y","z"]),ua=j.filter((e=>!sa.has(e)));const ca={width:(e,t)=>{let{x:n}=e,{paddingLeft:r="0",paddingRight:o="0"}=t;return n.max-n.min-parseFloat(r)-parseFloat(o)},height:(e,t)=>{let{y:n}=e,{paddingTop:r="0",paddingBottom:o="0"}=t;return n.max-n.min-parseFloat(r)-parseFloat(o)},top:(e,t)=>{let{top:n}=t;return parseFloat(n)},left:(e,t)=>{let{left:n}=t;return parseFloat(n)},bottom:(e,t)=>{let{y:n}=e,{top:r}=t;return parseFloat(r)+(n.max-n.min)},right:(e,t)=>{let{x:n}=e,{left:r}=t;return parseFloat(r)+(n.max-n.min)},x:la(4,13),y:la(5,14)},da=function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};t=u({},t),r=u({},r);const o=Object.keys(t).filter(na);let a=[],i=!1;const l=[];if(o.forEach((o=>{const s=e.getValue(o);if(!e.hasValue(o))return;let u=n[o],c=xn(u);const d=t[o];let f;if(Fe(d)){const e=d.length,t=null===d[0]?1:0;u=d[t],c=xn(u);for(let n=t;n<e;n++)f?xn(d[n]):(f=xn(d[n]),f===c||oa(c)&&oa(f))}else f=xn(d);if(c!==f)if(oa(c)&&oa(f)){const e=s.get();"string"===typeof e&&s.set(parseFloat(e)),"string"===typeof d?t[o]=parseFloat(d):Array.isArray(d)&&f===ie&&(t[o]=d.map(parseFloat))}else(null===c||void 0===c?void 0:c.transform)&&(null===f||void 0===f?void 0:f.transform)&&(0===u||0===d)?0===u?s.set(f.transform(u)):t[o]=c.transform(d):(i||(a=function(e){const t=[];return ua.forEach((n=>{const r=e.getValue(n);void 0!==r&&(t.push([n,r.get()]),r.set(n.startsWith("scale")?1:0))})),t.length&&e.render(),t}(e),i=!0),l.push(o),r[o]=void 0!==r[o]?r[o]:t[o],ra(s,d))})),l.length){const n=l.indexOf("height")>=0?window.pageYOffset:null,o=((e,t,n)=>{const r=t.measureViewportBox(),o=t.current,a=getComputedStyle(o),{display:i}=a,l={};"none"===i&&t.setStaticValue("display",e.display||"block"),n.forEach((e=>{l[e]=ca[e](r,a)})),t.render();const s=t.measureViewportBox();return n.forEach((n=>{const r=t.getValue(n);ra(r,l[n]),e[n]=ca[n](s,a)})),e})(t,e,l);return a.length&&a.forEach((t=>{let[n,r]=t;e.getValue(n).set(r)})),e.render(),p&&null!==n&&window.scrollTo({top:n}),{target:o,transitionEnd:r}}return{target:t,transitionEnd:r}};function fa(e,t,n,r){return(e=>Object.keys(e).some(na))(t)?da(e,t,n,r):{target:t,transitionEnd:r}}const pa={current:null},ha={current:!1};const ma=["willChange"],va=Object.keys(P),ga=va.length,ya=["AnimationStart","AnimationComplete","Update","Unmount","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class ba{constructor(e){let{parent:t,props:n,reducedMotionConfig:r,visualState:o}=e,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.isPresent=!0,this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.scheduleRender=()=>It.render(this.render,!1,!0);const{latestValues:i,renderState:l}=o;this.latestValues=i,this.baseTarget=u({},i),this.initialValues=n.initial?u({},i):{},this.renderState=l,this.parent=t,this.props=n,this.depth=t?t.depth+1:0,this.reducedMotionConfig=r,this.options=a,this.isControllingVariants=x(n),this.isVariantNode=k(n),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=Boolean(t&&t.current);const s=this.scrapeMotionValuesFromProps(n),{willChange:c}=s,d=xe(s,ma);for(const u in d){const e=d[u];void 0!==i[u]&&H(e)&&(e.set(i[u],!1),Mn(c)&&c.add(u))}}scrapeMotionValuesFromProps(e){return{}}mount(e){var t;this.current=e,this.projection&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=null===(t=this.parent)||void 0===t?void 0:t.addVariantChild(this)),this.values.forEach(((e,t)=>this.bindToMotionValue(t,e))),ha.current||function(){if(ha.current=!0,p)if(window.matchMedia){const e=window.matchMedia("(prefers-reduced-motion)"),t=()=>pa.current=e.matches;e.addListener(t),t()}else pa.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||pa.current),this.parent&&this.parent.children.add(this),this.setProps(this.props)}unmount(){var e,t,n;null===(e=this.projection)||void 0===e||e.unmount(),jt.update(this.notifyUpdate),jt.render(this.render),this.valueSubscriptions.forEach((e=>e())),null===(t=this.removeFromVariantTree)||void 0===t||t.call(this),null===(n=this.parent)||void 0===n||n.children.delete(this);for(const r in this.events)this.events[r].clear();this.current=null}bindToMotionValue(e,t){const n=B.has(e),r=t.on("change",(t=>{this.latestValues[e]=t,this.props.onUpdate&&It.update(this.notifyUpdate,!1,!0),n&&this.projection&&(this.projection.isTransformDirty=!0)})),o=t.on("renderRequest",this.scheduleRender);this.valueSubscriptions.set(e,(()=>{r(),o()}))}sortNodePosition(e){return this.current&&this.sortInstanceNodePosition&&this.type===e.type?this.sortInstanceNodePosition(this.current,e.current):0}loadFeatures(e,t,n,o,a,i){const l=[];for(let s=0;s<ga;s++){const t=va[s],{isEnabled:n,Component:o}=P[t];n(e)&&o&&l.push((0,r.createElement)(o,u(u({key:t},e),{},{visualElement:this})))}if(!this.projection&&a){this.projection=new a(o,this.latestValues,this.parent&&this.parent.projection);const{layoutId:t,layout:n,drag:r,dragConstraints:l,layoutScroll:s}=e;this.projection.setOptions({layoutId:t,layout:n,alwaysMeasureLayout:Boolean(r)||l&&g(l),visualElement:this,scheduleRender:()=>this.scheduleRender(),animationType:"string"===typeof n?n:"both",initialPromotionConfig:i,layoutScroll:s})}return l}triggerBuild(){this.build(this.renderState,this.latestValues,this.options,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):{x:{min:0,max:0},y:{min:0,max:0}}}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,t){this.latestValues[e]=t}makeTargetAnimatable(e){let t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];return this.makeTargetAnimatableFromInstance(e,this.props,t)}setProps(e){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.props=e;for(let t=0;t<ya.length;t++){const n=ya[t];this.propEventSubscriptions[n]&&(this.propEventSubscriptions[n](),delete this.propEventSubscriptions[n]);const r=e["on"+n];r&&(this.propEventSubscriptions[n]=this.on(n,r))}this.prevMotionValues=function(e,t,n){const{willChange:r}=t;for(const o in t){const a=t[o],i=n[o];if(H(a))e.addValue(o,a),Mn(r)&&r.add(o);else if(H(i))e.addValue(o,Kt(a,{owner:e})),Mn(r)&&r.remove(o);else if(i!==a)if(e.hasValue(o)){const t=e.getValue(o);!t.hasAnimated&&t.set(a)}else{const t=e.getStaticValue(o);e.addValue(o,Kt(void 0!==t?t:a))}}for(const o in n)void 0===t[o]&&e.removeValue(o);return t}(this,this.scrapeMotionValuesFromProps(e),this.prevMotionValues)}getProps(){return this.props}getVariant(e){var t;return null===(t=this.props.variants)||void 0===t?void 0:t[e]}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){var e;return this.isVariantNode?this:null===(e=this.parent)||void 0===e?void 0:e.getClosestVariantNode()}getVariantContext(){var e,t;if(arguments.length>0&&void 0!==arguments[0]&&arguments[0])return null===(e=this.parent)||void 0===e?void 0:e.getVariantContext();if(!this.isControllingVariants){const e=(null===(t=this.parent)||void 0===t?void 0:t.getVariantContext())||{};return void 0!==this.props.initial&&(e.initial=this.props.initial),e}const n={};for(let r=0;r<xa;r++){const e=wa[r],t=this.props[e];(y(t)||!1===t)&&(n[e]=t)}return n}addVariantChild(e){var t;const n=this.getClosestVariantNode();if(n)return null===(t=n.variantChildren)||void 0===t||t.add(e),()=>n.variantChildren.delete(e)}addValue(e,t){this.hasValue(e)&&this.removeValue(e),this.values.set(e,t),this.latestValues[e]=t.get(),this.bindToMotionValue(e,t)}removeValue(e){var t;this.values.delete(e),null===(t=this.valueSubscriptions.get(e))||void 0===t||t(),this.valueSubscriptions.delete(e),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,t){if(this.props.values&&this.props.values[e])return this.props.values[e];let n=this.values.get(e);return void 0===n&&void 0!==t&&(n=Kt(t,{owner:this}),this.addValue(e,n)),n}readValue(e){return void 0===this.latestValues[e]&&this.current?this.readValueFromInstance(this.current,e,this.options):this.latestValues[e]}setBaseTarget(e,t){this.baseTarget[e]=t}getBaseTarget(e){var t;const{initial:n}=this.props,r="string"===typeof n||"object"===typeof n?null===(t=Oe(this.props,n))||void 0===t?void 0:t[e]:void 0;if(n&&void 0!==r)return r;const o=this.getBaseTargetFromProps(this.props,e);return void 0===o||H(o)?void 0!==this.initialValues[e]&&void 0===r?void 0:this.baseTarget[e]:o}on(e,t){return this.events[e]||(this.events[e]=new Yt),this.events[e].add(t)}notify(e){for(var t,n=arguments.length,r=new Array(n>1?n-1:0),o=1;o<n;o++)r[o-1]=arguments[o];null===(t=this.events[e])||void 0===t||t.notify(...r)}}const wa=["initial",...eo],xa=wa.length,ka=["transition","transitionEnd"];class Sa extends ba{sortInstanceNodePosition(e,t){return 2&e.compareDocumentPosition(t)?1:-1}getBaseTargetFromProps(e,t){var n;return null===(n=e.style)||void 0===n?void 0:n[t]}removeValueFromRenderState(e,t){let{vars:n,style:r}=t;delete n[e],delete r[e]}makeTargetAnimatableFromInstance(e,t,n){let{transition:r,transitionEnd:o}=e,a=xe(e,ka),{transformValues:i}=t,l=function(e,t,n){var r;const o={};for(const a in e){const e=Ln(a,t);o[a]=void 0!==e?e:null===(r=n.getValue(a))||void 0===r?void 0:r.get()}return o}(a,r||{},this);if(i&&(o&&(o=i(o)),a&&(a=i(a)),l&&(l=i(l))),n){!function(e,t,n){var r,o;const a=Object.keys(t).filter((t=>!e.hasValue(t))),i=a.length;if(i)for(let l=0;l<i;l++){const i=a[l],s=t[i];let u=null;Array.isArray(s)&&(u=s[0]),null===u&&(u=null!==(o=null!==(r=n[i])&&void 0!==r?r:e.readValue(i))&&void 0!==o?o:t[i]),void 0!==u&&null!==u&&("string"===typeof u&&(/^\-?\d*\.?\d+$/.test(u)||Mt(u))?u=parseFloat(u):!Sn(u)&&dn.test(s)&&(u=yn(i,s)),e.addValue(i,Kt(u,{owner:e})),void 0===n[i]&&(n[i]=u),null!==u&&e.setBaseTarget(i,u))}}(this,a,l);const e=((e,t,n,r)=>{const o=ea(e,t,r);return fa(e,t=o.target,n,r=o.transitionEnd)})(this,a,l,o);o=e.transitionEnd,a=e.target}return u({transition:r,transitionEnd:o},a)}}class Ea extends Sa{readValueFromInstance(e,t){if(B.has(t)){const e=gn(t);return e&&e.default||0}{const r=(n=e,window.getComputedStyle(n)),o=(Q(t)?r.getPropertyValue(t):r[t])||0;return"string"===typeof o?o.trim():o}var n}measureInstanceViewportBox(e,t){let{transformPagePoint:n}=t;return Qo(e,n)}build(e,t,n,r){fe(e,t,n,r.transformTemplate)}scrapeMotionValuesFromProps(e){return Ae(e)}renderInstance(e,t,n,r){Ve(e,t,n,r)}}class Ca extends Sa{constructor(){super(...arguments),this.isSVGTag=!1}getBaseTargetFromProps(e,t){return e[t]}readValueFromInstance(e,t){var n;return B.has(t)?(null===(n=gn(t))||void 0===n?void 0:n.default)||0:(t=Re.has(t)?t:Ne(t),e.getAttribute(t))}measureInstanceViewportBox(){return{x:{min:0,max:0},y:{min:0,max:0}}}scrapeMotionValuesFromProps(e){return ze(e)}build(e,t,n,r){Pe(e,t,n,this.isSVGTag,r.transformTemplate)}renderInstance(e,t,n,r){De(e,t,0,r)}mount(e){this.isSVGTag=Le(e.tagName),super.mount(e)}}const Pa=(e,t)=>F(e)?new Ca(t,{enableHardwareAcceleration:!1}):new Ea(t,{enableHardwareAcceleration:!0});function Ta(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}const La={correct:(e,t)=>{if(!t.target)return e;if("string"===typeof e){if(!ie.test(e))return e;e=parseFloat(e)}const n=Ta(e,t.target.x),r=Ta(e,t.target.y);return"".concat(n,"% ").concat(r,"%")}},Ma="_$css",_a={correct:(e,t)=>{let{treeScale:n,projectionDelta:r}=t;const o=e,a=e.includes("var("),i=[];a&&(e=e.replace(Zo,(e=>(i.push(e),Ma))));const l=dn.parse(e);if(l.length>5)return o;const s=dn.createTransformer(e),u="number"!==typeof l[0]?1:0,c=r.x.scale*n.x,d=r.y.scale*n.y;l[0+u]/=c,l[1+u]/=d;const f=In(c,d,.5);"number"===typeof l[2+u]&&(l[2+u]/=f),"number"===typeof l[3+u]&&(l[3+u]/=f);let p=s(l);if(a){let e=0;p=p.replace(Ma,(()=>{const t=i[e];return e++,t}))}return p}};class Na extends r.Component{componentDidMount(){const{visualElement:e,layoutGroup:t,switchLayoutGroup:n,layoutId:r}=this.props,{projection:o}=e;var a;a=Va,Object.assign(I,a),o&&(t.group&&t.group.add(o),n&&n.register&&r&&n.register(o),o.root.didUpdate(),o.addEventListener("animationComplete",(()=>{this.safeToRemove()})),o.setOptions(u(u({},o.options),{},{onExitComplete:()=>this.safeToRemove()}))),L.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){const{layoutDependency:t,visualElement:n,drag:r,isPresent:o}=this.props,a=n.projection;return a?(a.isPresent=o,r||e.layoutDependency!==t||void 0===t?a.willUpdate():this.safeToRemove(),e.isPresent!==o&&(o?a.promote():a.relegate()||It.postRender((()=>{var e;(null===(e=a.getStack())||void 0===e?void 0:e.members.length)||this.safeToRemove()}))),null):null}componentDidUpdate(){const{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),!e.currentAnimation&&e.isLead()&&this.safeToRemove())}componentWillUnmount(){const{visualElement:e,layoutGroup:t,switchLayoutGroup:n}=this.props,{projection:r}=e;r&&(r.scheduleCheckAfterUnmount(),(null===t||void 0===t?void 0:t.group)&&t.group.remove(r),(null===n||void 0===n?void 0:n.deregister)&&n.deregister(r))}safeToRemove(){const{safeToRemove:e}=this.props;null===e||void 0===e||e()}render(){return null}}const Va={borderRadius:u(u({},La),{},{applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]}),borderTopLeftRadius:La,borderTopRightRadius:La,borderBottomLeftRadius:La,borderBottomRightRadius:La,boxShadow:_a},Ra={measureLayout:function(e){const[t,n]=Tt(),o=(0,r.useContext)(_);return r.createElement(Na,u(u({},e),{},{layoutGroup:o,switchLayoutGroup:(0,r.useContext)(V),isPresent:t,safeToRemove:n}))}};const Da=["TopLeft","TopRight","BottomLeft","BottomRight"],Aa=Da.length,za=e=>"string"===typeof e?parseFloat(e):e,Oa=e=>"number"===typeof e||ie.test(e);function Fa(e,t){return void 0!==e[t]?e[t]:e.borderRadius}const Ia=Ba(0,.5,rr),ja=Ba(.5,.95,Jn);function Ba(e,t,n){return r=>r<e?0:r>t?1:n(qn(e,t,r))}function Ua(e,t){e.min=t.min,e.max=t.max}function Ha(e,t){Ua(e.x,t.x),Ua(e.y,t.y)}function Wa(e,t,n,r,o){return e=zo(e-=t,1/n,r),void 0!==o&&(e=zo(e,1/o,r)),e}function $a(e,t,n,r,o){let[a,i,l]=n;!function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:.5,o=arguments.length>4?arguments[4]:void 0,a=arguments.length>5&&void 0!==arguments[5]?arguments[5]:e,i=arguments.length>6&&void 0!==arguments[6]?arguments[6]:e;ae.test(t)&&(t=parseFloat(t),t=In(i.min,i.max,t/100)-i.min);if("number"!==typeof t)return;let l=In(a.min,a.max,r);e===a&&(l-=t),e.min=Wa(e.min,t,n,l,o),e.max=Wa(e.max,t,n,l,o)}(e,t[a],t[i],t[l],t.scale,r,o)}const Qa=["x","scaleX","originX"],Ya=["y","scaleY","originY"];function Xa(e,t,n,r){$a(e.x,t,Qa,null===n||void 0===n?void 0:n.x,null===r||void 0===r?void 0:r.x),$a(e.y,t,Ya,null===n||void 0===n?void 0:n.y,null===r||void 0===r?void 0:r.y)}function qa(e){return 0===e.translate&&1===e.scale}function Ka(e){return qa(e.x)&&qa(e.y)}function Ga(e,t){return e.x.min===t.x.min&&e.x.max===t.x.max&&e.y.min===t.y.min&&e.y.max===t.y.max}function Za(e){return go(e.x)/go(e.y)}class Ja{constructor(){this.members=[]}add(e){$t(this.members,e),e.scheduleRender()}remove(e){if(Qt(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){const e=this.members[this.members.length-1];e&&this.promote(e)}}relegate(e){const t=this.members.findIndex((t=>e===t));if(0===t)return!1;let n;for(let r=t;r>=0;r--){const e=this.members[r];if(!1!==e.isPresent){n=e;break}}return!!n&&(this.promote(n),!0)}promote(e,t){var n;const r=this.lead;if(e!==r&&(this.prevLead=r,this.lead=e,e.show(),r)){r.instance&&r.scheduleRender(),e.scheduleRender(),e.resumeFrom=r,t&&(e.resumeFrom.preserveOpacity=!0),r.snapshot&&(e.snapshot=r.snapshot,e.snapshot.latestValues=r.animationValues||r.latestValues),(null===(n=e.root)||void 0===n?void 0:n.isUpdating)&&(e.isLayoutDirty=!0);const{crossfade:o}=e.options;!1===o&&r.hide()}}exitAnimationComplete(){this.members.forEach((e=>{var t,n,r,o,a;null===(n=(t=e.options).onExitComplete)||void 0===n||n.call(t),null===(a=null===(r=e.resumingFrom)||void 0===r?void 0:(o=r.options).onExitComplete)||void 0===a||a.call(o)}))}scheduleRender(){this.members.forEach((e=>{e.instance&&e.scheduleRender(!1)}))}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function ei(e,t,n){let r="";const o=e.x.translate/t.x,a=e.y.translate/t.y;if((o||a)&&(r="translate3d(".concat(o,"px, ").concat(a,"px, 0) ")),1===t.x&&1===t.y||(r+="scale(".concat(1/t.x,", ").concat(1/t.y,") ")),n){const{rotate:e,rotateX:t,rotateY:o}=n;e&&(r+="rotate(".concat(e,"deg) ")),t&&(r+="rotateX(".concat(t,"deg) ")),o&&(r+="rotateY(".concat(o,"deg) "))}const i=e.x.scale*t.x,l=e.y.scale*t.y;return 1===i&&1===l||(r+="scale(".concat(i,", ").concat(l,")")),r||"none"}const ti=(e,t)=>e.depth-t.depth;class ni{constructor(){this.children=[],this.isDirty=!1}add(e){$t(this.children,e),this.isDirty=!0}remove(e){Qt(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(ti),this.isDirty=!1,this.children.forEach(e)}}const ri=["","X","Y","Z"];let oi=0;function ai(e){let{attachResizeListener:t,defaultParent:n,measureScroll:r,checkIsScrollRoot:o,resetTransform:a}=e;return class{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null===n||void 0===n?void 0:n();this.id=oi++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isTransformDirty=!1,this.isProjectionDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.potentialNodes=new Map,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.nodes.forEach(si),this.nodes.forEach(pi),this.nodes.forEach(hi)},this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.elementId=e,this.latestValues=t,this.root=r?r.root||r:this,this.path=r?[...r.path,r]:[],this.parent=r,this.depth=r?r.depth+1:0,e&&this.root.registerPotentialNode(e,this);for(let n=0;n<this.path.length;n++)this.path[n].shouldResetTransform=!0;this.root===this&&(this.nodes=new ni)}addEventListener(e,t){return this.eventHandlers.has(e)||this.eventHandlers.set(e,new Yt),this.eventHandlers.get(e).add(t)}notifyListeners(e){const t=this.eventHandlers.get(e);for(var n=arguments.length,r=new Array(n>1?n-1:0),o=1;o<n;o++)r[o-1]=arguments[o];null===t||void 0===t||t.notify(...r)}hasListeners(e){return this.eventHandlers.has(e)}registerPotentialNode(e,t){this.potentialNodes.set(e,t)}mount(e){let n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];var r;if(this.instance)return;this.isSVG=e instanceof SVGElement&&"svg"!==e.tagName,this.instance=e;const{layoutId:o,layout:a,visualElement:i}=this.options;if(i&&!i.current&&i.mount(e),this.root.nodes.add(this),null===(r=this.parent)||void 0===r||r.children.add(this),this.elementId&&this.root.potentialNodes.delete(this.elementId),n&&(a||o)&&(this.isLayoutDirty=!0),t){let n;const r=()=>this.root.updateBlockedByResize=!1;t(e,(()=>{this.root.updateBlockedByResize=!0,n&&n(),n=Nr(r,250),L.hasAnimatedSinceResize&&(L.hasAnimatedSinceResize=!1,this.nodes.forEach(fi))}))}o&&this.root.registerSharedNode(o,this),!1!==this.options.animate&&i&&(o||a)&&this.addEventListener("didUpdate",(e=>{let{delta:t,hasLayoutChanged:n,hasRelativeTargetChanged:r,layout:o}=e;var a,l,s,c,d;if(this.isTreeAnimationBlocked())return this.target=void 0,void(this.relativeTarget=void 0);const f=null!==(l=null!==(a=this.options.transition)&&void 0!==a?a:i.getDefaultTransition())&&void 0!==l?l:wi,{onLayoutAnimationStart:p,onLayoutAnimationComplete:h}=i.getProps(),m=!this.targetLayout||!Ga(this.targetLayout,o)||r,v=!n&&r;if((null===(s=this.resumeFrom)||void 0===s?void 0:s.instance)||v||n&&(m||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(t,v);const e=u(u({},Ur(f,"layout")),{},{onPlay:p,onComplete:h});i.shouldReduceMotion&&(e.delay=0,e.type=!1),this.startAnimation(e)}else n||0!==this.animationProgress||fi(this),this.isLead()&&(null===(d=(c=this.options).onExitComplete)||void 0===d||d.call(c));this.targetLayout=o}))}unmount(){var e,t;this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this),null===(e=this.getStack())||void 0===e||e.remove(this),null===(t=this.parent)||void 0===t||t.children.delete(this),this.instance=void 0,jt.preRender(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){var e;return this.isAnimationBlocked||(null===(e=this.parent)||void 0===e?void 0:e.isTreeAnimationBlocked())||!1}startUpdate(){var e;this.isUpdateBlocked()||(this.isUpdating=!0,null===(e=this.nodes)||void 0===e||e.forEach(mi),this.animationId++)}willUpdate(){let e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];var t,n,r;if(this.root.isUpdateBlocked())return void(null===(n=(t=this.options).onExitComplete)||void 0===n||n.call(t));if(!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let l=0;l<this.path.length;l++){const e=this.path[l];e.shouldResetTransform=!0,e.updateScroll("snapshot")}const{layoutId:o,layout:a}=this.options;if(void 0===o&&!a)return;const i=null===(r=this.options.visualElement)||void 0===r?void 0:r.getProps().transformTemplate;this.prevTransformTemplateValue=null===i||void 0===i?void 0:i(this.latestValues,""),this.updateSnapshot(),e&&this.notifyListeners("willUpdate")}didUpdate(){if(this.isUpdateBlocked())return this.unblockUpdate(),this.clearAllSnapshots(),void this.nodes.forEach(ci);this.isUpdating&&(this.isUpdating=!1,this.potentialNodes.size&&(this.potentialNodes.forEach(xi),this.potentialNodes.clear()),this.nodes.forEach(di),this.nodes.forEach(ii),this.nodes.forEach(li),this.clearAllSnapshots(),Bt.update(),Bt.preRender(),Bt.render())}clearAllSnapshots(){this.nodes.forEach(ui),this.sharedNodes.forEach(vi)}scheduleUpdateProjection(){It.preRender(this.updateProjection,!1,!0)}scheduleCheckAfterUnmount(){It.postRender((()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()}))}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure())}updateLayout(){var e;if(!this.instance)return;if(this.updateScroll(),(!this.options.alwaysMeasureLayout||!this.isLead())&&!this.isLayoutDirty)return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let n=0;n<this.path.length;n++){this.path[n].updateScroll()}const t=this.layout;this.layout=this.measure(!1),this.layoutCorrected={x:{min:0,max:0},y:{min:0,max:0}},this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox),null===(e=this.options.visualElement)||void 0===e||e.notify("LayoutMeasure",this.layout.layoutBox,null===t||void 0===t?void 0:t.layoutBox)}updateScroll(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"measure",t=Boolean(this.options.layoutScroll&&this.instance);this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===e&&(t=!1),t&&(this.scroll={animationId:this.root.animationId,phase:e,isRoot:o(this.instance),offset:r(this.instance)})}resetTransform(){var e;if(!a)return;const t=this.isLayoutDirty||this.shouldResetTransform,n=this.projectionDelta&&!Ka(this.projectionDelta),r=null===(e=this.options.visualElement)||void 0===e?void 0:e.getProps().transformTemplate,o=null===r||void 0===r?void 0:r(this.latestValues,""),i=o!==this.prevTransformTemplateValue;t&&(n||Ro(this.latestValues)||i)&&(a(this.instance,o),this.shouldResetTransform=!1,this.scheduleRender())}measure(){let e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];const t=this.measurePageBox();let n=this.removeElementScroll(t);var r;return e&&(n=this.removeTransform(n)),ki((r=n).x),ki(r.y),{animationId:this.root.animationId,measuredBox:t,layoutBox:n,latestValues:{},source:this.id}}measurePageBox(){const{visualElement:e}=this.options;if(!e)return{x:{min:0,max:0},y:{min:0,max:0}};const t=e.measureViewportBox(),{scroll:n}=this.root;return n&&(Bo(t.x,n.offset.x),Bo(t.y,n.offset.y)),t}removeElementScroll(e){const t={x:{min:0,max:0},y:{min:0,max:0}};Ha(t,e);for(let n=0;n<this.path.length;n++){const r=this.path[n],{scroll:o,options:a}=r;if(r!==this.root&&o&&a.layoutScroll){if(o.isRoot){Ha(t,e);const{scroll:n}=this.root;n&&(Bo(t.x,-n.offset.x),Bo(t.y,-n.offset.y))}Bo(t.x,o.offset.x),Bo(t.y,o.offset.y)}}return t}applyTransform(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];const n={x:{min:0,max:0},y:{min:0,max:0}};Ha(n,e);for(let r=0;r<this.path.length;r++){const e=this.path[r];!t&&e.options.layoutScroll&&e.scroll&&e!==e.root&&$o(n,{x:-e.scroll.offset.x,y:-e.scroll.offset.y}),Ro(e.latestValues)&&$o(n,e.latestValues)}return Ro(this.latestValues)&&$o(n,this.latestValues),n}removeTransform(e){var t;const n={x:{min:0,max:0},y:{min:0,max:0}};Ha(n,e);for(let r=0;r<this.path.length;r++){const e=this.path[r];if(!e.instance)continue;if(!Ro(e.latestValues))continue;Vo(e.latestValues)&&e.updateSnapshot();const o={x:{min:0,max:0},y:{min:0,max:0}};Ha(o,e.measurePageBox()),Xa(n,e.latestValues,null===(t=e.snapshot)||void 0===t?void 0:t.layoutBox,o)}return Ro(this.latestValues)&&Xa(n,this.latestValues),n}setTargetDelta(e){this.targetDelta=e,this.isProjectionDirty=!0,this.root.scheduleUpdateProjection()}setOptions(e){this.options=u(u(u({},this.options),e),{},{crossfade:void 0===e.crossfade||e.crossfade})}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}resolveTargetDelta(){var e;const t=this.getLead();if(this.isProjectionDirty||(this.isProjectionDirty=t.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=t.isTransformDirty),!this.isProjectionDirty&&!this.attemptToResolveRelativeTarget)return;const{layout:n,layoutId:r}=this.options;if(this.layout&&(n||r)){if(!this.targetDelta&&!this.relativeTarget){const e=this.getClosestProjectingParent();e&&e.layout?(this.relativeParent=e,this.relativeTarget={x:{min:0,max:0},y:{min:0,max:0}},this.relativeTargetOrigin={x:{min:0,max:0},y:{min:0,max:0}},So(this.relativeTargetOrigin,this.layout.layoutBox,e.layout.layoutBox),Ha(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}var o,a,i;if(this.relativeTarget||this.targetDelta)if(this.target||(this.target={x:{min:0,max:0},y:{min:0,max:0}},this.targetWithTransforms={x:{min:0,max:0},y:{min:0,max:0}}),this.relativeTarget&&this.relativeTargetOrigin&&(null===(e=this.relativeParent)||void 0===e?void 0:e.target)?(o=this.target,a=this.relativeTarget,i=this.relativeParent.target,xo(o.x,a.x,i.x),xo(o.y,a.y,i.y)):this.targetDelta?(Boolean(this.resumingFrom)?this.target=this.applyTransform(this.layout.layoutBox):Ha(this.target,this.layout.layoutBox),Io(this.target,this.targetDelta)):Ha(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;const e=this.getClosestProjectingParent();e&&Boolean(e.resumingFrom)===Boolean(this.resumingFrom)&&!e.options.layoutScroll&&e.target?(this.relativeParent=e,this.relativeTarget={x:{min:0,max:0},y:{min:0,max:0}},this.relativeTargetOrigin={x:{min:0,max:0},y:{min:0,max:0}},So(this.relativeTargetOrigin,this.target,e.target),Ha(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}}}getClosestProjectingParent(){if(this.parent&&!Vo(this.parent.latestValues)&&!Do(this.parent.latestValues))return(this.parent.relativeTarget||this.parent.targetDelta)&&this.parent.layout?this.parent:this.parent.getClosestProjectingParent()}calcProjection(){var e;const{isProjectionDirty:t,isTransformDirty:n}=this;this.isProjectionDirty=this.isTransformDirty=!1;const r=this.getLead(),o=Boolean(this.resumingFrom)||this!==r;let a=!0;if(t&&(a=!1),o&&n&&(a=!1),a)return;const{layout:i,layoutId:l}=this.options;if(this.isTreeAnimating=Boolean((null===(e=this.parent)||void 0===e?void 0:e.isTreeAnimating)||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!i&&!l)return;Ha(this.layoutCorrected,this.layout.layoutBox),function(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]&&arguments[3];var o,a;const i=n.length;if(!i)return;let l,s;t.x=t.y=1;for(let u=0;u<i;u++)l=n[u],s=l.projectionDelta,"contents"!==(null===(a=null===(o=l.instance)||void 0===o?void 0:o.style)||void 0===a?void 0:a.display)&&(r&&l.options.layoutScroll&&l.scroll&&l!==l.root&&$o(e,{x:-l.scroll.offset.x,y:-l.scroll.offset.y}),s&&(t.x*=s.x.scale,t.y*=s.y.scale,Io(e,s)),r&&Ro(l.latestValues)&&$o(e,l.latestValues));t.x=jo(t.x),t.y=jo(t.y)}(this.layoutCorrected,this.treeScale,this.path,o);const{target:s}=r;if(!s)return;this.projectionDelta||(this.projectionDelta={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}},this.projectionDeltaWithTransform={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}});const u=this.treeScale.x,c=this.treeScale.y,d=this.projectionTransform;wo(this.projectionDelta,this.layoutCorrected,s,this.latestValues),this.projectionTransform=ei(this.projectionDelta,this.treeScale),this.projectionTransform===d&&this.treeScale.x===u&&this.treeScale.y===c||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",s))}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(){let e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];var t,n,r;null===(n=(t=this.options).scheduleRender)||void 0===n||n.call(t),e&&(null===(r=this.getStack())||void 0===r||r.scheduleRender()),this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}setAnimationOrigin(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];var n,r;const o=this.snapshot,a=(null===o||void 0===o?void 0:o.latestValues)||{},i=u({},this.latestValues),l={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};this.relativeTarget=this.relativeTargetOrigin=void 0,this.attemptToResolveRelativeTarget=!t;const s={x:{min:0,max:0},y:{min:0,max:0}},c=(null===o||void 0===o?void 0:o.source)!==(null===(n=this.layout)||void 0===n?void 0:n.source),d=((null===(r=this.getStack())||void 0===r?void 0:r.members.length)||0)<=1,f=Boolean(c&&!d&&!0===this.options.crossfade&&!this.path.some(bi));this.animationProgress=0,this.mixTargetDelta=t=>{var n;const r=t/1e3;var o,u,p,h;gi(l.x,e.x,r),gi(l.y,e.y,r),this.setTargetDelta(l),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&(null===(n=this.relativeParent)||void 0===n?void 0:n.layout)&&(So(s,this.layout.layoutBox,this.relativeParent.layout.layoutBox),o=this.relativeTarget,u=this.relativeTargetOrigin,p=s,h=r,yi(o.x,u.x,p.x,h),yi(o.y,u.y,p.y,h)),c&&(this.animationValues=i,function(e,t,n,r,o,a){o?(e.opacity=In(0,void 0!==n.opacity?n.opacity:1,Ia(r)),e.opacityExit=In(void 0!==t.opacity?t.opacity:1,0,ja(r))):a&&(e.opacity=In(void 0!==t.opacity?t.opacity:1,void 0!==n.opacity?n.opacity:1,r));for(let i=0;i<Aa;i++){const o="border".concat(Da[i],"Radius");let a=Fa(t,o),l=Fa(n,o);void 0===a&&void 0===l||(a||(a=0),l||(l=0),0===a||0===l||Oa(a)===Oa(l)?(e[o]=Math.max(In(za(a),za(l),r),0),(ae.test(l)||ae.test(a))&&(e[o]+="%")):e[o]=l)}(t.rotate||n.rotate)&&(e.rotate=In(t.rotate||0,n.rotate||0,r))}(i,a,this.latestValues,r,f,d)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=r},this.mixTargetDelta(0)}startAnimation(e){var t,n;this.notifyListeners("animationStart"),null===(t=this.currentAnimation)||void 0===t||t.stop(),this.resumingFrom&&(null===(n=this.resumingFrom.currentAnimation)||void 0===n||n.stop()),this.pendingAnimation&&(jt.update(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=It.update((()=>{L.hasAnimatedSinceResize=!0,this.currentAnimation=function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const r=H(e)?e:Kt(e);return r.start(Yr("",r,t,n)),{stop:()=>r.stop(),isAnimating:()=>r.isAnimating()}}(0,1e3,u(u({},e),{},{onUpdate:t=>{var n;this.mixTargetDelta(t),null===(n=e.onUpdate)||void 0===n||n.call(e,t)},onComplete:()=>{var t;null===(t=e.onComplete)||void 0===t||t.call(e),this.completeAnimation()}})),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0}))}completeAnimation(){var e;this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0),null===(e=this.getStack())||void 0===e||e.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){var e;this.currentAnimation&&(null===(e=this.mixTargetDelta)||void 0===e||e.call(this,1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const e=this.getLead();let{targetWithTransforms:t,target:n,layout:r,latestValues:o}=e;if(t&&n&&r){if(this!==e&&this.layout&&r&&Si(this.options.animationType,this.layout.layoutBox,r.layoutBox)){n=this.target||{x:{min:0,max:0},y:{min:0,max:0}};const t=go(this.layout.layoutBox.x);n.x.min=e.target.x.min,n.x.max=n.x.min+t;const r=go(this.layout.layoutBox.y);n.y.min=e.target.y.min,n.y.max=n.y.min+r}Ha(t,n),$o(t,o),wo(this.projectionDeltaWithTransform,this.layoutCorrected,t,o)}}registerSharedNode(e,t){var n,r,o;this.sharedNodes.has(e)||this.sharedNodes.set(e,new Ja);this.sharedNodes.get(e).add(t),t.promote({transition:null===(n=t.options.initialPromotionConfig)||void 0===n?void 0:n.transition,preserveFollowOpacity:null===(o=null===(r=t.options.initialPromotionConfig)||void 0===r?void 0:r.shouldPreserveFollowOpacity)||void 0===o?void 0:o.call(r,t)})}isLead(){const e=this.getStack();return!e||e.lead===this}getLead(){var e;const{layoutId:t}=this.options;return t&&(null===(e=this.getStack())||void 0===e?void 0:e.lead)||this}getPrevLead(){var e;const{layoutId:t}=this.options;return t?null===(e=this.getStack())||void 0===e?void 0:e.prevLead:void 0}getStack(){const{layoutId:e}=this.options;if(e)return this.root.sharedNodes.get(e)}promote(){let{needsReset:e,transition:t,preserveFollowOpacity:n}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const r=this.getStack();r&&r.promote(this,n),e&&(this.projectionDelta=void 0,this.needsReset=!0),t&&this.setOptions({transition:t})}relegate(){const e=this.getStack();return!!e&&e.relegate(this)}resetRotation(){const{visualElement:e}=this.options;if(!e)return;let t=!1;const{latestValues:n}=e;if((n.rotate||n.rotateX||n.rotateY||n.rotateZ)&&(t=!0),!t)return;const r={};for(let o=0;o<ri.length;o++){const t="rotate"+ri[o];n[t]&&(r[t]=n[t],e.setStaticValue(t,0))}null===e||void 0===e||e.render();for(const o in r)e.setStaticValue(o,r[o]);e.scheduleRender()}getProjectionStyles(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};var t,n,r;const o={};if(!this.instance||this.isSVG)return o;if(!this.isVisible)return{visibility:"hidden"};o.visibility="";const a=null===(t=this.options.visualElement)||void 0===t?void 0:t.getProps().transformTemplate;if(this.needsReset)return this.needsReset=!1,o.opacity="",o.pointerEvents=Ie(e.pointerEvents)||"",o.transform=a?a(this.latestValues,""):"none",o;const i=this.getLead();if(!this.projectionDelta||!this.layout||!i.target){const t={};return this.options.layoutId&&(t.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,t.pointerEvents=Ie(e.pointerEvents)||""),this.hasProjected&&!Ro(this.latestValues)&&(t.transform=a?a({},""):"none",this.hasProjected=!1),t}const l=i.animationValues||i.latestValues;this.applyTransformsToTarget(),o.transform=ei(this.projectionDeltaWithTransform,this.treeScale,l),a&&(o.transform=a(l,o.transform));const{x:s,y:u}=this.projectionDelta;o.transformOrigin="".concat(100*s.origin,"% ").concat(100*u.origin,"% 0"),i.animationValues?o.opacity=i===this?null!==(r=null!==(n=l.opacity)&&void 0!==n?n:this.latestValues.opacity)&&void 0!==r?r:1:this.preserveOpacity?this.latestValues.opacity:l.opacityExit:o.opacity=i===this?void 0!==l.opacity?l.opacity:"":void 0!==l.opacityExit?l.opacityExit:0;for(const c in I){if(void 0===l[c])continue;const{correct:e,applyTo:t}=I[c],n=e(l[c],i);if(t){const e=t.length;for(let r=0;r<e;r++)o[t[r]]=n}else o[c]=n}return this.options.layoutId&&(o.pointerEvents=i===this?Ie(e.pointerEvents)||"":"none"),o}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach((e=>{var t;return null===(t=e.currentAnimation)||void 0===t?void 0:t.stop()})),this.root.nodes.forEach(ci),this.root.sharedNodes.clear()}}}function ii(e){e.updateLayout()}function li(e){var t,n,r;const o=(null===(t=e.resumeFrom)||void 0===t?void 0:t.snapshot)||e.snapshot;if(e.isLead()&&e.layout&&o&&e.hasListeners("didUpdate")){const{layoutBox:t,measuredBox:n}=e.layout,{animationType:r}=e.options,a=o.source!==e.layout.source;"size"===r?Mo((e=>{const n=a?o.measuredBox[e]:o.layoutBox[e],r=go(n);n.min=t[e].min,n.max=n.min+r})):Si(r,o.layoutBox,t)&&Mo((e=>{const n=a?o.measuredBox[e]:o.layoutBox[e],r=go(t[e]);n.max=n.min+r}));const i={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};wo(i,t,o.layoutBox);const l={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};a?wo(l,e.applyTransform(n,!0),o.measuredBox):wo(l,t,o.layoutBox);const s=!Ka(i);let u=!1;if(!e.resumeFrom){const n=e.getClosestProjectingParent();if(n&&!n.resumeFrom){const{snapshot:e,layout:r}=n;if(e&&r){const n={x:{min:0,max:0},y:{min:0,max:0}};So(n,o.layoutBox,e.layoutBox);const a={x:{min:0,max:0},y:{min:0,max:0}};So(a,t,r.layoutBox),Ga(n,a)||(u=!0)}}}e.notifyListeners("didUpdate",{layout:t,snapshot:o,delta:l,layoutDelta:i,hasLayoutChanged:s,hasRelativeTargetChanged:u})}else e.isLead()&&(null===(r=(n=e.options).onExitComplete)||void 0===r||r.call(n));e.options.transition=void 0}function si(e){e.isProjectionDirty||(e.isProjectionDirty=Boolean(e.parent&&e.parent.isProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=Boolean(e.parent&&e.parent.isTransformDirty))}function ui(e){e.clearSnapshot()}function ci(e){e.clearMeasurements()}function di(e){const{visualElement:t}=e.options;(null===t||void 0===t?void 0:t.getProps().onBeforeLayoutMeasure)&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function fi(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0}function pi(e){e.resolveTargetDelta()}function hi(e){e.calcProjection()}function mi(e){e.resetRotation()}function vi(e){e.removeLeadSnapshot()}function gi(e,t,n){e.translate=In(t.translate,0,n),e.scale=In(t.scale,1,n),e.origin=t.origin,e.originPoint=t.originPoint}function yi(e,t,n,r){e.min=In(t.min,n.min,r),e.max=In(t.max,n.max,r)}function bi(e){return e.animationValues&&void 0!==e.animationValues.opacityExit}const wi={duration:.45,ease:[.4,0,.1,1]};function xi(e,t){let n=e.root;for(let o=e.path.length-1;o>=0;o--)if(Boolean(e.path[o].instance)){n=e.path[o];break}const r=(n&&n!==e.root?n.instance:document).querySelector('[data-projection-id="'.concat(t,'"]'));r&&e.mount(r,!0)}function ki(e){e.min=Math.round(e.min),e.max=Math.round(e.max)}function Si(e,t,n){return"position"===e||"preserve-aspect"===e&&!yo(Za(t),Za(n),.2)}const Ei=ai({attachResizeListener:(e,t)=>Qe(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),Ci={current:void 0},Pi=ai({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!Ci.current){const e=new Ei(0,{});e.mount(window),e.setOptions({layoutScroll:!0}),Ci.current=e}return Ci.current},resetTransform:(e,t)=>{e.style.transform=void 0!==t?t:"none"},checkIsScrollRoot:e=>Boolean("fixed"===window.getComputedStyle(e).position)}),Ti=u(u(u(u({},lo),Pt),Ko),Ra),Li=z(((e,t)=>function(e,t,n,r,o){let{forwardMotionProps:a=!1}=t;return u(u({},F(e)?He:We),{},{preloadedFeatures:n,useRender:_e(a),createVisualElement:r,projectionNodeConstructor:o,Component:e})}(e,t,Ti,Pa,Pi)));var Mi=n(579);const _i=()=>{const[e,t]=(0,r.useState)(""),[n,o]=(0,r.useState)([]);return(0,r.useEffect)((()=>{const e="INITIALIZING JARVIS INTERFACE...";let n="",r=0;const a=setInterval((()=>{r<32?(n+=e[r],t(n),r++):clearInterval(a)}),100);return o(Array.from({length:20},((e,t)=>t))),()=>clearInterval(a)}),[]),(0,Mi.jsx)("div",{className:"min-h-screen bg-gray-900 flex items-center justify-center p-4",children:(0,Mi.jsx)("div",{className:"relative w-full max-w-4xl",children:(0,Mi.jsxs)(Li.div,{initial:{scale:0},animate:{scale:1},transition:{duration:1},className:"relative z-10",children:[(0,Mi.jsxs)(Li.div,{animate:{scale:[1,1.1,1]},transition:{duration:2,repeat:1/0,ease:"easeInOut"},className:"w-32 h-32 rounded-full bg-blue-500 mx-auto relative",children:[(0,Mi.jsx)("div",{className:"absolute inset-2 rounded-full bg-blue-600 animate-pulse"}),(0,Mi.jsx)("div",{className:"absolute inset-4 rounded-full bg-blue-400 animate-pulse"})]}),n.map((e=>(0,Mi.jsx)(Li.div,{initial:{opacity:0,x:400*Math.random()-200,y:400*Math.random()-200},animate:{opacity:[0,1,0],x:400*Math.random()-200,y:400*Math.random()-200},transition:{duration:3,repeat:1/0,repeatType:"reverse"},className:"absolute top-1/2 left-1/2 w-2 h-2 bg-blue-400 rounded-full"},e))),(0,Mi.jsx)(Li.div,{initial:{opacity:0},animate:{opacity:1},className:"text-center mt-8",children:(0,Mi.jsx)("h2",{className:"text-blue-400 text-2xl font-mono mb-4",children:e})}),(0,Mi.jsx)("div",{className:"flex justify-center gap-4 mt-8",children:["SCAN","ANALYZE","DEPLOY"].map((e=>(0,Mi.jsx)(Li.button,{whileHover:{scale:1.1},whileTap:{scale:.95},className:"px-6 py-2 bg-blue-600 text-blue-100 rounded-lg font-mono hover:bg-blue-500 transition-colors duration-200",children:e},e)))})]})})})};o.createRoot(document.getElementById("root")).render((0,Mi.jsx)(r.StrictMode,{children:(0,Mi.jsx)(_i,{})}))})();
//# sourceMappingURL=main.5f23b248.js.map