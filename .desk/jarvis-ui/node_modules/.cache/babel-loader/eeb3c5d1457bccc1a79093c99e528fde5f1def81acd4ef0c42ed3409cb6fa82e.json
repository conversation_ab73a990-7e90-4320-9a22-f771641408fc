{"ast": null, "code": "function addUniqueItem(array, item) {\n  array.indexOf(item) === -1 && array.push(item);\n}\nfunction removeItem(arr, item) {\n  const index = arr.indexOf(item);\n  index > -1 && arr.splice(index, 1);\n}\nexport { addUniqueItem, removeItem };", "map": {"version": 3, "names": ["addUniqueItem", "array", "item", "indexOf", "push", "removeItem", "arr", "index", "splice"], "sources": ["/home/<USER>/Desktop/jarvis-ui/node_modules/@motionone/utils/dist/array.es.js"], "sourcesContent": ["function addUniqueItem(array, item) {\n    array.indexOf(item) === -1 && array.push(item);\n}\nfunction removeItem(arr, item) {\n    const index = arr.indexOf(item);\n    index > -1 && arr.splice(index, 1);\n}\n\nexport { addUniqueItem, removeItem };\n"], "mappings": "AAAA,SAASA,aAAaA,CAACC,KAAK,EAAEC,IAAI,EAAE;EAChCD,KAAK,CAACE,OAAO,CAACD,IAAI,CAAC,KAAK,CAAC,CAAC,IAAID,KAAK,CAACG,IAAI,CAACF,IAAI,CAAC;AAClD;AACA,SAASG,UAAUA,CAACC,GAAG,EAAEJ,IAAI,EAAE;EAC3B,MAAMK,KAAK,GAAGD,GAAG,CAACH,OAAO,CAACD,IAAI,CAAC;EAC/BK,KAAK,GAAG,CAAC,CAAC,IAAID,GAAG,CAACE,MAAM,CAACD,KAAK,EAAE,CAAC,CAAC;AACtC;AAEA,SAASP,aAAa,EAAEK,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}