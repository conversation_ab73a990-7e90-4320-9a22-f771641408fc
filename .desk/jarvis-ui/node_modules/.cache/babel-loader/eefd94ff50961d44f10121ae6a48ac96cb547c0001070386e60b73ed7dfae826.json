{"ast": null, "code": "import { __rest } from 'tslib';\nimport { dispatchViewEvent } from '../utils/events.es.js';\nimport { inView as inView$1 } from '../../gestures/in-view.es.js';\nconst inView = {\n  isActive: options => <PERSON><PERSON><PERSON>(options.inView),\n  subscribe: (element, _ref, _ref2) => {\n    let {\n      enable,\n      disable\n    } = _ref;\n    let {\n      inViewOptions = {}\n    } = _ref2;\n    const {\n        once\n      } = inViewOptions,\n      viewOptions = __rest(inViewOptions, [\"once\"]);\n    return inView$1(element, enterEntry => {\n      enable();\n      dispatchViewEvent(element, \"viewenter\", enterEntry);\n      if (!once) {\n        return leaveEntry => {\n          disable();\n          dispatchViewEvent(element, \"viewleave\", leaveEntry);\n        };\n      }\n    }, viewOptions);\n  }\n};\nexport { inView };", "map": {"version": 3, "names": ["__rest", "dispatchViewEvent", "inView", "inView$1", "isActive", "options", "Boolean", "subscribe", "element", "_ref", "_ref2", "enable", "disable", "inViewOptions", "once", "viewOptions", "enterEntry", "leaveEntry"], "sources": ["/home/<USER>/Desktop/jarvis-ui/node_modules/@motionone/dom/dist/state/gestures/in-view.es.js"], "sourcesContent": ["import { __rest } from 'tslib';\nimport { dispatchViewEvent } from '../utils/events.es.js';\nimport { inView as inView$1 } from '../../gestures/in-view.es.js';\n\nconst inView = {\n    isActive: (options) => <PERSON><PERSON><PERSON>(options.inView),\n    subscribe: (element, { enable, disable }, { inViewOptions = {} }) => {\n        const { once } = inViewOptions, viewOptions = __rest(inViewOptions, [\"once\"]);\n        return inView$1(element, (enterEntry) => {\n            enable();\n            dispatchViewEvent(element, \"viewenter\", enterEntry);\n            if (!once) {\n                return (leaveEntry) => {\n                    disable();\n                    dispatchViewEvent(element, \"viewleave\", leaveEntry);\n                };\n            }\n        }, viewOptions);\n    },\n};\n\nexport { inView };\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,OAAO;AAC9B,SAASC,iBAAiB,QAAQ,uBAAuB;AACzD,SAASC,MAAM,IAAIC,QAAQ,QAAQ,8BAA8B;AAEjE,MAAMD,MAAM,GAAG;EACXE,QAAQ,EAAGC,OAAO,IAAKC,OAAO,CAACD,OAAO,CAACH,MAAM,CAAC;EAC9CK,SAAS,EAAEA,CAACC,OAAO,EAAAC,IAAA,EAAAC,KAAA,KAAkD;IAAA,IAAhD;MAAEC,MAAM;MAAEC;IAAQ,CAAC,GAAAH,IAAA;IAAA,IAAE;MAAEI,aAAa,GAAG,CAAC;IAAE,CAAC,GAAAH,KAAA;IAC5D,MAAM;QAAEI;MAAK,CAAC,GAAGD,aAAa;MAAEE,WAAW,GAAGf,MAAM,CAACa,aAAa,EAAE,CAAC,MAAM,CAAC,CAAC;IAC7E,OAAOV,QAAQ,CAACK,OAAO,EAAGQ,UAAU,IAAK;MACrCL,MAAM,CAAC,CAAC;MACRV,iBAAiB,CAACO,OAAO,EAAE,WAAW,EAAEQ,UAAU,CAAC;MACnD,IAAI,CAACF,IAAI,EAAE;QACP,OAAQG,UAAU,IAAK;UACnBL,OAAO,CAAC,CAAC;UACTX,iBAAiB,CAACO,OAAO,EAAE,WAAW,EAAES,UAAU,CAAC;QACvD,CAAC;MACL;IACJ,CAAC,EAAEF,WAAW,CAAC;EACnB;AACJ,CAAC;AAED,SAASb,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}