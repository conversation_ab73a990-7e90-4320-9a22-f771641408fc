{"ast": null, "code": "import { sync } from '../../frameloop/index.mjs';\nimport { transformProps } from '../../render/html/utils/transform.mjs';\nimport { appearStoreId } from './store-id.mjs';\nfunction handoffOptimizedAppearAnimation(id, name) {\n  const {\n    MotionAppearAnimations\n  } = window;\n  const animationId = appearStoreId(id, transformProps.has(name) ? \"transform\" : name);\n  const animation = MotionAppearAnimations && MotionAppearAnimations.get(animationId);\n  if (animation) {\n    /**\n     * We allow the animation to persist until the next frame:\n     *   1. So it continues to play until Framer Motion is ready to render\n     *      (avoiding a potential flash of the element's original state)\n     *   2. As all independent transforms share a single transform animation, stopping\n     *      it synchronously would prevent subsequent transforms from handing off.\n     */\n    sync.render(() => {\n      /**\n       * Animation.cancel() throws so it needs to be wrapped in a try/catch\n       */\n      try {\n        animation.cancel();\n        MotionAppearAnimations.delete(animationId);\n      } catch (e) {}\n    });\n    return animation.currentTime || 0;\n  } else {\n    return 0;\n  }\n}\nexport { handoffOptimizedAppearAnimation };", "map": {"version": 3, "names": ["sync", "transformProps", "appearStoreId", "handoffOptimizedAppearAnimation", "id", "name", "MotionAppearAnimations", "window", "animationId", "has", "animation", "get", "render", "cancel", "delete", "e", "currentTime"], "sources": ["/home/<USER>/Desktop/jarvis-ui/node_modules/framer-motion/dist/es/animation/optimized-appear/handoff.mjs"], "sourcesContent": ["import { sync } from '../../frameloop/index.mjs';\nimport { transformProps } from '../../render/html/utils/transform.mjs';\nimport { appearStoreId } from './store-id.mjs';\n\nfunction handoffOptimizedAppearAnimation(id, name) {\n    const { MotionAppearAnimations } = window;\n    const animationId = appearStoreId(id, transformProps.has(name) ? \"transform\" : name);\n    const animation = MotionAppearAnimations && MotionAppearAnimations.get(animationId);\n    if (animation) {\n        /**\n         * We allow the animation to persist until the next frame:\n         *   1. So it continues to play until Framer Motion is ready to render\n         *      (avoiding a potential flash of the element's original state)\n         *   2. As all independent transforms share a single transform animation, stopping\n         *      it synchronously would prevent subsequent transforms from handing off.\n         */\n        sync.render(() => {\n            /**\n             * Animation.cancel() throws so it needs to be wrapped in a try/catch\n             */\n            try {\n                animation.cancel();\n                MotionAppearAnimations.delete(animationId);\n            }\n            catch (e) { }\n        });\n        return animation.currentTime || 0;\n    }\n    else {\n        return 0;\n    }\n}\n\nexport { handoffOptimizedAppearAnimation };\n"], "mappings": "AAAA,SAASA,IAAI,QAAQ,2BAA2B;AAChD,SAASC,cAAc,QAAQ,uCAAuC;AACtE,SAASC,aAAa,QAAQ,gBAAgB;AAE9C,SAASC,+BAA+BA,CAACC,EAAE,EAAEC,IAAI,EAAE;EAC/C,MAAM;IAAEC;EAAuB,CAAC,GAAGC,MAAM;EACzC,MAAMC,WAAW,GAAGN,aAAa,CAACE,EAAE,EAAEH,cAAc,CAACQ,GAAG,CAACJ,IAAI,CAAC,GAAG,WAAW,GAAGA,IAAI,CAAC;EACpF,MAAMK,SAAS,GAAGJ,sBAAsB,IAAIA,sBAAsB,CAACK,GAAG,CAACH,WAAW,CAAC;EACnF,IAAIE,SAAS,EAAE;IACX;AACR;AACA;AACA;AACA;AACA;AACA;IACQV,IAAI,CAACY,MAAM,CAAC,MAAM;MACd;AACZ;AACA;MACY,IAAI;QACAF,SAAS,CAACG,MAAM,CAAC,CAAC;QAClBP,sBAAsB,CAACQ,MAAM,CAACN,WAAW,CAAC;MAC9C,CAAC,CACD,OAAOO,CAAC,EAAE,CAAE;IAChB,CAAC,CAAC;IACF,OAAOL,SAAS,CAACM,WAAW,IAAI,CAAC;EACrC,CAAC,MACI;IACD,OAAO,CAAC;EACZ;AACJ;AAEA,SAASb,+BAA+B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}