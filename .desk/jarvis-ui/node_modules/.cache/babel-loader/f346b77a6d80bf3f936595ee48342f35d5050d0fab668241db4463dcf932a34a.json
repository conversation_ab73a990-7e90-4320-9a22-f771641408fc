{"ast": null, "code": "import { useConstant } from '../../utils/use-constant.mjs';\nimport { globalProjectionState } from './state.mjs';\nlet id = 1;\nfunction useProjectionId() {\n  return useConstant(() => {\n    if (globalProjectionState.hasEverUpdated) {\n      return id++;\n    }\n  });\n}\nexport { useProjectionId };", "map": {"version": 3, "names": ["useConstant", "globalProjectionState", "id", "useProjectionId", "hasEverUpdated"], "sources": ["/home/<USER>/Desktop/jarvis-ui/node_modules/framer-motion/dist/es/projection/node/id.mjs"], "sourcesContent": ["import { useConstant } from '../../utils/use-constant.mjs';\nimport { globalProjectionState } from './state.mjs';\n\nlet id = 1;\nfunction useProjectionId() {\n    return useConstant(() => {\n        if (globalProjectionState.hasEverUpdated) {\n            return id++;\n        }\n    });\n}\n\nexport { useProjectionId };\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,8BAA8B;AAC1D,SAASC,qBAAqB,QAAQ,aAAa;AAEnD,IAAIC,EAAE,GAAG,CAAC;AACV,SAASC,eAAeA,CAAA,EAAG;EACvB,OAAOH,WAAW,CAAC,MAAM;IACrB,IAAIC,qBAAqB,CAACG,cAAc,EAAE;MACtC,OAAOF,EAAE,EAAE;IACf;EACJ,CAAC,CAAC;AACN;AAEA,SAASC,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}