{"ast": null, "code": "import { useRef, useContext, useEffect } from 'react';\nimport { MotionConfigContext } from '../context/MotionConfigContext.mjs';\nimport { useUnmountEffect } from '../utils/use-unmount-effect.mjs';\nimport { usePointerEvent } from '../events/use-pointer-event.mjs';\nimport { PanSession } from './PanSession.mjs';\n\n/**\n *\n * @param handlers -\n * @param ref -\n *\n * @privateRemarks\n * Currently this sets new pan gesture functions every render. The memo route has been explored\n * in the past but ultimately we're still creating new functions every render. An optimisation\n * to explore is creating the pan gestures and loading them into a `ref`.\n *\n * @internal\n */\nfunction usePanGesture(_ref) {\n  let {\n    onPan,\n    onPanStart,\n    onPanEnd,\n    onPanSessionStart,\n    visualElement\n  } = _ref;\n  const hasPanEvents = onPan || onPanStart || onPanEnd || onPanSessionStart;\n  const panSession = useRef(null);\n  const {\n    transformPagePoint\n  } = useContext(MotionConfigContext);\n  const handlers = {\n    onSessionStart: onPanSessionStart,\n    onStart: onPanStart,\n    onMove: onPan,\n    onEnd: (event, info) => {\n      panSession.current = null;\n      onPanEnd && onPanEnd(event, info);\n    }\n  };\n  useEffect(() => {\n    if (panSession.current !== null) {\n      panSession.current.updateHandlers(handlers);\n    }\n  });\n  function onPointerDown(event) {\n    panSession.current = new PanSession(event, handlers, {\n      transformPagePoint\n    });\n  }\n  usePointerEvent(visualElement, \"pointerdown\", hasPanEvents && onPointerDown);\n  useUnmountEffect(() => panSession.current && panSession.current.end());\n}\nexport { usePanGesture };", "map": {"version": 3, "names": ["useRef", "useContext", "useEffect", "MotionConfigContext", "useUnmountEffect", "usePointerEvent", "PanSession", "usePanGesture", "_ref", "onPan", "onPanStart", "onPanEnd", "onPanSessionStart", "visualElement", "hasPanEvents", "panSession", "transformPagePoint", "handlers", "onSessionStart", "onStart", "onMove", "onEnd", "event", "info", "current", "updateHandlers", "onPointerDown", "end"], "sources": ["/home/<USER>/Desktop/jarvis-ui/node_modules/framer-motion/dist/es/gestures/use-pan-gesture.mjs"], "sourcesContent": ["import { useRef, useContext, useEffect } from 'react';\nimport { MotionConfigContext } from '../context/MotionConfigContext.mjs';\nimport { useUnmountEffect } from '../utils/use-unmount-effect.mjs';\nimport { usePointerEvent } from '../events/use-pointer-event.mjs';\nimport { PanSession } from './PanSession.mjs';\n\n/**\n *\n * @param handlers -\n * @param ref -\n *\n * @privateRemarks\n * Currently this sets new pan gesture functions every render. The memo route has been explored\n * in the past but ultimately we're still creating new functions every render. An optimisation\n * to explore is creating the pan gestures and loading them into a `ref`.\n *\n * @internal\n */\nfunction usePanGesture({ onPan, onPanStart, onPanEnd, onPanSessionStart, visualElement, }) {\n    const hasPanEvents = onPan || onPanStart || onPanEnd || onPanSessionStart;\n    const panSession = useRef(null);\n    const { transformPagePoint } = useContext(MotionConfigContext);\n    const handlers = {\n        onSessionStart: onPanSessionStart,\n        onStart: onPanStart,\n        onMove: onPan,\n        onEnd: (event, info) => {\n            panSession.current = null;\n            onPanEnd && onPanEnd(event, info);\n        },\n    };\n    useEffect(() => {\n        if (panSession.current !== null) {\n            panSession.current.updateHandlers(handlers);\n        }\n    });\n    function onPointerDown(event) {\n        panSession.current = new PanSession(event, handlers, {\n            transformPagePoint,\n        });\n    }\n    usePointerEvent(visualElement, \"pointerdown\", hasPanEvents && onPointerDown);\n    useUnmountEffect(() => panSession.current && panSession.current.end());\n}\n\nexport { usePanGesture };\n"], "mappings": "AAAA,SAASA,MAAM,EAAEC,UAAU,EAAEC,SAAS,QAAQ,OAAO;AACrD,SAASC,mBAAmB,QAAQ,oCAAoC;AACxE,SAASC,gBAAgB,QAAQ,iCAAiC;AAClE,SAASC,eAAe,QAAQ,iCAAiC;AACjE,SAASC,UAAU,QAAQ,kBAAkB;;AAE7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,aAAaA,CAAAC,IAAA,EAAqE;EAAA,IAApE;IAAEC,KAAK;IAAEC,UAAU;IAAEC,QAAQ;IAAEC,iBAAiB;IAAEC;EAAe,CAAC,GAAAL,IAAA;EACrF,MAAMM,YAAY,GAAGL,KAAK,IAAIC,UAAU,IAAIC,QAAQ,IAAIC,iBAAiB;EACzE,MAAMG,UAAU,GAAGf,MAAM,CAAC,IAAI,CAAC;EAC/B,MAAM;IAAEgB;EAAmB,CAAC,GAAGf,UAAU,CAACE,mBAAmB,CAAC;EAC9D,MAAMc,QAAQ,GAAG;IACbC,cAAc,EAAEN,iBAAiB;IACjCO,OAAO,EAAET,UAAU;IACnBU,MAAM,EAAEX,KAAK;IACbY,KAAK,EAAEA,CAACC,KAAK,EAAEC,IAAI,KAAK;MACpBR,UAAU,CAACS,OAAO,GAAG,IAAI;MACzBb,QAAQ,IAAIA,QAAQ,CAACW,KAAK,EAAEC,IAAI,CAAC;IACrC;EACJ,CAAC;EACDrB,SAAS,CAAC,MAAM;IACZ,IAAIa,UAAU,CAACS,OAAO,KAAK,IAAI,EAAE;MAC7BT,UAAU,CAACS,OAAO,CAACC,cAAc,CAACR,QAAQ,CAAC;IAC/C;EACJ,CAAC,CAAC;EACF,SAASS,aAAaA,CAACJ,KAAK,EAAE;IAC1BP,UAAU,CAACS,OAAO,GAAG,IAAIlB,UAAU,CAACgB,KAAK,EAAEL,QAAQ,EAAE;MACjDD;IACJ,CAAC,CAAC;EACN;EACAX,eAAe,CAACQ,aAAa,EAAE,aAAa,EAAEC,YAAY,IAAIY,aAAa,CAAC;EAC5EtB,gBAAgB,CAAC,MAAMW,UAAU,CAACS,OAAO,IAAIT,UAAU,CAACS,OAAO,CAACG,GAAG,CAAC,CAAC,CAAC;AAC1E;AAEA,SAASpB,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}