{"ast": null, "code": "import _objectSpread from \"/home/<USER>/Desktop/jarvis-ui/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport { cancelSync, flushSync, sync } from '../../frameloop/index.mjs';\nimport { animate } from '../../animation/animate.mjs';\nimport { SubscriptionManager } from '../../utils/subscription-manager.mjs';\nimport { mixValues } from '../animation/mix-values.mjs';\nimport { copyBoxInto } from '../geometry/copy.mjs';\nimport { translateAxis, transformBox, applyBoxDelta, applyTreeDeltas } from '../geometry/delta-apply.mjs';\nimport { calcRelativePosition, calcRelativeBox, calcBoxDelta, calcLength, isNear } from '../geometry/delta-calc.mjs';\nimport { removeBoxTransforms } from '../geometry/delta-remove.mjs';\nimport { createBox, createDelta } from '../geometry/models.mjs';\nimport { getValueTransition } from '../../animation/utils/transitions.mjs';\nimport { boxEquals, isDeltaZero, aspectRatio } from '../geometry/utils.mjs';\nimport { NodeStack } from '../shared/stack.mjs';\nimport { scaleCorrectors } from '../styles/scale-correction.mjs';\nimport { buildProjectionTransform } from '../styles/transform.mjs';\nimport { eachAxis } from '../utils/each-axis.mjs';\nimport { hasTransform, hasScale, has2DTranslate } from '../utils/has-transform.mjs';\nimport { FlatTree } from '../../render/utils/flat-tree.mjs';\nimport { resolveMotionValue } from '../../value/utils/resolve-motion-value.mjs';\nimport { globalProjectionState } from './state.mjs';\nimport { delay } from '../../utils/delay.mjs';\nimport { mix } from '../../utils/mix.mjs';\nconst transformAxes = [\"\", \"X\", \"Y\", \"Z\"];\n/**\n * We use 1000 as the animation target as 0-1000 maps better to pixels than 0-1\n * which has a noticeable difference in spring animations\n */\nconst animationTarget = 1000;\nlet id = 0;\nfunction createProjectionNode(_ref) {\n  let {\n    attachResizeListener,\n    defaultParent,\n    measureScroll,\n    checkIsScrollRoot,\n    resetTransform\n  } = _ref;\n  return class ProjectionNode {\n    constructor(elementId) {\n      let latestValues = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n      let parent = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : defaultParent === null || defaultParent === void 0 ? void 0 : defaultParent();\n      /**\n       * A unique ID generated for every projection node.\n       */\n      this.id = id++;\n      /**\n       * An id that represents a unique session instigated by startUpdate.\n       */\n      this.animationId = 0;\n      /**\n       * A Set containing all this component's children. This is used to iterate\n       * through the children.\n       *\n       * TODO: This could be faster to iterate as a flat array stored on the root node.\n       */\n      this.children = new Set();\n      /**\n       * Options for the node. We use this to configure what kind of layout animations\n       * we should perform (if any).\n       */\n      this.options = {};\n      /**\n       * We use this to detect when its safe to shut down part of a projection tree.\n       * We have to keep projecting children for scale correction and relative projection\n       * until all their parents stop performing layout animations.\n       */\n      this.isTreeAnimating = false;\n      this.isAnimationBlocked = false;\n      /**\n       * Flag to true if we think this layout has been changed. We can't always know this,\n       * currently we set it to true every time a component renders, or if it has a layoutDependency\n       * if that has changed between renders. Additionally, components can be grouped by LayoutGroup\n       * and if one node is dirtied, they all are.\n       */\n      this.isLayoutDirty = false;\n      this.isTransformDirty = false;\n      /**\n       * Flag to true if we think the projection calculations for this or any\n       * child might need recalculating as a result of an updated transform or layout animation.\n       */\n      this.isProjectionDirty = false;\n      /**\n       * Block layout updates for instant layout transitions throughout the tree.\n       */\n      this.updateManuallyBlocked = false;\n      this.updateBlockedByResize = false;\n      /**\n       * Set to true between the start of the first `willUpdate` call and the end of the `didUpdate`\n       * call.\n       */\n      this.isUpdating = false;\n      /**\n       * If this is an SVG element we currently disable projection transforms\n       */\n      this.isSVG = false;\n      /**\n       * Flag to true (during promotion) if a node doing an instant layout transition needs to reset\n       * its projection styles.\n       */\n      this.needsReset = false;\n      /**\n       * Flags whether this node should have its transform reset prior to measuring.\n       */\n      this.shouldResetTransform = false;\n      /**\n       * An object representing the calculated contextual/accumulated/tree scale.\n       * This will be used to scale calculcated projection transforms, as these are\n       * calculated in screen-space but need to be scaled for elements to layoutly\n       * make it to their calculated destinations.\n       *\n       * TODO: Lazy-init\n       */\n      this.treeScale = {\n        x: 1,\n        y: 1\n      };\n      /**\n       *\n       */\n      this.eventHandlers = new Map();\n      // Note: Currently only running on root node\n      this.potentialNodes = new Map();\n      this.checkUpdateFailed = () => {\n        if (this.isUpdating) {\n          this.isUpdating = false;\n          this.clearAllSnapshots();\n        }\n      };\n      /**\n       * This is a multi-step process as shared nodes might be of different depths. Nodes\n       * are sorted by depth order, so we need to resolve the entire tree before moving to\n       * the next step.\n       */\n      this.updateProjection = () => {\n        this.nodes.forEach(propagateDirtyNodes);\n        this.nodes.forEach(resolveTargetDelta);\n        this.nodes.forEach(calcProjection);\n      };\n      this.hasProjected = false;\n      this.isVisible = true;\n      this.animationProgress = 0;\n      /**\n       * Shared layout\n       */\n      // TODO Only running on root node\n      this.sharedNodes = new Map();\n      this.elementId = elementId;\n      this.latestValues = latestValues;\n      this.root = parent ? parent.root || parent : this;\n      this.path = parent ? [...parent.path, parent] : [];\n      this.parent = parent;\n      this.depth = parent ? parent.depth + 1 : 0;\n      elementId && this.root.registerPotentialNode(elementId, this);\n      for (let i = 0; i < this.path.length; i++) {\n        this.path[i].shouldResetTransform = true;\n      }\n      if (this.root === this) this.nodes = new FlatTree();\n    }\n    addEventListener(name, handler) {\n      if (!this.eventHandlers.has(name)) {\n        this.eventHandlers.set(name, new SubscriptionManager());\n      }\n      return this.eventHandlers.get(name).add(handler);\n    }\n    notifyListeners(name) {\n      const subscriptionManager = this.eventHandlers.get(name);\n      for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n        args[_key - 1] = arguments[_key];\n      }\n      subscriptionManager === null || subscriptionManager === void 0 ? void 0 : subscriptionManager.notify(...args);\n    }\n    hasListeners(name) {\n      return this.eventHandlers.has(name);\n    }\n    registerPotentialNode(elementId, node) {\n      this.potentialNodes.set(elementId, node);\n    }\n    /**\n     * Lifecycles\n     */\n    mount(instance) {\n      let isLayoutDirty = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n      var _a;\n      if (this.instance) return;\n      this.isSVG = instance instanceof SVGElement && instance.tagName !== \"svg\";\n      this.instance = instance;\n      const {\n        layoutId,\n        layout,\n        visualElement\n      } = this.options;\n      if (visualElement && !visualElement.current) {\n        visualElement.mount(instance);\n      }\n      this.root.nodes.add(this);\n      (_a = this.parent) === null || _a === void 0 ? void 0 : _a.children.add(this);\n      this.elementId && this.root.potentialNodes.delete(this.elementId);\n      if (isLayoutDirty && (layout || layoutId)) {\n        this.isLayoutDirty = true;\n      }\n      if (attachResizeListener) {\n        let cancelDelay;\n        const resizeUnblockUpdate = () => this.root.updateBlockedByResize = false;\n        attachResizeListener(instance, () => {\n          this.root.updateBlockedByResize = true;\n          cancelDelay && cancelDelay();\n          cancelDelay = delay(resizeUnblockUpdate, 250);\n          if (globalProjectionState.hasAnimatedSinceResize) {\n            globalProjectionState.hasAnimatedSinceResize = false;\n            this.nodes.forEach(finishAnimation);\n          }\n        });\n      }\n      if (layoutId) {\n        this.root.registerSharedNode(layoutId, this);\n      }\n      // Only register the handler if it requires layout animation\n      if (this.options.animate !== false && visualElement && (layoutId || layout)) {\n        this.addEventListener(\"didUpdate\", _ref2 => {\n          let {\n            delta,\n            hasLayoutChanged,\n            hasRelativeTargetChanged,\n            layout: newLayout\n          } = _ref2;\n          var _a, _b, _c, _d, _e;\n          if (this.isTreeAnimationBlocked()) {\n            this.target = undefined;\n            this.relativeTarget = undefined;\n            return;\n          }\n          // TODO: Check here if an animation exists\n          const layoutTransition = (_b = (_a = this.options.transition) !== null && _a !== void 0 ? _a : visualElement.getDefaultTransition()) !== null && _b !== void 0 ? _b : defaultLayoutTransition;\n          const {\n            onLayoutAnimationStart,\n            onLayoutAnimationComplete\n          } = visualElement.getProps();\n          /**\n           * The target layout of the element might stay the same,\n           * but its position relative to its parent has changed.\n           */\n          const targetChanged = !this.targetLayout || !boxEquals(this.targetLayout, newLayout) || hasRelativeTargetChanged;\n          /**\n           * If the layout hasn't seemed to have changed, it might be that the\n           * element is visually in the same place in the document but its position\n           * relative to its parent has indeed changed. So here we check for that.\n           */\n          const hasOnlyRelativeTargetChanged = !hasLayoutChanged && hasRelativeTargetChanged;\n          if (((_c = this.resumeFrom) === null || _c === void 0 ? void 0 : _c.instance) || hasOnlyRelativeTargetChanged || hasLayoutChanged && (targetChanged || !this.currentAnimation)) {\n            if (this.resumeFrom) {\n              this.resumingFrom = this.resumeFrom;\n              this.resumingFrom.resumingFrom = undefined;\n            }\n            this.setAnimationOrigin(delta, hasOnlyRelativeTargetChanged);\n            const animationOptions = _objectSpread(_objectSpread({}, getValueTransition(layoutTransition, \"layout\")), {}, {\n              onPlay: onLayoutAnimationStart,\n              onComplete: onLayoutAnimationComplete\n            });\n            if (visualElement.shouldReduceMotion) {\n              animationOptions.delay = 0;\n              animationOptions.type = false;\n            }\n            this.startAnimation(animationOptions);\n          } else {\n            /**\n             * If the layout hasn't changed and we have an animation that hasn't started yet,\n             * finish it immediately. Otherwise it will be animating from a location\n             * that was probably never commited to screen and look like a jumpy box.\n             */\n            if (!hasLayoutChanged && this.animationProgress === 0) {\n              finishAnimation(this);\n            }\n            this.isLead() && ((_e = (_d = this.options).onExitComplete) === null || _e === void 0 ? void 0 : _e.call(_d));\n          }\n          this.targetLayout = newLayout;\n        });\n      }\n    }\n    unmount() {\n      var _a, _b;\n      this.options.layoutId && this.willUpdate();\n      this.root.nodes.remove(this);\n      (_a = this.getStack()) === null || _a === void 0 ? void 0 : _a.remove(this);\n      (_b = this.parent) === null || _b === void 0 ? void 0 : _b.children.delete(this);\n      this.instance = undefined;\n      cancelSync.preRender(this.updateProjection);\n    }\n    // only on the root\n    blockUpdate() {\n      this.updateManuallyBlocked = true;\n    }\n    unblockUpdate() {\n      this.updateManuallyBlocked = false;\n    }\n    isUpdateBlocked() {\n      return this.updateManuallyBlocked || this.updateBlockedByResize;\n    }\n    isTreeAnimationBlocked() {\n      var _a;\n      return this.isAnimationBlocked || ((_a = this.parent) === null || _a === void 0 ? void 0 : _a.isTreeAnimationBlocked()) || false;\n    }\n    // Note: currently only running on root node\n    startUpdate() {\n      var _a;\n      if (this.isUpdateBlocked()) return;\n      this.isUpdating = true;\n      (_a = this.nodes) === null || _a === void 0 ? void 0 : _a.forEach(resetRotation);\n      this.animationId++;\n    }\n    willUpdate() {\n      let shouldNotifyListeners = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : true;\n      var _a, _b, _c;\n      if (this.root.isUpdateBlocked()) {\n        (_b = (_a = this.options).onExitComplete) === null || _b === void 0 ? void 0 : _b.call(_a);\n        return;\n      }\n      !this.root.isUpdating && this.root.startUpdate();\n      if (this.isLayoutDirty) return;\n      this.isLayoutDirty = true;\n      for (let i = 0; i < this.path.length; i++) {\n        const node = this.path[i];\n        node.shouldResetTransform = true;\n        node.updateScroll(\"snapshot\");\n      }\n      const {\n        layoutId,\n        layout\n      } = this.options;\n      if (layoutId === undefined && !layout) return;\n      const transformTemplate = (_c = this.options.visualElement) === null || _c === void 0 ? void 0 : _c.getProps().transformTemplate;\n      this.prevTransformTemplateValue = transformTemplate === null || transformTemplate === void 0 ? void 0 : transformTemplate(this.latestValues, \"\");\n      this.updateSnapshot();\n      shouldNotifyListeners && this.notifyListeners(\"willUpdate\");\n    }\n    // Note: Currently only running on root node\n    didUpdate() {\n      const updateWasBlocked = this.isUpdateBlocked();\n      // When doing an instant transition, we skip the layout update,\n      // but should still clean up the measurements so that the next\n      // snapshot could be taken correctly.\n      if (updateWasBlocked) {\n        this.unblockUpdate();\n        this.clearAllSnapshots();\n        this.nodes.forEach(clearMeasurements);\n        return;\n      }\n      if (!this.isUpdating) return;\n      this.isUpdating = false;\n      /**\n       * Search for and mount newly-added projection elements.\n       *\n       * TODO: Every time a new component is rendered we could search up the tree for\n       * the closest mounted node and query from there rather than document.\n       */\n      if (this.potentialNodes.size) {\n        this.potentialNodes.forEach(mountNodeEarly);\n        this.potentialNodes.clear();\n      }\n      /**\n       * Write\n       */\n      this.nodes.forEach(resetTransformStyle);\n      /**\n       * Read ==================\n       */\n      // Update layout measurements of updated children\n      this.nodes.forEach(updateLayout);\n      /**\n       * Write\n       */\n      // Notify listeners that the layout is updated\n      this.nodes.forEach(notifyLayoutUpdate);\n      this.clearAllSnapshots();\n      // Flush any scheduled updates\n      flushSync.update();\n      flushSync.preRender();\n      flushSync.render();\n    }\n    clearAllSnapshots() {\n      this.nodes.forEach(clearSnapshot);\n      this.sharedNodes.forEach(removeLeadSnapshots);\n    }\n    scheduleUpdateProjection() {\n      sync.preRender(this.updateProjection, false, true);\n    }\n    scheduleCheckAfterUnmount() {\n      /**\n       * If the unmounting node is in a layoutGroup and did trigger a willUpdate,\n       * we manually call didUpdate to give a chance to the siblings to animate.\n       * Otherwise, cleanup all snapshots to prevents future nodes from reusing them.\n       */\n      sync.postRender(() => {\n        if (this.isLayoutDirty) {\n          this.root.didUpdate();\n        } else {\n          this.root.checkUpdateFailed();\n        }\n      });\n    }\n    /**\n     * Update measurements\n     */\n    updateSnapshot() {\n      if (this.snapshot || !this.instance) return;\n      this.snapshot = this.measure();\n    }\n    updateLayout() {\n      var _a;\n      if (!this.instance) return;\n      // TODO: Incorporate into a forwarded scroll offset\n      this.updateScroll();\n      if (!(this.options.alwaysMeasureLayout && this.isLead()) && !this.isLayoutDirty) {\n        return;\n      }\n      /**\n       * When a node is mounted, it simply resumes from the prevLead's\n       * snapshot instead of taking a new one, but the ancestors scroll\n       * might have updated while the prevLead is unmounted. We need to\n       * update the scroll again to make sure the layout we measure is\n       * up to date.\n       */\n      if (this.resumeFrom && !this.resumeFrom.instance) {\n        for (let i = 0; i < this.path.length; i++) {\n          const node = this.path[i];\n          node.updateScroll();\n        }\n      }\n      const prevLayout = this.layout;\n      this.layout = this.measure(false);\n      this.layoutCorrected = createBox();\n      this.isLayoutDirty = false;\n      this.projectionDelta = undefined;\n      this.notifyListeners(\"measure\", this.layout.layoutBox);\n      (_a = this.options.visualElement) === null || _a === void 0 ? void 0 : _a.notify(\"LayoutMeasure\", this.layout.layoutBox, prevLayout === null || prevLayout === void 0 ? void 0 : prevLayout.layoutBox);\n    }\n    updateScroll() {\n      let phase = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : \"measure\";\n      let needsMeasurement = Boolean(this.options.layoutScroll && this.instance);\n      if (this.scroll && this.scroll.animationId === this.root.animationId && this.scroll.phase === phase) {\n        needsMeasurement = false;\n      }\n      if (needsMeasurement) {\n        this.scroll = {\n          animationId: this.root.animationId,\n          phase,\n          isRoot: checkIsScrollRoot(this.instance),\n          offset: measureScroll(this.instance)\n        };\n      }\n    }\n    resetTransform() {\n      var _a;\n      if (!resetTransform) return;\n      const isResetRequested = this.isLayoutDirty || this.shouldResetTransform;\n      const hasProjection = this.projectionDelta && !isDeltaZero(this.projectionDelta);\n      const transformTemplate = (_a = this.options.visualElement) === null || _a === void 0 ? void 0 : _a.getProps().transformTemplate;\n      const transformTemplateValue = transformTemplate === null || transformTemplate === void 0 ? void 0 : transformTemplate(this.latestValues, \"\");\n      const transformTemplateHasChanged = transformTemplateValue !== this.prevTransformTemplateValue;\n      if (isResetRequested && (hasProjection || hasTransform(this.latestValues) || transformTemplateHasChanged)) {\n        resetTransform(this.instance, transformTemplateValue);\n        this.shouldResetTransform = false;\n        this.scheduleRender();\n      }\n    }\n    measure() {\n      let removeTransform = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : true;\n      const pageBox = this.measurePageBox();\n      let layoutBox = this.removeElementScroll(pageBox);\n      /**\n       * Measurements taken during the pre-render stage\n       * still have transforms applied so we remove them\n       * via calculation.\n       */\n      if (removeTransform) {\n        layoutBox = this.removeTransform(layoutBox);\n      }\n      roundBox(layoutBox);\n      return {\n        animationId: this.root.animationId,\n        measuredBox: pageBox,\n        layoutBox,\n        latestValues: {},\n        source: this.id\n      };\n    }\n    measurePageBox() {\n      const {\n        visualElement\n      } = this.options;\n      if (!visualElement) return createBox();\n      const box = visualElement.measureViewportBox();\n      // Remove viewport scroll to give page-relative coordinates\n      const {\n        scroll\n      } = this.root;\n      if (scroll) {\n        translateAxis(box.x, scroll.offset.x);\n        translateAxis(box.y, scroll.offset.y);\n      }\n      return box;\n    }\n    removeElementScroll(box) {\n      const boxWithoutScroll = createBox();\n      copyBoxInto(boxWithoutScroll, box);\n      /**\n       * Performance TODO: Keep a cumulative scroll offset down the tree\n       * rather than loop back up the path.\n       */\n      for (let i = 0; i < this.path.length; i++) {\n        const node = this.path[i];\n        const {\n          scroll,\n          options\n        } = node;\n        if (node !== this.root && scroll && options.layoutScroll) {\n          /**\n           * If this is a new scroll root, we want to remove all previous scrolls\n           * from the viewport box.\n           */\n          if (scroll.isRoot) {\n            copyBoxInto(boxWithoutScroll, box);\n            const {\n              scroll: rootScroll\n            } = this.root;\n            /**\n             * Undo the application of page scroll that was originally added\n             * to the measured bounding box.\n             */\n            if (rootScroll) {\n              translateAxis(boxWithoutScroll.x, -rootScroll.offset.x);\n              translateAxis(boxWithoutScroll.y, -rootScroll.offset.y);\n            }\n          }\n          translateAxis(boxWithoutScroll.x, scroll.offset.x);\n          translateAxis(boxWithoutScroll.y, scroll.offset.y);\n        }\n      }\n      return boxWithoutScroll;\n    }\n    applyTransform(box) {\n      let transformOnly = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n      const withTransforms = createBox();\n      copyBoxInto(withTransforms, box);\n      for (let i = 0; i < this.path.length; i++) {\n        const node = this.path[i];\n        if (!transformOnly && node.options.layoutScroll && node.scroll && node !== node.root) {\n          transformBox(withTransforms, {\n            x: -node.scroll.offset.x,\n            y: -node.scroll.offset.y\n          });\n        }\n        if (!hasTransform(node.latestValues)) continue;\n        transformBox(withTransforms, node.latestValues);\n      }\n      if (hasTransform(this.latestValues)) {\n        transformBox(withTransforms, this.latestValues);\n      }\n      return withTransforms;\n    }\n    removeTransform(box) {\n      var _a;\n      const boxWithoutTransform = createBox();\n      copyBoxInto(boxWithoutTransform, box);\n      for (let i = 0; i < this.path.length; i++) {\n        const node = this.path[i];\n        if (!node.instance) continue;\n        if (!hasTransform(node.latestValues)) continue;\n        hasScale(node.latestValues) && node.updateSnapshot();\n        const sourceBox = createBox();\n        const nodeBox = node.measurePageBox();\n        copyBoxInto(sourceBox, nodeBox);\n        removeBoxTransforms(boxWithoutTransform, node.latestValues, (_a = node.snapshot) === null || _a === void 0 ? void 0 : _a.layoutBox, sourceBox);\n      }\n      if (hasTransform(this.latestValues)) {\n        removeBoxTransforms(boxWithoutTransform, this.latestValues);\n      }\n      return boxWithoutTransform;\n    }\n    /**\n     *\n     */\n    setTargetDelta(delta) {\n      this.targetDelta = delta;\n      this.isProjectionDirty = true;\n      this.root.scheduleUpdateProjection();\n    }\n    setOptions(options) {\n      this.options = _objectSpread(_objectSpread(_objectSpread({}, this.options), options), {}, {\n        crossfade: options.crossfade !== undefined ? options.crossfade : true\n      });\n    }\n    clearMeasurements() {\n      this.scroll = undefined;\n      this.layout = undefined;\n      this.snapshot = undefined;\n      this.prevTransformTemplateValue = undefined;\n      this.targetDelta = undefined;\n      this.target = undefined;\n      this.isLayoutDirty = false;\n    }\n    /**\n     * Frame calculations\n     */\n    resolveTargetDelta() {\n      var _a;\n      /**\n       * Once the dirty status of nodes has been spread through the tree, we also\n       * need to check if we have a shared node of a different depth that has itself\n       * been dirtied.\n       */\n      const lead = this.getLead();\n      this.isProjectionDirty || (this.isProjectionDirty = lead.isProjectionDirty);\n      this.isTransformDirty || (this.isTransformDirty = lead.isTransformDirty);\n      /**\n       * We don't use transform for this step of processing so we don't\n       * need to check whether any nodes have changed transform.\n       */\n      if (!this.isProjectionDirty && !this.attemptToResolveRelativeTarget) return;\n      const {\n        layout,\n        layoutId\n      } = this.options;\n      /**\n       * If we have no layout, we can't perform projection, so early return\n       */\n      if (!this.layout || !(layout || layoutId)) return;\n      /**\n       * If we don't have a targetDelta but do have a layout, we can attempt to resolve\n       * a relativeParent. This will allow a component to perform scale correction\n       * even if no animation has started.\n       */\n      // TODO If this is unsuccessful this currently happens every frame\n      if (!this.targetDelta && !this.relativeTarget) {\n        // TODO: This is a semi-repetition of further down this function, make DRY\n        const relativeParent = this.getClosestProjectingParent();\n        if (relativeParent && relativeParent.layout) {\n          this.relativeParent = relativeParent;\n          this.relativeTarget = createBox();\n          this.relativeTargetOrigin = createBox();\n          calcRelativePosition(this.relativeTargetOrigin, this.layout.layoutBox, relativeParent.layout.layoutBox);\n          copyBoxInto(this.relativeTarget, this.relativeTargetOrigin);\n        } else {\n          this.relativeParent = this.relativeTarget = undefined;\n        }\n      }\n      /**\n       * If we have no relative target or no target delta our target isn't valid\n       * for this frame.\n       */\n      if (!this.relativeTarget && !this.targetDelta) return;\n      /**\n       * Lazy-init target data structure\n       */\n      if (!this.target) {\n        this.target = createBox();\n        this.targetWithTransforms = createBox();\n      }\n      /**\n       * If we've got a relative box for this component, resolve it into a target relative to the parent.\n       */\n      if (this.relativeTarget && this.relativeTargetOrigin && ((_a = this.relativeParent) === null || _a === void 0 ? void 0 : _a.target)) {\n        calcRelativeBox(this.target, this.relativeTarget, this.relativeParent.target);\n        /**\n         * If we've only got a targetDelta, resolve it into a target\n         */\n      } else if (this.targetDelta) {\n        if (Boolean(this.resumingFrom)) {\n          // TODO: This is creating a new object every frame\n          this.target = this.applyTransform(this.layout.layoutBox);\n        } else {\n          copyBoxInto(this.target, this.layout.layoutBox);\n        }\n        applyBoxDelta(this.target, this.targetDelta);\n      } else {\n        /**\n         * If no target, use own layout as target\n         */\n        copyBoxInto(this.target, this.layout.layoutBox);\n      }\n      /**\n       * If we've been told to attempt to resolve a relative target, do so.\n       */\n      if (this.attemptToResolveRelativeTarget) {\n        this.attemptToResolveRelativeTarget = false;\n        const relativeParent = this.getClosestProjectingParent();\n        if (relativeParent && Boolean(relativeParent.resumingFrom) === Boolean(this.resumingFrom) && !relativeParent.options.layoutScroll && relativeParent.target) {\n          this.relativeParent = relativeParent;\n          this.relativeTarget = createBox();\n          this.relativeTargetOrigin = createBox();\n          calcRelativePosition(this.relativeTargetOrigin, this.target, relativeParent.target);\n          copyBoxInto(this.relativeTarget, this.relativeTargetOrigin);\n        } else {\n          this.relativeParent = this.relativeTarget = undefined;\n        }\n      }\n    }\n    getClosestProjectingParent() {\n      if (!this.parent || hasScale(this.parent.latestValues) || has2DTranslate(this.parent.latestValues)) return undefined;\n      if ((this.parent.relativeTarget || this.parent.targetDelta) && this.parent.layout) {\n        return this.parent;\n      } else {\n        return this.parent.getClosestProjectingParent();\n      }\n    }\n    calcProjection() {\n      var _a;\n      const {\n        isProjectionDirty,\n        isTransformDirty\n      } = this;\n      this.isProjectionDirty = this.isTransformDirty = false;\n      const lead = this.getLead();\n      const isShared = Boolean(this.resumingFrom) || this !== lead;\n      let canSkip = true;\n      if (isProjectionDirty) canSkip = false;\n      if (isShared && isTransformDirty) canSkip = false;\n      if (canSkip) return;\n      const {\n        layout,\n        layoutId\n      } = this.options;\n      /**\n       * If this section of the tree isn't animating we can\n       * delete our target sources for the following frame.\n       */\n      this.isTreeAnimating = Boolean(((_a = this.parent) === null || _a === void 0 ? void 0 : _a.isTreeAnimating) || this.currentAnimation || this.pendingAnimation);\n      if (!this.isTreeAnimating) {\n        this.targetDelta = this.relativeTarget = undefined;\n      }\n      if (!this.layout || !(layout || layoutId)) return;\n      /**\n       * Reset the corrected box with the latest values from box, as we're then going\n       * to perform mutative operations on it.\n       */\n      copyBoxInto(this.layoutCorrected, this.layout.layoutBox);\n      /**\n       * Apply all the parent deltas to this box to produce the corrected box. This\n       * is the layout box, as it will appear on screen as a result of the transforms of its parents.\n       */\n      applyTreeDeltas(this.layoutCorrected, this.treeScale, this.path, isShared);\n      const {\n        target\n      } = lead;\n      if (!target) return;\n      if (!this.projectionDelta) {\n        this.projectionDelta = createDelta();\n        this.projectionDeltaWithTransform = createDelta();\n      }\n      const prevTreeScaleX = this.treeScale.x;\n      const prevTreeScaleY = this.treeScale.y;\n      const prevProjectionTransform = this.projectionTransform;\n      /**\n       * Update the delta between the corrected box and the target box before user-set transforms were applied.\n       * This will allow us to calculate the corrected borderRadius and boxShadow to compensate\n       * for our layout reprojection, but still allow them to be scaled correctly by the user.\n       * It might be that to simplify this we may want to accept that user-set scale is also corrected\n       * and we wouldn't have to keep and calc both deltas, OR we could support a user setting\n       * to allow people to choose whether these styles are corrected based on just the\n       * layout reprojection or the final bounding box.\n       */\n      calcBoxDelta(this.projectionDelta, this.layoutCorrected, target, this.latestValues);\n      this.projectionTransform = buildProjectionTransform(this.projectionDelta, this.treeScale);\n      if (this.projectionTransform !== prevProjectionTransform || this.treeScale.x !== prevTreeScaleX || this.treeScale.y !== prevTreeScaleY) {\n        this.hasProjected = true;\n        this.scheduleRender();\n        this.notifyListeners(\"projectionUpdate\", target);\n      }\n    }\n    hide() {\n      this.isVisible = false;\n      // TODO: Schedule render\n    }\n    show() {\n      this.isVisible = true;\n      // TODO: Schedule render\n    }\n    scheduleRender() {\n      let notifyAll = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : true;\n      var _a, _b, _c;\n      (_b = (_a = this.options).scheduleRender) === null || _b === void 0 ? void 0 : _b.call(_a);\n      notifyAll && ((_c = this.getStack()) === null || _c === void 0 ? void 0 : _c.scheduleRender());\n      if (this.resumingFrom && !this.resumingFrom.instance) {\n        this.resumingFrom = undefined;\n      }\n    }\n    setAnimationOrigin(delta) {\n      let hasOnlyRelativeTargetChanged = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n      var _a, _b;\n      const snapshot = this.snapshot;\n      const snapshotLatestValues = (snapshot === null || snapshot === void 0 ? void 0 : snapshot.latestValues) || {};\n      const mixedValues = _objectSpread({}, this.latestValues);\n      const targetDelta = createDelta();\n      this.relativeTarget = this.relativeTargetOrigin = undefined;\n      this.attemptToResolveRelativeTarget = !hasOnlyRelativeTargetChanged;\n      const relativeLayout = createBox();\n      const isSharedLayoutAnimation = (snapshot === null || snapshot === void 0 ? void 0 : snapshot.source) !== ((_a = this.layout) === null || _a === void 0 ? void 0 : _a.source);\n      const isOnlyMember = (((_b = this.getStack()) === null || _b === void 0 ? void 0 : _b.members.length) || 0) <= 1;\n      const shouldCrossfadeOpacity = Boolean(isSharedLayoutAnimation && !isOnlyMember && this.options.crossfade === true && !this.path.some(hasOpacityCrossfade));\n      this.animationProgress = 0;\n      this.mixTargetDelta = latest => {\n        var _a;\n        const progress = latest / 1000;\n        mixAxisDelta(targetDelta.x, delta.x, progress);\n        mixAxisDelta(targetDelta.y, delta.y, progress);\n        this.setTargetDelta(targetDelta);\n        if (this.relativeTarget && this.relativeTargetOrigin && this.layout && ((_a = this.relativeParent) === null || _a === void 0 ? void 0 : _a.layout)) {\n          calcRelativePosition(relativeLayout, this.layout.layoutBox, this.relativeParent.layout.layoutBox);\n          mixBox(this.relativeTarget, this.relativeTargetOrigin, relativeLayout, progress);\n        }\n        if (isSharedLayoutAnimation) {\n          this.animationValues = mixedValues;\n          mixValues(mixedValues, snapshotLatestValues, this.latestValues, progress, shouldCrossfadeOpacity, isOnlyMember);\n        }\n        this.root.scheduleUpdateProjection();\n        this.scheduleRender();\n        this.animationProgress = progress;\n      };\n      this.mixTargetDelta(0);\n    }\n    startAnimation(options) {\n      var _a, _b;\n      this.notifyListeners(\"animationStart\");\n      (_a = this.currentAnimation) === null || _a === void 0 ? void 0 : _a.stop();\n      if (this.resumingFrom) {\n        (_b = this.resumingFrom.currentAnimation) === null || _b === void 0 ? void 0 : _b.stop();\n      }\n      if (this.pendingAnimation) {\n        cancelSync.update(this.pendingAnimation);\n        this.pendingAnimation = undefined;\n      }\n      /**\n       * Start the animation in the next frame to have a frame with progress 0,\n       * where the target is the same as when the animation started, so we can\n       * calculate the relative positions correctly for instant transitions.\n       */\n      this.pendingAnimation = sync.update(() => {\n        globalProjectionState.hasAnimatedSinceResize = true;\n        this.currentAnimation = animate(0, animationTarget, _objectSpread(_objectSpread({}, options), {}, {\n          onUpdate: latest => {\n            var _a;\n            this.mixTargetDelta(latest);\n            (_a = options.onUpdate) === null || _a === void 0 ? void 0 : _a.call(options, latest);\n          },\n          onComplete: () => {\n            var _a;\n            (_a = options.onComplete) === null || _a === void 0 ? void 0 : _a.call(options);\n            this.completeAnimation();\n          }\n        }));\n        if (this.resumingFrom) {\n          this.resumingFrom.currentAnimation = this.currentAnimation;\n        }\n        this.pendingAnimation = undefined;\n      });\n    }\n    completeAnimation() {\n      var _a;\n      if (this.resumingFrom) {\n        this.resumingFrom.currentAnimation = undefined;\n        this.resumingFrom.preserveOpacity = undefined;\n      }\n      (_a = this.getStack()) === null || _a === void 0 ? void 0 : _a.exitAnimationComplete();\n      this.resumingFrom = this.currentAnimation = this.animationValues = undefined;\n      this.notifyListeners(\"animationComplete\");\n    }\n    finishAnimation() {\n      var _a;\n      if (this.currentAnimation) {\n        (_a = this.mixTargetDelta) === null || _a === void 0 ? void 0 : _a.call(this, animationTarget);\n        this.currentAnimation.stop();\n      }\n      this.completeAnimation();\n    }\n    applyTransformsToTarget() {\n      const lead = this.getLead();\n      let {\n        targetWithTransforms,\n        target,\n        layout,\n        latestValues\n      } = lead;\n      if (!targetWithTransforms || !target || !layout) return;\n      /**\n       * If we're only animating position, and this element isn't the lead element,\n       * then instead of projecting into the lead box we instead want to calculate\n       * a new target that aligns the two boxes but maintains the layout shape.\n       */\n      if (this !== lead && this.layout && layout && shouldAnimatePositionOnly(this.options.animationType, this.layout.layoutBox, layout.layoutBox)) {\n        target = this.target || createBox();\n        const xLength = calcLength(this.layout.layoutBox.x);\n        target.x.min = lead.target.x.min;\n        target.x.max = target.x.min + xLength;\n        const yLength = calcLength(this.layout.layoutBox.y);\n        target.y.min = lead.target.y.min;\n        target.y.max = target.y.min + yLength;\n      }\n      copyBoxInto(targetWithTransforms, target);\n      /**\n       * Apply the latest user-set transforms to the targetBox to produce the targetBoxFinal.\n       * This is the final box that we will then project into by calculating a transform delta and\n       * applying it to the corrected box.\n       */\n      transformBox(targetWithTransforms, latestValues);\n      /**\n       * Update the delta between the corrected box and the final target box, after\n       * user-set transforms are applied to it. This will be used by the renderer to\n       * create a transform style that will reproject the element from its layout layout\n       * into the desired bounding box.\n       */\n      calcBoxDelta(this.projectionDeltaWithTransform, this.layoutCorrected, targetWithTransforms, latestValues);\n    }\n    registerSharedNode(layoutId, node) {\n      var _a, _b, _c;\n      if (!this.sharedNodes.has(layoutId)) {\n        this.sharedNodes.set(layoutId, new NodeStack());\n      }\n      const stack = this.sharedNodes.get(layoutId);\n      stack.add(node);\n      node.promote({\n        transition: (_a = node.options.initialPromotionConfig) === null || _a === void 0 ? void 0 : _a.transition,\n        preserveFollowOpacity: (_c = (_b = node.options.initialPromotionConfig) === null || _b === void 0 ? void 0 : _b.shouldPreserveFollowOpacity) === null || _c === void 0 ? void 0 : _c.call(_b, node)\n      });\n    }\n    isLead() {\n      const stack = this.getStack();\n      return stack ? stack.lead === this : true;\n    }\n    getLead() {\n      var _a;\n      const {\n        layoutId\n      } = this.options;\n      return layoutId ? ((_a = this.getStack()) === null || _a === void 0 ? void 0 : _a.lead) || this : this;\n    }\n    getPrevLead() {\n      var _a;\n      const {\n        layoutId\n      } = this.options;\n      return layoutId ? (_a = this.getStack()) === null || _a === void 0 ? void 0 : _a.prevLead : undefined;\n    }\n    getStack() {\n      const {\n        layoutId\n      } = this.options;\n      if (layoutId) return this.root.sharedNodes.get(layoutId);\n    }\n    promote() {\n      let {\n        needsReset,\n        transition,\n        preserveFollowOpacity\n      } = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n      const stack = this.getStack();\n      if (stack) stack.promote(this, preserveFollowOpacity);\n      if (needsReset) {\n        this.projectionDelta = undefined;\n        this.needsReset = true;\n      }\n      if (transition) this.setOptions({\n        transition\n      });\n    }\n    relegate() {\n      const stack = this.getStack();\n      if (stack) {\n        return stack.relegate(this);\n      } else {\n        return false;\n      }\n    }\n    resetRotation() {\n      const {\n        visualElement\n      } = this.options;\n      if (!visualElement) return;\n      // If there's no detected rotation values, we can early return without a forced render.\n      let hasRotate = false;\n      /**\n       * An unrolled check for rotation values. Most elements don't have any rotation and\n       * skipping the nested loop and new object creation is 50% faster.\n       */\n      const {\n        latestValues\n      } = visualElement;\n      if (latestValues.rotate || latestValues.rotateX || latestValues.rotateY || latestValues.rotateZ) {\n        hasRotate = true;\n      }\n      // If there's no rotation values, we don't need to do any more.\n      if (!hasRotate) return;\n      const resetValues = {};\n      // Check the rotate value of all axes and reset to 0\n      for (let i = 0; i < transformAxes.length; i++) {\n        const key = \"rotate\" + transformAxes[i];\n        // Record the rotation and then temporarily set it to 0\n        if (latestValues[key]) {\n          resetValues[key] = latestValues[key];\n          visualElement.setStaticValue(key, 0);\n        }\n      }\n      // Force a render of this element to apply the transform with all rotations\n      // set to 0.\n      visualElement === null || visualElement === void 0 ? void 0 : visualElement.render();\n      // Put back all the values we reset\n      for (const key in resetValues) {\n        visualElement.setStaticValue(key, resetValues[key]);\n      }\n      // Schedule a render for the next frame. This ensures we won't visually\n      // see the element with the reset rotate value applied.\n      visualElement.scheduleRender();\n    }\n    getProjectionStyles() {\n      let styleProp = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n      var _a, _b, _c;\n      // TODO: Return lifecycle-persistent object\n      const styles = {};\n      if (!this.instance || this.isSVG) return styles;\n      if (!this.isVisible) {\n        return {\n          visibility: \"hidden\"\n        };\n      } else {\n        styles.visibility = \"\";\n      }\n      const transformTemplate = (_a = this.options.visualElement) === null || _a === void 0 ? void 0 : _a.getProps().transformTemplate;\n      if (this.needsReset) {\n        this.needsReset = false;\n        styles.opacity = \"\";\n        styles.pointerEvents = resolveMotionValue(styleProp.pointerEvents) || \"\";\n        styles.transform = transformTemplate ? transformTemplate(this.latestValues, \"\") : \"none\";\n        return styles;\n      }\n      const lead = this.getLead();\n      if (!this.projectionDelta || !this.layout || !lead.target) {\n        const emptyStyles = {};\n        if (this.options.layoutId) {\n          emptyStyles.opacity = this.latestValues.opacity !== undefined ? this.latestValues.opacity : 1;\n          emptyStyles.pointerEvents = resolveMotionValue(styleProp.pointerEvents) || \"\";\n        }\n        if (this.hasProjected && !hasTransform(this.latestValues)) {\n          emptyStyles.transform = transformTemplate ? transformTemplate({}, \"\") : \"none\";\n          this.hasProjected = false;\n        }\n        return emptyStyles;\n      }\n      const valuesToRender = lead.animationValues || lead.latestValues;\n      this.applyTransformsToTarget();\n      styles.transform = buildProjectionTransform(this.projectionDeltaWithTransform, this.treeScale, valuesToRender);\n      if (transformTemplate) {\n        styles.transform = transformTemplate(valuesToRender, styles.transform);\n      }\n      const {\n        x,\n        y\n      } = this.projectionDelta;\n      styles.transformOrigin = \"\".concat(x.origin * 100, \"% \").concat(y.origin * 100, \"% 0\");\n      if (lead.animationValues) {\n        /**\n         * If the lead component is animating, assign this either the entering/leaving\n         * opacity\n         */\n        styles.opacity = lead === this ? (_c = (_b = valuesToRender.opacity) !== null && _b !== void 0 ? _b : this.latestValues.opacity) !== null && _c !== void 0 ? _c : 1 : this.preserveOpacity ? this.latestValues.opacity : valuesToRender.opacityExit;\n      } else {\n        /**\n         * Or we're not animating at all, set the lead component to its layout\n         * opacity and other components to hidden.\n         */\n        styles.opacity = lead === this ? valuesToRender.opacity !== undefined ? valuesToRender.opacity : \"\" : valuesToRender.opacityExit !== undefined ? valuesToRender.opacityExit : 0;\n      }\n      /**\n       * Apply scale correction\n       */\n      for (const key in scaleCorrectors) {\n        if (valuesToRender[key] === undefined) continue;\n        const {\n          correct,\n          applyTo\n        } = scaleCorrectors[key];\n        const corrected = correct(valuesToRender[key], lead);\n        if (applyTo) {\n          const num = applyTo.length;\n          for (let i = 0; i < num; i++) {\n            styles[applyTo[i]] = corrected;\n          }\n        } else {\n          styles[key] = corrected;\n        }\n      }\n      /**\n       * Disable pointer events on follow components. This is to ensure\n       * that if a follow component covers a lead component it doesn't block\n       * pointer events on the lead.\n       */\n      if (this.options.layoutId) {\n        styles.pointerEvents = lead === this ? resolveMotionValue(styleProp.pointerEvents) || \"\" : \"none\";\n      }\n      return styles;\n    }\n    clearSnapshot() {\n      this.resumeFrom = this.snapshot = undefined;\n    }\n    // Only run on root\n    resetTree() {\n      this.root.nodes.forEach(node => {\n        var _a;\n        return (_a = node.currentAnimation) === null || _a === void 0 ? void 0 : _a.stop();\n      });\n      this.root.nodes.forEach(clearMeasurements);\n      this.root.sharedNodes.clear();\n    }\n  };\n}\nfunction updateLayout(node) {\n  node.updateLayout();\n}\nfunction notifyLayoutUpdate(node) {\n  var _a, _b, _c;\n  const snapshot = ((_a = node.resumeFrom) === null || _a === void 0 ? void 0 : _a.snapshot) || node.snapshot;\n  if (node.isLead() && node.layout && snapshot && node.hasListeners(\"didUpdate\")) {\n    const {\n      layoutBox: layout,\n      measuredBox: measuredLayout\n    } = node.layout;\n    const {\n      animationType\n    } = node.options;\n    const isShared = snapshot.source !== node.layout.source;\n    // TODO Maybe we want to also resize the layout snapshot so we don't trigger\n    // animations for instance if layout=\"size\" and an element has only changed position\n    if (animationType === \"size\") {\n      eachAxis(axis => {\n        const axisSnapshot = isShared ? snapshot.measuredBox[axis] : snapshot.layoutBox[axis];\n        const length = calcLength(axisSnapshot);\n        axisSnapshot.min = layout[axis].min;\n        axisSnapshot.max = axisSnapshot.min + length;\n      });\n    } else if (shouldAnimatePositionOnly(animationType, snapshot.layoutBox, layout)) {\n      eachAxis(axis => {\n        const axisSnapshot = isShared ? snapshot.measuredBox[axis] : snapshot.layoutBox[axis];\n        const length = calcLength(layout[axis]);\n        axisSnapshot.max = axisSnapshot.min + length;\n      });\n    }\n    const layoutDelta = createDelta();\n    calcBoxDelta(layoutDelta, layout, snapshot.layoutBox);\n    const visualDelta = createDelta();\n    if (isShared) {\n      calcBoxDelta(visualDelta, node.applyTransform(measuredLayout, true), snapshot.measuredBox);\n    } else {\n      calcBoxDelta(visualDelta, layout, snapshot.layoutBox);\n    }\n    const hasLayoutChanged = !isDeltaZero(layoutDelta);\n    let hasRelativeTargetChanged = false;\n    if (!node.resumeFrom) {\n      const relativeParent = node.getClosestProjectingParent();\n      /**\n       * If the relativeParent is itself resuming from a different element then\n       * the relative snapshot is not relavent\n       */\n      if (relativeParent && !relativeParent.resumeFrom) {\n        const {\n          snapshot: parentSnapshot,\n          layout: parentLayout\n        } = relativeParent;\n        if (parentSnapshot && parentLayout) {\n          const relativeSnapshot = createBox();\n          calcRelativePosition(relativeSnapshot, snapshot.layoutBox, parentSnapshot.layoutBox);\n          const relativeLayout = createBox();\n          calcRelativePosition(relativeLayout, layout, parentLayout.layoutBox);\n          if (!boxEquals(relativeSnapshot, relativeLayout)) {\n            hasRelativeTargetChanged = true;\n          }\n        }\n      }\n    }\n    node.notifyListeners(\"didUpdate\", {\n      layout,\n      snapshot,\n      delta: visualDelta,\n      layoutDelta,\n      hasLayoutChanged,\n      hasRelativeTargetChanged\n    });\n  } else if (node.isLead()) {\n    (_c = (_b = node.options).onExitComplete) === null || _c === void 0 ? void 0 : _c.call(_b);\n  }\n  /**\n   * Clearing transition\n   * TODO: Investigate why this transition is being passed in as {type: false } from Framer\n   * and why we need it at all\n   */\n  node.options.transition = undefined;\n}\nfunction propagateDirtyNodes(node) {\n  /**\n   * Propagate isProjectionDirty. Nodes are ordered by depth, so if the parent here\n   * is dirty we can simply pass this forward.\n   */\n  node.isProjectionDirty || (node.isProjectionDirty = Boolean(node.parent && node.parent.isProjectionDirty));\n  /**\n   * Propagate isTransformDirty.\n   */\n  node.isTransformDirty || (node.isTransformDirty = Boolean(node.parent && node.parent.isTransformDirty));\n}\nfunction clearSnapshot(node) {\n  node.clearSnapshot();\n}\nfunction clearMeasurements(node) {\n  node.clearMeasurements();\n}\nfunction resetTransformStyle(node) {\n  const {\n    visualElement\n  } = node.options;\n  if (visualElement === null || visualElement === void 0 ? void 0 : visualElement.getProps().onBeforeLayoutMeasure) {\n    visualElement.notify(\"BeforeLayoutMeasure\");\n  }\n  node.resetTransform();\n}\nfunction finishAnimation(node) {\n  node.finishAnimation();\n  node.targetDelta = node.relativeTarget = node.target = undefined;\n}\nfunction resolveTargetDelta(node) {\n  node.resolveTargetDelta();\n}\nfunction calcProjection(node) {\n  node.calcProjection();\n}\nfunction resetRotation(node) {\n  node.resetRotation();\n}\nfunction removeLeadSnapshots(stack) {\n  stack.removeLeadSnapshot();\n}\nfunction mixAxisDelta(output, delta, p) {\n  output.translate = mix(delta.translate, 0, p);\n  output.scale = mix(delta.scale, 1, p);\n  output.origin = delta.origin;\n  output.originPoint = delta.originPoint;\n}\nfunction mixAxis(output, from, to, p) {\n  output.min = mix(from.min, to.min, p);\n  output.max = mix(from.max, to.max, p);\n}\nfunction mixBox(output, from, to, p) {\n  mixAxis(output.x, from.x, to.x, p);\n  mixAxis(output.y, from.y, to.y, p);\n}\nfunction hasOpacityCrossfade(node) {\n  return node.animationValues && node.animationValues.opacityExit !== undefined;\n}\nconst defaultLayoutTransition = {\n  duration: 0.45,\n  ease: [0.4, 0, 0.1, 1]\n};\nfunction mountNodeEarly(node, elementId) {\n  /**\n   * Rather than searching the DOM from document we can search the\n   * path for the deepest mounted ancestor and search from there\n   */\n  let searchNode = node.root;\n  for (let i = node.path.length - 1; i >= 0; i--) {\n    if (Boolean(node.path[i].instance)) {\n      searchNode = node.path[i];\n      break;\n    }\n  }\n  const searchElement = searchNode && searchNode !== node.root ? searchNode.instance : document;\n  const element = searchElement.querySelector(\"[data-projection-id=\\\"\".concat(elementId, \"\\\"]\"));\n  if (element) node.mount(element, true);\n}\nfunction roundAxis(axis) {\n  axis.min = Math.round(axis.min);\n  axis.max = Math.round(axis.max);\n}\nfunction roundBox(box) {\n  roundAxis(box.x);\n  roundAxis(box.y);\n}\nfunction shouldAnimatePositionOnly(animationType, snapshot, layout) {\n  return animationType === \"position\" || animationType === \"preserve-aspect\" && !isNear(aspectRatio(snapshot), aspectRatio(layout), 0.2);\n}\nexport { createProjectionNode, mixAxis, mixAxisDelta, mixBox, propagateDirtyNodes };", "map": {"version": 3, "names": ["cancelSync", "flushSync", "sync", "animate", "SubscriptionManager", "mixValues", "copyBoxInto", "translateAxis", "transformBox", "applyBoxDelta", "applyTreeDeltas", "calcRelativePosition", "calcRelativeBox", "calcBoxDelta", "calcLength", "isNear", "removeBoxTransforms", "createBox", "create<PERSON><PERSON><PERSON>", "getValueTransition", "boxEquals", "isDeltaZero", "aspectRatio", "NodeStack", "scaleCorrectors", "buildProjectionTransform", "eachAxis", "hasTransform", "hasScale", "has2DTranslate", "FlatTree", "resolveMotionValue", "globalProjectionState", "delay", "mix", "transformAxes", "animationTarget", "id", "createProjectionNode", "_ref", "attachResizeListener", "defaultParent", "measureScroll", "checkIsScrollRoot", "resetTransform", "ProjectionNode", "constructor", "elementId", "latestValues", "arguments", "length", "undefined", "parent", "animationId", "children", "Set", "options", "isTreeAnimating", "isAnimationBlocked", "isLayoutDirty", "isTransformDirty", "isProjectionDirty", "updateManuallyBlocked", "updateBlockedByResize", "isUpdating", "isSVG", "needsReset", "shouldResetTransform", "treeScale", "x", "y", "eventHandlers", "Map", "potentialNodes", "checkUpdateFailed", "clearAllSnapshots", "updateProjection", "nodes", "for<PERSON>ach", "propagateDirtyNodes", "resolveTargetDel<PERSON>", "calcProjection", "hasProjected", "isVisible", "animationProgress", "sharedNodes", "root", "path", "depth", "registerPotentialNode", "i", "addEventListener", "name", "handler", "has", "set", "get", "add", "notifyListeners", "subscriptionManager", "_len", "args", "Array", "_key", "notify", "hasListeners", "node", "mount", "instance", "_a", "SVGElement", "tagName", "layoutId", "layout", "visualElement", "current", "delete", "cancelDelay", "resizeUnblockUpdate", "hasAnimatedSinceResize", "finishAnimation", "registerSharedNode", "_ref2", "delta", "hasLayoutChanged", "hasRelativeTargetChanged", "newLayout", "_b", "_c", "_d", "_e", "isTreeAnimationBlocked", "target", "<PERSON><PERSON><PERSON><PERSON>", "layoutTransition", "transition", "getDefaultTransition", "defaultLayoutTransition", "onLayoutAnimationStart", "onLayoutAnimationComplete", "getProps", "targetChanged", "targetLayout", "hasOnlyRelativeTargetChanged", "resumeFrom", "currentAnimation", "resumingFrom", "setAnimationOrigin", "animationOptions", "_objectSpread", "onPlay", "onComplete", "shouldReduceMotion", "type", "startAnimation", "isLead", "onExitComplete", "call", "unmount", "willUpdate", "remove", "getStack", "preRender", "blockUpdate", "unblockUpdate", "isUpdateBlocked", "startUpdate", "resetRotation", "shouldNotifyListeners", "updateScroll", "transformTemplate", "prevTransformTemplateValue", "updateSnapshot", "didUpdate", "updateWasBlocked", "clearMeasurements", "size", "mount<PERSON>ode<PERSON>arly", "clear", "resetTransformStyle", "updateLayout", "notifyLayoutUpdate", "update", "render", "clearSnapshot", "removeLeadSnapshots", "scheduleUpdateProjection", "scheduleCheckAfterUnmount", "postRender", "snapshot", "measure", "alwaysMeasureLayout", "prevLayout", "layoutCorrected", "projectionDel<PERSON>", "layoutBox", "phase", "needsMeasurement", "Boolean", "layoutScroll", "scroll", "isRoot", "offset", "isResetRequested", "hasProjection", "transformTemplateValue", "transformTemplateHasChanged", "scheduleRender", "removeTransform", "pageBox", "measurePageBox", "removeElementScroll", "roundBox", "measuredBox", "source", "box", "measureViewportBox", "boxWithoutScroll", "rootScroll", "applyTransform", "transformOnly", "withTransforms", "boxWithoutTransform", "sourceBox", "nodeBox", "set<PERSON>argetD<PERSON><PERSON>", "targetDel<PERSON>", "setOptions", "crossfade", "lead", "getLead", "attemptToResolveRelativeTarget", "relativeParent", "getClosestProjectingParent", "relativeTarget<PERSON><PERSON>in", "targetWithTransforms", "isShared", "canSkip", "pendingAnimation", "projectionDeltaWithTransform", "prevTreeScaleX", "prevTreeScaleY", "prevProjectionTransform", "projectionTransform", "hide", "show", "notifyAll", "snapshotLatestValues", "mixedValues", "relativeLayout", "isSharedLayoutAnimation", "isOnlyMember", "members", "shouldCrossfadeOpacity", "some", "hasOpacityCrossfade", "mixTargetDelta", "latest", "progress", "mixAxisDelta", "mixBox", "animationValues", "stop", "onUpdate", "completeAnimation", "preserveOpacity", "exitAnimationComplete", "applyTransformsToTarget", "shouldAnimatePositionOnly", "animationType", "xLength", "min", "max", "y<PERSON><PERSON><PERSON>", "stack", "promote", "initialPromotionConfig", "preserveFollowOpacity", "shouldPreserveFollowOpacity", "getPrevLead", "prevLead", "relegate", "hasRotate", "rotate", "rotateX", "rotateY", "rotateZ", "resetValues", "key", "setStaticValue", "getProjectionStyles", "styleProp", "styles", "visibility", "opacity", "pointerEvents", "transform", "emptyStyles", "valuesToRender", "transform<PERSON><PERSON>in", "concat", "origin", "opacityExit", "correct", "applyTo", "corrected", "num", "resetTree", "measuredLayout", "axis", "axisSnapshot", "<PERSON><PERSON><PERSON><PERSON>", "visualD<PERSON><PERSON>", "parentSnapshot", "parentLayout", "relativeSnapshot", "onBeforeLayoutMeasure", "removeLeadSnapshot", "output", "p", "translate", "scale", "originPoint", "mixAxis", "from", "to", "duration", "ease", "searchNode", "searchElement", "document", "element", "querySelector", "roundAxis", "Math", "round"], "sources": ["/home/<USER>/Desktop/jarvis-ui/node_modules/framer-motion/dist/es/projection/node/create-projection-node.mjs"], "sourcesContent": ["import { cancelSync, flushSync, sync } from '../../frameloop/index.mjs';\nimport { animate } from '../../animation/animate.mjs';\nimport { SubscriptionManager } from '../../utils/subscription-manager.mjs';\nimport { mixValues } from '../animation/mix-values.mjs';\nimport { copyBoxInto } from '../geometry/copy.mjs';\nimport { translateAxis, transformBox, applyBoxDelta, applyTreeDeltas } from '../geometry/delta-apply.mjs';\nimport { calcRelativePosition, calcRelativeBox, calcBoxDelta, calcLength, isNear } from '../geometry/delta-calc.mjs';\nimport { removeBoxTransforms } from '../geometry/delta-remove.mjs';\nimport { createBox, createDelta } from '../geometry/models.mjs';\nimport { getValueTransition } from '../../animation/utils/transitions.mjs';\nimport { boxEquals, isDelta<PERSON>ero, aspectRatio } from '../geometry/utils.mjs';\nimport { NodeStack } from '../shared/stack.mjs';\nimport { scaleCorrectors } from '../styles/scale-correction.mjs';\nimport { buildProjectionTransform } from '../styles/transform.mjs';\nimport { eachAxis } from '../utils/each-axis.mjs';\nimport { hasTransform, hasScale, has2DTranslate } from '../utils/has-transform.mjs';\nimport { FlatTree } from '../../render/utils/flat-tree.mjs';\nimport { resolveMotionValue } from '../../value/utils/resolve-motion-value.mjs';\nimport { globalProjectionState } from './state.mjs';\nimport { delay } from '../../utils/delay.mjs';\nimport { mix } from '../../utils/mix.mjs';\n\nconst transformAxes = [\"\", \"X\", \"Y\", \"Z\"];\n/**\n * We use 1000 as the animation target as 0-1000 maps better to pixels than 0-1\n * which has a noticeable difference in spring animations\n */\nconst animationTarget = 1000;\nlet id = 0;\nfunction createProjectionNode({ attachResizeListener, defaultParent, measureScroll, checkIsScrollRoot, resetTransform, }) {\n    return class ProjectionNode {\n        constructor(elementId, latestValues = {}, parent = defaultParent === null || defaultParent === void 0 ? void 0 : defaultParent()) {\n            /**\n             * A unique ID generated for every projection node.\n             */\n            this.id = id++;\n            /**\n             * An id that represents a unique session instigated by startUpdate.\n             */\n            this.animationId = 0;\n            /**\n             * A Set containing all this component's children. This is used to iterate\n             * through the children.\n             *\n             * TODO: This could be faster to iterate as a flat array stored on the root node.\n             */\n            this.children = new Set();\n            /**\n             * Options for the node. We use this to configure what kind of layout animations\n             * we should perform (if any).\n             */\n            this.options = {};\n            /**\n             * We use this to detect when its safe to shut down part of a projection tree.\n             * We have to keep projecting children for scale correction and relative projection\n             * until all their parents stop performing layout animations.\n             */\n            this.isTreeAnimating = false;\n            this.isAnimationBlocked = false;\n            /**\n             * Flag to true if we think this layout has been changed. We can't always know this,\n             * currently we set it to true every time a component renders, or if it has a layoutDependency\n             * if that has changed between renders. Additionally, components can be grouped by LayoutGroup\n             * and if one node is dirtied, they all are.\n             */\n            this.isLayoutDirty = false;\n            this.isTransformDirty = false;\n            /**\n             * Flag to true if we think the projection calculations for this or any\n             * child might need recalculating as a result of an updated transform or layout animation.\n             */\n            this.isProjectionDirty = false;\n            /**\n             * Block layout updates for instant layout transitions throughout the tree.\n             */\n            this.updateManuallyBlocked = false;\n            this.updateBlockedByResize = false;\n            /**\n             * Set to true between the start of the first `willUpdate` call and the end of the `didUpdate`\n             * call.\n             */\n            this.isUpdating = false;\n            /**\n             * If this is an SVG element we currently disable projection transforms\n             */\n            this.isSVG = false;\n            /**\n             * Flag to true (during promotion) if a node doing an instant layout transition needs to reset\n             * its projection styles.\n             */\n            this.needsReset = false;\n            /**\n             * Flags whether this node should have its transform reset prior to measuring.\n             */\n            this.shouldResetTransform = false;\n            /**\n             * An object representing the calculated contextual/accumulated/tree scale.\n             * This will be used to scale calculcated projection transforms, as these are\n             * calculated in screen-space but need to be scaled for elements to layoutly\n             * make it to their calculated destinations.\n             *\n             * TODO: Lazy-init\n             */\n            this.treeScale = { x: 1, y: 1 };\n            /**\n             *\n             */\n            this.eventHandlers = new Map();\n            // Note: Currently only running on root node\n            this.potentialNodes = new Map();\n            this.checkUpdateFailed = () => {\n                if (this.isUpdating) {\n                    this.isUpdating = false;\n                    this.clearAllSnapshots();\n                }\n            };\n            /**\n             * This is a multi-step process as shared nodes might be of different depths. Nodes\n             * are sorted by depth order, so we need to resolve the entire tree before moving to\n             * the next step.\n             */\n            this.updateProjection = () => {\n                this.nodes.forEach(propagateDirtyNodes);\n                this.nodes.forEach(resolveTargetDelta);\n                this.nodes.forEach(calcProjection);\n            };\n            this.hasProjected = false;\n            this.isVisible = true;\n            this.animationProgress = 0;\n            /**\n             * Shared layout\n             */\n            // TODO Only running on root node\n            this.sharedNodes = new Map();\n            this.elementId = elementId;\n            this.latestValues = latestValues;\n            this.root = parent ? parent.root || parent : this;\n            this.path = parent ? [...parent.path, parent] : [];\n            this.parent = parent;\n            this.depth = parent ? parent.depth + 1 : 0;\n            elementId && this.root.registerPotentialNode(elementId, this);\n            for (let i = 0; i < this.path.length; i++) {\n                this.path[i].shouldResetTransform = true;\n            }\n            if (this.root === this)\n                this.nodes = new FlatTree();\n        }\n        addEventListener(name, handler) {\n            if (!this.eventHandlers.has(name)) {\n                this.eventHandlers.set(name, new SubscriptionManager());\n            }\n            return this.eventHandlers.get(name).add(handler);\n        }\n        notifyListeners(name, ...args) {\n            const subscriptionManager = this.eventHandlers.get(name);\n            subscriptionManager === null || subscriptionManager === void 0 ? void 0 : subscriptionManager.notify(...args);\n        }\n        hasListeners(name) {\n            return this.eventHandlers.has(name);\n        }\n        registerPotentialNode(elementId, node) {\n            this.potentialNodes.set(elementId, node);\n        }\n        /**\n         * Lifecycles\n         */\n        mount(instance, isLayoutDirty = false) {\n            var _a;\n            if (this.instance)\n                return;\n            this.isSVG =\n                instance instanceof SVGElement && instance.tagName !== \"svg\";\n            this.instance = instance;\n            const { layoutId, layout, visualElement } = this.options;\n            if (visualElement && !visualElement.current) {\n                visualElement.mount(instance);\n            }\n            this.root.nodes.add(this);\n            (_a = this.parent) === null || _a === void 0 ? void 0 : _a.children.add(this);\n            this.elementId && this.root.potentialNodes.delete(this.elementId);\n            if (isLayoutDirty && (layout || layoutId)) {\n                this.isLayoutDirty = true;\n            }\n            if (attachResizeListener) {\n                let cancelDelay;\n                const resizeUnblockUpdate = () => (this.root.updateBlockedByResize = false);\n                attachResizeListener(instance, () => {\n                    this.root.updateBlockedByResize = true;\n                    cancelDelay && cancelDelay();\n                    cancelDelay = delay(resizeUnblockUpdate, 250);\n                    if (globalProjectionState.hasAnimatedSinceResize) {\n                        globalProjectionState.hasAnimatedSinceResize = false;\n                        this.nodes.forEach(finishAnimation);\n                    }\n                });\n            }\n            if (layoutId) {\n                this.root.registerSharedNode(layoutId, this);\n            }\n            // Only register the handler if it requires layout animation\n            if (this.options.animate !== false &&\n                visualElement &&\n                (layoutId || layout)) {\n                this.addEventListener(\"didUpdate\", ({ delta, hasLayoutChanged, hasRelativeTargetChanged, layout: newLayout, }) => {\n                    var _a, _b, _c, _d, _e;\n                    if (this.isTreeAnimationBlocked()) {\n                        this.target = undefined;\n                        this.relativeTarget = undefined;\n                        return;\n                    }\n                    // TODO: Check here if an animation exists\n                    const layoutTransition = (_b = (_a = this.options.transition) !== null && _a !== void 0 ? _a : visualElement.getDefaultTransition()) !== null && _b !== void 0 ? _b : defaultLayoutTransition;\n                    const { onLayoutAnimationStart, onLayoutAnimationComplete, } = visualElement.getProps();\n                    /**\n                     * The target layout of the element might stay the same,\n                     * but its position relative to its parent has changed.\n                     */\n                    const targetChanged = !this.targetLayout ||\n                        !boxEquals(this.targetLayout, newLayout) ||\n                        hasRelativeTargetChanged;\n                    /**\n                     * If the layout hasn't seemed to have changed, it might be that the\n                     * element is visually in the same place in the document but its position\n                     * relative to its parent has indeed changed. So here we check for that.\n                     */\n                    const hasOnlyRelativeTargetChanged = !hasLayoutChanged && hasRelativeTargetChanged;\n                    if (((_c = this.resumeFrom) === null || _c === void 0 ? void 0 : _c.instance) ||\n                        hasOnlyRelativeTargetChanged ||\n                        (hasLayoutChanged &&\n                            (targetChanged || !this.currentAnimation))) {\n                        if (this.resumeFrom) {\n                            this.resumingFrom = this.resumeFrom;\n                            this.resumingFrom.resumingFrom = undefined;\n                        }\n                        this.setAnimationOrigin(delta, hasOnlyRelativeTargetChanged);\n                        const animationOptions = {\n                            ...getValueTransition(layoutTransition, \"layout\"),\n                            onPlay: onLayoutAnimationStart,\n                            onComplete: onLayoutAnimationComplete,\n                        };\n                        if (visualElement.shouldReduceMotion) {\n                            animationOptions.delay = 0;\n                            animationOptions.type = false;\n                        }\n                        this.startAnimation(animationOptions);\n                    }\n                    else {\n                        /**\n                         * If the layout hasn't changed and we have an animation that hasn't started yet,\n                         * finish it immediately. Otherwise it will be animating from a location\n                         * that was probably never commited to screen and look like a jumpy box.\n                         */\n                        if (!hasLayoutChanged &&\n                            this.animationProgress === 0) {\n                            finishAnimation(this);\n                        }\n                        this.isLead() && ((_e = (_d = this.options).onExitComplete) === null || _e === void 0 ? void 0 : _e.call(_d));\n                    }\n                    this.targetLayout = newLayout;\n                });\n            }\n        }\n        unmount() {\n            var _a, _b;\n            this.options.layoutId && this.willUpdate();\n            this.root.nodes.remove(this);\n            (_a = this.getStack()) === null || _a === void 0 ? void 0 : _a.remove(this);\n            (_b = this.parent) === null || _b === void 0 ? void 0 : _b.children.delete(this);\n            this.instance = undefined;\n            cancelSync.preRender(this.updateProjection);\n        }\n        // only on the root\n        blockUpdate() {\n            this.updateManuallyBlocked = true;\n        }\n        unblockUpdate() {\n            this.updateManuallyBlocked = false;\n        }\n        isUpdateBlocked() {\n            return this.updateManuallyBlocked || this.updateBlockedByResize;\n        }\n        isTreeAnimationBlocked() {\n            var _a;\n            return (this.isAnimationBlocked ||\n                ((_a = this.parent) === null || _a === void 0 ? void 0 : _a.isTreeAnimationBlocked()) ||\n                false);\n        }\n        // Note: currently only running on root node\n        startUpdate() {\n            var _a;\n            if (this.isUpdateBlocked())\n                return;\n            this.isUpdating = true;\n            (_a = this.nodes) === null || _a === void 0 ? void 0 : _a.forEach(resetRotation);\n            this.animationId++;\n        }\n        willUpdate(shouldNotifyListeners = true) {\n            var _a, _b, _c;\n            if (this.root.isUpdateBlocked()) {\n                (_b = (_a = this.options).onExitComplete) === null || _b === void 0 ? void 0 : _b.call(_a);\n                return;\n            }\n            !this.root.isUpdating && this.root.startUpdate();\n            if (this.isLayoutDirty)\n                return;\n            this.isLayoutDirty = true;\n            for (let i = 0; i < this.path.length; i++) {\n                const node = this.path[i];\n                node.shouldResetTransform = true;\n                node.updateScroll(\"snapshot\");\n            }\n            const { layoutId, layout } = this.options;\n            if (layoutId === undefined && !layout)\n                return;\n            const transformTemplate = (_c = this.options.visualElement) === null || _c === void 0 ? void 0 : _c.getProps().transformTemplate;\n            this.prevTransformTemplateValue = transformTemplate === null || transformTemplate === void 0 ? void 0 : transformTemplate(this.latestValues, \"\");\n            this.updateSnapshot();\n            shouldNotifyListeners && this.notifyListeners(\"willUpdate\");\n        }\n        // Note: Currently only running on root node\n        didUpdate() {\n            const updateWasBlocked = this.isUpdateBlocked();\n            // When doing an instant transition, we skip the layout update,\n            // but should still clean up the measurements so that the next\n            // snapshot could be taken correctly.\n            if (updateWasBlocked) {\n                this.unblockUpdate();\n                this.clearAllSnapshots();\n                this.nodes.forEach(clearMeasurements);\n                return;\n            }\n            if (!this.isUpdating)\n                return;\n            this.isUpdating = false;\n            /**\n             * Search for and mount newly-added projection elements.\n             *\n             * TODO: Every time a new component is rendered we could search up the tree for\n             * the closest mounted node and query from there rather than document.\n             */\n            if (this.potentialNodes.size) {\n                this.potentialNodes.forEach(mountNodeEarly);\n                this.potentialNodes.clear();\n            }\n            /**\n             * Write\n             */\n            this.nodes.forEach(resetTransformStyle);\n            /**\n             * Read ==================\n             */\n            // Update layout measurements of updated children\n            this.nodes.forEach(updateLayout);\n            /**\n             * Write\n             */\n            // Notify listeners that the layout is updated\n            this.nodes.forEach(notifyLayoutUpdate);\n            this.clearAllSnapshots();\n            // Flush any scheduled updates\n            flushSync.update();\n            flushSync.preRender();\n            flushSync.render();\n        }\n        clearAllSnapshots() {\n            this.nodes.forEach(clearSnapshot);\n            this.sharedNodes.forEach(removeLeadSnapshots);\n        }\n        scheduleUpdateProjection() {\n            sync.preRender(this.updateProjection, false, true);\n        }\n        scheduleCheckAfterUnmount() {\n            /**\n             * If the unmounting node is in a layoutGroup and did trigger a willUpdate,\n             * we manually call didUpdate to give a chance to the siblings to animate.\n             * Otherwise, cleanup all snapshots to prevents future nodes from reusing them.\n             */\n            sync.postRender(() => {\n                if (this.isLayoutDirty) {\n                    this.root.didUpdate();\n                }\n                else {\n                    this.root.checkUpdateFailed();\n                }\n            });\n        }\n        /**\n         * Update measurements\n         */\n        updateSnapshot() {\n            if (this.snapshot || !this.instance)\n                return;\n            this.snapshot = this.measure();\n        }\n        updateLayout() {\n            var _a;\n            if (!this.instance)\n                return;\n            // TODO: Incorporate into a forwarded scroll offset\n            this.updateScroll();\n            if (!(this.options.alwaysMeasureLayout && this.isLead()) &&\n                !this.isLayoutDirty) {\n                return;\n            }\n            /**\n             * When a node is mounted, it simply resumes from the prevLead's\n             * snapshot instead of taking a new one, but the ancestors scroll\n             * might have updated while the prevLead is unmounted. We need to\n             * update the scroll again to make sure the layout we measure is\n             * up to date.\n             */\n            if (this.resumeFrom && !this.resumeFrom.instance) {\n                for (let i = 0; i < this.path.length; i++) {\n                    const node = this.path[i];\n                    node.updateScroll();\n                }\n            }\n            const prevLayout = this.layout;\n            this.layout = this.measure(false);\n            this.layoutCorrected = createBox();\n            this.isLayoutDirty = false;\n            this.projectionDelta = undefined;\n            this.notifyListeners(\"measure\", this.layout.layoutBox);\n            (_a = this.options.visualElement) === null || _a === void 0 ? void 0 : _a.notify(\"LayoutMeasure\", this.layout.layoutBox, prevLayout === null || prevLayout === void 0 ? void 0 : prevLayout.layoutBox);\n        }\n        updateScroll(phase = \"measure\") {\n            let needsMeasurement = Boolean(this.options.layoutScroll && this.instance);\n            if (this.scroll &&\n                this.scroll.animationId === this.root.animationId &&\n                this.scroll.phase === phase) {\n                needsMeasurement = false;\n            }\n            if (needsMeasurement) {\n                this.scroll = {\n                    animationId: this.root.animationId,\n                    phase,\n                    isRoot: checkIsScrollRoot(this.instance),\n                    offset: measureScroll(this.instance),\n                };\n            }\n        }\n        resetTransform() {\n            var _a;\n            if (!resetTransform)\n                return;\n            const isResetRequested = this.isLayoutDirty || this.shouldResetTransform;\n            const hasProjection = this.projectionDelta && !isDeltaZero(this.projectionDelta);\n            const transformTemplate = (_a = this.options.visualElement) === null || _a === void 0 ? void 0 : _a.getProps().transformTemplate;\n            const transformTemplateValue = transformTemplate === null || transformTemplate === void 0 ? void 0 : transformTemplate(this.latestValues, \"\");\n            const transformTemplateHasChanged = transformTemplateValue !== this.prevTransformTemplateValue;\n            if (isResetRequested &&\n                (hasProjection ||\n                    hasTransform(this.latestValues) ||\n                    transformTemplateHasChanged)) {\n                resetTransform(this.instance, transformTemplateValue);\n                this.shouldResetTransform = false;\n                this.scheduleRender();\n            }\n        }\n        measure(removeTransform = true) {\n            const pageBox = this.measurePageBox();\n            let layoutBox = this.removeElementScroll(pageBox);\n            /**\n             * Measurements taken during the pre-render stage\n             * still have transforms applied so we remove them\n             * via calculation.\n             */\n            if (removeTransform) {\n                layoutBox = this.removeTransform(layoutBox);\n            }\n            roundBox(layoutBox);\n            return {\n                animationId: this.root.animationId,\n                measuredBox: pageBox,\n                layoutBox,\n                latestValues: {},\n                source: this.id,\n            };\n        }\n        measurePageBox() {\n            const { visualElement } = this.options;\n            if (!visualElement)\n                return createBox();\n            const box = visualElement.measureViewportBox();\n            // Remove viewport scroll to give page-relative coordinates\n            const { scroll } = this.root;\n            if (scroll) {\n                translateAxis(box.x, scroll.offset.x);\n                translateAxis(box.y, scroll.offset.y);\n            }\n            return box;\n        }\n        removeElementScroll(box) {\n            const boxWithoutScroll = createBox();\n            copyBoxInto(boxWithoutScroll, box);\n            /**\n             * Performance TODO: Keep a cumulative scroll offset down the tree\n             * rather than loop back up the path.\n             */\n            for (let i = 0; i < this.path.length; i++) {\n                const node = this.path[i];\n                const { scroll, options } = node;\n                if (node !== this.root && scroll && options.layoutScroll) {\n                    /**\n                     * If this is a new scroll root, we want to remove all previous scrolls\n                     * from the viewport box.\n                     */\n                    if (scroll.isRoot) {\n                        copyBoxInto(boxWithoutScroll, box);\n                        const { scroll: rootScroll } = this.root;\n                        /**\n                         * Undo the application of page scroll that was originally added\n                         * to the measured bounding box.\n                         */\n                        if (rootScroll) {\n                            translateAxis(boxWithoutScroll.x, -rootScroll.offset.x);\n                            translateAxis(boxWithoutScroll.y, -rootScroll.offset.y);\n                        }\n                    }\n                    translateAxis(boxWithoutScroll.x, scroll.offset.x);\n                    translateAxis(boxWithoutScroll.y, scroll.offset.y);\n                }\n            }\n            return boxWithoutScroll;\n        }\n        applyTransform(box, transformOnly = false) {\n            const withTransforms = createBox();\n            copyBoxInto(withTransforms, box);\n            for (let i = 0; i < this.path.length; i++) {\n                const node = this.path[i];\n                if (!transformOnly &&\n                    node.options.layoutScroll &&\n                    node.scroll &&\n                    node !== node.root) {\n                    transformBox(withTransforms, {\n                        x: -node.scroll.offset.x,\n                        y: -node.scroll.offset.y,\n                    });\n                }\n                if (!hasTransform(node.latestValues))\n                    continue;\n                transformBox(withTransforms, node.latestValues);\n            }\n            if (hasTransform(this.latestValues)) {\n                transformBox(withTransforms, this.latestValues);\n            }\n            return withTransforms;\n        }\n        removeTransform(box) {\n            var _a;\n            const boxWithoutTransform = createBox();\n            copyBoxInto(boxWithoutTransform, box);\n            for (let i = 0; i < this.path.length; i++) {\n                const node = this.path[i];\n                if (!node.instance)\n                    continue;\n                if (!hasTransform(node.latestValues))\n                    continue;\n                hasScale(node.latestValues) && node.updateSnapshot();\n                const sourceBox = createBox();\n                const nodeBox = node.measurePageBox();\n                copyBoxInto(sourceBox, nodeBox);\n                removeBoxTransforms(boxWithoutTransform, node.latestValues, (_a = node.snapshot) === null || _a === void 0 ? void 0 : _a.layoutBox, sourceBox);\n            }\n            if (hasTransform(this.latestValues)) {\n                removeBoxTransforms(boxWithoutTransform, this.latestValues);\n            }\n            return boxWithoutTransform;\n        }\n        /**\n         *\n         */\n        setTargetDelta(delta) {\n            this.targetDelta = delta;\n            this.isProjectionDirty = true;\n            this.root.scheduleUpdateProjection();\n        }\n        setOptions(options) {\n            this.options = {\n                ...this.options,\n                ...options,\n                crossfade: options.crossfade !== undefined ? options.crossfade : true,\n            };\n        }\n        clearMeasurements() {\n            this.scroll = undefined;\n            this.layout = undefined;\n            this.snapshot = undefined;\n            this.prevTransformTemplateValue = undefined;\n            this.targetDelta = undefined;\n            this.target = undefined;\n            this.isLayoutDirty = false;\n        }\n        /**\n         * Frame calculations\n         */\n        resolveTargetDelta() {\n            var _a;\n            /**\n             * Once the dirty status of nodes has been spread through the tree, we also\n             * need to check if we have a shared node of a different depth that has itself\n             * been dirtied.\n             */\n            const lead = this.getLead();\n            this.isProjectionDirty || (this.isProjectionDirty = lead.isProjectionDirty);\n            this.isTransformDirty || (this.isTransformDirty = lead.isTransformDirty);\n            /**\n             * We don't use transform for this step of processing so we don't\n             * need to check whether any nodes have changed transform.\n             */\n            if (!this.isProjectionDirty && !this.attemptToResolveRelativeTarget)\n                return;\n            const { layout, layoutId } = this.options;\n            /**\n             * If we have no layout, we can't perform projection, so early return\n             */\n            if (!this.layout || !(layout || layoutId))\n                return;\n            /**\n             * If we don't have a targetDelta but do have a layout, we can attempt to resolve\n             * a relativeParent. This will allow a component to perform scale correction\n             * even if no animation has started.\n             */\n            // TODO If this is unsuccessful this currently happens every frame\n            if (!this.targetDelta && !this.relativeTarget) {\n                // TODO: This is a semi-repetition of further down this function, make DRY\n                const relativeParent = this.getClosestProjectingParent();\n                if (relativeParent && relativeParent.layout) {\n                    this.relativeParent = relativeParent;\n                    this.relativeTarget = createBox();\n                    this.relativeTargetOrigin = createBox();\n                    calcRelativePosition(this.relativeTargetOrigin, this.layout.layoutBox, relativeParent.layout.layoutBox);\n                    copyBoxInto(this.relativeTarget, this.relativeTargetOrigin);\n                }\n                else {\n                    this.relativeParent = this.relativeTarget = undefined;\n                }\n            }\n            /**\n             * If we have no relative target or no target delta our target isn't valid\n             * for this frame.\n             */\n            if (!this.relativeTarget && !this.targetDelta)\n                return;\n            /**\n             * Lazy-init target data structure\n             */\n            if (!this.target) {\n                this.target = createBox();\n                this.targetWithTransforms = createBox();\n            }\n            /**\n             * If we've got a relative box for this component, resolve it into a target relative to the parent.\n             */\n            if (this.relativeTarget &&\n                this.relativeTargetOrigin &&\n                ((_a = this.relativeParent) === null || _a === void 0 ? void 0 : _a.target)) {\n                calcRelativeBox(this.target, this.relativeTarget, this.relativeParent.target);\n                /**\n                 * If we've only got a targetDelta, resolve it into a target\n                 */\n            }\n            else if (this.targetDelta) {\n                if (Boolean(this.resumingFrom)) {\n                    // TODO: This is creating a new object every frame\n                    this.target = this.applyTransform(this.layout.layoutBox);\n                }\n                else {\n                    copyBoxInto(this.target, this.layout.layoutBox);\n                }\n                applyBoxDelta(this.target, this.targetDelta);\n            }\n            else {\n                /**\n                 * If no target, use own layout as target\n                 */\n                copyBoxInto(this.target, this.layout.layoutBox);\n            }\n            /**\n             * If we've been told to attempt to resolve a relative target, do so.\n             */\n            if (this.attemptToResolveRelativeTarget) {\n                this.attemptToResolveRelativeTarget = false;\n                const relativeParent = this.getClosestProjectingParent();\n                if (relativeParent &&\n                    Boolean(relativeParent.resumingFrom) ===\n                        Boolean(this.resumingFrom) &&\n                    !relativeParent.options.layoutScroll &&\n                    relativeParent.target) {\n                    this.relativeParent = relativeParent;\n                    this.relativeTarget = createBox();\n                    this.relativeTargetOrigin = createBox();\n                    calcRelativePosition(this.relativeTargetOrigin, this.target, relativeParent.target);\n                    copyBoxInto(this.relativeTarget, this.relativeTargetOrigin);\n                }\n                else {\n                    this.relativeParent = this.relativeTarget = undefined;\n                }\n            }\n        }\n        getClosestProjectingParent() {\n            if (!this.parent ||\n                hasScale(this.parent.latestValues) ||\n                has2DTranslate(this.parent.latestValues))\n                return undefined;\n            if ((this.parent.relativeTarget || this.parent.targetDelta) &&\n                this.parent.layout) {\n                return this.parent;\n            }\n            else {\n                return this.parent.getClosestProjectingParent();\n            }\n        }\n        calcProjection() {\n            var _a;\n            const { isProjectionDirty, isTransformDirty } = this;\n            this.isProjectionDirty = this.isTransformDirty = false;\n            const lead = this.getLead();\n            const isShared = Boolean(this.resumingFrom) || this !== lead;\n            let canSkip = true;\n            if (isProjectionDirty)\n                canSkip = false;\n            if (isShared && isTransformDirty)\n                canSkip = false;\n            if (canSkip)\n                return;\n            const { layout, layoutId } = this.options;\n            /**\n             * If this section of the tree isn't animating we can\n             * delete our target sources for the following frame.\n             */\n            this.isTreeAnimating = Boolean(((_a = this.parent) === null || _a === void 0 ? void 0 : _a.isTreeAnimating) ||\n                this.currentAnimation ||\n                this.pendingAnimation);\n            if (!this.isTreeAnimating) {\n                this.targetDelta = this.relativeTarget = undefined;\n            }\n            if (!this.layout || !(layout || layoutId))\n                return;\n            /**\n             * Reset the corrected box with the latest values from box, as we're then going\n             * to perform mutative operations on it.\n             */\n            copyBoxInto(this.layoutCorrected, this.layout.layoutBox);\n            /**\n             * Apply all the parent deltas to this box to produce the corrected box. This\n             * is the layout box, as it will appear on screen as a result of the transforms of its parents.\n             */\n            applyTreeDeltas(this.layoutCorrected, this.treeScale, this.path, isShared);\n            const { target } = lead;\n            if (!target)\n                return;\n            if (!this.projectionDelta) {\n                this.projectionDelta = createDelta();\n                this.projectionDeltaWithTransform = createDelta();\n            }\n            const prevTreeScaleX = this.treeScale.x;\n            const prevTreeScaleY = this.treeScale.y;\n            const prevProjectionTransform = this.projectionTransform;\n            /**\n             * Update the delta between the corrected box and the target box before user-set transforms were applied.\n             * This will allow us to calculate the corrected borderRadius and boxShadow to compensate\n             * for our layout reprojection, but still allow them to be scaled correctly by the user.\n             * It might be that to simplify this we may want to accept that user-set scale is also corrected\n             * and we wouldn't have to keep and calc both deltas, OR we could support a user setting\n             * to allow people to choose whether these styles are corrected based on just the\n             * layout reprojection or the final bounding box.\n             */\n            calcBoxDelta(this.projectionDelta, this.layoutCorrected, target, this.latestValues);\n            this.projectionTransform = buildProjectionTransform(this.projectionDelta, this.treeScale);\n            if (this.projectionTransform !== prevProjectionTransform ||\n                this.treeScale.x !== prevTreeScaleX ||\n                this.treeScale.y !== prevTreeScaleY) {\n                this.hasProjected = true;\n                this.scheduleRender();\n                this.notifyListeners(\"projectionUpdate\", target);\n            }\n        }\n        hide() {\n            this.isVisible = false;\n            // TODO: Schedule render\n        }\n        show() {\n            this.isVisible = true;\n            // TODO: Schedule render\n        }\n        scheduleRender(notifyAll = true) {\n            var _a, _b, _c;\n            (_b = (_a = this.options).scheduleRender) === null || _b === void 0 ? void 0 : _b.call(_a);\n            notifyAll && ((_c = this.getStack()) === null || _c === void 0 ? void 0 : _c.scheduleRender());\n            if (this.resumingFrom && !this.resumingFrom.instance) {\n                this.resumingFrom = undefined;\n            }\n        }\n        setAnimationOrigin(delta, hasOnlyRelativeTargetChanged = false) {\n            var _a, _b;\n            const snapshot = this.snapshot;\n            const snapshotLatestValues = (snapshot === null || snapshot === void 0 ? void 0 : snapshot.latestValues) || {};\n            const mixedValues = { ...this.latestValues };\n            const targetDelta = createDelta();\n            this.relativeTarget = this.relativeTargetOrigin = undefined;\n            this.attemptToResolveRelativeTarget = !hasOnlyRelativeTargetChanged;\n            const relativeLayout = createBox();\n            const isSharedLayoutAnimation = (snapshot === null || snapshot === void 0 ? void 0 : snapshot.source) !== ((_a = this.layout) === null || _a === void 0 ? void 0 : _a.source);\n            const isOnlyMember = (((_b = this.getStack()) === null || _b === void 0 ? void 0 : _b.members.length) || 0) <= 1;\n            const shouldCrossfadeOpacity = Boolean(isSharedLayoutAnimation &&\n                !isOnlyMember &&\n                this.options.crossfade === true &&\n                !this.path.some(hasOpacityCrossfade));\n            this.animationProgress = 0;\n            this.mixTargetDelta = (latest) => {\n                var _a;\n                const progress = latest / 1000;\n                mixAxisDelta(targetDelta.x, delta.x, progress);\n                mixAxisDelta(targetDelta.y, delta.y, progress);\n                this.setTargetDelta(targetDelta);\n                if (this.relativeTarget &&\n                    this.relativeTargetOrigin &&\n                    this.layout &&\n                    ((_a = this.relativeParent) === null || _a === void 0 ? void 0 : _a.layout)) {\n                    calcRelativePosition(relativeLayout, this.layout.layoutBox, this.relativeParent.layout.layoutBox);\n                    mixBox(this.relativeTarget, this.relativeTargetOrigin, relativeLayout, progress);\n                }\n                if (isSharedLayoutAnimation) {\n                    this.animationValues = mixedValues;\n                    mixValues(mixedValues, snapshotLatestValues, this.latestValues, progress, shouldCrossfadeOpacity, isOnlyMember);\n                }\n                this.root.scheduleUpdateProjection();\n                this.scheduleRender();\n                this.animationProgress = progress;\n            };\n            this.mixTargetDelta(0);\n        }\n        startAnimation(options) {\n            var _a, _b;\n            this.notifyListeners(\"animationStart\");\n            (_a = this.currentAnimation) === null || _a === void 0 ? void 0 : _a.stop();\n            if (this.resumingFrom) {\n                (_b = this.resumingFrom.currentAnimation) === null || _b === void 0 ? void 0 : _b.stop();\n            }\n            if (this.pendingAnimation) {\n                cancelSync.update(this.pendingAnimation);\n                this.pendingAnimation = undefined;\n            }\n            /**\n             * Start the animation in the next frame to have a frame with progress 0,\n             * where the target is the same as when the animation started, so we can\n             * calculate the relative positions correctly for instant transitions.\n             */\n            this.pendingAnimation = sync.update(() => {\n                globalProjectionState.hasAnimatedSinceResize = true;\n                this.currentAnimation = animate(0, animationTarget, {\n                    ...options,\n                    onUpdate: (latest) => {\n                        var _a;\n                        this.mixTargetDelta(latest);\n                        (_a = options.onUpdate) === null || _a === void 0 ? void 0 : _a.call(options, latest);\n                    },\n                    onComplete: () => {\n                        var _a;\n                        (_a = options.onComplete) === null || _a === void 0 ? void 0 : _a.call(options);\n                        this.completeAnimation();\n                    },\n                });\n                if (this.resumingFrom) {\n                    this.resumingFrom.currentAnimation = this.currentAnimation;\n                }\n                this.pendingAnimation = undefined;\n            });\n        }\n        completeAnimation() {\n            var _a;\n            if (this.resumingFrom) {\n                this.resumingFrom.currentAnimation = undefined;\n                this.resumingFrom.preserveOpacity = undefined;\n            }\n            (_a = this.getStack()) === null || _a === void 0 ? void 0 : _a.exitAnimationComplete();\n            this.resumingFrom =\n                this.currentAnimation =\n                    this.animationValues =\n                        undefined;\n            this.notifyListeners(\"animationComplete\");\n        }\n        finishAnimation() {\n            var _a;\n            if (this.currentAnimation) {\n                (_a = this.mixTargetDelta) === null || _a === void 0 ? void 0 : _a.call(this, animationTarget);\n                this.currentAnimation.stop();\n            }\n            this.completeAnimation();\n        }\n        applyTransformsToTarget() {\n            const lead = this.getLead();\n            let { targetWithTransforms, target, layout, latestValues } = lead;\n            if (!targetWithTransforms || !target || !layout)\n                return;\n            /**\n             * If we're only animating position, and this element isn't the lead element,\n             * then instead of projecting into the lead box we instead want to calculate\n             * a new target that aligns the two boxes but maintains the layout shape.\n             */\n            if (this !== lead &&\n                this.layout &&\n                layout &&\n                shouldAnimatePositionOnly(this.options.animationType, this.layout.layoutBox, layout.layoutBox)) {\n                target = this.target || createBox();\n                const xLength = calcLength(this.layout.layoutBox.x);\n                target.x.min = lead.target.x.min;\n                target.x.max = target.x.min + xLength;\n                const yLength = calcLength(this.layout.layoutBox.y);\n                target.y.min = lead.target.y.min;\n                target.y.max = target.y.min + yLength;\n            }\n            copyBoxInto(targetWithTransforms, target);\n            /**\n             * Apply the latest user-set transforms to the targetBox to produce the targetBoxFinal.\n             * This is the final box that we will then project into by calculating a transform delta and\n             * applying it to the corrected box.\n             */\n            transformBox(targetWithTransforms, latestValues);\n            /**\n             * Update the delta between the corrected box and the final target box, after\n             * user-set transforms are applied to it. This will be used by the renderer to\n             * create a transform style that will reproject the element from its layout layout\n             * into the desired bounding box.\n             */\n            calcBoxDelta(this.projectionDeltaWithTransform, this.layoutCorrected, targetWithTransforms, latestValues);\n        }\n        registerSharedNode(layoutId, node) {\n            var _a, _b, _c;\n            if (!this.sharedNodes.has(layoutId)) {\n                this.sharedNodes.set(layoutId, new NodeStack());\n            }\n            const stack = this.sharedNodes.get(layoutId);\n            stack.add(node);\n            node.promote({\n                transition: (_a = node.options.initialPromotionConfig) === null || _a === void 0 ? void 0 : _a.transition,\n                preserveFollowOpacity: (_c = (_b = node.options.initialPromotionConfig) === null || _b === void 0 ? void 0 : _b.shouldPreserveFollowOpacity) === null || _c === void 0 ? void 0 : _c.call(_b, node),\n            });\n        }\n        isLead() {\n            const stack = this.getStack();\n            return stack ? stack.lead === this : true;\n        }\n        getLead() {\n            var _a;\n            const { layoutId } = this.options;\n            return layoutId ? ((_a = this.getStack()) === null || _a === void 0 ? void 0 : _a.lead) || this : this;\n        }\n        getPrevLead() {\n            var _a;\n            const { layoutId } = this.options;\n            return layoutId ? (_a = this.getStack()) === null || _a === void 0 ? void 0 : _a.prevLead : undefined;\n        }\n        getStack() {\n            const { layoutId } = this.options;\n            if (layoutId)\n                return this.root.sharedNodes.get(layoutId);\n        }\n        promote({ needsReset, transition, preserveFollowOpacity, } = {}) {\n            const stack = this.getStack();\n            if (stack)\n                stack.promote(this, preserveFollowOpacity);\n            if (needsReset) {\n                this.projectionDelta = undefined;\n                this.needsReset = true;\n            }\n            if (transition)\n                this.setOptions({ transition });\n        }\n        relegate() {\n            const stack = this.getStack();\n            if (stack) {\n                return stack.relegate(this);\n            }\n            else {\n                return false;\n            }\n        }\n        resetRotation() {\n            const { visualElement } = this.options;\n            if (!visualElement)\n                return;\n            // If there's no detected rotation values, we can early return without a forced render.\n            let hasRotate = false;\n            /**\n             * An unrolled check for rotation values. Most elements don't have any rotation and\n             * skipping the nested loop and new object creation is 50% faster.\n             */\n            const { latestValues } = visualElement;\n            if (latestValues.rotate ||\n                latestValues.rotateX ||\n                latestValues.rotateY ||\n                latestValues.rotateZ) {\n                hasRotate = true;\n            }\n            // If there's no rotation values, we don't need to do any more.\n            if (!hasRotate)\n                return;\n            const resetValues = {};\n            // Check the rotate value of all axes and reset to 0\n            for (let i = 0; i < transformAxes.length; i++) {\n                const key = \"rotate\" + transformAxes[i];\n                // Record the rotation and then temporarily set it to 0\n                if (latestValues[key]) {\n                    resetValues[key] = latestValues[key];\n                    visualElement.setStaticValue(key, 0);\n                }\n            }\n            // Force a render of this element to apply the transform with all rotations\n            // set to 0.\n            visualElement === null || visualElement === void 0 ? void 0 : visualElement.render();\n            // Put back all the values we reset\n            for (const key in resetValues) {\n                visualElement.setStaticValue(key, resetValues[key]);\n            }\n            // Schedule a render for the next frame. This ensures we won't visually\n            // see the element with the reset rotate value applied.\n            visualElement.scheduleRender();\n        }\n        getProjectionStyles(styleProp = {}) {\n            var _a, _b, _c;\n            // TODO: Return lifecycle-persistent object\n            const styles = {};\n            if (!this.instance || this.isSVG)\n                return styles;\n            if (!this.isVisible) {\n                return { visibility: \"hidden\" };\n            }\n            else {\n                styles.visibility = \"\";\n            }\n            const transformTemplate = (_a = this.options.visualElement) === null || _a === void 0 ? void 0 : _a.getProps().transformTemplate;\n            if (this.needsReset) {\n                this.needsReset = false;\n                styles.opacity = \"\";\n                styles.pointerEvents =\n                    resolveMotionValue(styleProp.pointerEvents) || \"\";\n                styles.transform = transformTemplate\n                    ? transformTemplate(this.latestValues, \"\")\n                    : \"none\";\n                return styles;\n            }\n            const lead = this.getLead();\n            if (!this.projectionDelta || !this.layout || !lead.target) {\n                const emptyStyles = {};\n                if (this.options.layoutId) {\n                    emptyStyles.opacity =\n                        this.latestValues.opacity !== undefined\n                            ? this.latestValues.opacity\n                            : 1;\n                    emptyStyles.pointerEvents =\n                        resolveMotionValue(styleProp.pointerEvents) || \"\";\n                }\n                if (this.hasProjected && !hasTransform(this.latestValues)) {\n                    emptyStyles.transform = transformTemplate\n                        ? transformTemplate({}, \"\")\n                        : \"none\";\n                    this.hasProjected = false;\n                }\n                return emptyStyles;\n            }\n            const valuesToRender = lead.animationValues || lead.latestValues;\n            this.applyTransformsToTarget();\n            styles.transform = buildProjectionTransform(this.projectionDeltaWithTransform, this.treeScale, valuesToRender);\n            if (transformTemplate) {\n                styles.transform = transformTemplate(valuesToRender, styles.transform);\n            }\n            const { x, y } = this.projectionDelta;\n            styles.transformOrigin = `${x.origin * 100}% ${y.origin * 100}% 0`;\n            if (lead.animationValues) {\n                /**\n                 * If the lead component is animating, assign this either the entering/leaving\n                 * opacity\n                 */\n                styles.opacity =\n                    lead === this\n                        ? (_c = (_b = valuesToRender.opacity) !== null && _b !== void 0 ? _b : this.latestValues.opacity) !== null && _c !== void 0 ? _c : 1\n                        : this.preserveOpacity\n                            ? this.latestValues.opacity\n                            : valuesToRender.opacityExit;\n            }\n            else {\n                /**\n                 * Or we're not animating at all, set the lead component to its layout\n                 * opacity and other components to hidden.\n                 */\n                styles.opacity =\n                    lead === this\n                        ? valuesToRender.opacity !== undefined\n                            ? valuesToRender.opacity\n                            : \"\"\n                        : valuesToRender.opacityExit !== undefined\n                            ? valuesToRender.opacityExit\n                            : 0;\n            }\n            /**\n             * Apply scale correction\n             */\n            for (const key in scaleCorrectors) {\n                if (valuesToRender[key] === undefined)\n                    continue;\n                const { correct, applyTo } = scaleCorrectors[key];\n                const corrected = correct(valuesToRender[key], lead);\n                if (applyTo) {\n                    const num = applyTo.length;\n                    for (let i = 0; i < num; i++) {\n                        styles[applyTo[i]] = corrected;\n                    }\n                }\n                else {\n                    styles[key] = corrected;\n                }\n            }\n            /**\n             * Disable pointer events on follow components. This is to ensure\n             * that if a follow component covers a lead component it doesn't block\n             * pointer events on the lead.\n             */\n            if (this.options.layoutId) {\n                styles.pointerEvents =\n                    lead === this\n                        ? resolveMotionValue(styleProp.pointerEvents) || \"\"\n                        : \"none\";\n            }\n            return styles;\n        }\n        clearSnapshot() {\n            this.resumeFrom = this.snapshot = undefined;\n        }\n        // Only run on root\n        resetTree() {\n            this.root.nodes.forEach((node) => { var _a; return (_a = node.currentAnimation) === null || _a === void 0 ? void 0 : _a.stop(); });\n            this.root.nodes.forEach(clearMeasurements);\n            this.root.sharedNodes.clear();\n        }\n    };\n}\nfunction updateLayout(node) {\n    node.updateLayout();\n}\nfunction notifyLayoutUpdate(node) {\n    var _a, _b, _c;\n    const snapshot = ((_a = node.resumeFrom) === null || _a === void 0 ? void 0 : _a.snapshot) || node.snapshot;\n    if (node.isLead() &&\n        node.layout &&\n        snapshot &&\n        node.hasListeners(\"didUpdate\")) {\n        const { layoutBox: layout, measuredBox: measuredLayout } = node.layout;\n        const { animationType } = node.options;\n        const isShared = snapshot.source !== node.layout.source;\n        // TODO Maybe we want to also resize the layout snapshot so we don't trigger\n        // animations for instance if layout=\"size\" and an element has only changed position\n        if (animationType === \"size\") {\n            eachAxis((axis) => {\n                const axisSnapshot = isShared\n                    ? snapshot.measuredBox[axis]\n                    : snapshot.layoutBox[axis];\n                const length = calcLength(axisSnapshot);\n                axisSnapshot.min = layout[axis].min;\n                axisSnapshot.max = axisSnapshot.min + length;\n            });\n        }\n        else if (shouldAnimatePositionOnly(animationType, snapshot.layoutBox, layout)) {\n            eachAxis((axis) => {\n                const axisSnapshot = isShared\n                    ? snapshot.measuredBox[axis]\n                    : snapshot.layoutBox[axis];\n                const length = calcLength(layout[axis]);\n                axisSnapshot.max = axisSnapshot.min + length;\n            });\n        }\n        const layoutDelta = createDelta();\n        calcBoxDelta(layoutDelta, layout, snapshot.layoutBox);\n        const visualDelta = createDelta();\n        if (isShared) {\n            calcBoxDelta(visualDelta, node.applyTransform(measuredLayout, true), snapshot.measuredBox);\n        }\n        else {\n            calcBoxDelta(visualDelta, layout, snapshot.layoutBox);\n        }\n        const hasLayoutChanged = !isDeltaZero(layoutDelta);\n        let hasRelativeTargetChanged = false;\n        if (!node.resumeFrom) {\n            const relativeParent = node.getClosestProjectingParent();\n            /**\n             * If the relativeParent is itself resuming from a different element then\n             * the relative snapshot is not relavent\n             */\n            if (relativeParent && !relativeParent.resumeFrom) {\n                const { snapshot: parentSnapshot, layout: parentLayout } = relativeParent;\n                if (parentSnapshot && parentLayout) {\n                    const relativeSnapshot = createBox();\n                    calcRelativePosition(relativeSnapshot, snapshot.layoutBox, parentSnapshot.layoutBox);\n                    const relativeLayout = createBox();\n                    calcRelativePosition(relativeLayout, layout, parentLayout.layoutBox);\n                    if (!boxEquals(relativeSnapshot, relativeLayout)) {\n                        hasRelativeTargetChanged = true;\n                    }\n                }\n            }\n        }\n        node.notifyListeners(\"didUpdate\", {\n            layout,\n            snapshot,\n            delta: visualDelta,\n            layoutDelta,\n            hasLayoutChanged,\n            hasRelativeTargetChanged,\n        });\n    }\n    else if (node.isLead()) {\n        (_c = (_b = node.options).onExitComplete) === null || _c === void 0 ? void 0 : _c.call(_b);\n    }\n    /**\n     * Clearing transition\n     * TODO: Investigate why this transition is being passed in as {type: false } from Framer\n     * and why we need it at all\n     */\n    node.options.transition = undefined;\n}\nfunction propagateDirtyNodes(node) {\n    /**\n     * Propagate isProjectionDirty. Nodes are ordered by depth, so if the parent here\n     * is dirty we can simply pass this forward.\n     */\n    node.isProjectionDirty || (node.isProjectionDirty = Boolean(node.parent && node.parent.isProjectionDirty));\n    /**\n     * Propagate isTransformDirty.\n     */\n    node.isTransformDirty || (node.isTransformDirty = Boolean(node.parent && node.parent.isTransformDirty));\n}\nfunction clearSnapshot(node) {\n    node.clearSnapshot();\n}\nfunction clearMeasurements(node) {\n    node.clearMeasurements();\n}\nfunction resetTransformStyle(node) {\n    const { visualElement } = node.options;\n    if (visualElement === null || visualElement === void 0 ? void 0 : visualElement.getProps().onBeforeLayoutMeasure) {\n        visualElement.notify(\"BeforeLayoutMeasure\");\n    }\n    node.resetTransform();\n}\nfunction finishAnimation(node) {\n    node.finishAnimation();\n    node.targetDelta = node.relativeTarget = node.target = undefined;\n}\nfunction resolveTargetDelta(node) {\n    node.resolveTargetDelta();\n}\nfunction calcProjection(node) {\n    node.calcProjection();\n}\nfunction resetRotation(node) {\n    node.resetRotation();\n}\nfunction removeLeadSnapshots(stack) {\n    stack.removeLeadSnapshot();\n}\nfunction mixAxisDelta(output, delta, p) {\n    output.translate = mix(delta.translate, 0, p);\n    output.scale = mix(delta.scale, 1, p);\n    output.origin = delta.origin;\n    output.originPoint = delta.originPoint;\n}\nfunction mixAxis(output, from, to, p) {\n    output.min = mix(from.min, to.min, p);\n    output.max = mix(from.max, to.max, p);\n}\nfunction mixBox(output, from, to, p) {\n    mixAxis(output.x, from.x, to.x, p);\n    mixAxis(output.y, from.y, to.y, p);\n}\nfunction hasOpacityCrossfade(node) {\n    return (node.animationValues && node.animationValues.opacityExit !== undefined);\n}\nconst defaultLayoutTransition = {\n    duration: 0.45,\n    ease: [0.4, 0, 0.1, 1],\n};\nfunction mountNodeEarly(node, elementId) {\n    /**\n     * Rather than searching the DOM from document we can search the\n     * path for the deepest mounted ancestor and search from there\n     */\n    let searchNode = node.root;\n    for (let i = node.path.length - 1; i >= 0; i--) {\n        if (Boolean(node.path[i].instance)) {\n            searchNode = node.path[i];\n            break;\n        }\n    }\n    const searchElement = searchNode && searchNode !== node.root ? searchNode.instance : document;\n    const element = searchElement.querySelector(`[data-projection-id=\"${elementId}\"]`);\n    if (element)\n        node.mount(element, true);\n}\nfunction roundAxis(axis) {\n    axis.min = Math.round(axis.min);\n    axis.max = Math.round(axis.max);\n}\nfunction roundBox(box) {\n    roundAxis(box.x);\n    roundAxis(box.y);\n}\nfunction shouldAnimatePositionOnly(animationType, snapshot, layout) {\n    return (animationType === \"position\" ||\n        (animationType === \"preserve-aspect\" &&\n            !isNear(aspectRatio(snapshot), aspectRatio(layout), 0.2)));\n}\n\nexport { createProjectionNode, mixAxis, mixAxisDelta, mixBox, propagateDirtyNodes };\n"], "mappings": ";AAAA,SAASA,UAAU,EAAEC,SAAS,EAAEC,IAAI,QAAQ,2BAA2B;AACvE,SAASC,OAAO,QAAQ,6BAA6B;AACrD,SAASC,mBAAmB,QAAQ,sCAAsC;AAC1E,SAASC,SAAS,QAAQ,6BAA6B;AACvD,SAASC,WAAW,QAAQ,sBAAsB;AAClD,SAASC,aAAa,EAAEC,YAAY,EAAEC,aAAa,EAAEC,eAAe,QAAQ,6BAA6B;AACzG,SAASC,oBAAoB,EAAEC,eAAe,EAAEC,YAAY,EAAEC,UAAU,EAAEC,MAAM,QAAQ,4BAA4B;AACpH,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,SAASC,SAAS,EAAEC,WAAW,QAAQ,wBAAwB;AAC/D,SAASC,kBAAkB,QAAQ,uCAAuC;AAC1E,SAASC,SAAS,EAAEC,WAAW,EAAEC,WAAW,QAAQ,uBAAuB;AAC3E,SAASC,SAAS,QAAQ,qBAAqB;AAC/C,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SAASC,wBAAwB,QAAQ,yBAAyB;AAClE,SAASC,QAAQ,QAAQ,wBAAwB;AACjD,SAASC,YAAY,EAAEC,QAAQ,EAAEC,cAAc,QAAQ,4BAA4B;AACnF,SAASC,QAAQ,QAAQ,kCAAkC;AAC3D,SAASC,kBAAkB,QAAQ,4CAA4C;AAC/E,SAASC,qBAAqB,QAAQ,aAAa;AACnD,SAASC,KAAK,QAAQ,uBAAuB;AAC7C,SAASC,GAAG,QAAQ,qBAAqB;AAEzC,MAAMC,aAAa,GAAG,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AACzC;AACA;AACA;AACA;AACA,MAAMC,eAAe,GAAG,IAAI;AAC5B,IAAIC,EAAE,GAAG,CAAC;AACV,SAASC,oBAAoBA,CAAAC,IAAA,EAA6F;EAAA,IAA5F;IAAEC,oBAAoB;IAAEC,aAAa;IAAEC,aAAa;IAAEC,iBAAiB;IAAEC;EAAgB,CAAC,GAAAL,IAAA;EACpH,OAAO,MAAMM,cAAc,CAAC;IACxBC,WAAWA,CAACC,SAAS,EAA6G;MAAA,IAA3GC,YAAY,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;MAAA,IAAEG,MAAM,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGR,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAAC,CAAC;MAC5H;AACZ;AACA;MACY,IAAI,CAACJ,EAAE,GAAGA,EAAE,EAAE;MACd;AACZ;AACA;MACY,IAAI,CAACgB,WAAW,GAAG,CAAC;MACpB;AACZ;AACA;AACA;AACA;AACA;MACY,IAAI,CAACC,QAAQ,GAAG,IAAIC,GAAG,CAAC,CAAC;MACzB;AACZ;AACA;AACA;MACY,IAAI,CAACC,OAAO,GAAG,CAAC,CAAC;MACjB;AACZ;AACA;AACA;AACA;MACY,IAAI,CAACC,eAAe,GAAG,KAAK;MAC5B,IAAI,CAACC,kBAAkB,GAAG,KAAK;MAC/B;AACZ;AACA;AACA;AACA;AACA;MACY,IAAI,CAACC,aAAa,GAAG,KAAK;MAC1B,IAAI,CAACC,gBAAgB,GAAG,KAAK;MAC7B;AACZ;AACA;AACA;MACY,IAAI,CAACC,iBAAiB,GAAG,KAAK;MAC9B;AACZ;AACA;MACY,IAAI,CAACC,qBAAqB,GAAG,KAAK;MAClC,IAAI,CAACC,qBAAqB,GAAG,KAAK;MAClC;AACZ;AACA;AACA;MACY,IAAI,CAACC,UAAU,GAAG,KAAK;MACvB;AACZ;AACA;MACY,IAAI,CAACC,KAAK,GAAG,KAAK;MAClB;AACZ;AACA;AACA;MACY,IAAI,CAACC,UAAU,GAAG,KAAK;MACvB;AACZ;AACA;MACY,IAAI,CAACC,oBAAoB,GAAG,KAAK;MACjC;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;MACY,IAAI,CAACC,SAAS,GAAG;QAAEC,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAC;MAC/B;AACZ;AACA;MACY,IAAI,CAACC,aAAa,GAAG,IAAIC,GAAG,CAAC,CAAC;MAC9B;MACA,IAAI,CAACC,cAAc,GAAG,IAAID,GAAG,CAAC,CAAC;MAC/B,IAAI,CAACE,iBAAiB,GAAG,MAAM;QAC3B,IAAI,IAAI,CAACV,UAAU,EAAE;UACjB,IAAI,CAACA,UAAU,GAAG,KAAK;UACvB,IAAI,CAACW,iBAAiB,CAAC,CAAC;QAC5B;MACJ,CAAC;MACD;AACZ;AACA;AACA;AACA;MACY,IAAI,CAACC,gBAAgB,GAAG,MAAM;QAC1B,IAAI,CAACC,KAAK,CAACC,OAAO,CAACC,mBAAmB,CAAC;QACvC,IAAI,CAACF,KAAK,CAACC,OAAO,CAACE,kBAAkB,CAAC;QACtC,IAAI,CAACH,KAAK,CAACC,OAAO,CAACG,cAAc,CAAC;MACtC,CAAC;MACD,IAAI,CAACC,YAAY,GAAG,KAAK;MACzB,IAAI,CAACC,SAAS,GAAG,IAAI;MACrB,IAAI,CAACC,iBAAiB,GAAG,CAAC;MAC1B;AACZ;AACA;MACY;MACA,IAAI,CAACC,WAAW,GAAG,IAAIb,GAAG,CAAC,CAAC;MAC5B,IAAI,CAACzB,SAAS,GAAGA,SAAS;MAC1B,IAAI,CAACC,YAAY,GAAGA,YAAY;MAChC,IAAI,CAACsC,IAAI,GAAGlC,MAAM,GAAGA,MAAM,CAACkC,IAAI,IAAIlC,MAAM,GAAG,IAAI;MACjD,IAAI,CAACmC,IAAI,GAAGnC,MAAM,GAAG,CAAC,GAAGA,MAAM,CAACmC,IAAI,EAAEnC,MAAM,CAAC,GAAG,EAAE;MAClD,IAAI,CAACA,MAAM,GAAGA,MAAM;MACpB,IAAI,CAACoC,KAAK,GAAGpC,MAAM,GAAGA,MAAM,CAACoC,KAAK,GAAG,CAAC,GAAG,CAAC;MAC1CzC,SAAS,IAAI,IAAI,CAACuC,IAAI,CAACG,qBAAqB,CAAC1C,SAAS,EAAE,IAAI,CAAC;MAC7D,KAAK,IAAI2C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACH,IAAI,CAACrC,MAAM,EAAEwC,CAAC,EAAE,EAAE;QACvC,IAAI,CAACH,IAAI,CAACG,CAAC,CAAC,CAACvB,oBAAoB,GAAG,IAAI;MAC5C;MACA,IAAI,IAAI,CAACmB,IAAI,KAAK,IAAI,EAClB,IAAI,CAACT,KAAK,GAAG,IAAI/C,QAAQ,CAAC,CAAC;IACnC;IACA6D,gBAAgBA,CAACC,IAAI,EAAEC,OAAO,EAAE;MAC5B,IAAI,CAAC,IAAI,CAACtB,aAAa,CAACuB,GAAG,CAACF,IAAI,CAAC,EAAE;QAC/B,IAAI,CAACrB,aAAa,CAACwB,GAAG,CAACH,IAAI,EAAE,IAAIxF,mBAAmB,CAAC,CAAC,CAAC;MAC3D;MACA,OAAO,IAAI,CAACmE,aAAa,CAACyB,GAAG,CAACJ,IAAI,CAAC,CAACK,GAAG,CAACJ,OAAO,CAAC;IACpD;IACAK,eAAeA,CAACN,IAAI,EAAW;MAC3B,MAAMO,mBAAmB,GAAG,IAAI,CAAC5B,aAAa,CAACyB,GAAG,CAACJ,IAAI,CAAC;MAAC,SAAAQ,IAAA,GAAAnD,SAAA,CAAAC,MAAA,EADpCmD,IAAI,OAAAC,KAAA,CAAAF,IAAA,OAAAA,IAAA,WAAAG,IAAA,MAAAA,IAAA,GAAAH,IAAA,EAAAG,IAAA;QAAJF,IAAI,CAAAE,IAAA,QAAAtD,SAAA,CAAAsD,IAAA;MAAA;MAEzBJ,mBAAmB,KAAK,IAAI,IAAIA,mBAAmB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,mBAAmB,CAACK,MAAM,CAAC,GAAGH,IAAI,CAAC;IACjH;IACAI,YAAYA,CAACb,IAAI,EAAE;MACf,OAAO,IAAI,CAACrB,aAAa,CAACuB,GAAG,CAACF,IAAI,CAAC;IACvC;IACAH,qBAAqBA,CAAC1C,SAAS,EAAE2D,IAAI,EAAE;MACnC,IAAI,CAACjC,cAAc,CAACsB,GAAG,CAAChD,SAAS,EAAE2D,IAAI,CAAC;IAC5C;IACA;AACR;AACA;IACQC,KAAKA,CAACC,QAAQ,EAAyB;MAAA,IAAvBjD,aAAa,GAAAV,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;MACjC,IAAI4D,EAAE;MACN,IAAI,IAAI,CAACD,QAAQ,EACb;MACJ,IAAI,CAAC3C,KAAK,GACN2C,QAAQ,YAAYE,UAAU,IAAIF,QAAQ,CAACG,OAAO,KAAK,KAAK;MAChE,IAAI,CAACH,QAAQ,GAAGA,QAAQ;MACxB,MAAM;QAAEI,QAAQ;QAAEC,MAAM;QAAEC;MAAc,CAAC,GAAG,IAAI,CAAC1D,OAAO;MACxD,IAAI0D,aAAa,IAAI,CAACA,aAAa,CAACC,OAAO,EAAE;QACzCD,aAAa,CAACP,KAAK,CAACC,QAAQ,CAAC;MACjC;MACA,IAAI,CAACtB,IAAI,CAACT,KAAK,CAACoB,GAAG,CAAC,IAAI,CAAC;MACzB,CAACY,EAAE,GAAG,IAAI,CAACzD,MAAM,MAAM,IAAI,IAAIyD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACvD,QAAQ,CAAC2C,GAAG,CAAC,IAAI,CAAC;MAC7E,IAAI,CAAClD,SAAS,IAAI,IAAI,CAACuC,IAAI,CAACb,cAAc,CAAC2C,MAAM,CAAC,IAAI,CAACrE,SAAS,CAAC;MACjE,IAAIY,aAAa,KAAKsD,MAAM,IAAID,QAAQ,CAAC,EAAE;QACvC,IAAI,CAACrD,aAAa,GAAG,IAAI;MAC7B;MACA,IAAInB,oBAAoB,EAAE;QACtB,IAAI6E,WAAW;QACf,MAAMC,mBAAmB,GAAGA,CAAA,KAAO,IAAI,CAAChC,IAAI,CAACvB,qBAAqB,GAAG,KAAM;QAC3EvB,oBAAoB,CAACoE,QAAQ,EAAE,MAAM;UACjC,IAAI,CAACtB,IAAI,CAACvB,qBAAqB,GAAG,IAAI;UACtCsD,WAAW,IAAIA,WAAW,CAAC,CAAC;UAC5BA,WAAW,GAAGpF,KAAK,CAACqF,mBAAmB,EAAE,GAAG,CAAC;UAC7C,IAAItF,qBAAqB,CAACuF,sBAAsB,EAAE;YAC9CvF,qBAAqB,CAACuF,sBAAsB,GAAG,KAAK;YACpD,IAAI,CAAC1C,KAAK,CAACC,OAAO,CAAC0C,eAAe,CAAC;UACvC;QACJ,CAAC,CAAC;MACN;MACA,IAAIR,QAAQ,EAAE;QACV,IAAI,CAAC1B,IAAI,CAACmC,kBAAkB,CAACT,QAAQ,EAAE,IAAI,CAAC;MAChD;MACA;MACA,IAAI,IAAI,CAACxD,OAAO,CAACrD,OAAO,KAAK,KAAK,IAC9B+G,aAAa,KACZF,QAAQ,IAAIC,MAAM,CAAC,EAAE;QACtB,IAAI,CAACtB,gBAAgB,CAAC,WAAW,EAAE+B,KAAA,IAA+E;UAAA,IAA9E;YAAEC,KAAK;YAAEC,gBAAgB;YAAEC,wBAAwB;YAAEZ,MAAM,EAAEa;UAAW,CAAC,GAAAJ,KAAA;UACzG,IAAIb,EAAE,EAAEkB,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE;UACtB,IAAI,IAAI,CAACC,sBAAsB,CAAC,CAAC,EAAE;YAC/B,IAAI,CAACC,MAAM,GAAGjF,SAAS;YACvB,IAAI,CAACkF,cAAc,GAAGlF,SAAS;YAC/B;UACJ;UACA;UACA,MAAMmF,gBAAgB,GAAG,CAACP,EAAE,GAAG,CAAClB,EAAE,GAAG,IAAI,CAACrD,OAAO,CAAC+E,UAAU,MAAM,IAAI,IAAI1B,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGK,aAAa,CAACsB,oBAAoB,CAAC,CAAC,MAAM,IAAI,IAAIT,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGU,uBAAuB;UAC7L,MAAM;YAAEC,sBAAsB;YAAEC;UAA2B,CAAC,GAAGzB,aAAa,CAAC0B,QAAQ,CAAC,CAAC;UACvF;AACpB;AACA;AACA;UACoB,MAAMC,aAAa,GAAG,CAAC,IAAI,CAACC,YAAY,IACpC,CAAC1H,SAAS,CAAC,IAAI,CAAC0H,YAAY,EAAEhB,SAAS,CAAC,IACxCD,wBAAwB;UAC5B;AACpB;AACA;AACA;AACA;UACoB,MAAMkB,4BAA4B,GAAG,CAACnB,gBAAgB,IAAIC,wBAAwB;UAClF,IAAI,CAAC,CAACG,EAAE,GAAG,IAAI,CAACgB,UAAU,MAAM,IAAI,IAAIhB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACpB,QAAQ,KACxEmC,4BAA4B,IAC3BnB,gBAAgB,KACZiB,aAAa,IAAI,CAAC,IAAI,CAACI,gBAAgB,CAAE,EAAE;YAChD,IAAI,IAAI,CAACD,UAAU,EAAE;cACjB,IAAI,CAACE,YAAY,GAAG,IAAI,CAACF,UAAU;cACnC,IAAI,CAACE,YAAY,CAACA,YAAY,GAAG/F,SAAS;YAC9C;YACA,IAAI,CAACgG,kBAAkB,CAACxB,KAAK,EAAEoB,4BAA4B,CAAC;YAC5D,MAAMK,gBAAgB,GAAAC,aAAA,CAAAA,aAAA,KACflI,kBAAkB,CAACmH,gBAAgB,EAAE,QAAQ,CAAC;cACjDgB,MAAM,EAAEZ,sBAAsB;cAC9Ba,UAAU,EAAEZ;YAAyB,EACxC;YACD,IAAIzB,aAAa,CAACsC,kBAAkB,EAAE;cAClCJ,gBAAgB,CAACnH,KAAK,GAAG,CAAC;cAC1BmH,gBAAgB,CAACK,IAAI,GAAG,KAAK;YACjC;YACA,IAAI,CAACC,cAAc,CAACN,gBAAgB,CAAC;UACzC,CAAC,MACI;YACD;AACxB;AACA;AACA;AACA;YACwB,IAAI,CAACxB,gBAAgB,IACjB,IAAI,CAACxC,iBAAiB,KAAK,CAAC,EAAE;cAC9BoC,eAAe,CAAC,IAAI,CAAC;YACzB;YACA,IAAI,CAACmC,MAAM,CAAC,CAAC,KAAK,CAACzB,EAAE,GAAG,CAACD,EAAE,GAAG,IAAI,CAACzE,OAAO,EAAEoG,cAAc,MAAM,IAAI,IAAI1B,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC2B,IAAI,CAAC5B,EAAE,CAAC,CAAC;UACjH;UACA,IAAI,CAACa,YAAY,GAAGhB,SAAS;QACjC,CAAC,CAAC;MACN;IACJ;IACAgC,OAAOA,CAAA,EAAG;MACN,IAAIjD,EAAE,EAAEkB,EAAE;MACV,IAAI,CAACvE,OAAO,CAACwD,QAAQ,IAAI,IAAI,CAAC+C,UAAU,CAAC,CAAC;MAC1C,IAAI,CAACzE,IAAI,CAACT,KAAK,CAACmF,MAAM,CAAC,IAAI,CAAC;MAC5B,CAACnD,EAAE,GAAG,IAAI,CAACoD,QAAQ,CAAC,CAAC,MAAM,IAAI,IAAIpD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACmD,MAAM,CAAC,IAAI,CAAC;MAC3E,CAACjC,EAAE,GAAG,IAAI,CAAC3E,MAAM,MAAM,IAAI,IAAI2E,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACzE,QAAQ,CAAC8D,MAAM,CAAC,IAAI,CAAC;MAChF,IAAI,CAACR,QAAQ,GAAGzD,SAAS;MACzBnD,UAAU,CAACkK,SAAS,CAAC,IAAI,CAACtF,gBAAgB,CAAC;IAC/C;IACA;IACAuF,WAAWA,CAAA,EAAG;MACV,IAAI,CAACrG,qBAAqB,GAAG,IAAI;IACrC;IACAsG,aAAaA,CAAA,EAAG;MACZ,IAAI,CAACtG,qBAAqB,GAAG,KAAK;IACtC;IACAuG,eAAeA,CAAA,EAAG;MACd,OAAO,IAAI,CAACvG,qBAAqB,IAAI,IAAI,CAACC,qBAAqB;IACnE;IACAoE,sBAAsBA,CAAA,EAAG;MACrB,IAAItB,EAAE;MACN,OAAQ,IAAI,CAACnD,kBAAkB,KAC1B,CAACmD,EAAE,GAAG,IAAI,CAACzD,MAAM,MAAM,IAAI,IAAIyD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACsB,sBAAsB,CAAC,CAAC,CAAC,IACrF,KAAK;IACb;IACA;IACAmC,WAAWA,CAAA,EAAG;MACV,IAAIzD,EAAE;MACN,IAAI,IAAI,CAACwD,eAAe,CAAC,CAAC,EACtB;MACJ,IAAI,CAACrG,UAAU,GAAG,IAAI;MACtB,CAAC6C,EAAE,GAAG,IAAI,CAAChC,KAAK,MAAM,IAAI,IAAIgC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC/B,OAAO,CAACyF,aAAa,CAAC;MAChF,IAAI,CAAClH,WAAW,EAAE;IACtB;IACA0G,UAAUA,CAAA,EAA+B;MAAA,IAA9BS,qBAAqB,GAAAvH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;MACnC,IAAI4D,EAAE,EAAEkB,EAAE,EAAEC,EAAE;MACd,IAAI,IAAI,CAAC1C,IAAI,CAAC+E,eAAe,CAAC,CAAC,EAAE;QAC7B,CAACtC,EAAE,GAAG,CAAClB,EAAE,GAAG,IAAI,CAACrD,OAAO,EAAEoG,cAAc,MAAM,IAAI,IAAI7B,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC8B,IAAI,CAAChD,EAAE,CAAC;QAC1F;MACJ;MACA,CAAC,IAAI,CAACvB,IAAI,CAACtB,UAAU,IAAI,IAAI,CAACsB,IAAI,CAACgF,WAAW,CAAC,CAAC;MAChD,IAAI,IAAI,CAAC3G,aAAa,EAClB;MACJ,IAAI,CAACA,aAAa,GAAG,IAAI;MACzB,KAAK,IAAI+B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACH,IAAI,CAACrC,MAAM,EAAEwC,CAAC,EAAE,EAAE;QACvC,MAAMgB,IAAI,GAAG,IAAI,CAACnB,IAAI,CAACG,CAAC,CAAC;QACzBgB,IAAI,CAACvC,oBAAoB,GAAG,IAAI;QAChCuC,IAAI,CAAC+D,YAAY,CAAC,UAAU,CAAC;MACjC;MACA,MAAM;QAAEzD,QAAQ;QAAEC;MAAO,CAAC,GAAG,IAAI,CAACzD,OAAO;MACzC,IAAIwD,QAAQ,KAAK7D,SAAS,IAAI,CAAC8D,MAAM,EACjC;MACJ,MAAMyD,iBAAiB,GAAG,CAAC1C,EAAE,GAAG,IAAI,CAACxE,OAAO,CAAC0D,aAAa,MAAM,IAAI,IAAIc,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACY,QAAQ,CAAC,CAAC,CAAC8B,iBAAiB;MAChI,IAAI,CAACC,0BAA0B,GAAGD,iBAAiB,KAAK,IAAI,IAAIA,iBAAiB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,iBAAiB,CAAC,IAAI,CAAC1H,YAAY,EAAE,EAAE,CAAC;MAChJ,IAAI,CAAC4H,cAAc,CAAC,CAAC;MACrBJ,qBAAqB,IAAI,IAAI,CAACtE,eAAe,CAAC,YAAY,CAAC;IAC/D;IACA;IACA2E,SAASA,CAAA,EAAG;MACR,MAAMC,gBAAgB,GAAG,IAAI,CAACT,eAAe,CAAC,CAAC;MAC/C;MACA;MACA;MACA,IAAIS,gBAAgB,EAAE;QAClB,IAAI,CAACV,aAAa,CAAC,CAAC;QACpB,IAAI,CAACzF,iBAAiB,CAAC,CAAC;QACxB,IAAI,CAACE,KAAK,CAACC,OAAO,CAACiG,iBAAiB,CAAC;QACrC;MACJ;MACA,IAAI,CAAC,IAAI,CAAC/G,UAAU,EAChB;MACJ,IAAI,CAACA,UAAU,GAAG,KAAK;MACvB;AACZ;AACA;AACA;AACA;AACA;MACY,IAAI,IAAI,CAACS,cAAc,CAACuG,IAAI,EAAE;QAC1B,IAAI,CAACvG,cAAc,CAACK,OAAO,CAACmG,cAAc,CAAC;QAC3C,IAAI,CAACxG,cAAc,CAACyG,KAAK,CAAC,CAAC;MAC/B;MACA;AACZ;AACA;MACY,IAAI,CAACrG,KAAK,CAACC,OAAO,CAACqG,mBAAmB,CAAC;MACvC;AACZ;AACA;MACY;MACA,IAAI,CAACtG,KAAK,CAACC,OAAO,CAACsG,YAAY,CAAC;MAChC;AACZ;AACA;MACY;MACA,IAAI,CAACvG,KAAK,CAACC,OAAO,CAACuG,kBAAkB,CAAC;MACtC,IAAI,CAAC1G,iBAAiB,CAAC,CAAC;MACxB;MACA1E,SAAS,CAACqL,MAAM,CAAC,CAAC;MAClBrL,SAAS,CAACiK,SAAS,CAAC,CAAC;MACrBjK,SAAS,CAACsL,MAAM,CAAC,CAAC;IACtB;IACA5G,iBAAiBA,CAAA,EAAG;MAChB,IAAI,CAACE,KAAK,CAACC,OAAO,CAAC0G,aAAa,CAAC;MACjC,IAAI,CAACnG,WAAW,CAACP,OAAO,CAAC2G,mBAAmB,CAAC;IACjD;IACAC,wBAAwBA,CAAA,EAAG;MACvBxL,IAAI,CAACgK,SAAS,CAAC,IAAI,CAACtF,gBAAgB,EAAE,KAAK,EAAE,IAAI,CAAC;IACtD;IACA+G,yBAAyBA,CAAA,EAAG;MACxB;AACZ;AACA;AACA;AACA;MACYzL,IAAI,CAAC0L,UAAU,CAAC,MAAM;QAClB,IAAI,IAAI,CAACjI,aAAa,EAAE;UACpB,IAAI,CAAC2B,IAAI,CAACuF,SAAS,CAAC,CAAC;QACzB,CAAC,MACI;UACD,IAAI,CAACvF,IAAI,CAACZ,iBAAiB,CAAC,CAAC;QACjC;MACJ,CAAC,CAAC;IACN;IACA;AACR;AACA;IACQkG,cAAcA,CAAA,EAAG;MACb,IAAI,IAAI,CAACiB,QAAQ,IAAI,CAAC,IAAI,CAACjF,QAAQ,EAC/B;MACJ,IAAI,CAACiF,QAAQ,GAAG,IAAI,CAACC,OAAO,CAAC,CAAC;IAClC;IACAV,YAAYA,CAAA,EAAG;MACX,IAAIvE,EAAE;MACN,IAAI,CAAC,IAAI,CAACD,QAAQ,EACd;MACJ;MACA,IAAI,CAAC6D,YAAY,CAAC,CAAC;MACnB,IAAI,EAAE,IAAI,CAACjH,OAAO,CAACuI,mBAAmB,IAAI,IAAI,CAACpC,MAAM,CAAC,CAAC,CAAC,IACpD,CAAC,IAAI,CAAChG,aAAa,EAAE;QACrB;MACJ;MACA;AACZ;AACA;AACA;AACA;AACA;AACA;MACY,IAAI,IAAI,CAACqF,UAAU,IAAI,CAAC,IAAI,CAACA,UAAU,CAACpC,QAAQ,EAAE;QAC9C,KAAK,IAAIlB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACH,IAAI,CAACrC,MAAM,EAAEwC,CAAC,EAAE,EAAE;UACvC,MAAMgB,IAAI,GAAG,IAAI,CAACnB,IAAI,CAACG,CAAC,CAAC;UACzBgB,IAAI,CAAC+D,YAAY,CAAC,CAAC;QACvB;MACJ;MACA,MAAMuB,UAAU,GAAG,IAAI,CAAC/E,MAAM;MAC9B,IAAI,CAACA,MAAM,GAAG,IAAI,CAAC6E,OAAO,CAAC,KAAK,CAAC;MACjC,IAAI,CAACG,eAAe,GAAGhL,SAAS,CAAC,CAAC;MAClC,IAAI,CAAC0C,aAAa,GAAG,KAAK;MAC1B,IAAI,CAACuI,eAAe,GAAG/I,SAAS;MAChC,IAAI,CAAC+C,eAAe,CAAC,SAAS,EAAE,IAAI,CAACe,MAAM,CAACkF,SAAS,CAAC;MACtD,CAACtF,EAAE,GAAG,IAAI,CAACrD,OAAO,CAAC0D,aAAa,MAAM,IAAI,IAAIL,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACL,MAAM,CAAC,eAAe,EAAE,IAAI,CAACS,MAAM,CAACkF,SAAS,EAAEH,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAACG,SAAS,CAAC;IAC1M;IACA1B,YAAYA,CAAA,EAAoB;MAAA,IAAnB2B,KAAK,GAAAnJ,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,SAAS;MAC1B,IAAIoJ,gBAAgB,GAAGC,OAAO,CAAC,IAAI,CAAC9I,OAAO,CAAC+I,YAAY,IAAI,IAAI,CAAC3F,QAAQ,CAAC;MAC1E,IAAI,IAAI,CAAC4F,MAAM,IACX,IAAI,CAACA,MAAM,CAACnJ,WAAW,KAAK,IAAI,CAACiC,IAAI,CAACjC,WAAW,IACjD,IAAI,CAACmJ,MAAM,CAACJ,KAAK,KAAKA,KAAK,EAAE;QAC7BC,gBAAgB,GAAG,KAAK;MAC5B;MACA,IAAIA,gBAAgB,EAAE;QAClB,IAAI,CAACG,MAAM,GAAG;UACVnJ,WAAW,EAAE,IAAI,CAACiC,IAAI,CAACjC,WAAW;UAClC+I,KAAK;UACLK,MAAM,EAAE9J,iBAAiB,CAAC,IAAI,CAACiE,QAAQ,CAAC;UACxC8F,MAAM,EAAEhK,aAAa,CAAC,IAAI,CAACkE,QAAQ;QACvC,CAAC;MACL;IACJ;IACAhE,cAAcA,CAAA,EAAG;MACb,IAAIiE,EAAE;MACN,IAAI,CAACjE,cAAc,EACf;MACJ,MAAM+J,gBAAgB,GAAG,IAAI,CAAChJ,aAAa,IAAI,IAAI,CAACQ,oBAAoB;MACxE,MAAMyI,aAAa,GAAG,IAAI,CAACV,eAAe,IAAI,CAAC7K,WAAW,CAAC,IAAI,CAAC6K,eAAe,CAAC;MAChF,MAAMxB,iBAAiB,GAAG,CAAC7D,EAAE,GAAG,IAAI,CAACrD,OAAO,CAAC0D,aAAa,MAAM,IAAI,IAAIL,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC+B,QAAQ,CAAC,CAAC,CAAC8B,iBAAiB;MAChI,MAAMmC,sBAAsB,GAAGnC,iBAAiB,KAAK,IAAI,IAAIA,iBAAiB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,iBAAiB,CAAC,IAAI,CAAC1H,YAAY,EAAE,EAAE,CAAC;MAC7I,MAAM8J,2BAA2B,GAAGD,sBAAsB,KAAK,IAAI,CAAClC,0BAA0B;MAC9F,IAAIgC,gBAAgB,KACfC,aAAa,IACVjL,YAAY,CAAC,IAAI,CAACqB,YAAY,CAAC,IAC/B8J,2BAA2B,CAAC,EAAE;QAClClK,cAAc,CAAC,IAAI,CAACgE,QAAQ,EAAEiG,sBAAsB,CAAC;QACrD,IAAI,CAAC1I,oBAAoB,GAAG,KAAK;QACjC,IAAI,CAAC4I,cAAc,CAAC,CAAC;MACzB;IACJ;IACAjB,OAAOA,CAAA,EAAyB;MAAA,IAAxBkB,eAAe,GAAA/J,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;MAC1B,MAAMgK,OAAO,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;MACrC,IAAIf,SAAS,GAAG,IAAI,CAACgB,mBAAmB,CAACF,OAAO,CAAC;MACjD;AACZ;AACA;AACA;AACA;MACY,IAAID,eAAe,EAAE;QACjBb,SAAS,GAAG,IAAI,CAACa,eAAe,CAACb,SAAS,CAAC;MAC/C;MACAiB,QAAQ,CAACjB,SAAS,CAAC;MACnB,OAAO;QACH9I,WAAW,EAAE,IAAI,CAACiC,IAAI,CAACjC,WAAW;QAClCgK,WAAW,EAAEJ,OAAO;QACpBd,SAAS;QACTnJ,YAAY,EAAE,CAAC,CAAC;QAChBsK,MAAM,EAAE,IAAI,CAACjL;MACjB,CAAC;IACL;IACA6K,cAAcA,CAAA,EAAG;MACb,MAAM;QAAEhG;MAAc,CAAC,GAAG,IAAI,CAAC1D,OAAO;MACtC,IAAI,CAAC0D,aAAa,EACd,OAAOjG,SAAS,CAAC,CAAC;MACtB,MAAMsM,GAAG,GAAGrG,aAAa,CAACsG,kBAAkB,CAAC,CAAC;MAC9C;MACA,MAAM;QAAEhB;MAAO,CAAC,GAAG,IAAI,CAAClH,IAAI;MAC5B,IAAIkH,MAAM,EAAE;QACRjM,aAAa,CAACgN,GAAG,CAAClJ,CAAC,EAAEmI,MAAM,CAACE,MAAM,CAACrI,CAAC,CAAC;QACrC9D,aAAa,CAACgN,GAAG,CAACjJ,CAAC,EAAEkI,MAAM,CAACE,MAAM,CAACpI,CAAC,CAAC;MACzC;MACA,OAAOiJ,GAAG;IACd;IACAJ,mBAAmBA,CAACI,GAAG,EAAE;MACrB,MAAME,gBAAgB,GAAGxM,SAAS,CAAC,CAAC;MACpCX,WAAW,CAACmN,gBAAgB,EAAEF,GAAG,CAAC;MAClC;AACZ;AACA;AACA;MACY,KAAK,IAAI7H,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACH,IAAI,CAACrC,MAAM,EAAEwC,CAAC,EAAE,EAAE;QACvC,MAAMgB,IAAI,GAAG,IAAI,CAACnB,IAAI,CAACG,CAAC,CAAC;QACzB,MAAM;UAAE8G,MAAM;UAAEhJ;QAAQ,CAAC,GAAGkD,IAAI;QAChC,IAAIA,IAAI,KAAK,IAAI,CAACpB,IAAI,IAAIkH,MAAM,IAAIhJ,OAAO,CAAC+I,YAAY,EAAE;UACtD;AACpB;AACA;AACA;UACoB,IAAIC,MAAM,CAACC,MAAM,EAAE;YACfnM,WAAW,CAACmN,gBAAgB,EAAEF,GAAG,CAAC;YAClC,MAAM;cAAEf,MAAM,EAAEkB;YAAW,CAAC,GAAG,IAAI,CAACpI,IAAI;YACxC;AACxB;AACA;AACA;YACwB,IAAIoI,UAAU,EAAE;cACZnN,aAAa,CAACkN,gBAAgB,CAACpJ,CAAC,EAAE,CAACqJ,UAAU,CAAChB,MAAM,CAACrI,CAAC,CAAC;cACvD9D,aAAa,CAACkN,gBAAgB,CAACnJ,CAAC,EAAE,CAACoJ,UAAU,CAAChB,MAAM,CAACpI,CAAC,CAAC;YAC3D;UACJ;UACA/D,aAAa,CAACkN,gBAAgB,CAACpJ,CAAC,EAAEmI,MAAM,CAACE,MAAM,CAACrI,CAAC,CAAC;UAClD9D,aAAa,CAACkN,gBAAgB,CAACnJ,CAAC,EAAEkI,MAAM,CAACE,MAAM,CAACpI,CAAC,CAAC;QACtD;MACJ;MACA,OAAOmJ,gBAAgB;IAC3B;IACAE,cAAcA,CAACJ,GAAG,EAAyB;MAAA,IAAvBK,aAAa,GAAA3K,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;MACrC,MAAM4K,cAAc,GAAG5M,SAAS,CAAC,CAAC;MAClCX,WAAW,CAACuN,cAAc,EAAEN,GAAG,CAAC;MAChC,KAAK,IAAI7H,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACH,IAAI,CAACrC,MAAM,EAAEwC,CAAC,EAAE,EAAE;QACvC,MAAMgB,IAAI,GAAG,IAAI,CAACnB,IAAI,CAACG,CAAC,CAAC;QACzB,IAAI,CAACkI,aAAa,IACdlH,IAAI,CAAClD,OAAO,CAAC+I,YAAY,IACzB7F,IAAI,CAAC8F,MAAM,IACX9F,IAAI,KAAKA,IAAI,CAACpB,IAAI,EAAE;UACpB9E,YAAY,CAACqN,cAAc,EAAE;YACzBxJ,CAAC,EAAE,CAACqC,IAAI,CAAC8F,MAAM,CAACE,MAAM,CAACrI,CAAC;YACxBC,CAAC,EAAE,CAACoC,IAAI,CAAC8F,MAAM,CAACE,MAAM,CAACpI;UAC3B,CAAC,CAAC;QACN;QACA,IAAI,CAAC3C,YAAY,CAAC+E,IAAI,CAAC1D,YAAY,CAAC,EAChC;QACJxC,YAAY,CAACqN,cAAc,EAAEnH,IAAI,CAAC1D,YAAY,CAAC;MACnD;MACA,IAAIrB,YAAY,CAAC,IAAI,CAACqB,YAAY,CAAC,EAAE;QACjCxC,YAAY,CAACqN,cAAc,EAAE,IAAI,CAAC7K,YAAY,CAAC;MACnD;MACA,OAAO6K,cAAc;IACzB;IACAb,eAAeA,CAACO,GAAG,EAAE;MACjB,IAAI1G,EAAE;MACN,MAAMiH,mBAAmB,GAAG7M,SAAS,CAAC,CAAC;MACvCX,WAAW,CAACwN,mBAAmB,EAAEP,GAAG,CAAC;MACrC,KAAK,IAAI7H,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACH,IAAI,CAACrC,MAAM,EAAEwC,CAAC,EAAE,EAAE;QACvC,MAAMgB,IAAI,GAAG,IAAI,CAACnB,IAAI,CAACG,CAAC,CAAC;QACzB,IAAI,CAACgB,IAAI,CAACE,QAAQ,EACd;QACJ,IAAI,CAACjF,YAAY,CAAC+E,IAAI,CAAC1D,YAAY,CAAC,EAChC;QACJpB,QAAQ,CAAC8E,IAAI,CAAC1D,YAAY,CAAC,IAAI0D,IAAI,CAACkE,cAAc,CAAC,CAAC;QACpD,MAAMmD,SAAS,GAAG9M,SAAS,CAAC,CAAC;QAC7B,MAAM+M,OAAO,GAAGtH,IAAI,CAACwG,cAAc,CAAC,CAAC;QACrC5M,WAAW,CAACyN,SAAS,EAAEC,OAAO,CAAC;QAC/BhN,mBAAmB,CAAC8M,mBAAmB,EAAEpH,IAAI,CAAC1D,YAAY,EAAE,CAAC6D,EAAE,GAAGH,IAAI,CAACmF,QAAQ,MAAM,IAAI,IAAIhF,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACsF,SAAS,EAAE4B,SAAS,CAAC;MAClJ;MACA,IAAIpM,YAAY,CAAC,IAAI,CAACqB,YAAY,CAAC,EAAE;QACjChC,mBAAmB,CAAC8M,mBAAmB,EAAE,IAAI,CAAC9K,YAAY,CAAC;MAC/D;MACA,OAAO8K,mBAAmB;IAC9B;IACA;AACR;AACA;IACQG,cAAcA,CAACtG,KAAK,EAAE;MAClB,IAAI,CAACuG,WAAW,GAAGvG,KAAK;MACxB,IAAI,CAAC9D,iBAAiB,GAAG,IAAI;MAC7B,IAAI,CAACyB,IAAI,CAACoG,wBAAwB,CAAC,CAAC;IACxC;IACAyC,UAAUA,CAAC3K,OAAO,EAAE;MAChB,IAAI,CAACA,OAAO,GAAA6F,aAAA,CAAAA,aAAA,CAAAA,aAAA,KACL,IAAI,CAAC7F,OAAO,GACZA,OAAO;QACV4K,SAAS,EAAE5K,OAAO,CAAC4K,SAAS,KAAKjL,SAAS,GAAGK,OAAO,CAAC4K,SAAS,GAAG;MAAI,EACxE;IACL;IACArD,iBAAiBA,CAAA,EAAG;MAChB,IAAI,CAACyB,MAAM,GAAGrJ,SAAS;MACvB,IAAI,CAAC8D,MAAM,GAAG9D,SAAS;MACvB,IAAI,CAAC0I,QAAQ,GAAG1I,SAAS;MACzB,IAAI,CAACwH,0BAA0B,GAAGxH,SAAS;MAC3C,IAAI,CAAC+K,WAAW,GAAG/K,SAAS;MAC5B,IAAI,CAACiF,MAAM,GAAGjF,SAAS;MACvB,IAAI,CAACQ,aAAa,GAAG,KAAK;IAC9B;IACA;AACR;AACA;IACQqB,kBAAkBA,CAAA,EAAG;MACjB,IAAI6B,EAAE;MACN;AACZ;AACA;AACA;AACA;MACY,MAAMwH,IAAI,GAAG,IAAI,CAACC,OAAO,CAAC,CAAC;MAC3B,IAAI,CAACzK,iBAAiB,KAAK,IAAI,CAACA,iBAAiB,GAAGwK,IAAI,CAACxK,iBAAiB,CAAC;MAC3E,IAAI,CAACD,gBAAgB,KAAK,IAAI,CAACA,gBAAgB,GAAGyK,IAAI,CAACzK,gBAAgB,CAAC;MACxE;AACZ;AACA;AACA;MACY,IAAI,CAAC,IAAI,CAACC,iBAAiB,IAAI,CAAC,IAAI,CAAC0K,8BAA8B,EAC/D;MACJ,MAAM;QAAEtH,MAAM;QAAED;MAAS,CAAC,GAAG,IAAI,CAACxD,OAAO;MACzC;AACZ;AACA;MACY,IAAI,CAAC,IAAI,CAACyD,MAAM,IAAI,EAAEA,MAAM,IAAID,QAAQ,CAAC,EACrC;MACJ;AACZ;AACA;AACA;AACA;MACY;MACA,IAAI,CAAC,IAAI,CAACkH,WAAW,IAAI,CAAC,IAAI,CAAC7F,cAAc,EAAE;QAC3C;QACA,MAAMmG,cAAc,GAAG,IAAI,CAACC,0BAA0B,CAAC,CAAC;QACxD,IAAID,cAAc,IAAIA,cAAc,CAACvH,MAAM,EAAE;UACzC,IAAI,CAACuH,cAAc,GAAGA,cAAc;UACpC,IAAI,CAACnG,cAAc,GAAGpH,SAAS,CAAC,CAAC;UACjC,IAAI,CAACyN,oBAAoB,GAAGzN,SAAS,CAAC,CAAC;UACvCN,oBAAoB,CAAC,IAAI,CAAC+N,oBAAoB,EAAE,IAAI,CAACzH,MAAM,CAACkF,SAAS,EAAEqC,cAAc,CAACvH,MAAM,CAACkF,SAAS,CAAC;UACvG7L,WAAW,CAAC,IAAI,CAAC+H,cAAc,EAAE,IAAI,CAACqG,oBAAoB,CAAC;QAC/D,CAAC,MACI;UACD,IAAI,CAACF,cAAc,GAAG,IAAI,CAACnG,cAAc,GAAGlF,SAAS;QACzD;MACJ;MACA;AACZ;AACA;AACA;MACY,IAAI,CAAC,IAAI,CAACkF,cAAc,IAAI,CAAC,IAAI,CAAC6F,WAAW,EACzC;MACJ;AACZ;AACA;MACY,IAAI,CAAC,IAAI,CAAC9F,MAAM,EAAE;QACd,IAAI,CAACA,MAAM,GAAGnH,SAAS,CAAC,CAAC;QACzB,IAAI,CAAC0N,oBAAoB,GAAG1N,SAAS,CAAC,CAAC;MAC3C;MACA;AACZ;AACA;MACY,IAAI,IAAI,CAACoH,cAAc,IACnB,IAAI,CAACqG,oBAAoB,KACxB,CAAC7H,EAAE,GAAG,IAAI,CAAC2H,cAAc,MAAM,IAAI,IAAI3H,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACuB,MAAM,CAAC,EAAE;QAC7ExH,eAAe,CAAC,IAAI,CAACwH,MAAM,EAAE,IAAI,CAACC,cAAc,EAAE,IAAI,CAACmG,cAAc,CAACpG,MAAM,CAAC;QAC7E;AAChB;AACA;MACY,CAAC,MACI,IAAI,IAAI,CAAC8F,WAAW,EAAE;QACvB,IAAI5B,OAAO,CAAC,IAAI,CAACpD,YAAY,CAAC,EAAE;UAC5B;UACA,IAAI,CAACd,MAAM,GAAG,IAAI,CAACuF,cAAc,CAAC,IAAI,CAAC1G,MAAM,CAACkF,SAAS,CAAC;QAC5D,CAAC,MACI;UACD7L,WAAW,CAAC,IAAI,CAAC8H,MAAM,EAAE,IAAI,CAACnB,MAAM,CAACkF,SAAS,CAAC;QACnD;QACA1L,aAAa,CAAC,IAAI,CAAC2H,MAAM,EAAE,IAAI,CAAC8F,WAAW,CAAC;MAChD,CAAC,MACI;QACD;AAChB;AACA;QACgB5N,WAAW,CAAC,IAAI,CAAC8H,MAAM,EAAE,IAAI,CAACnB,MAAM,CAACkF,SAAS,CAAC;MACnD;MACA;AACZ;AACA;MACY,IAAI,IAAI,CAACoC,8BAA8B,EAAE;QACrC,IAAI,CAACA,8BAA8B,GAAG,KAAK;QAC3C,MAAMC,cAAc,GAAG,IAAI,CAACC,0BAA0B,CAAC,CAAC;QACxD,IAAID,cAAc,IACdlC,OAAO,CAACkC,cAAc,CAACtF,YAAY,CAAC,KAChCoD,OAAO,CAAC,IAAI,CAACpD,YAAY,CAAC,IAC9B,CAACsF,cAAc,CAAChL,OAAO,CAAC+I,YAAY,IACpCiC,cAAc,CAACpG,MAAM,EAAE;UACvB,IAAI,CAACoG,cAAc,GAAGA,cAAc;UACpC,IAAI,CAACnG,cAAc,GAAGpH,SAAS,CAAC,CAAC;UACjC,IAAI,CAACyN,oBAAoB,GAAGzN,SAAS,CAAC,CAAC;UACvCN,oBAAoB,CAAC,IAAI,CAAC+N,oBAAoB,EAAE,IAAI,CAACtG,MAAM,EAAEoG,cAAc,CAACpG,MAAM,CAAC;UACnF9H,WAAW,CAAC,IAAI,CAAC+H,cAAc,EAAE,IAAI,CAACqG,oBAAoB,CAAC;QAC/D,CAAC,MACI;UACD,IAAI,CAACF,cAAc,GAAG,IAAI,CAACnG,cAAc,GAAGlF,SAAS;QACzD;MACJ;IACJ;IACAsL,0BAA0BA,CAAA,EAAG;MACzB,IAAI,CAAC,IAAI,CAACrL,MAAM,IACZxB,QAAQ,CAAC,IAAI,CAACwB,MAAM,CAACJ,YAAY,CAAC,IAClCnB,cAAc,CAAC,IAAI,CAACuB,MAAM,CAACJ,YAAY,CAAC,EACxC,OAAOG,SAAS;MACpB,IAAI,CAAC,IAAI,CAACC,MAAM,CAACiF,cAAc,IAAI,IAAI,CAACjF,MAAM,CAAC8K,WAAW,KACtD,IAAI,CAAC9K,MAAM,CAAC6D,MAAM,EAAE;QACpB,OAAO,IAAI,CAAC7D,MAAM;MACtB,CAAC,MACI;QACD,OAAO,IAAI,CAACA,MAAM,CAACqL,0BAA0B,CAAC,CAAC;MACnD;IACJ;IACAxJ,cAAcA,CAAA,EAAG;MACb,IAAI4B,EAAE;MACN,MAAM;QAAEhD,iBAAiB;QAAED;MAAiB,CAAC,GAAG,IAAI;MACpD,IAAI,CAACC,iBAAiB,GAAG,IAAI,CAACD,gBAAgB,GAAG,KAAK;MACtD,MAAMyK,IAAI,GAAG,IAAI,CAACC,OAAO,CAAC,CAAC;MAC3B,MAAMM,QAAQ,GAAGtC,OAAO,CAAC,IAAI,CAACpD,YAAY,CAAC,IAAI,IAAI,KAAKmF,IAAI;MAC5D,IAAIQ,OAAO,GAAG,IAAI;MAClB,IAAIhL,iBAAiB,EACjBgL,OAAO,GAAG,KAAK;MACnB,IAAID,QAAQ,IAAIhL,gBAAgB,EAC5BiL,OAAO,GAAG,KAAK;MACnB,IAAIA,OAAO,EACP;MACJ,MAAM;QAAE5H,MAAM;QAAED;MAAS,CAAC,GAAG,IAAI,CAACxD,OAAO;MACzC;AACZ;AACA;AACA;MACY,IAAI,CAACC,eAAe,GAAG6I,OAAO,CAAC,CAAC,CAACzF,EAAE,GAAG,IAAI,CAACzD,MAAM,MAAM,IAAI,IAAIyD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACpD,eAAe,KACtG,IAAI,CAACwF,gBAAgB,IACrB,IAAI,CAAC6F,gBAAgB,CAAC;MAC1B,IAAI,CAAC,IAAI,CAACrL,eAAe,EAAE;QACvB,IAAI,CAACyK,WAAW,GAAG,IAAI,CAAC7F,cAAc,GAAGlF,SAAS;MACtD;MACA,IAAI,CAAC,IAAI,CAAC8D,MAAM,IAAI,EAAEA,MAAM,IAAID,QAAQ,CAAC,EACrC;MACJ;AACZ;AACA;AACA;MACY1G,WAAW,CAAC,IAAI,CAAC2L,eAAe,EAAE,IAAI,CAAChF,MAAM,CAACkF,SAAS,CAAC;MACxD;AACZ;AACA;AACA;MACYzL,eAAe,CAAC,IAAI,CAACuL,eAAe,EAAE,IAAI,CAAC7H,SAAS,EAAE,IAAI,CAACmB,IAAI,EAAEqJ,QAAQ,CAAC;MAC1E,MAAM;QAAExG;MAAO,CAAC,GAAGiG,IAAI;MACvB,IAAI,CAACjG,MAAM,EACP;MACJ,IAAI,CAAC,IAAI,CAAC8D,eAAe,EAAE;QACvB,IAAI,CAACA,eAAe,GAAGhL,WAAW,CAAC,CAAC;QACpC,IAAI,CAAC6N,4BAA4B,GAAG7N,WAAW,CAAC,CAAC;MACrD;MACA,MAAM8N,cAAc,GAAG,IAAI,CAAC5K,SAAS,CAACC,CAAC;MACvC,MAAM4K,cAAc,GAAG,IAAI,CAAC7K,SAAS,CAACE,CAAC;MACvC,MAAM4K,uBAAuB,GAAG,IAAI,CAACC,mBAAmB;MACxD;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACYtO,YAAY,CAAC,IAAI,CAACqL,eAAe,EAAE,IAAI,CAACD,eAAe,EAAE7D,MAAM,EAAE,IAAI,CAACpF,YAAY,CAAC;MACnF,IAAI,CAACmM,mBAAmB,GAAG1N,wBAAwB,CAAC,IAAI,CAACyK,eAAe,EAAE,IAAI,CAAC9H,SAAS,CAAC;MACzF,IAAI,IAAI,CAAC+K,mBAAmB,KAAKD,uBAAuB,IACpD,IAAI,CAAC9K,SAAS,CAACC,CAAC,KAAK2K,cAAc,IACnC,IAAI,CAAC5K,SAAS,CAACE,CAAC,KAAK2K,cAAc,EAAE;QACrC,IAAI,CAAC/J,YAAY,GAAG,IAAI;QACxB,IAAI,CAAC6H,cAAc,CAAC,CAAC;QACrB,IAAI,CAAC7G,eAAe,CAAC,kBAAkB,EAAEkC,MAAM,CAAC;MACpD;IACJ;IACAgH,IAAIA,CAAA,EAAG;MACH,IAAI,CAACjK,SAAS,GAAG,KAAK;MACtB;IACJ;IACAkK,IAAIA,CAAA,EAAG;MACH,IAAI,CAAClK,SAAS,GAAG,IAAI;MACrB;IACJ;IACA4H,cAAcA,CAAA,EAAmB;MAAA,IAAlBuC,SAAS,GAAArM,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;MAC3B,IAAI4D,EAAE,EAAEkB,EAAE,EAAEC,EAAE;MACd,CAACD,EAAE,GAAG,CAAClB,EAAE,GAAG,IAAI,CAACrD,OAAO,EAAEuJ,cAAc,MAAM,IAAI,IAAIhF,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC8B,IAAI,CAAChD,EAAE,CAAC;MAC1FyI,SAAS,KAAK,CAACtH,EAAE,GAAG,IAAI,CAACiC,QAAQ,CAAC,CAAC,MAAM,IAAI,IAAIjC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC+E,cAAc,CAAC,CAAC,CAAC;MAC9F,IAAI,IAAI,CAAC7D,YAAY,IAAI,CAAC,IAAI,CAACA,YAAY,CAACtC,QAAQ,EAAE;QAClD,IAAI,CAACsC,YAAY,GAAG/F,SAAS;MACjC;IACJ;IACAgG,kBAAkBA,CAACxB,KAAK,EAAwC;MAAA,IAAtCoB,4BAA4B,GAAA9F,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;MAC1D,IAAI4D,EAAE,EAAEkB,EAAE;MACV,MAAM8D,QAAQ,GAAG,IAAI,CAACA,QAAQ;MAC9B,MAAM0D,oBAAoB,GAAG,CAAC1D,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAC7I,YAAY,KAAK,CAAC,CAAC;MAC9G,MAAMwM,WAAW,GAAAnG,aAAA,KAAQ,IAAI,CAACrG,YAAY,CAAE;MAC5C,MAAMkL,WAAW,GAAGhN,WAAW,CAAC,CAAC;MACjC,IAAI,CAACmH,cAAc,GAAG,IAAI,CAACqG,oBAAoB,GAAGvL,SAAS;MAC3D,IAAI,CAACoL,8BAA8B,GAAG,CAACxF,4BAA4B;MACnE,MAAM0G,cAAc,GAAGxO,SAAS,CAAC,CAAC;MAClC,MAAMyO,uBAAuB,GAAG,CAAC7D,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACyB,MAAM,OAAO,CAACzG,EAAE,GAAG,IAAI,CAACI,MAAM,MAAM,IAAI,IAAIJ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACyG,MAAM,CAAC;MAC7K,MAAMqC,YAAY,GAAG,CAAC,CAAC,CAAC5H,EAAE,GAAG,IAAI,CAACkC,QAAQ,CAAC,CAAC,MAAM,IAAI,IAAIlC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC6H,OAAO,CAAC1M,MAAM,KAAK,CAAC,KAAK,CAAC;MAChH,MAAM2M,sBAAsB,GAAGvD,OAAO,CAACoD,uBAAuB,IAC1D,CAACC,YAAY,IACb,IAAI,CAACnM,OAAO,CAAC4K,SAAS,KAAK,IAAI,IAC/B,CAAC,IAAI,CAAC7I,IAAI,CAACuK,IAAI,CAACC,mBAAmB,CAAC,CAAC;MACzC,IAAI,CAAC3K,iBAAiB,GAAG,CAAC;MAC1B,IAAI,CAAC4K,cAAc,GAAIC,MAAM,IAAK;QAC9B,IAAIpJ,EAAE;QACN,MAAMqJ,QAAQ,GAAGD,MAAM,GAAG,IAAI;QAC9BE,YAAY,CAACjC,WAAW,CAAC7J,CAAC,EAAEsD,KAAK,CAACtD,CAAC,EAAE6L,QAAQ,CAAC;QAC9CC,YAAY,CAACjC,WAAW,CAAC5J,CAAC,EAAEqD,KAAK,CAACrD,CAAC,EAAE4L,QAAQ,CAAC;QAC9C,IAAI,CAACjC,cAAc,CAACC,WAAW,CAAC;QAChC,IAAI,IAAI,CAAC7F,cAAc,IACnB,IAAI,CAACqG,oBAAoB,IACzB,IAAI,CAACzH,MAAM,KACV,CAACJ,EAAE,GAAG,IAAI,CAAC2H,cAAc,MAAM,IAAI,IAAI3H,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACI,MAAM,CAAC,EAAE;UAC7EtG,oBAAoB,CAAC8O,cAAc,EAAE,IAAI,CAACxI,MAAM,CAACkF,SAAS,EAAE,IAAI,CAACqC,cAAc,CAACvH,MAAM,CAACkF,SAAS,CAAC;UACjGiE,MAAM,CAAC,IAAI,CAAC/H,cAAc,EAAE,IAAI,CAACqG,oBAAoB,EAAEe,cAAc,EAAES,QAAQ,CAAC;QACpF;QACA,IAAIR,uBAAuB,EAAE;UACzB,IAAI,CAACW,eAAe,GAAGb,WAAW;UAClCnP,SAAS,CAACmP,WAAW,EAAED,oBAAoB,EAAE,IAAI,CAACvM,YAAY,EAAEkN,QAAQ,EAAEL,sBAAsB,EAAEF,YAAY,CAAC;QACnH;QACA,IAAI,CAACrK,IAAI,CAACoG,wBAAwB,CAAC,CAAC;QACpC,IAAI,CAACqB,cAAc,CAAC,CAAC;QACrB,IAAI,CAAC3H,iBAAiB,GAAG8K,QAAQ;MACrC,CAAC;MACD,IAAI,CAACF,cAAc,CAAC,CAAC,CAAC;IAC1B;IACAtG,cAAcA,CAAClG,OAAO,EAAE;MACpB,IAAIqD,EAAE,EAAEkB,EAAE;MACV,IAAI,CAAC7B,eAAe,CAAC,gBAAgB,CAAC;MACtC,CAACW,EAAE,GAAG,IAAI,CAACoC,gBAAgB,MAAM,IAAI,IAAIpC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACyJ,IAAI,CAAC,CAAC;MAC3E,IAAI,IAAI,CAACpH,YAAY,EAAE;QACnB,CAACnB,EAAE,GAAG,IAAI,CAACmB,YAAY,CAACD,gBAAgB,MAAM,IAAI,IAAIlB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACuI,IAAI,CAAC,CAAC;MAC5F;MACA,IAAI,IAAI,CAACxB,gBAAgB,EAAE;QACvB9O,UAAU,CAACsL,MAAM,CAAC,IAAI,CAACwD,gBAAgB,CAAC;QACxC,IAAI,CAACA,gBAAgB,GAAG3L,SAAS;MACrC;MACA;AACZ;AACA;AACA;AACA;MACY,IAAI,CAAC2L,gBAAgB,GAAG5O,IAAI,CAACoL,MAAM,CAAC,MAAM;QACtCtJ,qBAAqB,CAACuF,sBAAsB,GAAG,IAAI;QACnD,IAAI,CAAC0B,gBAAgB,GAAG9I,OAAO,CAAC,CAAC,EAAEiC,eAAe,EAAAiH,aAAA,CAAAA,aAAA,KAC3C7F,OAAO;UACV+M,QAAQ,EAAGN,MAAM,IAAK;YAClB,IAAIpJ,EAAE;YACN,IAAI,CAACmJ,cAAc,CAACC,MAAM,CAAC;YAC3B,CAACpJ,EAAE,GAAGrD,OAAO,CAAC+M,QAAQ,MAAM,IAAI,IAAI1J,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACgD,IAAI,CAACrG,OAAO,EAAEyM,MAAM,CAAC;UACzF,CAAC;UACD1G,UAAU,EAAEA,CAAA,KAAM;YACd,IAAI1C,EAAE;YACN,CAACA,EAAE,GAAGrD,OAAO,CAAC+F,UAAU,MAAM,IAAI,IAAI1C,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACgD,IAAI,CAACrG,OAAO,CAAC;YAC/E,IAAI,CAACgN,iBAAiB,CAAC,CAAC;UAC5B;QAAC,EACJ,CAAC;QACF,IAAI,IAAI,CAACtH,YAAY,EAAE;UACnB,IAAI,CAACA,YAAY,CAACD,gBAAgB,GAAG,IAAI,CAACA,gBAAgB;QAC9D;QACA,IAAI,CAAC6F,gBAAgB,GAAG3L,SAAS;MACrC,CAAC,CAAC;IACN;IACAqN,iBAAiBA,CAAA,EAAG;MAChB,IAAI3J,EAAE;MACN,IAAI,IAAI,CAACqC,YAAY,EAAE;QACnB,IAAI,CAACA,YAAY,CAACD,gBAAgB,GAAG9F,SAAS;QAC9C,IAAI,CAAC+F,YAAY,CAACuH,eAAe,GAAGtN,SAAS;MACjD;MACA,CAAC0D,EAAE,GAAG,IAAI,CAACoD,QAAQ,CAAC,CAAC,MAAM,IAAI,IAAIpD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC6J,qBAAqB,CAAC,CAAC;MACtF,IAAI,CAACxH,YAAY,GACb,IAAI,CAACD,gBAAgB,GACjB,IAAI,CAACoH,eAAe,GAChBlN,SAAS;MACrB,IAAI,CAAC+C,eAAe,CAAC,mBAAmB,CAAC;IAC7C;IACAsB,eAAeA,CAAA,EAAG;MACd,IAAIX,EAAE;MACN,IAAI,IAAI,CAACoC,gBAAgB,EAAE;QACvB,CAACpC,EAAE,GAAG,IAAI,CAACmJ,cAAc,MAAM,IAAI,IAAInJ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACgD,IAAI,CAAC,IAAI,EAAEzH,eAAe,CAAC;QAC9F,IAAI,CAAC6G,gBAAgB,CAACqH,IAAI,CAAC,CAAC;MAChC;MACA,IAAI,CAACE,iBAAiB,CAAC,CAAC;IAC5B;IACAG,uBAAuBA,CAAA,EAAG;MACtB,MAAMtC,IAAI,GAAG,IAAI,CAACC,OAAO,CAAC,CAAC;MAC3B,IAAI;QAAEK,oBAAoB;QAAEvG,MAAM;QAAEnB,MAAM;QAAEjE;MAAa,CAAC,GAAGqL,IAAI;MACjE,IAAI,CAACM,oBAAoB,IAAI,CAACvG,MAAM,IAAI,CAACnB,MAAM,EAC3C;MACJ;AACZ;AACA;AACA;AACA;MACY,IAAI,IAAI,KAAKoH,IAAI,IACb,IAAI,CAACpH,MAAM,IACXA,MAAM,IACN2J,yBAAyB,CAAC,IAAI,CAACpN,OAAO,CAACqN,aAAa,EAAE,IAAI,CAAC5J,MAAM,CAACkF,SAAS,EAAElF,MAAM,CAACkF,SAAS,CAAC,EAAE;QAChG/D,MAAM,GAAG,IAAI,CAACA,MAAM,IAAInH,SAAS,CAAC,CAAC;QACnC,MAAM6P,OAAO,GAAGhQ,UAAU,CAAC,IAAI,CAACmG,MAAM,CAACkF,SAAS,CAAC9H,CAAC,CAAC;QACnD+D,MAAM,CAAC/D,CAAC,CAAC0M,GAAG,GAAG1C,IAAI,CAACjG,MAAM,CAAC/D,CAAC,CAAC0M,GAAG;QAChC3I,MAAM,CAAC/D,CAAC,CAAC2M,GAAG,GAAG5I,MAAM,CAAC/D,CAAC,CAAC0M,GAAG,GAAGD,OAAO;QACrC,MAAMG,OAAO,GAAGnQ,UAAU,CAAC,IAAI,CAACmG,MAAM,CAACkF,SAAS,CAAC7H,CAAC,CAAC;QACnD8D,MAAM,CAAC9D,CAAC,CAACyM,GAAG,GAAG1C,IAAI,CAACjG,MAAM,CAAC9D,CAAC,CAACyM,GAAG;QAChC3I,MAAM,CAAC9D,CAAC,CAAC0M,GAAG,GAAG5I,MAAM,CAAC9D,CAAC,CAACyM,GAAG,GAAGE,OAAO;MACzC;MACA3Q,WAAW,CAACqO,oBAAoB,EAAEvG,MAAM,CAAC;MACzC;AACZ;AACA;AACA;AACA;MACY5H,YAAY,CAACmO,oBAAoB,EAAE3L,YAAY,CAAC;MAChD;AACZ;AACA;AACA;AACA;AACA;MACYnC,YAAY,CAAC,IAAI,CAACkO,4BAA4B,EAAE,IAAI,CAAC9C,eAAe,EAAE0C,oBAAoB,EAAE3L,YAAY,CAAC;IAC7G;IACAyE,kBAAkBA,CAACT,QAAQ,EAAEN,IAAI,EAAE;MAC/B,IAAIG,EAAE,EAAEkB,EAAE,EAAEC,EAAE;MACd,IAAI,CAAC,IAAI,CAAC3C,WAAW,CAACS,GAAG,CAACkB,QAAQ,CAAC,EAAE;QACjC,IAAI,CAAC3B,WAAW,CAACU,GAAG,CAACiB,QAAQ,EAAE,IAAIzF,SAAS,CAAC,CAAC,CAAC;MACnD;MACA,MAAM2P,KAAK,GAAG,IAAI,CAAC7L,WAAW,CAACW,GAAG,CAACgB,QAAQ,CAAC;MAC5CkK,KAAK,CAACjL,GAAG,CAACS,IAAI,CAAC;MACfA,IAAI,CAACyK,OAAO,CAAC;QACT5I,UAAU,EAAE,CAAC1B,EAAE,GAAGH,IAAI,CAAClD,OAAO,CAAC4N,sBAAsB,MAAM,IAAI,IAAIvK,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC0B,UAAU;QACzG8I,qBAAqB,EAAE,CAACrJ,EAAE,GAAG,CAACD,EAAE,GAAGrB,IAAI,CAAClD,OAAO,CAAC4N,sBAAsB,MAAM,IAAI,IAAIrJ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACuJ,2BAA2B,MAAM,IAAI,IAAItJ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC6B,IAAI,CAAC9B,EAAE,EAAErB,IAAI;MACtM,CAAC,CAAC;IACN;IACAiD,MAAMA,CAAA,EAAG;MACL,MAAMuH,KAAK,GAAG,IAAI,CAACjH,QAAQ,CAAC,CAAC;MAC7B,OAAOiH,KAAK,GAAGA,KAAK,CAAC7C,IAAI,KAAK,IAAI,GAAG,IAAI;IAC7C;IACAC,OAAOA,CAAA,EAAG;MACN,IAAIzH,EAAE;MACN,MAAM;QAAEG;MAAS,CAAC,GAAG,IAAI,CAACxD,OAAO;MACjC,OAAOwD,QAAQ,GAAG,CAAC,CAACH,EAAE,GAAG,IAAI,CAACoD,QAAQ,CAAC,CAAC,MAAM,IAAI,IAAIpD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACwH,IAAI,KAAK,IAAI,GAAG,IAAI;IAC1G;IACAkD,WAAWA,CAAA,EAAG;MACV,IAAI1K,EAAE;MACN,MAAM;QAAEG;MAAS,CAAC,GAAG,IAAI,CAACxD,OAAO;MACjC,OAAOwD,QAAQ,GAAG,CAACH,EAAE,GAAG,IAAI,CAACoD,QAAQ,CAAC,CAAC,MAAM,IAAI,IAAIpD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC2K,QAAQ,GAAGrO,SAAS;IACzG;IACA8G,QAAQA,CAAA,EAAG;MACP,MAAM;QAAEjD;MAAS,CAAC,GAAG,IAAI,CAACxD,OAAO;MACjC,IAAIwD,QAAQ,EACR,OAAO,IAAI,CAAC1B,IAAI,CAACD,WAAW,CAACW,GAAG,CAACgB,QAAQ,CAAC;IAClD;IACAmK,OAAOA,CAAA,EAA0D;MAAA,IAAzD;QAAEjN,UAAU;QAAEqE,UAAU;QAAE8I;MAAuB,CAAC,GAAApO,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;MAC3D,MAAMiO,KAAK,GAAG,IAAI,CAACjH,QAAQ,CAAC,CAAC;MAC7B,IAAIiH,KAAK,EACLA,KAAK,CAACC,OAAO,CAAC,IAAI,EAAEE,qBAAqB,CAAC;MAC9C,IAAInN,UAAU,EAAE;QACZ,IAAI,CAACgI,eAAe,GAAG/I,SAAS;QAChC,IAAI,CAACe,UAAU,GAAG,IAAI;MAC1B;MACA,IAAIqE,UAAU,EACV,IAAI,CAAC4F,UAAU,CAAC;QAAE5F;MAAW,CAAC,CAAC;IACvC;IACAkJ,QAAQA,CAAA,EAAG;MACP,MAAMP,KAAK,GAAG,IAAI,CAACjH,QAAQ,CAAC,CAAC;MAC7B,IAAIiH,KAAK,EAAE;QACP,OAAOA,KAAK,CAACO,QAAQ,CAAC,IAAI,CAAC;MAC/B,CAAC,MACI;QACD,OAAO,KAAK;MAChB;IACJ;IACAlH,aAAaA,CAAA,EAAG;MACZ,MAAM;QAAErD;MAAc,CAAC,GAAG,IAAI,CAAC1D,OAAO;MACtC,IAAI,CAAC0D,aAAa,EACd;MACJ;MACA,IAAIwK,SAAS,GAAG,KAAK;MACrB;AACZ;AACA;AACA;MACY,MAAM;QAAE1O;MAAa,CAAC,GAAGkE,aAAa;MACtC,IAAIlE,YAAY,CAAC2O,MAAM,IACnB3O,YAAY,CAAC4O,OAAO,IACpB5O,YAAY,CAAC6O,OAAO,IACpB7O,YAAY,CAAC8O,OAAO,EAAE;QACtBJ,SAAS,GAAG,IAAI;MACpB;MACA;MACA,IAAI,CAACA,SAAS,EACV;MACJ,MAAMK,WAAW,GAAG,CAAC,CAAC;MACtB;MACA,KAAK,IAAIrM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGvD,aAAa,CAACe,MAAM,EAAEwC,CAAC,EAAE,EAAE;QAC3C,MAAMsM,GAAG,GAAG,QAAQ,GAAG7P,aAAa,CAACuD,CAAC,CAAC;QACvC;QACA,IAAI1C,YAAY,CAACgP,GAAG,CAAC,EAAE;UACnBD,WAAW,CAACC,GAAG,CAAC,GAAGhP,YAAY,CAACgP,GAAG,CAAC;UACpC9K,aAAa,CAAC+K,cAAc,CAACD,GAAG,EAAE,CAAC,CAAC;QACxC;MACJ;MACA;MACA;MACA9K,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACqE,MAAM,CAAC,CAAC;MACpF;MACA,KAAK,MAAMyG,GAAG,IAAID,WAAW,EAAE;QAC3B7K,aAAa,CAAC+K,cAAc,CAACD,GAAG,EAAED,WAAW,CAACC,GAAG,CAAC,CAAC;MACvD;MACA;MACA;MACA9K,aAAa,CAAC6F,cAAc,CAAC,CAAC;IAClC;IACAmF,mBAAmBA,CAAA,EAAiB;MAAA,IAAhBC,SAAS,GAAAlP,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;MAC9B,IAAI4D,EAAE,EAAEkB,EAAE,EAAEC,EAAE;MACd;MACA,MAAMoK,MAAM,GAAG,CAAC,CAAC;MACjB,IAAI,CAAC,IAAI,CAACxL,QAAQ,IAAI,IAAI,CAAC3C,KAAK,EAC5B,OAAOmO,MAAM;MACjB,IAAI,CAAC,IAAI,CAACjN,SAAS,EAAE;QACjB,OAAO;UAAEkN,UAAU,EAAE;QAAS,CAAC;MACnC,CAAC,MACI;QACDD,MAAM,CAACC,UAAU,GAAG,EAAE;MAC1B;MACA,MAAM3H,iBAAiB,GAAG,CAAC7D,EAAE,GAAG,IAAI,CAACrD,OAAO,CAAC0D,aAAa,MAAM,IAAI,IAAIL,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC+B,QAAQ,CAAC,CAAC,CAAC8B,iBAAiB;MAChI,IAAI,IAAI,CAACxG,UAAU,EAAE;QACjB,IAAI,CAACA,UAAU,GAAG,KAAK;QACvBkO,MAAM,CAACE,OAAO,GAAG,EAAE;QACnBF,MAAM,CAACG,aAAa,GAChBxQ,kBAAkB,CAACoQ,SAAS,CAACI,aAAa,CAAC,IAAI,EAAE;QACrDH,MAAM,CAACI,SAAS,GAAG9H,iBAAiB,GAC9BA,iBAAiB,CAAC,IAAI,CAAC1H,YAAY,EAAE,EAAE,CAAC,GACxC,MAAM;QACZ,OAAOoP,MAAM;MACjB;MACA,MAAM/D,IAAI,GAAG,IAAI,CAACC,OAAO,CAAC,CAAC;MAC3B,IAAI,CAAC,IAAI,CAACpC,eAAe,IAAI,CAAC,IAAI,CAACjF,MAAM,IAAI,CAACoH,IAAI,CAACjG,MAAM,EAAE;QACvD,MAAMqK,WAAW,GAAG,CAAC,CAAC;QACtB,IAAI,IAAI,CAACjP,OAAO,CAACwD,QAAQ,EAAE;UACvByL,WAAW,CAACH,OAAO,GACf,IAAI,CAACtP,YAAY,CAACsP,OAAO,KAAKnP,SAAS,GACjC,IAAI,CAACH,YAAY,CAACsP,OAAO,GACzB,CAAC;UACXG,WAAW,CAACF,aAAa,GACrBxQ,kBAAkB,CAACoQ,SAAS,CAACI,aAAa,CAAC,IAAI,EAAE;QACzD;QACA,IAAI,IAAI,CAACrN,YAAY,IAAI,CAACvD,YAAY,CAAC,IAAI,CAACqB,YAAY,CAAC,EAAE;UACvDyP,WAAW,CAACD,SAAS,GAAG9H,iBAAiB,GACnCA,iBAAiB,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GACzB,MAAM;UACZ,IAAI,CAACxF,YAAY,GAAG,KAAK;QAC7B;QACA,OAAOuN,WAAW;MACtB;MACA,MAAMC,cAAc,GAAGrE,IAAI,CAACgC,eAAe,IAAIhC,IAAI,CAACrL,YAAY;MAChE,IAAI,CAAC2N,uBAAuB,CAAC,CAAC;MAC9ByB,MAAM,CAACI,SAAS,GAAG/Q,wBAAwB,CAAC,IAAI,CAACsN,4BAA4B,EAAE,IAAI,CAAC3K,SAAS,EAAEsO,cAAc,CAAC;MAC9G,IAAIhI,iBAAiB,EAAE;QACnB0H,MAAM,CAACI,SAAS,GAAG9H,iBAAiB,CAACgI,cAAc,EAAEN,MAAM,CAACI,SAAS,CAAC;MAC1E;MACA,MAAM;QAAEnO,CAAC;QAAEC;MAAE,CAAC,GAAG,IAAI,CAAC4H,eAAe;MACrCkG,MAAM,CAACO,eAAe,MAAAC,MAAA,CAAMvO,CAAC,CAACwO,MAAM,GAAG,GAAG,QAAAD,MAAA,CAAKtO,CAAC,CAACuO,MAAM,GAAG,GAAG,QAAK;MAClE,IAAIxE,IAAI,CAACgC,eAAe,EAAE;QACtB;AAChB;AACA;AACA;QACgB+B,MAAM,CAACE,OAAO,GACVjE,IAAI,KAAK,IAAI,GACP,CAACrG,EAAE,GAAG,CAACD,EAAE,GAAG2K,cAAc,CAACJ,OAAO,MAAM,IAAI,IAAIvK,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,IAAI,CAAC/E,YAAY,CAACsP,OAAO,MAAM,IAAI,IAAItK,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC,GAClI,IAAI,CAACyI,eAAe,GAChB,IAAI,CAACzN,YAAY,CAACsP,OAAO,GACzBI,cAAc,CAACI,WAAW;MAC5C,CAAC,MACI;QACD;AAChB;AACA;AACA;QACgBV,MAAM,CAACE,OAAO,GACVjE,IAAI,KAAK,IAAI,GACPqE,cAAc,CAACJ,OAAO,KAAKnP,SAAS,GAChCuP,cAAc,CAACJ,OAAO,GACtB,EAAE,GACNI,cAAc,CAACI,WAAW,KAAK3P,SAAS,GACpCuP,cAAc,CAACI,WAAW,GAC1B,CAAC;MACnB;MACA;AACZ;AACA;MACY,KAAK,MAAMd,GAAG,IAAIxQ,eAAe,EAAE;QAC/B,IAAIkR,cAAc,CAACV,GAAG,CAAC,KAAK7O,SAAS,EACjC;QACJ,MAAM;UAAE4P,OAAO;UAAEC;QAAQ,CAAC,GAAGxR,eAAe,CAACwQ,GAAG,CAAC;QACjD,MAAMiB,SAAS,GAAGF,OAAO,CAACL,cAAc,CAACV,GAAG,CAAC,EAAE3D,IAAI,CAAC;QACpD,IAAI2E,OAAO,EAAE;UACT,MAAME,GAAG,GAAGF,OAAO,CAAC9P,MAAM;UAC1B,KAAK,IAAIwC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwN,GAAG,EAAExN,CAAC,EAAE,EAAE;YAC1B0M,MAAM,CAACY,OAAO,CAACtN,CAAC,CAAC,CAAC,GAAGuN,SAAS;UAClC;QACJ,CAAC,MACI;UACDb,MAAM,CAACJ,GAAG,CAAC,GAAGiB,SAAS;QAC3B;MACJ;MACA;AACZ;AACA;AACA;AACA;MACY,IAAI,IAAI,CAACzP,OAAO,CAACwD,QAAQ,EAAE;QACvBoL,MAAM,CAACG,aAAa,GAChBlE,IAAI,KAAK,IAAI,GACPtM,kBAAkB,CAACoQ,SAAS,CAACI,aAAa,CAAC,IAAI,EAAE,GACjD,MAAM;MACpB;MACA,OAAOH,MAAM;IACjB;IACA5G,aAAaA,CAAA,EAAG;MACZ,IAAI,CAACxC,UAAU,GAAG,IAAI,CAAC6C,QAAQ,GAAG1I,SAAS;IAC/C;IACA;IACAgQ,SAASA,CAAA,EAAG;MACR,IAAI,CAAC7N,IAAI,CAACT,KAAK,CAACC,OAAO,CAAE4B,IAAI,IAAK;QAAE,IAAIG,EAAE;QAAE,OAAO,CAACA,EAAE,GAAGH,IAAI,CAACuC,gBAAgB,MAAM,IAAI,IAAIpC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACyJ,IAAI,CAAC,CAAC;MAAE,CAAC,CAAC;MAClI,IAAI,CAAChL,IAAI,CAACT,KAAK,CAACC,OAAO,CAACiG,iBAAiB,CAAC;MAC1C,IAAI,CAACzF,IAAI,CAACD,WAAW,CAAC6F,KAAK,CAAC,CAAC;IACjC;EACJ,CAAC;AACL;AACA,SAASE,YAAYA,CAAC1E,IAAI,EAAE;EACxBA,IAAI,CAAC0E,YAAY,CAAC,CAAC;AACvB;AACA,SAASC,kBAAkBA,CAAC3E,IAAI,EAAE;EAC9B,IAAIG,EAAE,EAAEkB,EAAE,EAAEC,EAAE;EACd,MAAM6D,QAAQ,GAAG,CAAC,CAAChF,EAAE,GAAGH,IAAI,CAACsC,UAAU,MAAM,IAAI,IAAInC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACgF,QAAQ,KAAKnF,IAAI,CAACmF,QAAQ;EAC3G,IAAInF,IAAI,CAACiD,MAAM,CAAC,CAAC,IACbjD,IAAI,CAACO,MAAM,IACX4E,QAAQ,IACRnF,IAAI,CAACD,YAAY,CAAC,WAAW,CAAC,EAAE;IAChC,MAAM;MAAE0F,SAAS,EAAElF,MAAM;MAAEoG,WAAW,EAAE+F;IAAe,CAAC,GAAG1M,IAAI,CAACO,MAAM;IACtE,MAAM;MAAE4J;IAAc,CAAC,GAAGnK,IAAI,CAAClD,OAAO;IACtC,MAAMoL,QAAQ,GAAG/C,QAAQ,CAACyB,MAAM,KAAK5G,IAAI,CAACO,MAAM,CAACqG,MAAM;IACvD;IACA;IACA,IAAIuD,aAAa,KAAK,MAAM,EAAE;MAC1BnP,QAAQ,CAAE2R,IAAI,IAAK;QACf,MAAMC,YAAY,GAAG1E,QAAQ,GACvB/C,QAAQ,CAACwB,WAAW,CAACgG,IAAI,CAAC,GAC1BxH,QAAQ,CAACM,SAAS,CAACkH,IAAI,CAAC;QAC9B,MAAMnQ,MAAM,GAAGpC,UAAU,CAACwS,YAAY,CAAC;QACvCA,YAAY,CAACvC,GAAG,GAAG9J,MAAM,CAACoM,IAAI,CAAC,CAACtC,GAAG;QACnCuC,YAAY,CAACtC,GAAG,GAAGsC,YAAY,CAACvC,GAAG,GAAG7N,MAAM;MAChD,CAAC,CAAC;IACN,CAAC,MACI,IAAI0N,yBAAyB,CAACC,aAAa,EAAEhF,QAAQ,CAACM,SAAS,EAAElF,MAAM,CAAC,EAAE;MAC3EvF,QAAQ,CAAE2R,IAAI,IAAK;QACf,MAAMC,YAAY,GAAG1E,QAAQ,GACvB/C,QAAQ,CAACwB,WAAW,CAACgG,IAAI,CAAC,GAC1BxH,QAAQ,CAACM,SAAS,CAACkH,IAAI,CAAC;QAC9B,MAAMnQ,MAAM,GAAGpC,UAAU,CAACmG,MAAM,CAACoM,IAAI,CAAC,CAAC;QACvCC,YAAY,CAACtC,GAAG,GAAGsC,YAAY,CAACvC,GAAG,GAAG7N,MAAM;MAChD,CAAC,CAAC;IACN;IACA,MAAMqQ,WAAW,GAAGrS,WAAW,CAAC,CAAC;IACjCL,YAAY,CAAC0S,WAAW,EAAEtM,MAAM,EAAE4E,QAAQ,CAACM,SAAS,CAAC;IACrD,MAAMqH,WAAW,GAAGtS,WAAW,CAAC,CAAC;IACjC,IAAI0N,QAAQ,EAAE;MACV/N,YAAY,CAAC2S,WAAW,EAAE9M,IAAI,CAACiH,cAAc,CAACyF,cAAc,EAAE,IAAI,CAAC,EAAEvH,QAAQ,CAACwB,WAAW,CAAC;IAC9F,CAAC,MACI;MACDxM,YAAY,CAAC2S,WAAW,EAAEvM,MAAM,EAAE4E,QAAQ,CAACM,SAAS,CAAC;IACzD;IACA,MAAMvE,gBAAgB,GAAG,CAACvG,WAAW,CAACkS,WAAW,CAAC;IAClD,IAAI1L,wBAAwB,GAAG,KAAK;IACpC,IAAI,CAACnB,IAAI,CAACsC,UAAU,EAAE;MAClB,MAAMwF,cAAc,GAAG9H,IAAI,CAAC+H,0BAA0B,CAAC,CAAC;MACxD;AACZ;AACA;AACA;MACY,IAAID,cAAc,IAAI,CAACA,cAAc,CAACxF,UAAU,EAAE;QAC9C,MAAM;UAAE6C,QAAQ,EAAE4H,cAAc;UAAExM,MAAM,EAAEyM;QAAa,CAAC,GAAGlF,cAAc;QACzE,IAAIiF,cAAc,IAAIC,YAAY,EAAE;UAChC,MAAMC,gBAAgB,GAAG1S,SAAS,CAAC,CAAC;UACpCN,oBAAoB,CAACgT,gBAAgB,EAAE9H,QAAQ,CAACM,SAAS,EAAEsH,cAAc,CAACtH,SAAS,CAAC;UACpF,MAAMsD,cAAc,GAAGxO,SAAS,CAAC,CAAC;UAClCN,oBAAoB,CAAC8O,cAAc,EAAExI,MAAM,EAAEyM,YAAY,CAACvH,SAAS,CAAC;UACpE,IAAI,CAAC/K,SAAS,CAACuS,gBAAgB,EAAElE,cAAc,CAAC,EAAE;YAC9C5H,wBAAwB,GAAG,IAAI;UACnC;QACJ;MACJ;IACJ;IACAnB,IAAI,CAACR,eAAe,CAAC,WAAW,EAAE;MAC9Be,MAAM;MACN4E,QAAQ;MACRlE,KAAK,EAAE6L,WAAW;MAClBD,WAAW;MACX3L,gBAAgB;MAChBC;IACJ,CAAC,CAAC;EACN,CAAC,MACI,IAAInB,IAAI,CAACiD,MAAM,CAAC,CAAC,EAAE;IACpB,CAAC3B,EAAE,GAAG,CAACD,EAAE,GAAGrB,IAAI,CAAClD,OAAO,EAAEoG,cAAc,MAAM,IAAI,IAAI5B,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC6B,IAAI,CAAC9B,EAAE,CAAC;EAC9F;EACA;AACJ;AACA;AACA;AACA;EACIrB,IAAI,CAAClD,OAAO,CAAC+E,UAAU,GAAGpF,SAAS;AACvC;AACA,SAAS4B,mBAAmBA,CAAC2B,IAAI,EAAE;EAC/B;AACJ;AACA;AACA;EACIA,IAAI,CAAC7C,iBAAiB,KAAK6C,IAAI,CAAC7C,iBAAiB,GAAGyI,OAAO,CAAC5F,IAAI,CAACtD,MAAM,IAAIsD,IAAI,CAACtD,MAAM,CAACS,iBAAiB,CAAC,CAAC;EAC1G;AACJ;AACA;EACI6C,IAAI,CAAC9C,gBAAgB,KAAK8C,IAAI,CAAC9C,gBAAgB,GAAG0I,OAAO,CAAC5F,IAAI,CAACtD,MAAM,IAAIsD,IAAI,CAACtD,MAAM,CAACQ,gBAAgB,CAAC,CAAC;AAC3G;AACA,SAAS4H,aAAaA,CAAC9E,IAAI,EAAE;EACzBA,IAAI,CAAC8E,aAAa,CAAC,CAAC;AACxB;AACA,SAAST,iBAAiBA,CAACrE,IAAI,EAAE;EAC7BA,IAAI,CAACqE,iBAAiB,CAAC,CAAC;AAC5B;AACA,SAASI,mBAAmBA,CAACzE,IAAI,EAAE;EAC/B,MAAM;IAAEQ;EAAc,CAAC,GAAGR,IAAI,CAAClD,OAAO;EACtC,IAAI0D,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAAC0B,QAAQ,CAAC,CAAC,CAACgL,qBAAqB,EAAE;IAC9G1M,aAAa,CAACV,MAAM,CAAC,qBAAqB,CAAC;EAC/C;EACAE,IAAI,CAAC9D,cAAc,CAAC,CAAC;AACzB;AACA,SAAS4E,eAAeA,CAACd,IAAI,EAAE;EAC3BA,IAAI,CAACc,eAAe,CAAC,CAAC;EACtBd,IAAI,CAACwH,WAAW,GAAGxH,IAAI,CAAC2B,cAAc,GAAG3B,IAAI,CAAC0B,MAAM,GAAGjF,SAAS;AACpE;AACA,SAAS6B,kBAAkBA,CAAC0B,IAAI,EAAE;EAC9BA,IAAI,CAAC1B,kBAAkB,CAAC,CAAC;AAC7B;AACA,SAASC,cAAcA,CAACyB,IAAI,EAAE;EAC1BA,IAAI,CAACzB,cAAc,CAAC,CAAC;AACzB;AACA,SAASsF,aAAaA,CAAC7D,IAAI,EAAE;EACzBA,IAAI,CAAC6D,aAAa,CAAC,CAAC;AACxB;AACA,SAASkB,mBAAmBA,CAACyF,KAAK,EAAE;EAChCA,KAAK,CAAC2C,kBAAkB,CAAC,CAAC;AAC9B;AACA,SAAS1D,YAAYA,CAAC2D,MAAM,EAAEnM,KAAK,EAAEoM,CAAC,EAAE;EACpCD,MAAM,CAACE,SAAS,GAAG9R,GAAG,CAACyF,KAAK,CAACqM,SAAS,EAAE,CAAC,EAAED,CAAC,CAAC;EAC7CD,MAAM,CAACG,KAAK,GAAG/R,GAAG,CAACyF,KAAK,CAACsM,KAAK,EAAE,CAAC,EAAEF,CAAC,CAAC;EACrCD,MAAM,CAACjB,MAAM,GAAGlL,KAAK,CAACkL,MAAM;EAC5BiB,MAAM,CAACI,WAAW,GAAGvM,KAAK,CAACuM,WAAW;AAC1C;AACA,SAASC,OAAOA,CAACL,MAAM,EAAEM,IAAI,EAAEC,EAAE,EAAEN,CAAC,EAAE;EAClCD,MAAM,CAAC/C,GAAG,GAAG7O,GAAG,CAACkS,IAAI,CAACrD,GAAG,EAAEsD,EAAE,CAACtD,GAAG,EAAEgD,CAAC,CAAC;EACrCD,MAAM,CAAC9C,GAAG,GAAG9O,GAAG,CAACkS,IAAI,CAACpD,GAAG,EAAEqD,EAAE,CAACrD,GAAG,EAAE+C,CAAC,CAAC;AACzC;AACA,SAAS3D,MAAMA,CAAC0D,MAAM,EAAEM,IAAI,EAAEC,EAAE,EAAEN,CAAC,EAAE;EACjCI,OAAO,CAACL,MAAM,CAACzP,CAAC,EAAE+P,IAAI,CAAC/P,CAAC,EAAEgQ,EAAE,CAAChQ,CAAC,EAAE0P,CAAC,CAAC;EAClCI,OAAO,CAACL,MAAM,CAACxP,CAAC,EAAE8P,IAAI,CAAC9P,CAAC,EAAE+P,EAAE,CAAC/P,CAAC,EAAEyP,CAAC,CAAC;AACtC;AACA,SAAShE,mBAAmBA,CAACrJ,IAAI,EAAE;EAC/B,OAAQA,IAAI,CAAC2J,eAAe,IAAI3J,IAAI,CAAC2J,eAAe,CAACyC,WAAW,KAAK3P,SAAS;AAClF;AACA,MAAMsF,uBAAuB,GAAG;EAC5B6L,QAAQ,EAAE,IAAI;EACdC,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC;AACzB,CAAC;AACD,SAAStJ,cAAcA,CAACvE,IAAI,EAAE3D,SAAS,EAAE;EACrC;AACJ;AACA;AACA;EACI,IAAIyR,UAAU,GAAG9N,IAAI,CAACpB,IAAI;EAC1B,KAAK,IAAII,CAAC,GAAGgB,IAAI,CAACnB,IAAI,CAACrC,MAAM,GAAG,CAAC,EAAEwC,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IAC5C,IAAI4G,OAAO,CAAC5F,IAAI,CAACnB,IAAI,CAACG,CAAC,CAAC,CAACkB,QAAQ,CAAC,EAAE;MAChC4N,UAAU,GAAG9N,IAAI,CAACnB,IAAI,CAACG,CAAC,CAAC;MACzB;IACJ;EACJ;EACA,MAAM+O,aAAa,GAAGD,UAAU,IAAIA,UAAU,KAAK9N,IAAI,CAACpB,IAAI,GAAGkP,UAAU,CAAC5N,QAAQ,GAAG8N,QAAQ;EAC7F,MAAMC,OAAO,GAAGF,aAAa,CAACG,aAAa,0BAAAhC,MAAA,CAAyB7P,SAAS,QAAI,CAAC;EAClF,IAAI4R,OAAO,EACPjO,IAAI,CAACC,KAAK,CAACgO,OAAO,EAAE,IAAI,CAAC;AACjC;AACA,SAASE,SAASA,CAACxB,IAAI,EAAE;EACrBA,IAAI,CAACtC,GAAG,GAAG+D,IAAI,CAACC,KAAK,CAAC1B,IAAI,CAACtC,GAAG,CAAC;EAC/BsC,IAAI,CAACrC,GAAG,GAAG8D,IAAI,CAACC,KAAK,CAAC1B,IAAI,CAACrC,GAAG,CAAC;AACnC;AACA,SAAS5D,QAAQA,CAACG,GAAG,EAAE;EACnBsH,SAAS,CAACtH,GAAG,CAAClJ,CAAC,CAAC;EAChBwQ,SAAS,CAACtH,GAAG,CAACjJ,CAAC,CAAC;AACpB;AACA,SAASsM,yBAAyBA,CAACC,aAAa,EAAEhF,QAAQ,EAAE5E,MAAM,EAAE;EAChE,OAAQ4J,aAAa,KAAK,UAAU,IAC/BA,aAAa,KAAK,iBAAiB,IAChC,CAAC9P,MAAM,CAACO,WAAW,CAACuK,QAAQ,CAAC,EAAEvK,WAAW,CAAC2F,MAAM,CAAC,EAAE,GAAG,CAAE;AACrE;AAEA,SAAS3E,oBAAoB,EAAE6R,OAAO,EAAEhE,YAAY,EAAEC,MAAM,EAAErL,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}