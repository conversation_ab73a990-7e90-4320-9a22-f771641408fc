{"ast": null, "code": "import { isNumber } from '@motionone/utils';\nimport { isTransform, transformAlias, asTransformCssVar, transformDefinitions, buildTransformTemplate } from './transforms.es.js';\nfunction createStyles(keyframes) {\n  const initialKeyframes = {};\n  const transformKeys = [];\n  for (let key in keyframes) {\n    const value = keyframes[key];\n    if (isTransform(key)) {\n      if (transformAlias[key]) key = transformAlias[key];\n      transformKeys.push(key);\n      key = asTransformCssVar(key);\n    }\n    let initialKeyframe = Array.isArray(value) ? value[0] : value;\n    /**\n     * If this is a number and we have a default value type, convert the number\n     * to this type.\n     */\n    const definition = transformDefinitions.get(key);\n    if (definition) {\n      initialKeyframe = isNumber(value) ? definition.toDefaultUnit(value) : value;\n    }\n    initialKeyframes[key] = initialKeyframe;\n  }\n  if (transformKeys.length) {\n    initialKeyframes.transform = buildTransformTemplate(transformKeys);\n  }\n  return initialKeyframes;\n}\nexport { createStyles };", "map": {"version": 3, "names": ["isNumber", "isTransform", "transformAlias", "asTransformCssVar", "transformDefinitions", "buildTransformTemplate", "createStyles", "keyframes", "initialKeyframes", "transformKeys", "key", "value", "push", "initialKeyframe", "Array", "isArray", "definition", "get", "toDefaultUnit", "length", "transform"], "sources": ["/home/<USER>/Desktop/jarvis-ui/node_modules/@motionone/dom/dist/animate/utils/style-object.es.js"], "sourcesContent": ["import { isNumber } from '@motionone/utils';\nimport { isTransform, transformAlias, asTransformCssVar, transformDefinitions, buildTransformTemplate } from './transforms.es.js';\n\nfunction createStyles(keyframes) {\n    const initialKeyframes = {};\n    const transformKeys = [];\n    for (let key in keyframes) {\n        const value = keyframes[key];\n        if (isTransform(key)) {\n            if (transformAlias[key])\n                key = transformAlias[key];\n            transformKeys.push(key);\n            key = asTransformCssVar(key);\n        }\n        let initialKeyframe = Array.isArray(value) ? value[0] : value;\n        /**\n         * If this is a number and we have a default value type, convert the number\n         * to this type.\n         */\n        const definition = transformDefinitions.get(key);\n        if (definition) {\n            initialKeyframe = isNumber(value)\n                ? definition.toDefaultUnit(value)\n                : value;\n        }\n        initialKeyframes[key] = initialKeyframe;\n    }\n    if (transformKeys.length) {\n        initialKeyframes.transform = buildTransformTemplate(transformKeys);\n    }\n    return initialKeyframes;\n}\n\nexport { createStyles };\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,kBAAkB;AAC3C,SAASC,WAAW,EAAEC,cAAc,EAAEC,iBAAiB,EAAEC,oBAAoB,EAAEC,sBAAsB,QAAQ,oBAAoB;AAEjI,SAASC,YAAYA,CAACC,SAAS,EAAE;EAC7B,MAAMC,gBAAgB,GAAG,CAAC,CAAC;EAC3B,MAAMC,aAAa,GAAG,EAAE;EACxB,KAAK,IAAIC,GAAG,IAAIH,SAAS,EAAE;IACvB,MAAMI,KAAK,GAAGJ,SAAS,CAACG,GAAG,CAAC;IAC5B,IAAIT,WAAW,CAACS,GAAG,CAAC,EAAE;MAClB,IAAIR,cAAc,CAACQ,GAAG,CAAC,EACnBA,GAAG,GAAGR,cAAc,CAACQ,GAAG,CAAC;MAC7BD,aAAa,CAACG,IAAI,CAACF,GAAG,CAAC;MACvBA,GAAG,GAAGP,iBAAiB,CAACO,GAAG,CAAC;IAChC;IACA,IAAIG,eAAe,GAAGC,KAAK,CAACC,OAAO,CAACJ,KAAK,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK;IAC7D;AACR;AACA;AACA;IACQ,MAAMK,UAAU,GAAGZ,oBAAoB,CAACa,GAAG,CAACP,GAAG,CAAC;IAChD,IAAIM,UAAU,EAAE;MACZH,eAAe,GAAGb,QAAQ,CAACW,KAAK,CAAC,GAC3BK,UAAU,CAACE,aAAa,CAACP,KAAK,CAAC,GAC/BA,KAAK;IACf;IACAH,gBAAgB,CAACE,GAAG,CAAC,GAAGG,eAAe;EAC3C;EACA,IAAIJ,aAAa,CAACU,MAAM,EAAE;IACtBX,gBAAgB,CAACY,SAAS,GAAGf,sBAAsB,CAACI,aAAa,CAAC;EACtE;EACA,OAAOD,gBAAgB;AAC3B;AAEA,SAASF,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}