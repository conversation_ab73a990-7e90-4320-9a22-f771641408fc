{"ast": null, "code": "import { resizeElement } from './handle-element.es.js';\nimport { resizeWindow } from './handle-window.es.js';\nimport { isFunction } from '@motionone/utils';\nfunction resize(a, b) {\n  return isFunction(a) ? resizeWindow(a) : resizeElement(a, b);\n}\nexport { resize };", "map": {"version": 3, "names": ["resizeElement", "resizeWindow", "isFunction", "resize", "a", "b"], "sources": ["/home/<USER>/Desktop/jarvis-ui/node_modules/@motionone/dom/dist/gestures/resize/index.es.js"], "sourcesContent": ["import { resizeElement } from './handle-element.es.js';\nimport { resizeWindow } from './handle-window.es.js';\nimport { isFunction } from '@motionone/utils';\n\nfunction resize(a, b) {\n    return isFunction(a) ? resizeWindow(a) : resizeElement(a, b);\n}\n\nexport { resize };\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,wBAAwB;AACtD,SAASC,YAAY,QAAQ,uBAAuB;AACpD,SAASC,UAAU,QAAQ,kBAAkB;AAE7C,SAASC,MAAMA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAClB,OAAOH,UAAU,CAACE,CAAC,CAAC,GAAGH,YAAY,CAACG,CAAC,CAAC,GAAGJ,aAAa,CAACI,CAAC,EAAEC,CAAC,CAAC;AAChE;AAEA,SAASF,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}