{"ast": null, "code": "function isAnimationControls(v) {\n  return typeof v === \"object\" && typeof v.start === \"function\";\n}\nexport { isAnimationControls };", "map": {"version": 3, "names": ["isAnimationControls", "v", "start"], "sources": ["/home/<USER>/Desktop/jarvis-ui/node_modules/framer-motion/dist/es/animation/utils/is-animation-controls.mjs"], "sourcesContent": ["function isAnimationControls(v) {\n    return typeof v === \"object\" && typeof v.start === \"function\";\n}\n\nexport { isAnimationControls };\n"], "mappings": "AAAA,SAASA,mBAAmBA,CAACC,CAAC,EAAE;EAC5B,OAAO,OAAOA,CAAC,KAAK,QAAQ,IAAI,OAAOA,CAAC,CAACC,KAAK,KAAK,UAAU;AACjE;AAEA,SAASF,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}