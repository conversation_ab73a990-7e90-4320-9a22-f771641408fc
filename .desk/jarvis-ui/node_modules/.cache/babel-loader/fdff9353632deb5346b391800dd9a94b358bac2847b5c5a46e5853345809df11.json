{"ast": null, "code": "function stopAnimation(animation) {\n  let needsCommit = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;\n  if (!animation || animation.playState === \"finished\") return;\n  // Suppress error thrown by WAAPI\n  try {\n    if (animation.stop) {\n      animation.stop();\n    } else {\n      needsCommit && animation.commitStyles();\n      animation.cancel();\n    }\n  } catch (e) {}\n}\nexport { stopAnimation };", "map": {"version": 3, "names": ["stopAnimation", "animation", "needsCommit", "arguments", "length", "undefined", "playState", "stop", "commitStyles", "cancel", "e"], "sources": ["/home/<USER>/Desktop/jarvis-ui/node_modules/@motionone/dom/dist/animate/utils/stop-animation.es.js"], "sourcesContent": ["function stopAnimation(animation, needsCommit = true) {\n    if (!animation || animation.playState === \"finished\")\n        return;\n    // Suppress error thrown by WAAPI\n    try {\n        if (animation.stop) {\n            animation.stop();\n        }\n        else {\n            needsCommit && animation.commitStyles();\n            animation.cancel();\n        }\n    }\n    catch (e) { }\n}\n\nexport { stopAnimation };\n"], "mappings": "AAAA,SAASA,aAAaA,CAACC,SAAS,EAAsB;EAAA,IAApBC,WAAW,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;EAChD,IAAI,CAACF,SAAS,IAAIA,SAAS,CAACK,SAAS,KAAK,UAAU,EAChD;EACJ;EACA,IAAI;IACA,IAAIL,SAAS,CAACM,IAAI,EAAE;MAChBN,SAAS,CAACM,IAAI,CAAC,CAAC;IACpB,CAAC,MACI;MACDL,WAAW,IAAID,SAAS,CAACO,YAAY,CAAC,CAAC;MACvCP,SAAS,CAACQ,MAAM,CAAC,CAAC;IACtB;EACJ,CAAC,CACD,OAAOC,CAAC,EAAE,CAAE;AAChB;AAEA,SAASV,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}