import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np

# Simple test to check for NaN issues
def test_model_components():
    print("Testing model components for NaN issues...")
    
    # Test with a simple input
    batch_size = 2
    d_model = 80
    seq_len = 196  # 14*14
    
    # Create test input
    x = torch.randn(batch_size, seq_len, d_model)
    print(f"Input shape: {x.shape}")
    print(f"Input contains NaN: {torch.isnan(x).any()}")
    print(f"Input contains Inf: {torch.isinf(x).any()}")
    
    # Test MambaBlock
    from train_segmentation_model_test import MambaBlock
    mamba = MambaBlock(d_model)
    
    print("\n--- Testing MambaBlock ---")
    try:
        output = mamba(x)
        print(f"MambaBlock output shape: {output.shape}")
        print(f"MambaBlock output contains NaN: {torch.isnan(output).any()}")
        print(f"MambaBlock output contains Inf: {torch.isinf(output).any()}")
        print(f"MambaBlock output min/max: {output.min().item():.6f} / {output.max().item():.6f}")
    except Exception as e:
        print(f"MambaBlock failed: {e}")
    
    # Test TransformerBlock
    from train_segmentation_model_test import TransformerBlock
    transformer = TransformerBlock(d_model)
    
    print("\n--- Testing TransformerBlock ---")
    try:
        output = transformer(x)
        print(f"TransformerBlock output shape: {output.shape}")
        print(f"TransformerBlock output contains NaN: {torch.isnan(output).any()}")
        print(f"TransformerBlock output contains Inf: {torch.isinf(output).any()}")
        print(f"TransformerBlock output min/max: {output.min().item():.6f} / {output.max().item():.6f}")
    except Exception as e:
        print(f"TransformerBlock failed: {e}")
    
    # Test full model with dummy data
    from train_segmentation_model_test import HybridMambaMViTUNet
    model = HybridMambaMViTUNet(num_classes=6, d_model=80)
    
    print("\n--- Testing Full Model ---")
    dummy_input = torch.randn(2, 3, 224, 224)
    print(f"Model input shape: {dummy_input.shape}")
    
    try:
        with torch.no_grad():
            output = model(dummy_input)
            print(f"Model output shape: {output.shape}")
            print(f"Model output contains NaN: {torch.isnan(output).any()}")
            print(f"Model output contains Inf: {torch.isinf(output).any()}")
            print(f"Model output min/max: {output.min().item():.6f} / {output.max().item():.6f}")
    except Exception as e:
        print(f"Full model failed: {e}")
    
    # Test loss computation
    print("\n--- Testing Loss Computation ---")
    dummy_target = torch.randint(0, 6, (2, 224, 224))
    loss_fn = nn.CrossEntropyLoss()
    
    try:
        with torch.no_grad():
            output = model(dummy_input)
            loss = loss_fn(output, dummy_target)
            print(f"Loss value: {loss.item()}")
            print(f"Loss contains NaN: {torch.isnan(loss).any()}")
    except Exception as e:
        print(f"Loss computation failed: {e}")

if __name__ == "__main__":
    test_model_components()
