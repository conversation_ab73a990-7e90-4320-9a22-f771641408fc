import os
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import DataLoader, Dataset
from torchvision import transforms
from PIL import Image, ImageDraw
import numpy as np

class MambaBlock(nn.Module):
    def __init__(self, d_model, d_state=16, d_conv=4, expand=2):
        super(MambaBlock, self).__init__()
        self.d_model = d_model
        self.d_state = d_state
        self.d_conv = d_conv
        self.expand = expand
        self.d_inner = expand * d_model

        # Input projection
        self.in_proj = nn.Linear(d_model, self.d_inner * 2)
        self.conv1d = nn.Conv1d(self.d_inner, self.d_inner, kernel_size=d_conv, padding=d_conv-1, groups=self.d_inner)
        self.out_proj = nn.Linear(self.d_inner, d_model)

        # SSM parameters
        self.A = nn.Parameter(torch.randn(self.d_state))
        self.B = nn.Parameter(torch.randn(self.d_inner, self.d_state))
        self.C = nn.Parameter(torch.randn(self.d_state, self.d_inner))
        self.D = nn.Parameter(torch.randn(self.d_inner))
        self.delta = nn.Parameter(torch.randn(self.d_state))

        # Feed-forward network
        self.ffn = nn.Sequential(
            nn.Linear(self.d_inner, self.d_inner * 4),
            nn.ReLU(),
            nn.Linear(self.d_inner * 4, self.d_inner)
        )
        self.norm1 = nn.LayerNorm(self.d_inner)
        self.norm2 = nn.LayerNorm(self.d_inner)

    def forward(self, x):
        batch, seq_len, _ = x.shape
        # Project input and split
        x_and_res = self.in_proj(x)  # [batch, seq_len, 2*d_inner]
        x, res = x_and_res.chunk(2, dim=-1)  # [batch, seq_len, d_inner]

        # Convolution
        x = x.transpose(1, 2)  # [batch, d_inner, seq_len]
        x = self.conv1d(x)[:, :, :seq_len]  # [batch, d_inner, seq_len]
        x = x.transpose(1, 2)  # [batch, seq_len, d_inner]
        x = F.silu(x)

        # SSM computation
        delta = F.softplus(self.delta)  # [d_state]
        A = -torch.exp(self.A)  # [d_state]
        h = torch.zeros(batch, self.d_state, device=x.device)  # [batch, d_state]
        x_ssm = torch.zeros_like(x)  # [batch, seq_len, d_inner]

        for t in range(seq_len):
            h = h * torch.exp(delta)  # [batch, d_state] * [d_state]
            h = h + torch.einsum('id,bi->bd', self.B, x[:, t, :])  # [batch, d_state]
            y_t = torch.einsum('sd,bs->bd', self.C, h)  # [batch, d_inner]
            x_ssm[:, t] = y_t + self.D * x[:, t]  # [batch, d_inner]

        # Combine with residual and normalize
        x = self.norm1(x_ssm + res)
        x = self.ffn(x)
        x = self.norm2(x + x_ssm)
        x = self.out_proj(x)  # [batch, seq_len, d_model]
        return x

class TransformerBlock(nn.Module):
    """
    A basic Transformer block for global context modeling.

    Args:
        d_model (int): Model dimension.
        nhead (int): Number of attention heads.
    """
    def __init__(self, d_model, nhead=4):
        super(TransformerBlock, self).__init__()
        self.self_attn = nn.MultiheadAttention(d_model, nhead)
        self.norm1 = nn.LayerNorm(d_model)
        self.ffn = nn.Sequential(
            nn.Linear(d_model, d_model * 4),
            nn.ReLU(),
            nn.Linear(d_model * 4, d_model)
        )
        self.norm2 = nn.LayerNorm(d_model)

    def forward(self, x):
        """
        Forward pass of the Transformer block.

        Args:
            x (torch.Tensor): Input tensor of shape [batch, seq_len, d_model]

        Returns:
            torch.Tensor: Output tensor of shape [batch, seq_len, d_model]
        """
        x = x.transpose(0, 1)  # [seq_len, batch, d_model]
        attn_output, _ = self.self_attn(x, x, x)
        x = self.norm1(x + attn_output)
        ffn_output = self.ffn(x)
        x = self.norm2(x + ffn_output)
        x = x.transpose(0, 1)  # [batch, seq_len, d_model]
        return x

class VisionMamba(nn.Module):
    """
    A simplified Vision Mamba encoder for image feature extraction.

    Args:
        d_model (int): Model dimension.
        num_layers (int): Number of Mamba and Transformer blocks.
        patch_size (int): Patch size for image tokenization.
    """
    def __init__(self, d_model=80, patch_size=16):
        super(VisionMamba, self).__init__()
        self.d_model = d_model

        # Initial patch embedding
        self.patch_embed = nn.Conv2d(3, d_model, kernel_size=patch_size, stride=patch_size)
        self.pos_embed = nn.Parameter(torch.randn(1, d_model, 14, 14))  # For 224x224 images

        # Create 4 encoder stages for UNet-style architecture
        self.stage1_mamba = MambaBlock(d_model)
        self.stage1_transformer = TransformerBlock(d_model)
        self.stage1_conv = nn.Conv2d(d_model, d_model, kernel_size=3, padding=1)

        self.down1 = nn.Conv2d(d_model, 2*d_model, kernel_size=3, stride=2, padding=1)
        self.stage2_mamba = MambaBlock(2*d_model)
        self.stage2_transformer = TransformerBlock(2*d_model)
        self.stage2_conv = nn.Conv2d(2*d_model, 2*d_model, kernel_size=3, padding=1)

        self.down2 = nn.Conv2d(2*d_model, 4*d_model, kernel_size=3, stride=2, padding=1)
        self.stage3_mamba = MambaBlock(4*d_model)
        self.stage3_transformer = TransformerBlock(4*d_model)
        self.stage3_conv = nn.Conv2d(4*d_model, 4*d_model, kernel_size=3, padding=1)

        self.down3 = nn.Conv2d(4*d_model, 8*d_model, kernel_size=3, stride=2, padding=1)
        self.stage4_mamba = MambaBlock(8*d_model)
        self.stage4_transformer = TransformerBlock(8*d_model)
        self.stage4_conv = nn.Conv2d(8*d_model, 8*d_model, kernel_size=3, padding=1)

    def forward(self, x):
        """
        Forward pass of the Vision Mamba encoder.

        Args:
            x (torch.Tensor): Input tensor of shape [batch, 3, H, W]

        Returns:
            list: List of feature maps at different scales [e1, e2, e3, e4]
        """
        # Initial patch embedding
        x = self.patch_embed(x)  # [batch, d_model, 14, 14]
        x = x + self.pos_embed

        # Stage 1: [batch, d_model, 14, 14]
        x_flat = x.flatten(2).transpose(1, 2)  # [batch, 196, d_model]
        x_flat = self.stage1_mamba(x_flat)
        x_flat = self.stage1_transformer(x_flat)
        e1 = x_flat.view(x.size(0), 14, 14, -1).permute(0, 3, 1, 2)  # [batch, d_model, 14, 14]
        e1 = self.stage1_conv(e1)

        # Stage 2: [batch, 2*d_model, 7, 7]
        x = self.down1(e1)  # [batch, 2*d_model, 7, 7]
        x_flat = x.flatten(2).transpose(1, 2)  # [batch, 49, 2*d_model]
        x_flat = self.stage2_mamba(x_flat)
        x_flat = self.stage2_transformer(x_flat)
        e2 = x_flat.view(x.size(0), 7, 7, -1).permute(0, 3, 1, 2)  # [batch, 2*d_model, 7, 7]
        e2 = self.stage2_conv(e2)

        # Stage 3: [batch, 4*d_model, 3, 3] (rounded down from 3.5)
        x = self.down2(e2)  # [batch, 4*d_model, 3, 3]
        h3, w3 = x.shape[2], x.shape[3]  # Get actual spatial dimensions
        x_flat = x.flatten(2).transpose(1, 2)  # [batch, h3*w3, 4*d_model]
        x_flat = self.stage3_mamba(x_flat)
        x_flat = self.stage3_transformer(x_flat)
        e3 = x_flat.view(x.size(0), h3, w3, -1).permute(0, 3, 1, 2)  # [batch, 4*d_model, h3, w3]
        e3 = self.stage3_conv(e3)

        # Stage 4: [batch, 8*d_model, 1, 1] (rounded down from 1.5)
        x = self.down3(e3)  # [batch, 8*d_model, 1, 1]
        h4, w4 = x.shape[2], x.shape[3]  # Get actual spatial dimensions
        x_flat = x.flatten(2).transpose(1, 2)  # [batch, h4*w4, 8*d_model]
        x_flat = self.stage4_mamba(x_flat)
        x_flat = self.stage4_transformer(x_flat)
        e4 = x_flat.view(x.size(0), h4, w4, -1).permute(0, 3, 1, 2)  # [batch, 8*d_model, h4, w4]
        e4 = self.stage4_conv(e4)

        return [e1, e2, e3, e4]

class HybridMambaMViTUNet(nn.Module):
    """
    A hybrid UNet architecture for X-ray segmentation using Vision Mamba and Transformer blocks.

    Args:
        num_classes (int): Number of output classes (e.g., 6 for 5 grades + background).
        d_model (int): Model dimension.
        num_layers (int): Number of layers in the encoder.
    """
    def __init__(self, num_classes, d_model=80):
        super(HybridMambaMViTUNet, self).__init__()

        # Encoder
        self.encoder = VisionMamba(d_model=d_model)

        # Decoder layers
        self.up1 = nn.ConvTranspose2d(8*d_model, 4*d_model, kernel_size=2, stride=2)
        self.conv1 = nn.Sequential(
            nn.Conv2d(4*d_model + 4*d_model, 4*d_model, kernel_size=3, padding=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(4*d_model, 4*d_model, kernel_size=3, padding=1),
            nn.ReLU(inplace=True)
        )
        self.up2 = nn.ConvTranspose2d(4*d_model, 2*d_model, kernel_size=2, stride=2)
        self.conv2 = nn.Sequential(
            nn.Conv2d(2*d_model + 2*d_model, 2*d_model, kernel_size=3, padding=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(2*d_model, 2*d_model, kernel_size=3, padding=1),
            nn.ReLU(inplace=True)
        )
        self.up3 = nn.ConvTranspose2d(2*d_model, d_model, kernel_size=2, stride=2)
        self.conv3 = nn.Sequential(
            nn.Conv2d(d_model + d_model, d_model, kernel_size=3, padding=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(d_model, d_model, kernel_size=3, padding=1),
            nn.ReLU(inplace=True)
        )
        self.up4 = nn.ConvTranspose2d(d_model, d_model, kernel_size=4, stride=4)
        self.final_conv = nn.Conv2d(d_model, num_classes, kernel_size=1)

    def forward(self, x):
        """
        Forward pass of the hybrid UNet.

        Args:
            x (torch.Tensor): Input tensor of shape [batch, in_channels, H, W]

        Returns:
            torch.Tensor: Output segmentation map of shape [batch, num_classes, H, W]
        """
        # Encoder
        features = self.encoder(x)  # List of feature maps
        e1, e2, e3, e4 = features

        # Decoder with skip connections
        d4 = self.up1(e4)
        # Resize d4 to match e3 spatial dimensions
        d4 = F.interpolate(d4, size=(e3.shape[2], e3.shape[3]), mode='bilinear', align_corners=False)
        d3 = torch.cat([d4, e3], dim=1)
        d3 = self.conv1(d3)

        d2 = self.up2(d3)
        # Resize d2 to match e2 spatial dimensions
        d2 = F.interpolate(d2, size=(e2.shape[2], e2.shape[3]), mode='bilinear', align_corners=False)
        d1 = torch.cat([d2, e2], dim=1)
        d1 = self.conv2(d1)

        d0 = self.up3(d1)
        # Resize d0 to match e1 spatial dimensions
        d0 = F.interpolate(d0, size=(e1.shape[2], e1.shape[3]), mode='bilinear', align_corners=False)
        d0 = torch.cat([d0, e1], dim=1)
        d0 = self.conv3(d0)

        out = self.up4(d0)
        out = self.final_conv(out)

        return out

class SegmentationDataset(Dataset):
    """
    Dataset class for loading X-ray images and YOLO-format polygon annotations.

    Args:
        image_dir (str): Directory containing images.
        label_dir (str): Directory containing label files.
        transform: Optional transform to apply to images.
    """
    def __init__(self, image_dir, label_dir, transform=None):
        self.image_dir = image_dir
        self.label_dir = label_dir
        self.transform = transform
        self.images = sorted(os.listdir(image_dir))
        self.labels = sorted(os.listdir(label_dir))
        assert len(self.images) == len(self.labels)

    def __len__(self):
        return len(self.images)

    def __getitem__(self, idx):
        img_path = os.path.join(self.image_dir, self.images[idx])
        label_path = os.path.join(self.label_dir, self.labels[idx])
        image = Image.open(img_path).convert("RGB")
        with open(label_path, 'r') as f:
            lines = f.readlines()
        width, height = image.size
        mask = Image.new('L', (width, height), 0)
        draw = ImageDraw.Draw(mask)
        for line in lines:
            parts = line.strip().split()
            class_idx = int(parts[0]) + 1  # Shift by 1, 0 is background
            points = [float(p) for p in parts[1:]]
            points = [(points[i]*width, points[i+1]*height) for i in range(0, len(points), 2)]
            draw.polygon(points, fill=class_idx)
        if self.transform:
            image = self.transform(image)
            mask = mask.resize((224, 224), Image.Resampling.NEAREST)
            mask = torch.tensor(np.array(mask), dtype=torch.long)
        return image, mask

# Define transforms for images
transform = transforms.Compose([
    transforms.Resize((224, 224)),
    transforms.ToTensor(),
    transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
])

# Set the base path to your dataset root directory
base_path = 'datset'  # Replace with your actual dataset path

# Define dataset paths
train_image_dir = os.path.join(base_path, 'train', 'images')
train_label_dir = os.path.join(base_path, 'train', 'labels')
val_image_dir = os.path.join(base_path, 'valid', 'images')
val_label_dir = os.path.join(base_path, 'valid', 'labels')

# Create datasets
train_dataset = SegmentationDataset(train_image_dir, train_label_dir, transform=transform)
val_dataset = SegmentationDataset(val_image_dir, val_label_dir, transform=transform)

# Create data loaders
train_loader = DataLoader(train_dataset, batch_size=16, shuffle=True)
val_loader = DataLoader(val_dataset, batch_size=16, shuffle=False)

# Initialize the model
model = HybridMambaMViTUNet(num_classes=6, d_model=80)  # 5 grades + background
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
model.to(device)

# Define loss function and optimizer
loss_fn = nn.CrossEntropyLoss()
optimizer = torch.optim.Adam(model.parameters(), lr=1e-4)

# Training loop
num_epochs = 10
for epoch in range(num_epochs):
    model.train()
    for images, masks in train_loader:
        images = images.to(device)
        masks = masks.to(device)
        outputs = model(images)
        loss = loss_fn(outputs, masks)
        optimizer.zero_grad()
        loss.backward()
        optimizer.step()
    # Validation
    model.eval()
    with torch.no_grad():
        val_loss = 0
        for images, masks in val_loader:
            images = images.to(device)
            masks = masks.to(device)
            outputs = model(images)
            loss = loss_fn(outputs, masks)
            val_loss += loss.item()
        val_loss /= len(val_loader)
        print(f'Epoch {epoch+1}, Val Loss: {val_loss}')
